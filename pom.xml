<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cloud</groupId>
    <artifactId>fi-consumer</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <description>财务中台集成</description>
    <modules>
        <module>fi-consumer-core</module>
        <module>fi-consumer-provider</module>
        <module>fi-consumer-api</module>
    </modules>

    <properties>
        <spring-boot.version>2.6.14</spring-boot.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cloud</groupId>
                <artifactId>cloud-common-bom</artifactId>
                <version>3.9.5-GA-2311-1</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <includeSystemScope>true</includeSystemScope>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <target>${maven.compiler.target}</target>
                    <source>${maven.compiler.source}</source>
                    <encoding>UTF-8</encoding>
                    <skip>true</skip>
                    <!--					<forceJavacCompilerUse>true</forceJavacCompilerUse>-->
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <version>3.1.0</version>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <version>2.2.5</version>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <repository>
            <id>maven-public-proxy</id>
            <url>http://nexus.jy.hyit.com.cn:8081/repository/chanye-maven-proxy/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>maven-public-releases</id>
            <url>http://nexus.jy.hyit.com.cn:8081/repository/chanye-maven-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>maven-public-snapshots</id>
            <url>http://nexus.jy.hyit.com.cn:8081/repository/chanye-maven-snapshots/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>maven-public-proxy</id>
            <url>http://nexus.jy.hyit.com.cn:8081/repository/chanye-maven-proxy/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>maven-public-snapshots</id>
            <url>http://nexus.jy.hyit.com.cn:8081/repository/chanye-maven-releases/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <id>maven-public-releases</id>
            <url>http://nexus.jy.hyit.com.cn:8081/repository/chanye-maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>maven-public-snapshots</id>
            <url>http://nexus.jy.hyit.com.cn:8081/repository/chanye-maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <!--不同环境打包配置文件-->
    <profiles>
        <profile>
            <id>prod</id>
            <properties>
                <package.environment>prod</package.environment>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--        <profile>-->
        <!--            <id>cy</id>-->
        <!--            <properties>-->
        <!--                <package.environment>cy</package.environment>-->
        <!--            </properties>-->
        <!--        </profile>-->
    </profiles>
</project>
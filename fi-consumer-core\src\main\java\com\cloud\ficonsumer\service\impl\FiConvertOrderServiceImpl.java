
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertOrder;
import com.cloud.ficonsumer.mapper.FiConvertOrderMapper;
import com.cloud.ficonsumer.service.FiConvertOrderService;
import org.springframework.stereotype.Service;

/**
 * 订单转换数据主表
 *
 * <AUTHOR>
 * @date 2025-01-16 16:52:58
 */
@Service
public class FiConvertOrderServiceImpl extends ServiceImpl<FiConvertOrderMapper, FiConvertOrder> implements FiConvertOrderService {

}

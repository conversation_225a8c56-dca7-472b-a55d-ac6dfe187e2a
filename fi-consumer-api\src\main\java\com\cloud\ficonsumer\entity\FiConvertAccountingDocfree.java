

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 凭证记账辅助核算转换数据明细表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:41
 */
@Data
@TableName("fi_convert_accounting_docfree")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "凭证记账辅助核算转换数据明细表")
public class FiConvertAccountingDocfree extends BaseEntity<FiConvertAccountingDocfree> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 辅助核算
     */
    @ApiModelProperty(value="辅助核算")
    private String assid;

    /**
     * 辅助核算
     */
    @ApiModelProperty(value="辅助核算")
    private String type;

    /**
     * 辅助核算
     */
    @ApiModelProperty(value="辅助核算")
    private String value;

    /**
     * 分录主键
     */
    @ApiModelProperty(value="分录主键")
    private String pkDetail;
}

package com.cloud.ficonsumer.common;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 智慧平台组织转换信息
 *
 * <AUTHOR>
 */
@Data
@TableName("fi_org_convert")
@ApiModel(value = "智慧平台组织转换信息")
public class FiOrgConvert {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "业+组织ID")
    private Long orgId;

    @ApiModelProperty(value = "组织主键")
    private String pkOrg;

    @ApiModelProperty(value = "组织编码")
    private String orgCode;
    
    @ApiModelProperty(value = "组织名称")
    private String orgName;

    @ApiModelProperty(value = "MDM组织编码")
    private String mdmOrgCode;

    @ApiModelProperty(value = "组织级参数编码")
    private String initCode;

    @ApiModelProperty(value = "组织级参数值")
    private String paramValue;
}


package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.*;
import com.cloud.ficonsumer.entity.FiConvertAccountingDocfree;
import com.cloud.ficonsumer.entity.FiSourceAccounting;
import com.cloud.ficonsumer.entity.FiSourceAccountingDocfree;
import com.cloud.ficonsumer.vo.FiSourceAccountingQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 采购入库源数据主表
 *
 * <AUTHOR>
 * @date 2025-04-08 15:02:35
 */
@Mapper
public interface FiSourceAccountingMapper extends CloudBaseMapper<FiSourceAccounting> {

    @Select("<script>SELECT * " +
            " FROM fi_source_accounting t " +
            " WHERE t.del_flag = 0" +
            "<if test=\"param.pkVoucher !=null and param.pkVoucher !=''\"> " +
            " and pk_voucher = #{pkVoucher} " +
            "</if> " +
            "<if test=\"param.num !=null and param.num !=''\"> " +
            " and num = #{num} " +
            "</if> " +
            "<if test=\"param.period !=null and param.period !=''\"> " +
            " and period = #{period} " +
            "</if> " +
            "<if test=\"param.pkOrgList != null and param.pkOrgList.size() > 0\"> " +
            "   AND pk_org in " +
            "   <foreach collection=\"param.pkOrgList\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "      #{item} " +
            "   </foreach>" +
            " </if> " +
            "<if test=\"param.groupCodeFlag !=null and param.groupCodeFlag !=''\"> " +
            " and pk_org in (select pk_org " +
            "       from fi_org_compare " +
            "       where group_code = #{param.groupCodeFlag} and del_flag =0) " +
            "</if> " +
            "<if test=\"param.pushStatus !=null and param.pushStatus !=''\"> " +
            " and push_status = #{param.pushStatus} " +
            "</if> " +
            "<if test=\"param.pushStatusList != null and param.pushStatusList.size() > 0\">" +
            " and push_status in " +
            "<foreach collection=\"param.pushStatusList\" item=\"item\" open=\"(\" separator=\",\" close=\")\">" +
            "#{item}" +
            "</foreach>" +
            "</if> " +
            "<if test=\"param.createTimeStart !=null and param.createTimeStart !=''\"> " +
            " and create_time >= #{param.createTimeStart} " +
            "</if> " +
            "<if test=\"param.createTimeEnd !=null and param.createTimeEnd !=''\"> " +
            " and #{param.createTimeEnd} >= create_time " +
            "</if> " +
            " order by create_time desc  " +
            "</script>")
    Page<FiSourceAccountingDTO> beforeConvertDataPage(Page page, @Param("param") FiSourceAccountingQueryVO queryVO);

    @Select("<script>" +
            "select * " +
            "from fi_source_accounting_detail " +
            "where del_flag = 0 and pk_voucher = #{param.pkVoucher}</script>")
    Page<FiSourceAccountingDetailDTO> getBeforeConvertDataPage(Page page, @Param("param") FiSourceAccountingQueryVO queryVO);

    @Select("<script>SELECT * " +
            "    FROM fi_convert_accounting_detail t " +
            "    WHERE t.del_flag = 0 and t.pk_voucher = #{pk}" +
            "</script>")
    Page<FiConvertAccountingDetailDTO> getConvertData(Page page,@Param("pk") String pk);

    @Select("select " +
            "* " +
            "from fi_source_accounting_docfree " +
            "where del_flag = 0 and pk_detail = #{pk}")
    List<FiSourceAccountingDocfree> getBeforeConvertDocfreeData(@Param("pk") String pk);

    @Select("select " +
            "* " +
            "from fi_convert_accounting_docfree " +
            "where del_flag = 0 and pk_detail = #{pk}")
    List<FiConvertAccountingDocfree> getConvertDocfreeData(@Param("pk") String pk);


}

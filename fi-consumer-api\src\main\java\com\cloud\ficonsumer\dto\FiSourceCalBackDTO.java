package com.cloud.ficonsumer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/2.
 */
@Data
public class FiSourceCalBackDTO {

    /**
     * 业务类型
     * 1、预付款；
     * 2、付款申请；
     * 3、发票校验，
     * 4非电收入确认
     * 5银行到账通知单
     * 6成本报账
     * 7成本预付款
     * 8成本付款申请
     */
    private String biType;

    /**
     * 返回同步消息
     */
    @ApiModelProperty(value="返回同步消息")
    private String returnMsg;

    /**
     * 成功消息
     */
    @ApiModelProperty(value="成功消息")
    private String successMsg;

    /**
     * 返回同步状态
     */
    @ApiModelProperty(value="返回同步状态")
    private String returnStatus;

    /**
     * 返回单号
     */
    @ApiModelProperty(value="返回单号")
    private String returnBill;

    /**
     * 返回接收时间
     */
    @ApiModelProperty(value="返回接收时间")
    private String returnTime;

    /**
     * 编码
     */
    @ApiModelProperty(value="编码")
    private String billno;

    @ApiModelProperty(value = "远光回调ID")
    private String ywid;
}

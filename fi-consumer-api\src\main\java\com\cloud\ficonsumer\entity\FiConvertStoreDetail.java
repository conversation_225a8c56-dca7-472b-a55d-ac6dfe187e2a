

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采购入库详情数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:46
 */
@Data
@TableName("fi_convert_store_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购入库详情数据主表")
public class FiConvertStoreDetail extends BaseEntity<FiConvertStoreDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 采购入库单明细主键
     */
    @ApiModelProperty(value="采购入库单明细主键")
    private String cbillBid;

    /**
     * 入库单主键
     */
    @ApiModelProperty(value="入库单主键")
    private String cbillid;

    /**
     * 订单编号
     */
    @ApiModelProperty(value="订单编号")
    private String vbillcode;

    /**
     * 行号
     */
    @ApiModelProperty(value="行号")
    private String crownoOrder;

    /**
     * 主数量
     */
    @ApiModelProperty(value="主数量")
    private String nnum;

    /**
     * 含税单价
     */
    @ApiModelProperty(value="含税单价")
    private String nqtorigtaxprice;

    /**
     * 单价
     */
    @ApiModelProperty(value="单价")
    private String nprice;

    /**
     * 税率
     */
    @ApiModelProperty(value="税率")
    private String ntaxrate;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String ntax;

    /**
     * 价税合计
     */
    @ApiModelProperty(value="价税合计")
    private String norigtaxmny;

    /**
     * 金额
     */
    @ApiModelProperty(value="金额")
    private String nmny;

    /**
     * 批次号
     */
    @ApiModelProperty(value="批次号")
    private String vbatchcode;

    /**
     * 项目
     */
    @ApiModelProperty(value="项目")
    private String cprojectid;

    /**
     * 物料多版本
     */
    @ApiModelProperty(value="物料多版本")
    private String cinventoryvid;

    /**
     * 行号
     */
    @ApiModelProperty(value="行号")
    private String crowno;

    /**
     * 规格
     */
    @ApiModelProperty(value="规格")
    private String pkMarbasclass;

    /**
     * 行号
     */
    @ApiModelProperty(value="行号")
    private String materialspec;

    /**
     * 主计量单位
     */
    @ApiModelProperty(value="主计量单位")
    private String pkMeasdoc;

    /**
     * 来源单据类型
     * 45 上游是“库存采购入库单”
     * 27 上游是“采购结算单”
     */
    @ApiModelProperty(value="来源单据类型")
    private String vsrctype;

    /**
     * 用途
     */
    @ApiModelProperty(value="用途")
    private String vbdef7;
}

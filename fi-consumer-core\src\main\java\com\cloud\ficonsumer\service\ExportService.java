package com.cloud.ficonsumer.service;


import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.listener.AbstractExportListener;
import com.cloud.ficonsumer.vo.ApiCompareVO;

import javax.servlet.http.HttpServletResponse;


/**
 * @Author: zhengjinjin
 * @Descrition: 导出excel文件 接口
 * @Date: 2020/11/11
 */
public interface ExportService {

    /**
     * 异步导出数据
     *
     * @param listener 导出listener
     * @param <T> 查询DTO类型
     */
    <T> void exportBeforeConvertData(UUID id, AbstractExportListener<T> listener);

    /**
     * 异步导出对照表信息
     *
     * @param page
     * @param id
     */
    void exportCompareAsync(Page page, ApiCompareVO vo, String id);

    /**
     * 导出通用报销单的统计
     *
     * @param billType
     * @param response
     */
    void exportStatistics(BillTypeEnum billType, HttpServletResponse response);
}

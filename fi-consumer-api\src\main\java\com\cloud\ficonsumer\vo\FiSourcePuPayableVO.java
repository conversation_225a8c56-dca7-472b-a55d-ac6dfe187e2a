package com.cloud.ficonsumer.vo;

import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FiSourcePuPayableVO extends FiSourcePuPayable {

    @ApiModelProperty(value = "接收状态")
    private String def24;

    @ApiModelProperty(value = "源头单据主键")
    private String srcBillId;

    /**
     * 返回状态
     */
    private String returnStatusName;


    @ApiModelProperty(value = "应付类型名称")
    private String pkTradetypeName;

    @ApiModelProperty(value="供应商名称")
    private String pkSupplierName;

    @ApiModelProperty(value = "制单人名称")
    private String billmakerName;

    @ApiModelProperty(value = "币种")
    private String pkCurrtypeName;

    /**
     * 最末级平台业务事项名称
     */
    @ApiModelProperty(value = "平台业务事项")
    private String def47Name;

    @ApiModelProperty(value = "订单号")
    private String billcode;
}

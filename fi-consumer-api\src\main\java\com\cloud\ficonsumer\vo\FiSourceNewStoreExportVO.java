package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购/项目应付导出VO
 */
@Data
@ColumnWidth(25)
public class FiSourceNewStoreExportVO {

    @ExcelProperty(value = "应付财务组织", index = 0)
    private String pkOrgV;

    @ExcelProperty(value = "应付单标识", index = 1)
    private String pkPayablebill;

    @ExcelProperty(value = "源头单据主键", index = 2)
    private String srcBillid;

    @ExcelProperty(value = "订单编号", index = 3)
    private String vbillcode;

    @ExcelProperty(value = "单据日期", index = 4)
    private String billdate;

    @ExcelProperty(value = "单据号", index = 5)
    private String billno;

    @ExcelProperty(value = "应付类型", index = 6)
    private String pkCurrtype;

    @ExcelProperty(value = "推送状态", index = 7)
    private Integer pushStatus;

    @ExcelProperty(value = "推送状态名称", index = 8)
    private String pushStatusName;

    @ExcelProperty(value = "推送成功失败信息", index = 9)
    private String pushMsg;
}
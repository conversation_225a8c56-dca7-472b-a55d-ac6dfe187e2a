

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * bip供应商
 *
 * <AUTHOR>
 * @date 2024-07-10 15:22:53
 */
@Data
@TableName("api_project")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "工程项目")
public class ApiProject extends BaseEntity<ApiProject> {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId
    @ApiModelProperty(value="ID")
    private Long id;

    /**
     * 工程主键
     */
    @NotBlank(message = "工程主键不可为空")
    @ApiModelProperty(value="工程主键")
    private String def1;

    /**
     * 管控ID
     */
    @ApiModelProperty(value="管控ID")
    private String def2;

    /**
     * 管理组织
     */
    @NotBlank(message = "立项单位请配置对应的erp编码")
    @ApiModelProperty(value="管理组织")
    private String pkOrg;

    /**
     * 管理组织名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value="管理组织名称")
    private String pkOrgName;

    /**
     * 管理组织名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value="管理组织名称")
    private String orgTurnName;

    /**
     * 管控组织id
     */
    @ApiModelProperty(value="管控组织id")
    private String controlOrgId;

    /**
     * 项目编码
     */
    @ApiModelProperty(value="项目编码")
    private String projectCode;

    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不可为空")
    @ApiModelProperty(value="项目名称")
    private String projectName;

    /**
     * 主业项目编码
     */
    @ApiModelProperty(value="主业项目编码")
    private String mainCode;

    /**
     * 是否历史导入项目，0-否，1-是
     */
    @NotBlank(message = "是否历史导入项目不可为空")
    @ApiModelProperty(value="是否历史导入项目，0-否，1-是")
    private String isHistoryPro;

    /**
     * 返回同步消息
     */
    @ApiModelProperty(value="返回同步消息")
    private String returnMsg;

    /**
     * 返回同步状态
     * 0 处理成功 1 处理失败
     */
    @ApiModelProperty(value="返回同步状态")
    private String returnStatus;

    /**
     * 重试次数
     */
    @ApiModelProperty(value="重试次数")
    private Integer tryNumber;


    /**
     * 管控码是否为空，0-否，1-是
     */
    @TableField(exist = false)
    @ApiModelProperty(value="管控码是否为空，0-否，1-是")
    private String isEmptyStr;


    /**
     * 是否历史导入项目，0-否，1-是
     */
    @TableField(exist = false)
    @ApiModelProperty(value="是否历史导入项目，0-否，1-是")
    private String isHistoryProStr;
}

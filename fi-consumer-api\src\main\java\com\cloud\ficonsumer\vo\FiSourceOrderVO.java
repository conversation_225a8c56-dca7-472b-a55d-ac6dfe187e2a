package com.cloud.ficonsumer.vo;

import com.cloud.ficonsumer.entity.FiSourceOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/17.
 */
@Data
public class FiSourceOrderVO extends FiSourceOrder {

    /**
     * 订单详情接口
     */
    private List<FiSourceOrderDetailVO> detailList;

    @ApiModelProperty(value = "应付财务组织编码", required = true)
    private String pkOrgCode;

    /**
     * 采购部门
     */
    @ApiModelProperty(value="采购部门")
    private String pkDeptV;

    /**
     * 订单日期
     */
    @ApiModelProperty(value="订单日期")
    private String dbilldate;

    /**
     * 价税合计
     */
    @ApiModelProperty(value="价税合计")
    private String ntotalorigmny;

    /**
     * 单据状态
     */
    @ApiModelProperty(value="单据状态")
    private String forderstatus;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloud.ficonsumer.mapper.FiSourceProjEstiPayableMapper">

    <select id="beforeConvertDataPage" resultType="com.cloud.ficonsumer.vo.FiSourceProjEstiPayableVO">
        SELECT *
        FROM fi_source_proj_esti_payable
        WHERE del_flag = 0
        <if test="query.billCode != null and query.billCode != ''">
            AND billcode LIKE CONCAT('%', #{query.billCode}, '%')
        </if>
        <if test="query.projectCode != null and query.projectCode != ''">
            AND project_code = #{query.projectCode}
        </if>
        <if test="query.billStatus != null and query.billStatus != ''">
            AND bill_status = #{query.billStatus}
        </if>
        <if test="query.pushStatus != null and query.pushStatus != ''">
            AND push_status = #{query.pushStatus}
        </if>
        <if test="query.pushStatusList != null and query.pushStatusList.size() > 0">
            AND push_status in
            <foreach collection="query.pushStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.createTimeStart != null and query.createTimeStart != ''">
            and create_time >= #{query.createTimeStart}
        </if>
        <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
            and #{query.createTimeEnd >= create_time
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="getBeforeConvertDataPage" resultType="com.cloud.ficonsumer.vo.FiSourceProjEstiPayableDetailVO">
        SELECT *
        FROM fi_source_proj_esti_payable_detail d
        WHERE d.del_flag = 0
        <if test="query.pkEstipayablebill != null and query.pkEstipayablebill != ''">
            AND d.pk_estipayablebill = #{query.pkEstipayablebill}
        </if>
        ORDER BY d.create_time DESC
    </select>

    <select id="getConvertList" resultType="com.cloud.ficonsumer.vo.FiConvertProjEstiPayableVO">
        SELECT *
        FROM fi_convert_proj_esti_payable
        WHERE del_flag = 0
          AND pk_estipayablebill = #{pkEstipayablebill}
        ORDER BY create_time DESC
    </select>

    <select id="getConvertData" resultType="com.cloud.ficonsumer.vo.FiConvertProjEstiPayableDetailVO">
        SELECT *
        FROM fi_convert_proj_esti_payable_detail
        WHERE del_flag = 0
          AND pk_estipayablebill = #{pkEstipayablebill}
        ORDER BY create_time DESC
    </select>

</mapper>
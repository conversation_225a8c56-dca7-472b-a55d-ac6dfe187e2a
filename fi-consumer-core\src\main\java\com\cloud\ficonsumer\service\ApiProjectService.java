

package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.*;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.vo.FiSourceFileInfoVO;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2024-07-10 15:22:53
 */
public interface ApiProjectService extends IService<ApiProject> {

    /**
     * 定时拉取订单增量数据
     */
    void getOrderList(FiSourceOrderDTO fiSourceOrderDTO);

    /**
     * 定时费用结算单接口（框架合同虚拟采购订单）增量数据
     */
    void getCostOrderList(FiSourceCostOrderDTO fiSourceCostOrderDTO);

    /**
     * 付款合同接口（虚拟采购订单）
     */
    void getVirtualOrderList(FiSourceVirtualOrderDTO fiSourceVirtualOrderDTO);

    /**
     * 定时拉取通用报销单接口数据（通用报账）
     */
    void getReimbursementList(FiSourceReimbursementDTO fiSourceReimbursementDTO);

    /**
     * 定时拉取增量的项目应收数据（承包业务）
     */
    void getReceivableList(FiSourceReceivableDTO fiSourceReceivableDTO);

    /**
     * 定时拉取增量的采购入库数据
     */
    void getStoreList(FiSourceStoreDTO fiSourceStoreDTO);

    /**
     * 增量推送【采购-发票校验申请服务】
     */
    void incrementalPushPuPayable(FiSourcePuPayableQueryDTO queryDTO);

    void processPuPayable(FiSourcePuPayable puPayable);

    void sendPuPayableMessage(String pkPayablebill);

    /**
     * 增量推送【采购-预付付款申请单接收服务 (结算对照)】
     *
     * @param queryDTO 查询参数
     */
    void incrementalPushPuPaybill(FiSourcePuPaybillQueryDTO queryDTO);

    void processPuPaybill(FiSourcePuPaybill puPaybill);

    void sendPuPaybillMessage(String pkSettlement);

    /**
     * 增量推送【项目分包-项目暂估应付单】
     */
    void incrementalPushProjEstiPayable();

    void processProjEstiPayable(FiSourceProjEstiPayable projEstiPayable);

    void sendProjEstiPayableMessage(String pkEstipayablebill);

    /**
     * 增量推送【项目分包-接收通用报账单服务】
     */
    void incrementalPushProjPaybill();

    void processProjPaybill(FiSourceProjPaybill projPaybill);

    void sendProjPaybillMessage(String pkSettlement);

    /**
     * 增量推送【项目分包-付款申请】
     */
    void incrementalPushProjsubPayable();

    void processProjsubPayable(FiSourceProjsubPayable projsubPayable);

    void sendProjsubPayableMessage(String pkPayablebill);

    /**
     * 增量推送【销售订单接口-接收销售订单信息】
     */
    void incrementalPushSaleOrder();

    void processSaleOrder(FiSourceSaleOrder salesOrder);

    void sendSaleOrderMessage(String pkSalesOrder);

    /**
     * 增量推送【进项发票采集-根据比对一致和人票关联数据创建进项发票入池信息服务】
     */
    void incrementalPushFileOcrInfo();

    void processFileOcrInfo(FiSourceFileOcrInfo purchaseInvoice, List<FiSourceFileInfoVO> fileInfoVOS);

    void sendFileOcrInfoMessage(String pkPurchaseInvoice);

    void getAccountingList(FiSourceAccountingDTO fiAccountDTO);

    void pushAgainTryJob();
}

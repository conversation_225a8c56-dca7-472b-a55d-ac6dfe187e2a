package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.FiSourcePuPayableQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPayableDetailQueryDTO;
import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourcePuPayableService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiConvertPuPayableVO;
import com.cloud.ficonsumer.vo.FiConvertPuPayableDetailVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourcePuPayableVO;
import com.cloud.ficonsumer.vo.FiSourcePuPayableDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 采购应付源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-07 15:52:38
 */
@RestController
@AllArgsConstructor
@RequestMapping("/puPayable")
@Api(value = "puPayable", tags = "采购应付源数据主表管理")
public class FiSourcePuPayableController {

    private final FiSourcePuPayableService fiSourcePuPayableService;

    /**
     * 报错重推
     */
    @ApiOperation(value = "报错重推", notes = "报错重推")
    @GetMapping("/pushAgain/{pkPayablebill}")
    public R<Boolean> pushAgain(@PathVariable("pkPayablebill") String pkPayablebill) {
        return R.ok(fiSourcePuPayableService.push(pkPayablebill));
    }

    /**
     * 转换前分页查询
     */
    @ApiOperation(value = "转换前分页查询", notes = "转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiSourcePuPayableVO>> beforeConvertDataPage(Page<FiSourcePuPayableVO> page, FiSourcePuPayableQueryDTO queryDTO) {
        AssertUtils.notBlank(queryDTO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryDTO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryDTO.getGroup());
            queryDTO.setGroupCodeFlag(groupCodeFlag);
        }
        return R.ok(fiSourcePuPayableService.beforeConvertDataPage(page, queryDTO));
    }

    /**
     * 转换前数据详情查询
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage")
    public R<Page<FiSourcePuPayableDetailVO>> getBeforeConvertDataPage(Page<FiSourcePuPayableDetailVO> page, FiSourcePuPayableDetailQueryDTO queryDTO) {
        return R.ok(fiSourcePuPayableService.getBeforeConvertDataPage(page, queryDTO));
    }

    /**
     * 主体表转换后单条字段
     */
    @ApiOperation(value = "主体表转换后单条字段", notes = "主体表转换后单条字段")
    @GetMapping("/getConvertList")
    public R<List<FiConvertPuPayableVO>> getConvertList(String pkPayablebill) {
        return R.ok(fiSourcePuPayableService.getConvertList(pkPayablebill));
    }

    /**
     * 获取转换后详情分页数据
     */
    @ApiOperation(value = "获取转换后详情分页数据", notes = "获取转换后详情分页数据")
    @GetMapping("/getConvertData")
    public R<Page<FiConvertPuPayableDetailVO>> getConvertData(Page<FiConvertPuPayableDetailVO> page, String pkPayablebill) {
        return R.ok(fiSourcePuPayableService.getConvertData(page, pkPayablebill));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result = fiSourcePuPayableService.update(Wrappers.<FiSourcePuPayable>lambdaUpdate()
                .set(FiSourcePuPayable::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiSourcePuPayable::getPushMsg, "成功")
                .in(FiSourcePuPayable::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiSourcePuPayableService.pushAgainBatch(queryVO));
    }
}
package com.cloud.ficonsumer.pupayable.builder;

import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.ficonsumer.builder.context.YgInvoiceCheckContext;
import com.cloud.ficonsumer.entity.FiConvertPuPayable;
import com.cloud.ficonsumer.entity.FiConvertPuPayableDetail;
import com.cloud.ficonsumer.entity.PoOrderB;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.service.FiSourceOrderService;
import com.cloud.ficonsumer.service.FiSourceStoreService;
import com.cloud.ficonsumer.service.YgInvoiceService;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class YgInvoiceCheckContextTest {
    
    private static MockedStatic<SpringContextHolder> springContextHolderMock;

    @BeforeAll
    static void setUpClass() {
        springContextHolderMock = mockStatic(SpringContextHolder.class);
    }

    @AfterAll
    static void tearDownClass() {
        springContextHolderMock.close();
    }

    @Mock
    private ApiCompareService apiCompareService;
    @Mock
    private YgInvoiceService ygInvoiceService;
    @Mock
    private DmMapper dmMapper;
    @Mock
    private FiSourceOrderService fiSourceOrderService;
    @Mock
    private FiSourceStoreService fiSourceStoreService;

    private List<FiConvertPuPayableDetail> convertDetails;
    private YgInvoiceCheckContext context;

    @BeforeEach
    void setUp() {
        // Mock Spring Context
        springContextHolderMock.when(() -> SpringContextHolder.getBean(ApiCompareService.class))
            .thenReturn(apiCompareService);
        springContextHolderMock.when(() -> SpringContextHolder.getBean(YgInvoiceService.class))
            .thenReturn(ygInvoiceService);
        springContextHolderMock.when(() -> SpringContextHolder.getBean(DmMapper.class))
            .thenReturn(dmMapper);
        springContextHolderMock.when(() -> SpringContextHolder.getBean(FiSourceOrderService.class))
            .thenReturn(fiSourceOrderService);
        springContextHolderMock.when(() -> SpringContextHolder.getBean(FiSourceStoreService.class))
            .thenReturn(fiSourceStoreService);

        // 准备测试数据
        FiConvertPuPayable convert = new FiConvertPuPayable();
        convert.setBillno("TEST001");
        
        convertDetails = new ArrayList<>();
        FiConvertPuPayableDetail detail = new FiConvertPuPayableDetail();
        detail.setProject("PROJECT001");
        detail.setSupplier("SUPPLIER001");
        convertDetails.add(detail);

        context = new YgInvoiceCheckContext(convert, convertDetails);
    }

    @Test
    @DisplayName("测试prepareData - 基本场景")
    void testPrepareData() {
        // Mock服务调用
        when(apiCompareService.getTurnByType("PROJECT001", Constants.project))
            .thenReturn("MAPPED_PROJECT");
        when(apiCompareService.getTurnByType("SUPPLIER001", Constants.PkSupplier))
            .thenReturn("MAPPED_SUPPLIER");

        // 执行测试
        context.prepareData();

        // 验证结果
        assertEquals("MAPPED_PROJECT", context.getProjectCode());
        assertEquals("MAPPED_SUPPLIER", context.getSupplierCode());
        verify(apiCompareService).getTurnByType("PROJECT001", Constants.project);
        verify(apiCompareService).getTurnByType("SUPPLIER001", Constants.PkSupplier);
    }

    @Test
    @DisplayName("测试prepareData - 空参数校验")
    void testPrepareDataWithNullParams() {
        context = new YgInvoiceCheckContext(null, null);
        
        CheckedException exception = assertThrows(
            CheckedException.class,
            () -> context.prepareData()
        );
        
        assertEquals("构建参数不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试prepareData - 供应商编码为空")
    void testPrepareDataWithEmptySupplier() {
        convertDetails.get(0).setSupplier("");
        
        CheckedException exception = assertThrows(
            CheckedException.class,
            () -> context.prepareData()
        );
        
        assertTrue(exception.getMessage().contains("供应商编码为空"));
    }

    @Test
    @DisplayName("测试validateBusinessRules - 货物类订单")
    void testValidateBusinessRulesForGoodsOrder() {
        // 准备测试数据
        PoOrderB poOrderB = new PoOrderB();
        poOrderB.setPkOrderB("ORDER001");
        poOrderB.setCtrantypeid("21-Cx-001");  // 货物类订单
        
        Map<String, PoOrderB> poOrderBMap = Collections.singletonMap("ORDER001", poOrderB);
        context.setPoOrderBMap(poOrderBMap);

        // Mock入库单不存在的情况
        context.setIaI2billBMap(new HashMap<>());

        // 执行测试并验证异常
        CheckedException exception = assertThrows(
            CheckedException.class,
            () -> context.prepareData()
        );
        
        assertTrue(exception.getMessage().contains("未找到对应的采购入库单"));
    }
}
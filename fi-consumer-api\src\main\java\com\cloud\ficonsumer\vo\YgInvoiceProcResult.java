package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "YgInvoiceProcResult", description = "远光【根据比对一致和人票关联数据创建进项发票入池信息服务】响应参数")
public class YgInvoiceProcResult implements Serializable {

    @ApiModelProperty(value = "具体的错误代码/入池成功代码", example = "110168/120000")
    private String procCode;

    @ApiModelProperty(value = "具体的错误描述/入池成功描述", example = "入池失败:更新票面信息失败;属性[userid]设置值业务异常：已被XXXX认领/入池成功")
    private String procMsg;

    @ApiModelProperty(value = "发票号码", example = "1234500006789")
    private String invoiceNo;

    @ApiModelProperty(value = "发票类型编码", example = "120102")
    private String invoType;

    @ApiModelProperty(value = "发票代码", example = "011001234567")
    private String invoCode;

    @ApiModelProperty(value = "提交gid", example = "20221026160205d31b3aa5")
    private String subGid;

    @ApiModelProperty(value = "发票gid", example = "1201021234500006789")
    private String invoiceGid;

    @ApiModelProperty(value = "开票日期", example = "2022-11-09")
    private String invDate;

    @ApiModelProperty(value = "认领用户id", example = "101")
    private String userid;

    @ApiModelProperty(value = "业务关系来源", example = "YG.FPZX")
    private String dataSource;

    @ApiModelProperty(value = "业务唯一标识", example = "ZDH1131132")
    private String buUniSym;

    @ApiModelProperty(value = "外部业务唯一标识", example = "")
    private String externalSystemGid;
}


package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 虚拟采购订单转换数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:05
 */
@Data
@TableName("fi_convert_cost_order")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "费用结算单转换数据主表")
public class FiConvertCostOrder extends BaseEntity<FiConvertCostOrder> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 单据主键
     */
    @ApiModelProperty(value="单据主键")
    private String pkFeebalance;

    /**
     * 组织
     */
    @ApiModelProperty(value="组织")
    private String pkOrg;

    /**
     * 财务组织名称
     */
    @ApiModelProperty(value="财务组织名称")
    private String pkOrgName;

    /**
     * 采购组织
     */
    @ApiModelProperty(value="采购组织")
    private String pkOrgV;

    /**
     * 订单编号
     */
    @ApiModelProperty(value="订单编号")
    private String billCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value="合同编号")
    private String protocolName;

    /**
     * 制单日期
     */
    @ApiModelProperty(value="制单日期")
    private String billmaketime;

    /**
     * 制单人
     */
    @ApiModelProperty(value="制单人")
    private String billmaker;

    /**
     * 供应商
     */
    @ApiModelProperty(value="供应商")
    private String pkSupplier;

    /**
     * 不含税合同金额
     */
    @ApiModelProperty(value="不含税合同金额")
    private String currMny;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String tax;

    /**
     * 含税总金额
     */
    @ApiModelProperty(value="含税总金额")
    private String totTaxmny;

}

package com.cloud.ficonsumer;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillQueryDTO;
import com.cloud.ficonsumer.entity.FiSourcePuPaybill;
import com.cloud.ficonsumer.entity.FiSourcePuPaybillDetail;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.service.FiSourcePuPaybillDetailService;
import com.cloud.ficonsumer.service.FiSourcePuPaybillService;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.vo.FiSourcePuPaybillVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class PuPaybillIntegTest {

    @SpyBean
    private DmMapper dmMapper;

    @SpyBean
    private ApiProjectService apiProjectService;

    @SpyBean
    private FiSourcePuPaybillService fiSourcePuPaybillService;

    @SpyBean
    private FiSourcePuPaybillDetailService fiSourcePuPaybillDetailService;

    @BeforeEach
    public void initData() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);

        // 初始化 erp 库数据：采购付款 oracle 数据库
        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator erpPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("pupaybill/schema-oracle.sql"),
                    new ClassPathResource("pupaybill/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(erpPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 初始化 mysql 库数据：采购付款 mysql 数据库
        DynamicDataSourceContextHolder.push("mysql");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("pupaybill/schema-mysql.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    @Test
    public void testProcessPuPaybill() {
        List<FiSourcePuPaybillVO> fiSourcePuPaybillVOS = dmMapper.listPuPaybill(new FiSourcePuPaybillQueryDTO());
        assertEquals(3, fiSourcePuPaybillVOS.size(), "ERP采购付款表 中应存在三条数据");

        for (FiSourcePuPaybillVO fiSourcePuPayable : fiSourcePuPaybillVOS) {
            apiProjectService.processPuPaybill(fiSourcePuPayable);
        }

        List<FiSourcePuPaybill> list = fiSourcePuPaybillService.list();
        List<FiSourcePuPaybillDetail> detailList = fiSourcePuPaybillDetailService.list();

        assertEquals(3, list.size(), "MySQL采购付款表 表中应存在三条数据");
        assertEquals(3, detailList.size(), "MySQL采购付款明细表 表中应存在三条数据");

        fiSourcePuPaybillVOS = dmMapper.listPuPaybill(new FiSourcePuPaybillQueryDTO());
        for (FiSourcePuPaybillVO fiSourcePuPaybill : fiSourcePuPaybillVOS) {
            assertEquals(fiSourcePuPaybill.getDef20(), "1", "ERP采购付款表 中接收状态已变更为1");
        }
    }
}
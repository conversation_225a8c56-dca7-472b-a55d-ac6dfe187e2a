package com.cloud.ficonsumer.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.util.R;
import com.cloud.cloud.common.security.annotation.InnerService;
import com.cloud.ficonsumer.common.FiOrgConvert;
import com.cloud.ficonsumer.service.FiOrgConvertService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 智慧平台组织转换信息 控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/org-convert")
@Api(value = "智慧平台组织转换信息", tags = "智慧平台组织转换信息")
public class FiOrgConvertController {

    private final FiOrgConvertService fiOrgConvertService;

    /**
     * 同步智慧平台组织转换信息
     */
    @ApiOperation("同步智慧平台组织转换信息")
    @PostMapping("/sync")
    public R<Void> syncOrg() {
        fiOrgConvertService.syncPushEnableOrg();
        return R.ok();
    }

    /**
     * 分页查询智慧平台组织转换信息
     */
    @ApiOperation("分页查询智慧平台组织转换信息")
    @GetMapping("/page")
    public R<IPage<FiOrgConvert>> listOrgs(Page<FiOrgConvert> page) {
        IPage<FiOrgConvert> result = fiOrgConvertService.page(page);
        return R.ok(result);
    }

    @ApiOperation("根据组织ID查询智慧平台组织转换信息")
    @GetMapping("/{orgId}")
    @InnerService
    public R<FiOrgConvert> getByOrgId(@PathVariable("orgId") Long orgId) {
        FiOrgConvert fiOrgConvert = fiOrgConvertService.getOne(
                Wrappers.<FiOrgConvert>lambdaQuery().eq(FiOrgConvert::getOrgId, orgId));
        return R.ok(fiOrgConvert);
    }
}
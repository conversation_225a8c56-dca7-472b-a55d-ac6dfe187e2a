package com.cloud.ficonsumer.consumers;

import com.cloud.apiexchange.client.consumer.IMqConsumer;
import com.cloud.apiexchange.client.consumer.MqResult;
import com.cloud.apiexchange.client.consumer.annotation.MqConsumer;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.service.FiSourceFileOcrInfoService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.cloud.ficonsumer.enums.Constants.TOPIC_FILE_OCR_INFO_TRANS;

@Slf4j
@MqConsumer(topic = TOPIC_FILE_OCR_INFO_TRANS, group = Constants.Group.group, transaction = false)
@Component
@AllArgsConstructor
public class FileOcrInfoPushConsumer implements IMqConsumer {

    private FiSourceFileOcrInfoService fiSourceFileOcrInfoService;

    @Override
    public MqResult consume(String uniquecodeofinvoice) {
        LogUtil.info(log, "【进项发票采集-根据比对一致和人票关联数据创建进项发票入池信息服务】处理开始:", uniquecodeofinvoice);
        try {
            fiSourceFileOcrInfoService.push(uniquecodeofinvoice);
        } catch (Exception e) {
            LogUtil.error(log, "【进项发票采集-根据比对一致和人票关联数据创建进项发票入池信息服务】处理失败:", e);
            return new MqResult(MqResult.FAIL_CODE, AdminUtils.truncateString(e.getMessage(), 1000));
        }
        return new MqResult(MqResult.SUCCESS_CODE, "【进项发票采集-根据比对一致和人票关联数据创建进项发票入池信息服务】处理成功");
    }
}
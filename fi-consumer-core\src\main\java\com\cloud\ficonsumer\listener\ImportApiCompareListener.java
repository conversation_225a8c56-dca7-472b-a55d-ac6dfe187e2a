package com.cloud.ficonsumer.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.ValidationUtil;
import com.cloud.ficonsumer.entity.ApiCompare;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.vo.ExcelImportStatusVO;
import com.cloud.ficonsumer.vo.ImportApiCompareVO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @Author: liyaolei
 * @Descrition: 导入工程信息信息 excel数据解析Listener
 * @Date: 2020/11/11
 */
@Slf4j
public class ImportApiCompareListener extends AnalysisEventListener<ImportApiCompareVO> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ImportApiCompareListener.class);

    private boolean isError = false;
    private boolean firstIN = true;
    private int checkNum = 0;
    private String errorMessage = "导入数据报错!";


    private String redisKey;
    private RedisTemplate redisTemplate;

    private ApiCompareService apiCompareService;

    /**
     * 批处理阈值,每50条保存一次数据库
     */
    private static final int BATCH_COUNT = 100;
    /**
     * 每检查10条更新一下缓存进度
     */
    private static final int SAVE_COUNT = 5;
    /**
     * 每满足50条报错弹出
     */
    private static final int ERROR_COUNT = 10;
    private ExcelImportStatusVO excelStatusVO;
    private List<ImportApiCompareVO> dataResult = new ArrayList<>();
    private Map<String, String> fileTypeDictItemsCollect;


    public ImportApiCompareListener(String redisKey,
                                    ExcelImportStatusVO excelStatusVO, RedisTemplate redisTemplate,
                                    ApiCompareService apiCompareService, Map<String, String> fileTypeDictItemsCollect) {
        this.redisTemplate = redisTemplate;
        this.excelStatusVO = excelStatusVO;
        this.redisKey = redisKey;
        this.apiCompareService=apiCompareService;
        this.fileTypeDictItemsCollect=fileTypeDictItemsCollect;
    }

    /**
     * 实体转换
     *
     * @param data
     * @param context
     */
    @Override
    public void invoke(ImportApiCompareVO data, AnalysisContext context) {
        if(firstIN){
            ValueOperations valueOperations = redisTemplate.opsForValue();
            excelStatusVO.setSum(context.readSheetHolder().getApproximateTotalRowNumber() - 1);
            valueOperations.set(redisKey, excelStatusVO, Duration.ofDays(1));
            firstIN = false;
        }else {
            //除数还有余数说明还有剩余的需要执行
            if (checkNum % SAVE_COUNT == 0) {
                ValueOperations valueOperations = redisTemplate.opsForValue();
                excelStatusVO.setCheckNum(checkNum);
                excelStatusVO.setHandle(checkNum);
                valueOperations.set(redisKey, excelStatusVO, Duration.ofDays(1));
            }
        }
        checkNum ++;
        this.checkImportDataIsVaild(data, context);
        if (dataResult.size() >= BATCH_COUNT) {
            this.saveData(context);
            dataResult.clear();
        }
    }

    /**
     * 保存实体
     *
     * @param context
     */
    private void saveData(AnalysisContext context) {
        List<ApiCompare> apiCompares = dataResult.stream().map(x -> {
            ApiCompare apiCompare=new ApiCompare();
            BeanUtil.copyProperties(x,apiCompare);
            return apiCompare;
        }).collect(Collectors.toList());
        apiCompareService.saveBatch(apiCompares);
        // 缓存结果
        ValueOperations valueOperations = redisTemplate.opsForValue();
        excelStatusVO.setSum(context.readSheetHolder().getApproximateTotalRowNumber() - 1);
        excelStatusVO.setHandle(excelStatusVO.getHandle() + dataResult.size());
        valueOperations.set(redisKey, excelStatusVO, Duration.ofDays(1));
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        excelStatusVO.setSum(context.readSheetHolder().getApproximateTotalRowNumber() - 1);
        excelStatusVO.setIsFinish(true);
        excelStatusVO.setCheckNum(checkNum);
        try {
            //除数还有余数说明还有剩余的需要执行
            if ((context.readSheetHolder().getApproximateTotalRowNumber() - 1) % BATCH_COUNT != 0) {
                this.saveData(context);
            }
            excelStatusVO.setHandle(checkNum);
            valueOperations.set(redisKey, excelStatusVO, Duration.ofDays(1));
            if(isError){
                throw new CheckedException(errorMessage);
            }
        } catch (Exception e) {
            this.onException(e,context);
            valueOperations.set(redisKey, excelStatusVO, Duration.ofDays(1));
            LOGGER.error("对照表导入异常:", e);
            throw e;
        } finally {
            valueOperations.set(redisKey, excelStatusVO, Duration.ofDays(1));
        }
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        String errorMsg = String.format("表格第%s行报错: %s", context.readRowHolder().getRowIndex() + 1, exception.getMessage());
        LOGGER.error(errorMsg, exception);
        if (CollectionUtil.isEmpty(excelStatusVO.getErrorMsg())) {
            excelStatusVO.setErrorMsg(new ArrayList<>());
        }
        ValueOperations valueOperations = redisTemplate.opsForValue();
        excelStatusVO.getErrorMsg().add(errorMsg);
        valueOperations.set(redisKey, excelStatusVO, Duration.ofDays(1));
        // 报错10条，就不再执行了
        if(excelStatusVO.getErrorMsg().size() >= ERROR_COUNT){
            excelStatusVO.setIsFinish(true);
            excelStatusVO.setCheckNum(checkNum);
            excelStatusVO.setHandle(checkNum);
            valueOperations.set(redisKey, excelStatusVO, Duration.ofDays(1));
            throw new CheckedException(errorMessage);
        }
        isError = true;
    }

    /**
     * 校验项目实体
     *
     * @param data
     * @param context
     */
    private void checkImportDataIsVaild(ImportApiCompareVO data, AnalysisContext context) {
        String msg = String.format("表格第%s行数据错误：", context.readRowHolder().getRowIndex() + 1);
        ValidationUtil.fastFailValidate(data);
        data.setSourceSystemId(data.getSourceSystemId().trim());
        data.setSourceSystemCode(data.getSourceSystemCode().trim());
        data.setSourceSystemName(data.getSourceSystemName().trim());
        data.setTargetSystemId(data.getTargetSystemId().trim());
        data.setTargetSystemCode(data.getTargetSystemCode().trim());
        data.setTargetSystemName(data.getTargetSystemName().trim());
//        List<SysDictItem> fileTypeDictItems = apiUpmsMapper.getFileTypeDictItems();
//        Map<String, String> collect = fileTypeDictItems.stream().collect(Collectors.toMap(SysDictItem::getLabel, SysDictItem::getValue, (key1, key2) -> key2));
        String value = fileTypeDictItemsCollect.get(data.getFileTypeName());
        //档案类型不存在
        if (StringUtils.isBlank(value)){
            throw new CheckedException(msg + "档案类型不存在，请核对档案类型");
        }
        data.setFileType(value);
        //校验数据库是否存在
        List<ApiCompare> compareList = apiCompareService.list(Wrappers.<ApiCompare>lambdaQuery().eq(ApiCompare::getFileType, data.getFileType())
                .eq(ApiCompare::getSourceSystemId, data.getSourceSystemId())
                .eq(ApiCompare::getSourceSystemCode, data.getSourceSystemCode())
                .eq(ApiCompare::getSourceSystemName, data.getSourceSystemName())
                .eq(ApiCompare::getTargetSystemId, data.getTargetSystemId())
                .eq(ApiCompare::getTargetSystemCode, data.getTargetSystemCode())
                .eq(ApiCompare::getTargetSystemName, data.getTargetSystemName()));
        if (CollectionUtil.isNotEmpty(compareList)){
            throw new CheckedException(msg + "系统已存在该数据，无法导入");
        }
        //校验数据是否重复
        List<ImportApiCompareVO> apiCompares = dataResult.stream().filter(apiCompareVO -> apiCompareVO.getFileType().equals(data.getFileType())
                && apiCompareVO.getSourceSystemId().equals(data.getSourceSystemId())
                && apiCompareVO.getSourceSystemCode().equals(data.getSourceSystemCode())
                && apiCompareVO.getSourceSystemName().equals(data.getSourceSystemName())
                && apiCompareVO.getTargetSystemId().equals(data.getTargetSystemId())
                && apiCompareVO.getTargetSystemCode().equals(data.getTargetSystemCode())
                && apiCompareVO.getTargetSystemName().equals(data.getTargetSystemName())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(apiCompares)){
            throw new CheckedException(msg + "当前数据有重复，无法导入");
        }
        dataResult.add(data);
    }

}
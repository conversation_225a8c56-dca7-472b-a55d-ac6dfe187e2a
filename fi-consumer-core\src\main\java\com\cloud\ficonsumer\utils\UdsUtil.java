package com.cloud.ficonsumer.utils;

import cn.hutool.core.io.FileUtil;
import com.amazonaws.services.s3.model.S3Object;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.common.oss.service.OssTemplate;
import com.cloud.ficonsumer.config.UdsConfig;
import com.cloud.ficonsumer.entity.FiUdsFileRecord;
import com.cloud.ficonsumer.service.FiUdsFileRecordService;
import com.sgcc.uds.cloud.sdk.UdsClient;
import com.sgcc.uds.cloud.sdk.action.result.AddActionResult;
import com.sgcc.uds.cloud.sdk.action.result.CommonActionResult;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @date 2025/1/14.
 */
@Slf4j
@UtilityClass
public class UdsUtil {

    /**
     * 将OSS中的文件上传到UDS中
     *
     * @param bucketName OSS的bucket
     * @param objectName OSS的文件名称
     * @param fileName 文件名称
     * @return 上传UDS是否成功
     * @throws CheckedException 上传UDS异常
     */
    public CommonActionResult pushFileToUdsFromOSS(String bucketName, String objectName, String fileName) {
        if (!UdsConfig.enable) {
            return new CommonActionResult();
        }

        FiUdsFileRecordService fileRecordService = SpringContextHolder.getBean(FiUdsFileRecordService.class);
        OssTemplate ossTemplate = SpringContextHolder.getBean(OssTemplate.class);

        FiUdsFileRecord existingRecord = fileRecordService.checkFileUploaded(bucketName, fileName);
        
        // 检查是否已上传成功
        if (existingRecord != null && "1".equals(existingRecord.getPushStatus())) {
            CommonActionResult result = new CommonActionResult();
            result.setDocumentId(existingRecord.getDocumentId());
            result.setFileName(existingRecord.getUdsFileName());
            return result;
        }

        try {
            S3Object object = ossTemplate.getObject(bucketName, fileName);
            CommonActionResult udsResult = pushFileToUds(
                    object.getObjectContent(),
                    fileName,
                    FileUtil.extName(fileName),
                    "YJCWXT_FPWJ"
            );

            fileRecordService.updateOrSaveUploadRecord(bucketName, objectName, fileName, udsResult, true, existingRecord, null);
            return udsResult;
        } catch (Exception e) {
            String errorMessage = String.format("从OSS推送文件到UDS失败。bucketName: %s, objectName: %s, fileName: %s",
                    bucketName, objectName, fileName);
            log.error(errorMessage, e);
            // 使用 AdminUtils.truncateString 限制错误信息长度
            String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
            fileRecordService.updateOrSaveUploadRecord(bucketName, objectName, fileName, null, false, existingRecord, pushMsg);
            throw new CheckedException(errorMessage, e);
        }
    }

    /**
     * 下载模板
     *
     */
    public CommonActionResult pushFileToUds(InputStream inputStream, String fileName, String extName, String metaTypeName) throws Exception {
        UdsConfig udsConfig = SpringContextHolder.getBean(UdsConfig.class);
        if (!udsConfig.getEnable()) {
            return new CommonActionResult();
        }
        // 初始化 UdsClient, 地址及AK、SK请联系现场⾮结运维进⾏获取
        UdsClient client = new UdsClient(udsConfig.getApiServiceUrl(), udsConfig.getAccessKey(), udsConfig.getSecretKey());
        // 上传数据并得到结果
        AddActionResult result = client.add(inputStream)
//        AddActionResult result = client.add("C:\\Users\\<USER>\\Pictures\\Camera Roll\\测试.jpg")
                // 若要指定⽂件名称采⽤下⾯⽅法设置
                .withFileName(fileName)
                // 若要指定⽂件名称采⽤下⾯⽅法设置
                .withFileType(extName)
                // 关闭秒传校验（能提⾼上传性能）
//                        .disableMoment()
                // 关联元数据类型, 例如这⾥关联元数据类型编码为 "yk" 的元数据类型
                .withMetaTypeName(metaTypeName)
                // 若要设置元数据属性采⽤下⾯⽅法设置
//                        .withBizMeta("test_meta", "我是测试元数据")
                //设置超时时间（毫秒数）
                .withTimeoutMill(5000)
                // 执⾏
                .execute();
        // 业务系统需要将结果中 documentId 和 versionId 保存
        return result;
    }
}

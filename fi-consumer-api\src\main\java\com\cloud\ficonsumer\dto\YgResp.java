package com.cloud.ficonsumer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "发票校验申请服务")
public class YgResp {

    @ApiModelProperty(value = "业务单据ID")
    private Integer ywid;
    
    @ApiModelProperty(value = "单据类型")
    private String typeId;
    
    @ApiModelProperty(value = "前端系统单据编号", required = true)
    private String disCode;
    
    @ApiModelProperty(value = "响应状态码(0-接收成功,1-接收失败)", required = true)
    private String code;
    
    @ApiModelProperty(value = "描述信息", required = true)
    private String message;
}

package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiConvertVirtualOrderDTO;
import com.cloud.ficonsumer.dto.FiConvertVirtualOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiConvertVirtualOrder;
import com.cloud.ficonsumer.entity.FiConvertVirtualOrderDetail;
import com.cloud.ficonsumer.entity.FiSourceReimbursement;
import com.cloud.ficonsumer.entity.FiSourceVirtualOrder;
import com.cloud.ficonsumer.entity.FiSourceVirtualOrderDetail;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceVirtualOrderMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;


/**
 * 虚拟采购订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:26
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiSourceVirtualOrderServiceImpl extends ServiceImpl<FiSourceVirtualOrderMapper, FiSourceVirtualOrder> implements FiSourceVirtualOrderService {

    private final FiSourceVirtualOrderDetailService fiSourceVirtualOrderDetailService;
    private final FiConvertVirtualOrderService fiConvertVirtualOrderService;
    private final FiConvertVirtualOrderDetailService fiConvertVirtualOrderDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;
    private final YgConfig ygConfig;

    /**
     * 费用结算单采购订单重新推送
     * @param
     */
    @Override
    public boolean pushAgain(String pk) {
        FiSourceVirtualOrder sourceMainData = this.getOne(Wrappers.<FiSourceVirtualOrder>lambdaQuery().eq(FiSourceVirtualOrder::getPkContr, pk));
        List<FiSourceVirtualOrderDetail> details = fiSourceVirtualOrderDetailService.list(Wrappers.<FiSourceVirtualOrderDetail>lambdaQuery().eq(FiSourceVirtualOrderDetail::getPkContr, pk));
        //重新转换
        try {
            this.turnData(sourceMainData,details);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            this.updateById(sourceMainData);
            LogUtil.info(log, "费用结算单采购订单集成信息转换失败:", e);
            throw new CheckedException("费用结算单采购订单集成信息转换失败:"+pushMsg);
        }
        //重新推送智慧平台
        sourceMainData.setPushTime(DateUtil.formatDateTime(new Date()));
        try {
            YgResult ygResult = this.pushData(pk);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
            sourceMainData.setPushMsg("");
            sourceMainData.setReadiness(1l);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk, Constants.TradeType.virtualOrder);
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000000");
                updatePlatFormLog.setMsg("");
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TradeType.virtualOrder);
                platFormLogVO.setCode("000000");
                platFormLogVO.setMsg("");
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,Constants.TradeType.virtualOrder);
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000001");
                updatePlatFormLog.setMsg(pushMsg);
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TradeType.virtualOrder);
                platFormLogVO.setCode("000001");
                platFormLogVO.setMsg(pushMsg);
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
            LogUtil.info(log, "费用结算单采购订单集成信息推送失败:", e);
            throw new CheckedException("费用结算单采购订单集成信息推送失败:"+pushMsg);
        }
        return true;
    }

    /**
     * 数据推送
     *
     * @param pk
     * @return
     * @throws Exception
     */
    private YgResult pushData(String pk) throws Exception {
        YgResult ygResult = new YgResult();
        if(ygConfig.getEnable()) {
            FiConvertVirtualOrder convertMainData = fiConvertVirtualOrderService.getOne(Wrappers.<FiConvertVirtualOrder>lambdaQuery().eq(FiConvertVirtualOrder::getPkContr, pk));
            List<FiConvertVirtualOrderDetail> details = fiConvertVirtualOrderDetailService.list(Wrappers.<FiConvertVirtualOrderDetail>lambdaQuery().eq(FiConvertVirtualOrderDetail::getPkContr,pk));
            YgOrderVO request = this.turnYgData(convertMainData,details);
            ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000011.getServCode());
            String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(request));
            ygResult = JSONObject.parseObject(result,YgResult.class);
            //000000：成功 其它：失败
            if(!ygResult.getCode().equals("000000")) {
                throw new CheckedException(ygResult.getmessage());
            }
        }
        return ygResult;
    }

    /**
     * 将转换后的数据用远光提供的格式推送
     *
     * @param convertMainData
     * @param details
     * @return
     */
    private YgOrderVO turnYgData(FiConvertVirtualOrder convertMainData, List<FiConvertVirtualOrderDetail> details) {
        YgOrderVO ygOrderVO = new YgOrderVO();
        String pkOrgV = apiCompareService.getTurnByType(convertMainData.getPkOrg(),Constants.PkOrg);
        if(StrUtil.isBlank(pkOrgV)) {
            throw new CheckedException("单位代号不可为空");
        }
        List<YgOrderDetailVO> items = new ArrayList<>();
        String prvMkCode = "00003010";
        ygOrderVO.setPrvMkCode(prvMkCode);
        ygOrderVO.setPurOrdNo(convertMainData.getBillCode());
        ygOrderVO.setAflUnit(pkOrgV);
        ygOrderVO.setCreTime(convertMainData.getBillmaketime());
        String purOrdTyp = "0L08";
        ygOrderVO.setPurOrdTyp(purOrdTyp);
        String contractNo = null;
        if(StringUtils.isNotEmpty(convertMainData.getBillCode()) && convertMainData.getBillCode().startsWith("SG")) {
            contractNo = convertMainData.getBillCode();
        }
        ygOrderVO.setContractNo(contractNo);
//        ygOrderVO.setCrePersNm(convertMainData.getBillmaker());
        String pkSupplier = apiCompareService.getTurnByType(convertMainData.getPkSupplier(), Constants.PkSupplier);
        ygOrderVO.setSupCod(pkSupplier);
        ygOrderVO.setTotAmExTax(convertMainData.getCurrMny());
        ygOrderVO.setPurOrdTaxAmount(convertMainData.getTax());
//        String purOrdTypDesc = apiCompareService.getTurnByType(convertMainData.getPkContracttype(),Constants.ctrantypeid);
//        ygOrderVO.setPurOrdTypDesc(purOrdTypDesc);
        String transmissionDate = DateUtil.formatDate(new Date());
        ygOrderVO.setTransmissionDate(transmissionDate);
//        ygOrderVO.setProCen(pkOrgV);
        ygOrderVO.setDataSouSys("ZJYJ");
        for(FiConvertVirtualOrderDetail convertDetail : details) {
            YgOrderDetailVO ygOrderDetailVO = new YgOrderDetailVO();
            ygOrderDetailVO.setPurOrdNo(convertDetail.getBillCode());
            ygOrderDetailVO.setPurOrdItem(convertDetail.getRowno());
            ygOrderDetailVO.setPrjCode(apiCompareService.getProjectCode(convertDetail.getPkProject()));
            ygOrderDetailVO.setItTotAmExTax(convertDetail.getCurrMny());
            BigDecimal ntaxrate = BigDecimal.ZERO;
            if(StrUtil.isNotBlank(convertDetail.getTaxrate())) {
                ntaxrate = new BigDecimal(convertDetail.getTaxrate());
                ntaxrate = ntaxrate.divide(new BigDecimal(100),2, RoundingMode.HALF_UP);
            }
            ygOrderDetailVO.setTaxrt(ntaxrate.toPlainString());
            ygOrderDetailVO.setTaxAmount(convertDetail.getTax());
            ygOrderDetailVO.setItTotAmIncTax(convertDetail.getCurrTaxmny());
            ygOrderDetailVO.setCeleIdInPurVou("");
            ygOrderDetailVO.setUnitPrice(convertDetail.getTaxprice());
            ygOrderDetailVO.setAmt(convertDetail.getCurrNum());
//            String pkOrgDetail = apiCompareService.getTurnByType(convertDetail.getPkOrg(),Constants.PkOrg);
            ygOrderDetailVO.setPrvMkCode(prvMkCode);
//            ygOrderDetailVO.setPricUni(convertDetail.getPkMeasdoc());
            ygOrderDetailVO.setTransmissionDate(transmissionDate);
//            ygOrderDetailVO.setMtCode(convertDetail.getMaterialCode());
//            ygOrderDetailVO.setMtName(convertDetail.getName());
//            ygOrderDetailVO.setProCen("");
            ygOrderDetailVO.setRecepId("否");
            ygOrderDetailVO.setRecepInvVerSign("否");
            ygOrderDetailVO.setSerInvVerSign("是");
            items.add(ygOrderDetailVO);
        }
        ygOrderVO.setItems(items);
        return ygOrderVO;
    }

    /**
     * 数据转换
     *
     * @param sourceMainData
     * @param details
     */
    private void turnData(FiSourceVirtualOrder sourceMainData, List<FiSourceVirtualOrderDetail> details) throws Exception {
        String pk = sourceMainData.getPkContr();
        FiConvertVirtualOrder fiConvertVirtualOrder = new FiConvertVirtualOrder();
        apiCompareCache.convertData(sourceMainData,fiConvertVirtualOrder, BillTypeEnum.COST_ORDER.getCode());
        FiConvertVirtualOrder convertMainData = fiConvertVirtualOrderService.getOne(Wrappers.<FiConvertVirtualOrder>lambdaQuery().eq(FiConvertVirtualOrder::getPkContr, pk));
        fiConvertVirtualOrder.setId(null);
        if(convertMainData != null) {
            fiConvertVirtualOrder.setId(convertMainData.getId());
            fiConvertVirtualOrderDetailService.remove(Wrappers.<FiConvertVirtualOrderDetail>lambdaQuery().eq(FiConvertVirtualOrderDetail::getPkContr,pk));
        }
        List<FiConvertVirtualOrderDetail> convertDetailList = new ArrayList<>();
        for(FiSourceVirtualOrderDetail sourceDetailData : details) {
            FiConvertVirtualOrderDetail fiConvertVirtualOrderDetail = new FiConvertVirtualOrderDetail();
            apiCompareCache.convertData(sourceDetailData,fiConvertVirtualOrderDetail,BillTypeEnum.COST_ORDER_DETAIL.getCode());
            fiConvertVirtualOrderDetail.setId(null);
            convertDetailList.add(fiConvertVirtualOrderDetail);
        }
        fiConvertVirtualOrderService.saveOrUpdate(fiConvertVirtualOrder);
        fiConvertVirtualOrderDetailService.saveOrUpdateBatch(convertDetailList);
    }

    @Override
    public Page<FiSourceVirtualOrderDTO> beforeConvertDataPage(Page page, FiSourceVirtualOrderQueryVO queryVO) {
        Page<FiSourceVirtualOrderDTO> result = this.baseMapper.beforeConvertDataPage(page,queryVO);
        if (CollectionUtil.isNotEmpty(result.getRecords())){
            for (FiSourceVirtualOrderDTO apiSource:result.getRecords()){
                if(StrUtil.isNotBlank(apiSource.getPkSupplier())) {
                    apiSource.setPkSupplierName(dmMapper.getSupplierName(apiSource.getPkSupplier()));
                }
            }
        }
        return result;
    }

    @Override
    public Page<FiSourceVirtualOrderDetailDTO> getBeforeConvertDataPage(Page page, FiSourceVirtualOrderQueryVO queryVO) {
        Page<FiSourceVirtualOrderDetailDTO> result = this.baseMapper.getBeforeConvertDataPage(page,queryVO);
        return result;
    }

    @Override
    public List<FiConvertVirtualOrderDTO> getConvertList(String pk) {
        List<FiConvertVirtualOrderDTO> convertDataDTOList = new ArrayList<>();
        FiConvertVirtualOrderDTO convertDataDTO = new FiConvertVirtualOrderDTO();
        FiConvertVirtualOrder convertDataOld = fiConvertVirtualOrderService.getOne(Wrappers.<FiConvertVirtualOrder>lambdaQuery().eq(FiConvertVirtualOrder::getPkContr, pk));
        if(null != convertDataOld) {
            BeanUtil.copyProperties(convertDataOld,convertDataDTO);
            convertDataDTOList.add(convertDataDTO);
        }
        return convertDataDTOList;
    }

    @Override
    public Page<FiConvertVirtualOrderDetailDTO> getConvertData(Page page, String pk) {
        Page<FiConvertVirtualOrderDetailDTO> result = this.baseMapper.getConvertData(page,pk);
        return result;
    }

    @Override
    public void checkSync(String pkContr) {
        FiSourceVirtualOrder fiSourceVirtualOrder = this.getOne(Wrappers.<FiSourceVirtualOrder>lambdaQuery()
                .eq(FiSourceVirtualOrder::getPkContr, pkContr));

        if (Objects.isNull(fiSourceVirtualOrder)) {
            throw new CheckedException(
                    MessageFormat.format("付款合同未同步成功, 付款合同不存在，付款合同ID: {0}", pkContr));
        }

        if (Objects.isNull(fiSourceVirtualOrder.getPushStatus()) ||
                ObjectUtil.notEqual(PushStatusEnum.PUSH_SUCCESS.getCode(), fiSourceVirtualOrder.getPushStatus())) {
            throw new CheckedException(
                    MessageFormat.format("付款合同未同步成功, pushStatus: {0}，付款合同编号: {1}",
                            fiSourceVirtualOrder.getPushStatus(), fiSourceVirtualOrder.getBillCode()));
        }
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourceVirtualOrder entity = this.getById(id);
            try {
                this.pushAgain(entity.getPkContr());
            } catch (Exception e) {
                resultList.add(entity.getBillCode()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}

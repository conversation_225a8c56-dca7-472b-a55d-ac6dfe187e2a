package com.cloud.ficonsumer.enums;


import java.util.Arrays;
import java.util.List;

public class Constants {

    public static final String TOPIC_ORDER_TRANS = "topic_order_trans";
    public static final String TOPIC_COST_ORDER_TRANS = "topic_cost_order_trans";
    public static final String TOPIC_VIRTUAL_ORDER_TRANS = "topic_virtual_order_trans";
    public static final String TOPIC_REIMBURSEMENT_TRANS = "topic_reimbursement_trans";
    public static final String TOPIC_RECEIVABLE_TRANS = "topic_receivable_trans";
    public static final String TOPIC_STORE_TRANS = "topic_store_trans";
    public static final String TOPIC_ACCOUNTING_TRANS = "topic_accounting_trans";


    public static final String TOPIC_INVOICE_POSTING_TRANS = "topic_invoice_posting_trans";

    /**
     * 【采购-发票校验申请】服务
     */
    public static final String TOPIC_PU_PAYABLE_TRANS = "topic_pu_payable_trans";

    /**
     * 采购付款
     */
    public static final String TOPIC_PU_PAYBILL_TRANS = "topic_pu_paybill_trans";

    /**
     * 项目分包应付
     */
    public static final String TOPIC_PROJSUB_PAYBILL_TRANS = "topic_projsub_paybill_trans";

    /**
     * 项目暂估应付
     */
    public static final String TOPIC_PROJ_ESTI_PAYABLE_TRANS = "topic_proj_esti_payable_trans";

    /**
     * 项目付款
     */
    public static final String TOPIC_PROJ_PAYBILL_TRANS = "topic_proj_paybill_trans";

    /**
     * 销售订单
     */
    public static final String TOPIC_SALE_ORDER_TRANS = "topic_sale_order_trans";

    /**
     * 【进项发票采集-根据比对一致和人票关联数据创建进项发票入池信息服务】
     */
    public static final String TOPIC_FILE_OCR_INFO_TRANS = "topic_file_ocr_info_trans";


    public static final String SUCCESS = "000000";
    public static final String FAIL = "000001";
    public static final String PkOppbank = "pk_oppbank";
    public static final String Vbillcode = "vbillcode";

    /**
     * 分组
     */
    public static final class Group {
        /**
         * 转换
         */
        public static final String group = "fi-consumer-provider";
    }

    /**
     * 贸易类型
     */
    public static final class TradeType {
        /**
         * payment
         * F3-Cxx-06 通用付款单
         * F3-Cxx-01 项目付款单
         * F3-Cxx-02 采购付款单
         */
        public static final String payment = "F3-Cxx-06";
        public static final String payment_xm = "F3-Cxx-01";
        public static final String payment_cg = "F3-Cxx-02";
        /**
         * reimbursement
         * F1-Cxx-06 通用报销单(报账)
         * F1-Cxx-03 其他应付单
         * F1-Cxx-02 采购应付
         */
        public static final String reimbursement = "F1-Cxx-06";
        /**
         * receivable
         * F0-Cxx-01 项目应收单
         * F0-Cxx-02 销售应收单
         */
        public static final String receivable = "F0-Cxx-01";
        /**
         * 21-Cxx-001工程項目物資采購
         * 21-Cxx-N01普通采購
         * 21-Cxx-N02直運采購銷售
         * 21-Cxx-N03志雲采購入庫推出庫
         * 21-Cxx-N05國網電商采購
         * 21-Cxx-N06銷售協同采購
         */
        public static final String gcxmwzcg = "21-Cxx-001";
        public static final String ptcg = "21-Cxx-N01";
        public static final String zycgxs = "21-Cxx-N02";
        public static final String zycgrktck = "21-Cxx-N03";
        public static final String gwdscg = "21-Cxx-N05";
        public static final String xsxtcg = "21-Cxx-N06";
        public static final List<String> order_type_1 = Arrays.asList(gcxmwzcg, ptcg,zycgxs,zycgrktck,gwdscg,xsxtcg);
        /**
         *
         */
        public static final String fwlcg = "21-Cxx-007";
        public static final String gdzccg = "21-Cxx-015";
        public static final String gwdscgfwl = "21-Cxx-018";
        public static final List<String> order_type_2 = Arrays.asList(fwlcg, gdzccg,gwdscgfwl);
        /**
         * 采购入库
         */
        public static final String Store = "Store";
        /**
         * 物料凭证入库
         */
        public static final String material = "material";
        /**
         * 费用结算单采购订单
         */
        public static final String costOrder = "costOrder";
        /**
         * 虚拟采购订单
         */
        public static final String virtualOrder = "virtualOrder";
    }

    public static final String PkOrg = "PkOrg";
    public static final String cosType = "cosType";
    public static final String PkSupplier = "PkSupplier";
    public static final String ctrantypeid = "ctrantypeid";
    public static final String PkCustomer = "PkCustomer";
    public static final String accNum = "accNum";
    public static final String payAmo = "payAmo";
    public static final String deptType = "deptType";
    public static final String isAcr = "isAcr";
    public static final String project = "project";
    public static final String orgCostregion = "orgCostregion";
    public static final String PkStockOrg = "PkStockOrg";
    public static final String PkStock = "PkStock";

    /**
     * 组织类型2
     */
    public static final class DepartType {
        /**
         * 集团(1-省管产业单位，2-市集团)
         */
        public static final List<String> TOP_ORG = Arrays.asList("1");

        /**
         * 集团(1-省管产业单位，2-市集团)
         */
        public static final List<String> GROUP_ORG = Arrays.asList("1", "2");

        /**
         * 单位（3-一级单位，4-二级单位，8-虚拟单位，12-工程产业单位）
         */
        public static final List<String> ORG = Arrays.asList("1","2","3", "4", "8", "12");
        /**
         * 部门（5-一级部门，6-二级部门，7-科室，9-班组，10-工程项目部，11-工程施工班组）
         */
        public static final List<String> DEPART_ORG = Arrays.asList("5", "6", "7", "9", "10", "11");
    }
}

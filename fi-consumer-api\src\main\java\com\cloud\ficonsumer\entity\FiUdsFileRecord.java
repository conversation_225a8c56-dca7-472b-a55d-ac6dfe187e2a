package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@TableName("fi_uds_file_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "OSS文件上传UDS记录")
public class FiUdsFileRecord extends BaseEntity<FiUdsFileRecord> {
    
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;
    
    // OSS相关信息
    @ApiModelProperty(value = "OSS存储桶名称")
    private String bucketName;
    
    @ApiModelProperty(value = "OSS文件名称")
    private String fileName;
    
    @ApiModelProperty(value = "OSS原始文件名")
    private String original;
    
    @ApiModelProperty(value = "文件类型")
    private String fileType;
    
    @ApiModelProperty(value = "文件大小")
    private String fileSize;
    
    // UDS相关信息
    @ApiModelProperty(value = "UDS文档ID")
    private String documentId;
    
    @ApiModelProperty(value = "UDS版本ID")
    private String versionId;
    
    @ApiModelProperty(value = "UDS返回的文件名")
    private String udsFileName;
    
    @ApiModelProperty(value = "UDS元数据类型名称")
    private String metaTypeName;
    
    @ApiModelProperty(value = "推送状态(0-未推送 1-推送成功)")
    private String pushStatus;
    
    @ApiModelProperty(value = "UDS推送时间")
    private LocalDateTime pushTime;
    
    @ApiModelProperty(value = "推送失败信息")
    private String pushMsg;
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloud.ficonsumer.mapper.FiUdsFileRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cloud.ficonsumer.entity.FiUdsFileRecord">
        <id column="id" property="id"/>
        <result column="bucket_name" property="bucketName"/>
        <result column="file_name" property="fileName"/>
        <result column="original" property="original"/>
        <result column="file_type" property="fileType"/>
        <result column="file_size" property="fileSize"/>
        <result column="document_id" property="documentId"/>
        <result column="version_id" property="versionId"/>
        <result column="uds_file_name" property="udsFileName"/>
        <result column="meta_type_name" property="metaTypeName"/>
        <result column="push_status" property="pushStatus"/>
        <result column="push_time" property="pushTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

</mapper>
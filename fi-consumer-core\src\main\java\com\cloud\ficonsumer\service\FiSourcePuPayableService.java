package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiSourcePuPayableDetailQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPayableQueryDTO;
import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import com.cloud.ficonsumer.vo.FiConvertPuPayableDetailVO;
import com.cloud.ficonsumer.vo.FiConvertPuPayableVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourcePuPayableDetailVO;
import com.cloud.ficonsumer.vo.FiSourcePuPayableVO;

import java.util.List;
import java.util.Map;

public interface FiSourcePuPayableService extends IService<FiSourcePuPayable> {

    boolean push(String pkPayablebill);

    Page<FiSourcePuPayableVO> beforeConvertDataPage(Page<FiSourcePuPayableVO> page, FiSourcePuPayableQueryDTO queryDTO);

    Page<FiSourcePuPayableDetailVO> getBeforeConvertDataPage(Page<FiSourcePuPayableDetailVO> page, FiSourcePuPayableDetailQueryDTO queryDTO);

    List<FiConvertPuPayableVO> getConvertList(String pkPayablebill);

    Page<FiConvertPuPayableDetailVO> getConvertData(Page<FiConvertPuPayableDetailVO> page, String pkPayablebill);

    /**
     * 根据来源单据ID列表查询未挂账的应付单明细
     * <p>
     * 查询条件：
     * 1. 主表未挂账(return_status不为2或为空)
     * 2. 源单据ID在指定列表中
     * 3. 数据未删除(del_flag = 0)
     *
     * @param srcBillIds 来源单据ID列表（采购订单主键）
     * @return Map<String, FiSourcePuPayableDetailVO> 来源单据ID -> 应付单明细的映射
     */
    Map<String, FiSourcePuPayableDetailVO> getNoPostedBySrcBillIds(List<String> srcBillIds);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}

package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertPuPaybillDetail;
import com.cloud.ficonsumer.mapper.FiConvertPuPaybillDetailMapper;
import com.cloud.ficonsumer.service.FiConvertPuPaybillDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiConvertPuPaybillDetailServiceImpl extends ServiceImpl<FiConvertPuPaybillDetailMapper, FiConvertPuPaybillDetail> implements FiConvertPuPaybillDetailService {

}

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@TableName("fi_convert_projeti_payable")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目暂估应付转换表")
public class FiConvertProjEstiPayable extends BaseEntity<FiConvertProjEstiPayable> {

    private static final long serialVersionUID = -1L;

    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value = "暂估应付单标识")
    private String pkEstipayablebill;

    @ApiModelProperty(value = "所属组织")
    private String pkOrgV;

    @ApiModelProperty(value = "制单人")
    private String billmaker;

    @ApiModelProperty(value = "组织本币金额")
    private BigDecimal localMoney;

    @ApiModelProperty(value = "删除标记", example = "0")
    private Integer dr;

    @ApiModelProperty(value = "单据状态", example = "1")
    private Integer billstatus;

    @ApiModelProperty(value = "交易类型")
    private String pkTradetype;

    @ApiModelProperty(value = "同步状态", example = "0")
    private String def20;

    @ApiModelProperty(value="集成状态")
    private String integrationStatus;

    @ApiModelProperty(value="4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败")
    private Integer pushStatus;

    @ApiModelProperty(value="推送成功失败信息")
    private String pushMsg;

    @ApiModelProperty(value="返回同步消息")
    private String returnMsg;

    @ApiModelProperty(value="返回同步状态")
    private String returnStatus;

    @ApiModelProperty(value="重试次数")
    private Long tryNumber;

    @ApiModelProperty(value="0 未就绪 1 就绪")
    private Long readiness;
}

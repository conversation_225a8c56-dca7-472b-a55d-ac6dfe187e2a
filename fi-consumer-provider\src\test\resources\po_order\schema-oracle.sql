create table PO_ORDER
(
    APPROVER          VARCHAR2(20)  default '~',
    BC<PERSON>OP<PERSON>SO         CHAR,
    <PERSON><PERSON>RE<PERSON>           CHAR,
    B<PERSON>NALCLOSE       CHAR          default 'N',
    <PERSON><PERSON><PERSON><PERSON><PERSON>           CHAR          default 'N',
    BILLMAKER         VARCHAR2(20)  default '~',
    <PERSON><PERSON><PERSON><PERSON><PERSON>         CHAR          default 'Y',
    BISREPLENISH      CHAR          default 'N',
    BREFWHENRETURN    CHAR          default 'N',
    BRETURN           CHAR          default 'N',
    BSOCOOPTOME       CHAR          default 'N',
    CCONTRACTTEXTPATH VARCHAR2(181),
    CEMPLOYEEID       VARCHAR2(20)  default '~',
    CORIGCURRENCYID   VARCHAR2(20)  default '~',
    CREATIONTIME      CHAR(19),
    CREATOR           VARCHAR2(20)  default '~',
    CREVISEPSN        VARCHAR2(20)  default '~',
    CTRADEWORDID      VARCHAR2(20)  default '~',
    CTRANTYPEID       VARCHAR2(20)  default '~',
    <PERSON><PERSON><PERSON><PERSON><PERSON>         CHAR(19),
    DCLOSEDATE        VARCHAR2(19),
    <PERSON><PERSON><PERSON>DA<PERSON>         CHAR(19),
    DR                NUMBER(10)    default 0,
    FHTAXTYPEFLAG     NUMBER(38)    default 1,
    FORDERSTATUS      NUMBER(38)    default 0,
    <PERSON>RINTCOUNT       NUMBER(38)    default 0,
    MODIFIEDTIME      VARCHAR2(19),
    MODIFIER          VARCHAR2(20)  default '~',
    NHTAXRATE         NUMBER(28, 8),
    NORGPREPAYLIMIT   NUMBER(28, 8),
    NTOTALASTNUM      NUMBER(28, 8),
    NTOTALORIGMNY     NUMBER(28, 8),
    NTOTALPIECE       NUMBER(28, 8),
    NTOTALVOLUME      NUMBER(28, 8),
    NTOTALWEIGHT      NUMBER(28, 8),
    NVERSION          NUMBER(38),
    PK_BALATYPE       VARCHAR2(20),
    PK_BANKDOC        VARCHAR2(20)  default '~',
    PK_BUSITYPE       VARCHAR2(20)  default '~',
    PK_DELIVERADD     VARCHAR2(20)  default '~',
    PK_DEPT           VARCHAR2(20)  default '~',
    PK_DEPT_V         VARCHAR2(20)  default '~',
    PK_FREECUST       VARCHAR2(20)  default '~',
    PK_FREEZEPSNDOC   VARCHAR2(20)  default '~',
    PK_GROUP          VARCHAR2(20)  default '~',
    PK_INVCSUPLLIER   VARCHAR2(20)  default '~',
    PK_ORDER          CHAR(20)                  not null
        constraint PK_PO_ORDER
            primary key,
    PK_ORG            VARCHAR2(20)  default '~',
    PK_ORG_V          VARCHAR2(20)  default '~',
    PK_PAYTERM        VARCHAR2(20)  default '~',
    PK_PROJECT        VARCHAR2(20)  default '~',
    PK_RECVCUSTOMER   VARCHAR2(20)  default '~',
    PK_SUPPLIER       VARCHAR2(20)  default '~' not null,
    PK_TRANSPORTTYPE  VARCHAR2(20)  default '~',
    TAUDITTIME        CHAR(19),
    TFREEZETIME       CHAR(19),
    TREVISIONTIME     CHAR(19),
    TS                CHAR(19)      default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VBILLCODE         VARCHAR2(40),
    VCOOPORDERCODE    VARCHAR2(40),
    VDEF1             VARCHAR2(101),
    VDEF10            VARCHAR2(101),
    VDEF11            VARCHAR2(101),
    VDEF12            VARCHAR2(101),
    VDEF13            VARCHAR2(101),
    VDEF14            VARCHAR2(101),
    VDEF15            VARCHAR2(101),
    VDEF16            VARCHAR2(101),
    VDEF17            VARCHAR2(101),
    VDEF18            VARCHAR2(101),
    VDEF19            VARCHAR2(101),
    VDEF2             VARCHAR2(101),
    VDEF20            VARCHAR2(101),
    VDEF3             VARCHAR2(101),
    VDEF4             VARCHAR2(101),
    VDEF5             VARCHAR2(101),
    VDEF6             VARCHAR2(101),
    VDEF7             VARCHAR2(101),
    VDEF8             VARCHAR2(101),
    VDEF9             VARCHAR2(101),
    VFROZENREASON     VARCHAR2(100),
    VMEMO             VARCHAR2(500),
    VTRANTYPECODE     VARCHAR2(20)  default '~',
    SAGA_BTXID        VARCHAR2(64),
    SAGA_FROZEN       NUMBER(38)    default 0,
    SAGA_GTXID        VARCHAR2(64),
    SAGA_STATUS       NUMBER(38)    default 0,
    NACCPAYREQMNY     NUMBER(28, 8),
    NACCPAYMNY        NUMBER(28, 8),
    NINVOICEMNY       NUMBER(28, 8),
    VDEF21            VARCHAR2(101),
    VDEF22            VARCHAR2(101),
    VDEF23            VARCHAR2(101),
    VDEF24            VARCHAR2(101),
    VDEF25            VARCHAR2(101),
    VDEF26            VARCHAR2(101),
    VDEF27            VARCHAR2(101),
    VDEF28            VARCHAR2(101),
    VDEF29            VARCHAR2(101),
    VDEF30            VARCHAR2(101),
    VDEF31            VARCHAR2(101) default '~',
    VDEF32            VARCHAR2(101) default '~',
    VDEF33            VARCHAR2(101) default '~',
    VDEF34            VARCHAR2(101) default '~',
    VDEF35            VARCHAR2(101) default '~',
    VDEF36            VARCHAR2(101) default '~',
    VDEF37            VARCHAR2(101) default '~',
    VDEF38            VARCHAR2(101) default '~',
    VDEF39            VARCHAR2(101) default '~',
    VDEF40            VARCHAR2(101) default '~',
    VDEF41            VARCHAR2(101) default '~',
    VDEF42            VARCHAR2(101) default '~',
    VDEF43            VARCHAR2(101) default '~',
    VDEF44            VARCHAR2(101) default '~',
    VDEF45            VARCHAR2(101) default '~',
    VDEF46            VARCHAR2(101) default '~',
    VDEF47            VARCHAR2(101) default '~',
    VDEF48            VARCHAR2(101) default '~',
    VDEF49            VARCHAR2(101) default '~',
    VDEF50            VARCHAR2(101) default '~',
    VDEF51            VARCHAR2(101) default '~',
    VDEF52            VARCHAR2(101) default '~',
    VDEF53            VARCHAR2(101) default '~',
    VDEF54            VARCHAR2(101) default '~',
    VDEF55            VARCHAR2(101) default '~',
    VDEF56            VARCHAR2(101) default '~',
    VDEF57            VARCHAR2(101) default '~',
    VDEF58            VARCHAR2(101) default '~',
    VDEF59            VARCHAR2(101) default '~',
    VDEF60            VARCHAR2(101) default '~',
    VDEF61            VARCHAR2(101) default '~',
    VDEF62            VARCHAR2(101) default '~',
    VDEF63            VARCHAR2(101) default '~',
    VDEF64            VARCHAR2(101) default '~',
    VDEF65            VARCHAR2(101) default '~',
    VDEF66            VARCHAR2(101) default '~',
    VDEF67            VARCHAR2(101) default '~',
    VDEF68            VARCHAR2(101) default '~',
    VDEF69            VARCHAR2(101) default '~',
    VDEF70            VARCHAR2(101) default '~',
    VDEF71            VARCHAR2(101) default '~',
    VDEF72            VARCHAR2(101) default '~',
    VDEF73            VARCHAR2(101) default '~',
    VDEF74            VARCHAR2(101) default '~',
    VDEF75            VARCHAR2(101) default '~',
    VDEF76            VARCHAR2(101) default '~',
    VDEF77            VARCHAR2(101) default '~',
    VDEF78            VARCHAR2(101) default '~',
    VDEF79            VARCHAR2(101) default '~',
    VDEF80            VARCHAR2(101) default '~'
);

create table PO_ORDER_B
(
    BARRIVECLOSE       CHAR          default 'N',
    BBORROWPUR         CHAR          default 'N',
    BCOMPARE           CHAR          default 'N',
    BINVOICECLOSE      CHAR          default 'N',
    BLARGESS           CHAR          default 'N',
    BPAYCLOSE          CHAR          default 'N',
    BRECEIVEPLAN       CHAR          default 'N',
    BSTOCKCLOSE        CHAR          default 'N',
    BTRANSCLOSED       CHAR          default 'N',
    BTRIATRADEFLAG     CHAR,
    CASSCUSTID         VARCHAR2(20)  default '~',
    CASTUNITID         VARCHAR2(20)  default '~',
    CCONTRACTID        VARCHAR2(20)  default '~',
    CCONTRACTROWID     VARCHAR2(20),
    CCURRENCYID        VARCHAR2(20)  default '~',
    CDESTIAREAID       VARCHAR2(20)  default '~',
    CDESTICOUNTRYID    VARCHAR2(20)  default '~',
    CDEVADDRID         VARCHAR2(20)  default '~',
    CDEVAREAID         VARCHAR2(20)  default '~',
    CECBILLBID         VARCHAR2(20),
    CECBILLID          VARCHAR2(20),
    CECTYPECODE        VARCHAR2(20)  default '~',
    CFFILEID           VARCHAR2(20),
    CFIRSTBID          VARCHAR2(20),
    CFIRSTID           VARCHAR2(20),
    CFIRSTTYPECODE     VARCHAR2(20)  default '~',
    CHANDLER           VARCHAR2(20)  default '~',
    CORIGAREAID        VARCHAR2(20)  default '~',
    CORIGCOUNTRYID     VARCHAR2(20)  default '~',
    CORIGCURRENCYID    VARCHAR2(20)  default '~',
    CPRAYBILLBID       VARCHAR2(20),
    CPRAYBILLCODE      VARCHAR2(40),
    CPRAYBILLHID       VARCHAR2(20),
    CPRAYBILLROWNO     VARCHAR2(20),
    CPRAYTYPECODE      VARCHAR2(20),
    CPRICEAUDIT_BB1ID  VARCHAR2(20),
    CPRICEAUDIT_BID    VARCHAR2(20),
    CPRICEAUDITID      VARCHAR2(20)  default '~',
    CPRODUCTORID       VARCHAR2(20)  default '~',
    CPROJECTID         VARCHAR2(20)  default '~',
    CPROJECTTASKID     VARCHAR2(20)  default '~',
    CQPBASESCHEMEID    VARCHAR2(20)  default '~',
    CQTUNITID          VARCHAR2(20)  default '~',
    CQUALITYLEVELID    VARCHAR2(20)  default '~',
    CRECECOUNTRYID     VARCHAR2(20)  default '~',
    CROWNO             VARCHAR2(20),
    CSENDCOUNTRYID     VARCHAR2(20)  default '~',
    CSOURCEBID         VARCHAR2(20),
    CSOURCEID          VARCHAR2(20),
    CSOURCETYPECODE    VARCHAR2(20)  default '~',
    CTAXCODEID         VARCHAR2(20)  default '~',
    CTAXCOUNTRYID      VARCHAR2(20)  default '~',
    CUNITID            VARCHAR2(20)  default '~',
    CVENDDEVADDRID     VARCHAR2(20)  default '~',
    CVENDDEVAREAID     VARCHAR2(20)  default '~',
    DBILLDATE          CHAR(19),
    DCORRECTDATE       VARCHAR2(19),
    DPLANARRVDATE      CHAR(19),
    DR                 NUMBER(10)    default 0,
    FBUYSELLFLAG       NUMBER(38),
    FISACTIVE          NUMBER(38)    default 0,
    FTAXTYPEFLAG       NUMBER(38)    default 1,
    NACCCANCELINVMNY   NUMBER(28, 8) default 0,
    NACCUMARRVNUM      NUMBER(28, 8),
    NACCUMDEVNUM       NUMBER(28, 8),
    NACCUMINVOICEMNY   NUMBER(28, 8) default 0,
    NACCUMINVOICENUM   NUMBER(28, 8),
    NACCUMPICKUPNUM    NUMBER(28, 8),
    NACCUMRPNUM        NUMBER(28, 8),
    NACCUMSTORENUM     NUMBER(28, 8),
    NACCUMWASTNUM      NUMBER(28, 8),
    NASTNUM            NUMBER(28, 8),
    NBACKARRVNUM       NUMBER(28, 8),
    NBACKSTORENUM      NUMBER(28, 8),
    NCALCOSTMNY        NUMBER(28, 8),
    NCALTAXMNY         NUMBER(28, 8),
    NEXCHANGERATE      NUMBER(28, 8),
    NFEEMNY            NUMBER(28, 8),
    NGLOBALEXCHGRATE   NUMBER(28, 8),
    NGLOBALMNY         NUMBER(28, 8),
    NGLOBALTAXMNY      NUMBER(28, 8),
    NGROUPEXCHGRATE    NUMBER(28, 8),
    NGROUPMNY          NUMBER(28, 8),
    NGROUPTAXMNY       NUMBER(28, 8),
    NITEMDISCOUNTRATE  NUMBER(28, 8),
    NMNY               NUMBER(28, 8),
    NNETPRICE          NUMBER(28, 8),
    NNOSUBTAX          NUMBER(28, 8),
    NNOSUBTAXRATE      NUMBER(28, 8),
    NNUM               NUMBER(28, 8),
    NORIGMNY           NUMBER(28, 8),
    NORIGNETPRICE      NUMBER(28, 8),
    NORIGPRICE         NUMBER(28, 8),
    NORIGTAXMNY        NUMBER(28, 8),
    NORIGTAXNETPRICE   NUMBER(28, 8),
    NORIGTAXPRICE      NUMBER(28, 8),
    NPACKNUM           NUMBER(28, 8),
    NPRICE             NUMBER(28, 8),
    NQTNETPRICE        NUMBER(28, 8),
    NQTORIGNETPRICE    NUMBER(28, 8),
    NQTORIGPRICE       NUMBER(28, 8),
    NQTORIGTAXNETPRC   NUMBER(28, 8),
    NQTORIGTAXPRICE    NUMBER(28, 8),
    NQTPRICE           NUMBER(28, 8),
    NQTTAXNETPRICE     NUMBER(28, 8),
    NQTTAXPRICE        NUMBER(28, 8),
    NQTUNITNUM         NUMBER(28, 8),
    NSUPRSNUM          NUMBER(28, 8),
    NTAX               NUMBER(28, 8),
    NTAXMNY            NUMBER(28, 8),
    NTAXNETPRICE       NUMBER(28, 8),
    NTAXPRICE          NUMBER(28, 8),
    NTAXRATE           NUMBER(28, 8),
    NVOLUMN            NUMBER(28, 8),
    NWEIGHT            NUMBER(28, 8),
    PK_APFINANCEORG    VARCHAR2(20)  default '~',
    PK_APFINANCEORG_V  VARCHAR2(20)  default '~',
    PK_APLIABCENTER    VARCHAR2(20)  default '~',
    PK_APLIABCENTER_V  VARCHAR2(20)  default '~',
    PK_ARRLIABCENTER   VARCHAR2(20)  default '~',
    PK_ARRLIABCENTER_V VARCHAR2(20)  default '~',
    PK_ARRVSTOORG      VARCHAR2(20)  default '~',
    PK_ARRVSTOORG_V    VARCHAR2(20)  default '~',
    PK_BATCHCODE       VARCHAR2(20),
    PK_DISCOUNT        VARCHAR2(20)  default '~',
    PK_FLOWSTOCKORG    VARCHAR2(20)  default '~',
    PK_FLOWSTOCKORG_V  VARCHAR2(20)  default '~',
    PK_GROUP           VARCHAR2(20)  default '~',
    PK_MATERIAL        VARCHAR2(20)  default '~' not null,
    PK_ORDER           CHAR(20)                  not null,
    PK_ORDER_B         CHAR(20)                  not null
        constraint PK_PO_ORDER_B
            primary key,
    PK_ORG             VARCHAR2(20)  default '~',
    PK_ORG_V           VARCHAR2(20)  default '~',
    PK_PSFINANCEORG    VARCHAR2(20)  default '~',
    PK_PSFINANCEORG_V  VARCHAR2(20)  default '~',
    PK_RECEIVEADDRESS  VARCHAR2(20)  default '~',
    PK_RECVSTORDOC     VARCHAR2(20)  default '~',
    PK_REQCORP         VARCHAR2(20)  default '~',
    PK_REQDEPT         VARCHAR2(20)  default '~',
    PK_REQDEPT_V       VARCHAR2(20)  default '~',
    PK_REQSTOORG       VARCHAR2(20)  default '~',
    PK_REQSTOORG_V     VARCHAR2(20)  default '~',
    PK_REQSTORDOC      VARCHAR2(20)  default '~',
    PK_SRCMATERIAL     VARCHAR2(20)  default '~' not null,
    PK_SRCORDER_B      CHAR(20),
    PK_SUPPLIER        VARCHAR2(20)  default '~',
    TS                 CHAR(19)      default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VBATCHCODE         VARCHAR2(40),
    VBDEF1             VARCHAR2(300),
    VBDEF10            VARCHAR2(101),
    VBDEF11            VARCHAR2(101),
    VBDEF12            VARCHAR2(101),
    VBDEF13            VARCHAR2(101),
    VBDEF14            VARCHAR2(101),
    VBDEF15            VARCHAR2(101),
    VBDEF16            VARCHAR2(101),
    VBDEF17            VARCHAR2(101),
    VBDEF18            VARCHAR2(101),
    VBDEF19            VARCHAR2(101),
    VBDEF2             VARCHAR2(101),
    VBDEF20            VARCHAR2(101),
    VBDEF3             VARCHAR2(101),
    VBDEF4             VARCHAR2(101),
    VBDEF5             VARCHAR2(101),
    VBDEF6             VARCHAR2(101),
    VBDEF7             VARCHAR2(101),
    VBDEF8             VARCHAR2(500),
    VBDEF9             VARCHAR2(101),
    VBMEMO             VARCHAR2(181),
    VCHANGERATE        VARCHAR2(60),
    VCONTRACTCODE      VARCHAR2(40),
    VECBILLCODE        VARCHAR2(40),
    VFIRSTCODE         VARCHAR2(40),
    VFIRSTROWNO        VARCHAR2(20),
    VFIRSTTRANTYPE     VARCHAR2(20)  default '~',
    VFREE1             VARCHAR2(101),
    VFREE10            VARCHAR2(101),
    VFREE2             VARCHAR2(101),
    VFREE3             VARCHAR2(101),
    VFREE4             VARCHAR2(101),
    VFREE5             VARCHAR2(101),
    VFREE6             VARCHAR2(101),
    VFREE7             VARCHAR2(101),
    VFREE8             VARCHAR2(101),
    VFREE9             VARCHAR2(101),
    VPRICEAUDITCODE    VARCHAR2(30),
    VQTUNITRATE        VARCHAR2(60),
    VSOURCECODE        VARCHAR2(40),
    VSOURCEROWNO       VARCHAR2(20),
    VSOURCETRANTYPE    VARCHAR2(20)  default '~',
    VVENDDEVADDR       VARCHAR2(20)  default '~',
    VVENDINVENTORYCODE VARCHAR2(30),
    VVENDINVENTORYNAME VARCHAR2(60),
    VBDEF21            VARCHAR2(101),
    VBDEF22            VARCHAR2(101),
    VBDEF23            VARCHAR2(101),
    VBDEF24            VARCHAR2(101),
    VBDEF25            VARCHAR2(101),
    VBDEF26            VARCHAR2(101),
    VBDEF27            VARCHAR2(101),
    VBDEF28            VARCHAR2(101),
    VBDEF29            VARCHAR2(101),
    VBDEF30            VARCHAR2(101),
    VBDEF31            VARCHAR2(101) default '~',
    VBDEF32            VARCHAR2(101) default '~',
    VBDEF33            VARCHAR2(101) default '~',
    VBDEF34            VARCHAR2(101) default '~',
    VBDEF35            VARCHAR2(101) default '~',
    VBDEF36            VARCHAR2(101) default '~',
    VBDEF37            VARCHAR2(101) default '~',
    VBDEF38            VARCHAR2(101) default '~',
    VBDEF39            VARCHAR2(101) default '~',
    VBDEF40            VARCHAR2(101) default '~',
    VBDEF41            VARCHAR2(101) default '~',
    VBDEF42            VARCHAR2(101) default '~',
    VBDEF43            VARCHAR2(101) default '~',
    VBDEF44            VARCHAR2(101) default '~',
    VBDEF45            VARCHAR2(101) default '~',
    VBDEF46            VARCHAR2(101) default '~',
    VBDEF47            VARCHAR2(101) default '~',
    VBDEF48            VARCHAR2(101) default '~',
    VBDEF49            VARCHAR2(101) default '~',
    VBDEF50            VARCHAR2(101) default '~',
    VBDEF51            VARCHAR2(101) default '~',
    VBDEF52            VARCHAR2(101) default '~',
    VBDEF53            VARCHAR2(101) default '~',
    VBDEF54            VARCHAR2(101) default '~',
    VBDEF55            VARCHAR2(101) default '~',
    VBDEF56            VARCHAR2(101) default '~',
    VBDEF57            VARCHAR2(101) default '~',
    VBDEF58            VARCHAR2(101) default '~',
    VBDEF59            VARCHAR2(101) default '~',
    VBDEF60            VARCHAR2(101) default '~',
    VBDEF61            VARCHAR2(101) default '~',
    VBDEF62            VARCHAR2(101) default '~',
    VBDEF63            VARCHAR2(101) default '~',
    VBDEF64            VARCHAR2(101) default '~',
    VBDEF65            VARCHAR2(101) default '~',
    VBDEF66            VARCHAR2(101) default '~',
    VBDEF67            VARCHAR2(101) default '~',
    VBDEF68            VARCHAR2(101) default '~',
    VBDEF69            VARCHAR2(101) default '~',
    VBDEF70            VARCHAR2(101) default '~',
    VBDEF71            VARCHAR2(101) default '~',
    VBDEF72            VARCHAR2(101) default '~',
    VBDEF73            VARCHAR2(101) default '~',
    VBDEF74            VARCHAR2(101) default '~',
    VBDEF75            VARCHAR2(101) default '~',
    VBDEF76            VARCHAR2(101) default '~',
    VBDEF77            VARCHAR2(101) default '~',
    VBDEF78            VARCHAR2(101) default '~',
    VBDEF79            VARCHAR2(101) default '~',
    VBDEF80            VARCHAR2(101) default '~'
);
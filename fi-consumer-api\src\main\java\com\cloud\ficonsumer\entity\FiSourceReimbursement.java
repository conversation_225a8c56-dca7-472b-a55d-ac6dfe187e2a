

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用报销单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:38
 */
@Data
@TableName("fi_source_reimbursement")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "通用报销单源数据主表")
public class FiSourceReimbursement extends BaseEntity<FiSourceReimbursement> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * pk主键
     */
    @ApiModelProperty(value="pk主键")
    private String pkPayablebill;

    /**
     * 编码
     */
    @ApiModelProperty(value="编码")
    private String billno;

    /**
     * 单据类型
     * F1-Cxx-06 通用报销单(报账)
     * F1-Cxx-02 采购应付
     */
    @ApiModelProperty(value="单据类型")
    private String pkTradetype;

    /**
     * 摘要
     */
    @ApiModelProperty(value="摘要")
    private String scomment;

    /**
     * 财务组织
     */
    @ApiModelProperty(value="财务组织")
    private String pkOrg;

    /**
     * 财务组织名称
     */
    @ApiModelProperty(value="财务组织名称")
    private String pkOrgName;

    /**
     * 应付财务组织版本
     */
    @ApiModelProperty(value="应付财务组织版本")
    private String pkOrgV;

    /**
     * 收支项目
     */
    @ApiModelProperty(value="收支项目")
    private String pkSubjcode;

    /**
     * 是否退回重传标识
     * Y-是，N-否；标识默认为N，回退成功后，更新为Y；单据集成成功后，重置为N
     */
    @ApiModelProperty(value="是否退回重传标识")
    private String backTag;

    /**
     * 往来对象
     */
    @ApiModelProperty(value="往来对象")
    private String objtype;

    /**
     * 制单人  - 取MDM编码
     */
    @ApiModelProperty(value="制单人")
    private String billmaker;

    /**
     * 制单人 - 取ISC登录账号
     */
    @ApiModelProperty(value="制单人")
    private String billmakerT;

    /**
     * 业务员
     */
    @ApiModelProperty(value="业务员")
    private String pkPsndoc;

    /**
     * 合同类型
     */
    @ApiModelProperty(value="合同类型")
    private String def5;

    /**
     * 付款方式
     */
    @ApiModelProperty(value="付款方式")
    private String def2;

    /**
     * 平台业务事项
     */
    @ApiModelProperty(value="平台业务事项")
    private String def47;

    /**
     * 部门分类
     */
    @ApiModelProperty(value="部门分类")
    private String def52;

    /**
     * 币种
     */
    @ApiModelProperty(value="币种")
    private String pkCurrtype;

    /**
     * 项目
     */
    @ApiModelProperty(value="项目")
    private String project;

    /**
     * 供应商
     */
    @ApiModelProperty(value="供应商")
    private String supplier;

    /**
     * 组织本币金额
     */
    @ApiModelProperty(value="组织本币金额")
    private String localMoney;

    /**
     * 部门
     */
    @ApiModelProperty(value="部门")
    private String pkDeptid;

    /**
     * 部门版本
     */
    @ApiModelProperty(value="部门版本")
    private String pkDeptidV;

    /**
     * 推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中
     */
    @ApiModelProperty(value="推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中")
    private Integer pushStatus;

    /**
     * 推送成功失败信息
     */
    @ApiModelProperty(value="推送成功失败信息")
    private String pushMsg;

    /**
     * 推送时间
     */
    @ApiModelProperty(value="推送时间")
    private String pushTime;

    /**
     * 返回同步消息
     */
    @ApiModelProperty(value="返回同步消息")
    private String returnMsg;

    /**
     * 返回同步状态
     */
    @ApiModelProperty(value="返回同步状态")
    private String returnStatus;

    /**
     * 返回同步单号
     */
    @ApiModelProperty(value="返回同步单号")
    private String returnBill;

    /**
     * 返回接收时间
     */
    @ApiModelProperty(value="返回接收时间")
    private String returnTime;

    /**
     * 重试次数
     */
    @ApiModelProperty(value="重试次数")
    private Long tryNumber;

    /**
     * 0 未就绪 1 就绪
     */
    @ApiModelProperty(value="0 未就绪 1 就绪")
    private Long readiness;

}

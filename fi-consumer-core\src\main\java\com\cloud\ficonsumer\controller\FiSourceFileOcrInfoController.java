package com.cloud.ficonsumer.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.FiSourceFileInfoQueryDTO;
import com.cloud.ficonsumer.dto.FiSourceFileOcrInfoQueryDTO;
import com.cloud.ficonsumer.entity.FiSourceFileOcrInfo;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceFileOcrInfoService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiConvertFileInfoVO;
import com.cloud.ficonsumer.vo.FiConvertFileOcrInfoVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceFileInfoVO;
import com.cloud.ficonsumer.vo.FiSourceFileOcrInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 文件OCR信息源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-07 15:52:38
 */
@RestController
@AllArgsConstructor
@RequestMapping("/fileOcrInfo")
@Api(value = "fileOcrInfo", tags = "文件OCR信息源数据主表管理")
public class FiSourceFileOcrInfoController {

    private final FiSourceFileOcrInfoService fiSourceFileOcrInfoService;

    /**
     * 报错重推
     *
     * @return
     */
    @ApiOperation(value = "报错重推", notes = "报错重推")
    @GetMapping("/pushAgain/{uniquecodeofinvoice}")
    public R<Boolean> pushAgain(@PathVariable("uniquecodeofinvoice") String uniquecodeofinvoice) {
        return R.ok(fiSourceFileOcrInfoService.push(uniquecodeofinvoice));
    }

    /**
     * 转换前分页查询
     *
     * @return
     */
    @ApiOperation(value = "转换前分页查询", notes = "转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiSourceFileOcrInfoVO>> beforeConvertDataPage(Page<FiSourceFileOcrInfoVO> page, FiSourceFileOcrInfoQueryDTO queryDTO) {
        // 构造组织条件
        Long orgId = AdminUtils.processToOrgId(queryDTO.getOrgCode());
        queryDTO.setOrgId(orgId);
        // 查询转换前分页数据
        return R.ok(fiSourceFileOcrInfoService.beforeConvertDataPage(page, queryDTO));
    }

    /**
     * 转换前数据
     * @return
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage")
    public R<Page<FiSourceFileInfoVO>> getBeforeConvertDataPage(Page<FiSourceFileInfoVO> page, FiSourceFileInfoQueryDTO queryDTO) {
        return R.ok(fiSourceFileOcrInfoService.getBeforeConvertDataPage(page, queryDTO));
    }

    /**
     * 主体表转换后单条字段
     * @return
     */
    @ApiOperation(value = "主体表转换后单条字段", notes = "主体表转换后单条字段")
    @GetMapping("/getConvertList")
    public R<List<FiConvertFileOcrInfoVO>> getConvertList(String uniquecodeofinvoice) {
        return R.ok(fiSourceFileOcrInfoService.getConvertList(uniquecodeofinvoice));
    }

    /**
     * 获取转换后详情分页数据
     * @return
     */
    @ApiOperation(value = "获取转换后详情分页数据", notes = "获取转换后详情分页数据")
    @GetMapping("/getConvertData")
    public R<Page<FiConvertFileInfoVO>> getConvertData(Page<FiConvertFileInfoVO> page, String uniquecodeofinvoice) {
        return R.ok(fiSourceFileOcrInfoService.getConvertData(page, uniquecodeofinvoice));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result = fiSourceFileOcrInfoService.update(Wrappers.<FiSourceFileOcrInfo>lambdaUpdate()
                .set(FiSourceFileOcrInfo::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiSourceFileOcrInfo::getPushMsg, "成功")
                .in(FiSourceFileOcrInfo::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiSourceFileOcrInfoService.pushAgainBatch(queryVO));
    }
}
package com.cloud.ficonsumer.vo;

import com.cloud.ficonsumer.entity.FiSourceOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/7.
 */
@Data
public class FiSourceOrderQueryVO extends FiSourceOrder {

    /**
     * 集团标识
     * 当查询为某一个集团下所有单位数据时候传集团编码
     */
    @ApiModelProperty(value="集团标识")
    private String groupCodeFlag;

    /**
     * 集团主键
     */
    @ApiModelProperty(value="集团主键")
    private String group;

    /**
     * 组织集合
     */
    @ApiModelProperty(value="组织集合")
    private List<String> pkOrgList;

    /**
     * 推送状态列表，用于多选查询
     */
    @ApiModelProperty(value = "推送状态列表，用于多选查询")
    private List<Integer> pushStatusList;

    /**
     * 拉取起始时间
     */
    @ApiModelProperty(value="拉取起始时间")
    private String createTimeStart;


    /**
     * 拉取结束时间
     */
    @ApiModelProperty(value="拉取结束时间")
    private String createTimeEnd;
}

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cloud</groupId>
        <artifactId>fi-consumer</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fi-consumer-core</artifactId>
    <description>业务核心包</description>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>cloud-plat-dependencies</artifactId>
            <version>3.9.5-GA-2311-1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.cloud</groupId>
                    <artifactId>cloud-common-sentinel</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>fi-consumer-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.finmp</groupId>
            <artifactId>finmp-sdk</artifactId>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/finmp-sdk-1.1.4.jar</systemPath>
            <version>1.1.4</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>cn.com.hyit.finmp</groupId>-->
<!--            <artifactId>finmp-sdk</artifactId>-->
<!--            <version>1.1.4</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.sgcc.uds.cloud</groupId>
            <artifactId>uds-sdk-java</artifactId>
            <version>2.1.8</version>
        </dependency>
        <dependency>
            <groupId>com.sgcc.uds.cloud</groupId>
            <artifactId>uds-ms-metadata-rpc-dto</artifactId>
            <version>2.1.8</version>
        </dependency>
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>cloud-upms-api</artifactId>
            <version>3.9.5-GA-2407-1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.27</version>
        </dependency>
        <!-- 定时任务 -->
        <dependency>
            <groupId>net.dreamlu</groupId>
            <artifactId>mica-jobs</artifactId>
            <version>2.6.8</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>cloud-common-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>cloud-common-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>cloud-common-minio</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.2.141</version>
        </dependency>
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>apiexchange-api</artifactId>
            <version>3.9.5-GA-2311-1</version>
        </dependency>
        <dependency>
            <groupId>com.cloud</groupId>
            <artifactId>apiexchange-client</artifactId>
            <version>3.9.5-GA-2412-1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web-services</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.axis</groupId>
            <artifactId>axis-jaxrpc</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>axis</groupId>
            <artifactId>axis-wsdl4j</artifactId>
            <version>1.5.1</version>
        </dependency>
        <dependency>
            <groupId>commons-discovery</groupId>
            <artifactId>commons-discovery</artifactId>
            <version>0.2</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>19.18.0.0</version>
        </dependency>
    </dependencies>
</project>

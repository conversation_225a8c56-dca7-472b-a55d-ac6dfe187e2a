package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "转换后文件信息")
@TableName("fi_convert_file_info")
public class FiConvertFileInfo extends BaseEntity<FiConvertFileInfo> {

    @TableId(value = "id")
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "FileManage系統中表主键")
    private String fileManageId;

    @ApiModelProperty(value = "业+组织ID")
    private Long orgId;

    @ApiModelProperty(value = "关联id")
    private Integer relationId;

    @ApiModelProperty(value = "继承id")
    private Integer extendsId;

    @ApiModelProperty(value = "影像类型编码")
    private String imageCode;

    @ApiModelProperty(value = "影像类型名称")
    private String imageName;

    @ApiModelProperty(value = "是否刚性控制 1-是 0-否")
    private String isUpload;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "保存路径")
    private String bucketName;

    @ApiModelProperty(value = "原文件名")
    private String original;

    @ApiModelProperty(value = "文件类型")
    private String fileType;

    @ApiModelProperty(value = "文件大小")
    private String fileSize;

    @ApiModelProperty(value = "上传方式（0-手动上传 1-扫描录入）")
    private String uploadType;

    @ApiModelProperty(value = "模块对应的表")
    private String moduleType;

    @ApiModelProperty(value = "模块业务id")
    private Integer moduleId;

    @ApiModelProperty(value = "模块子类型（0-未知，1-录音，2-图片）")
    private String type;

    @ApiModelProperty(value = "显示文件名")
    private String showName;

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "文件一致性校验结果 1-通过 0-不通过")
    private String erpFileCheck;

    @ApiModelProperty(value = "创建人姓名")
    private String createByName;

    @ApiModelProperty(value = "最后修改人姓名")
    private String updateByName;

    @ApiModelProperty(value = "来源主键id")
    private String sourceId;

    @ApiModelProperty(value = "来源关联id")
    private String sourceRelationId;

    @ApiModelProperty(value = "ocr类型")
    private String ocrType;
}

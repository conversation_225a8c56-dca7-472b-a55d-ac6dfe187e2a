package com.cloud.ficonsumer.exception;


/**
 * 业务异常类
 *
 * <AUTHOR>
 */
public class BusinessException extends BaseException {

    private Object data;

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(String message, Object data) {
        super(message);
        this.data = data;
    }

    public BusinessException(int code, String msg) {
        super(msg);
        this.setMsg(msg);
        this.setCode(code);
    }

    public BusinessException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum.getMsg());
        this.setMsg(exceptionEnum.getMsg());
        this.setCode(exceptionEnum.getCode());
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}

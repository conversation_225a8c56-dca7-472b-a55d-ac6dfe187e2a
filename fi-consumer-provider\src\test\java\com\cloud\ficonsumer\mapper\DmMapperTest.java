package com.cloud.ficonsumer.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.ficonsumer.BaseIntegrationTest;
import com.cloud.ficonsumer.dto.BillStatusDTO;
import com.cloud.ficonsumer.entity.BdUdsResult;
import com.cloud.ficonsumer.entity.IaI2billB;
import com.cloud.ficonsumer.entity.PoOrderB;
import com.cloud.ficonsumer.enums.InvoiceBillCheckEnum;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class DmMapperTest extends BaseIntegrationTest {
    
    @SpyBean
    private DmMapper dmMapper;
    
    @BeforeAll
    public void initData() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);
        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator populator = new ResourceDatabasePopulator(
                    // 采购发票
                    new ClassPathResource("pupayable/schema-oracle.sql"),
                    new ClassPathResource("pupayable/data-oracle.sql"),
                    // 收票登记
                    new ClassPathResource("pmreceiptreg/schema-oracle.sql"),
                    new ClassPathResource("pmreceiptreg/data-oracle.sql"),
                    // 采购订单
                    new ClassPathResource("po_order/schema-oracle.sql"),
                    new ClassPathResource("po_order/data-oracle.sql"),
                    // 采购到货
                    new ClassPathResource("ia_i2bill/schema-oracle.sql"),
                    new ClassPathResource("ia_i2bill/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(populator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    @Test
    @DisplayName("测试批量查询应付单状态")
    public void testBatchGetApPayablebillStatus() {
        List<String> billIds = Collections.singletonList("1005A1100000000EV72M");
        Map<String, BillStatusDTO> result = dmMapper.batchGetApPayablebillStatus(billIds,
                InvoiceBillCheckEnum.AP_PAYABLE_BILL.getValidStatuses());
        
        assertNotNull(result);
        assertEquals(1, result.size());
        // 根据data-oracle.sql中的实际数据验证状态
        assertEquals(1, result.get("1005A1100000000EV72M").getStatus());
    }

    @Test
    @DisplayName("测试批量查询采购发票状态")
    public void testBatchGetPoInvoiceStatus() {
        List<String> billIds = Collections.singletonList("1005A1100000000FA0XD");
        Map<String, BillStatusDTO> result = dmMapper.batchGetPoInvoiceStatus(billIds,
                InvoiceBillCheckEnum.PO_INVOICE.getValidStatuses());
        
        assertNotNull(result);
        assertEquals(1, result.size());
        // 根据data-oracle.sql中的实际数据验证状态
        assertEquals(3, result.get("1005A1100000000FA0XD").getStatus());
    }

    @Test
    @DisplayName("测试批量查询收票登记状态")
    public void testBatchGetPmReceiptregStatus() {
        List<String> billIds = Collections.singletonList("1005A1100000000PMREG");
        Map<String, BillStatusDTO> result = dmMapper.batchGetPmReceiptregStatus(billIds,
                InvoiceBillCheckEnum.PM_RECEIPT_REG.getValidStatuses());

        assertNotNull(result);
        assertEquals(1, result.size());
        // 根据data-oracle.sql中的实际数据验证状态
        assertEquals(1, result.get("1005A1100000000PMREG").getStatus());
    }

    @Test
    @DisplayName("测试查询采购订单明细")
    public void testListPoOrderBByPkB() {
        // 准备测试数据
        List<String> pkOrderBList = Collections.singletonList("1001A710000000LD4YOF");

        // 执行测试
        List<PoOrderB> result = dmMapper.listPoOrderBByPkB(pkOrderBList);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        PoOrderB orderB = result.get(0);
        assertEquals("1001A710000000LD4YOF", orderB.getPkOrderB());
        assertEquals("CD2025040900104915", orderB.getVbillcode());
        assertEquals("10", orderB.getCrowno());
        assertNotNull(orderB.getPkMaterial());
    }

    @Test
    @DisplayName("测试查询入库单明细")
    public void testListIaI2billBByBids() {
        // 准备测试数据
        List<String> cfirstbid = Collections.singletonList("1001A710000000LD4YOF");

        // 执行测试
        List<IaI2billB> result = dmMapper.listIaI2billBByBids(cfirstbid);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        IaI2billB iaI2billB = result.get(0);
        assertEquals("1001A710000000LD4YOF", iaI2billB.getCfirstbid());
        assertEquals("CR2025040900087830", iaI2billB.getVbillcode());
        assertEquals("10", iaI2billB.getCrowno());
        assertEquals(new BigDecimal("3"), iaI2billB.getNnum());
    }

    @Test
    @DisplayName("测试查询影像信息")
    public void testListUdsResult() {
        // 准备测试数据
        String pkBill = "1005A1100000000BILL001";
        String billType = "po_invoice";

        // 执行测试
        List<BdUdsResult> result = dmMapper.listUdsResult(pkBill, billType);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        BdUdsResult udsResult = result.get(0);
        assertNotNull(udsResult.getPkUdsresult());
    }
}

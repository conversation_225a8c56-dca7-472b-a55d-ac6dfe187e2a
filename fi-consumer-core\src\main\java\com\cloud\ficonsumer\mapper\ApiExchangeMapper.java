

package com.cloud.ficonsumer.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.apiexchange.entity.ApiInfoHis;
import com.cloud.cloud.admin.api.entity.SysDictItem;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.entity.ApiCompare;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 对照表
 *
 * <AUTHOR> code generator
 * @date 2024-07-12 10:14:10
 */
@DS("apiexchange")
@Mapper
public interface ApiExchangeMapper extends CloudBaseMapper<ApiCompare> {

    /**
     * 根据code查询转换配置
     *
     * @param code
     * @return
     */
    @Select("<script>select * from api_exchange where code=#{code} " +
            "</script>")
    ApiExchange getApiExchangeByCode(@Param("code") String code);

    @Insert("<script>INSERT INTO api_info_his (code,service_name,url,request_id,request,response,is_callback,status,transaction_id) " +
            " VALUES (#{query.code},#{query.serviceName},#{query.url},#{query.requestId},#{query.request},#{query.response},#{query.isCallback},#{query.status},#{query.transactionId}) " +
            "</script>")
    int saveApiInfoHis(@Param("query") ApiInfoHis apiMsg);
}

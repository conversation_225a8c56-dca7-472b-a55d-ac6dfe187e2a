package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceProjEstiPayableDetail;
import com.cloud.ficonsumer.mapper.FiSourceProjEstiPayableDetailMapper;
import com.cloud.ficonsumer.service.FiSourceProjEstiPayableDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourceProjEstiPayableDetailServiceImpl extends ServiceImpl<FiSourceProjEstiPayableDetailMapper, FiSourceProjEstiPayableDetail> implements FiSourceProjEstiPayableDetailService {

}

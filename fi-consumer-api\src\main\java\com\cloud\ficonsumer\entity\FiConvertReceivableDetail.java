

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目应收详情单转换数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:32
 */
@Data
@TableName("fi_convert_receivable_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目应收详情单转换数据主表")
public class FiConvertReceivableDetail extends BaseEntity<FiConvertReceivableDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * pk详情主键
     */
    @ApiModelProperty(value="pk详情主键")
    private String pkRecitem;

    /**
     * pk主键
     */
    @ApiModelProperty(value="pk主键")
    private String pkRecbill;

    /**
     * 客户名称
     */
    @ApiModelProperty(value="客户名称")
    private String customer;

    /**
     * 应收财务组织
     */
    @ApiModelProperty(value="应收财务组织")
    private String contractno;

    /**
     * 制单人
     */
    @ApiModelProperty(value="制单人")
    private String project;

    /**
     * 收支项目
     */
    @ApiModelProperty(value="收支项目")
    private String pkSubjcode;

    /**
     * 组织本币无税金额
     */
    @ApiModelProperty(value="组织本币无税金额")
    private String localNotaxDe;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String localTaxDe;

    /**
     * 组织本币金额
     */
    @ApiModelProperty(value="组织本币金额")
    private String localMoneyDe;

    /**
     * 发票号
     */
    @ApiModelProperty(value="发票号")
    private String invoiceno;

    /**
     * 摘要
     */
    @ApiModelProperty(value="摘要")
    private String scomment;
}

package com.cloud.ficonsumer.enums;

/**
 * 审批状态枚举  取字典pm_process_approve_status
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
public enum ReturnStatusEnum {

    //1	退回业务系统	退回之后，第三方系统原单更新
    //2	已挂账	发票校验申请单完成，返回凭证编号
    //3	已付款	资金支付完成状态+支付金额+收款方账号ID
    //4	已记账	支付完成生成付款凭证，返回凭证编号
    //5	已冲销
    //6	已作废	仅推出的付款申请单作废，返回前端业务单据号+付款单据号
    //作废之后，再次集成前端系统生成新单据编号
    //7	已开票	收入确认单完成返回发票+发票信息+挂账凭证编号
    //8	已开票已收款	收款金额+实收凭证编号
    //9	退款退回	资金退款退回状态+支付金额+收款方账号ID
        //前端业务系统接收到该状态后处理方式：
        //1、重新传入新的付款申请单，重新提报预算和进行支付；
        //2、基于原付款申请单修改收款账户或预计付款时间信息后重新传入，重新传入时需在原付款申请单信息基础上增加原单YWID和付款修改标识，并携带退款退回状态返回的支付计划号传入；以修改单传入的可基于原单已提报预算进行支付，无需再次提报预算。
    //10	支付退回	资金支付退回状态
    //11	预算退回	预算退回状态
    //12	单据集成成功	财务平台生成对应的单据ywid
    //13	单据集成失败	财务平台未生成单据
    Enum1("1", "退回业务系统"),
    Enum2("2", "已挂账"),
    Enum3("3", "已付款"),
    Enum4("4", "已记账"),
    Enum5("5", "已冲销"),
    Enum6("6", "已作废"),
    Enum7("7", "已开票"),
    Enum8("8", "已开票已收款"),
    Enum9("9", "退款退回"),
    Enum10("10", "支付退回"),
    Enum11("11", "预算退回"),
    Enum12("12", "单据集成成功"),
    Enum13("13", "单据集成失败"),
    Enum14("", ""),
    ;
    private String code;
    private String name;

    ReturnStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code获取枚举类
     *
     * @param code
     * @return
     */
    public static ReturnStatusEnum getByCode(String code) {
        if(null == code) {
            return ReturnStatusEnum.Enum14;
        }
        for (ReturnStatusEnum en : values()) {
            if (code.equals(en.getCode())) {
                return en;
            }
        }
        return null;
    }

}

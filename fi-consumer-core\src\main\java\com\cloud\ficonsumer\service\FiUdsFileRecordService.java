package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.entity.FiUdsFileRecord;
import com.sgcc.uds.cloud.sdk.action.result.CommonActionResult;

public interface FiUdsFileRecordService extends IService<FiUdsFileRecord> {

    /**
     * 检查文件是否已上传到UDS
     *
     * @param bucketName OSS存储桶名称
     * @param fileName   文件名称
     * @return UDS文件记录，如果不存在返回null
     */
    FiUdsFileRecord checkFileUploaded(String bucketName, String fileName);

    /**
     * 记录文件上传到UDS的结果
     *
     * @param bucketName OSS存储桶名称
     * @param objectName OSS对象名称
     * @param fileName 文件名称
     * @param udsResult UDS上传结果
     * @param isSuccess 是否上传成功
     * @param existingRecord 已存在的记录（如果有）
     * @param pushMsg 推送失败信息（成功时传null）
     */
    void updateOrSaveUploadRecord(String bucketName, String objectName, String fileName,
                                 CommonActionResult udsResult, boolean isSuccess,
                                 FiUdsFileRecord existingRecord, String pushMsg);
}

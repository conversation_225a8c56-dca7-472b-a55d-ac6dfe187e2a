package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.PlatformLogService;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class PlatformLogServiceImpl implements PlatformLogService {

    private final DmMapper dmMapper;

    @Override
    public void recordLog(String pkBill, String pkBillType, String code, String msg) {
        List<PlatFormLogVO> existingLogs = dmMapper.selectPlatFormLog(pkBill, pkBillType);

        if (CollectionUtil.isNotEmpty(existingLogs)) {
            // 更新已存在的记录
            PlatFormLogVO updateLog = existingLogs.get(0);
            updateLog.setCode(code);
            updateLog.setMsg(msg);
            dmMapper.updatePlatFormLog(updateLog);
        } else {
            // 新增记录
            PlatFormLogVO platFormLogVO = new PlatFormLogVO();
            platFormLogVO.setPkLog(UUID.randomUUID().toString());
            platFormLogVO.setPkBill(pkBill);
            platFormLogVO.setPkBillType(pkBillType);
            platFormLogVO.setCode(code);
            platFormLogVO.setMsg(msg);
            dmMapper.insertPlatFormLog(platFormLogVO);
        }
    }
}
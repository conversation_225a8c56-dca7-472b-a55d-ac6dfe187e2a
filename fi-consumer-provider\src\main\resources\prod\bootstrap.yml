server:
  port: 10001

spring:
  profiles:
    active: dev
  shardingsphere:
    enabled: false
#默认关闭seata
seata:
  enabled: false
#  registry:
#    type: nacos
#    nacos:
#      server-addr: *************:18848
#      namespace: f5415cdb-1ebe-405d-aef0-50c7be56f4b4
#      group: DEFAULT_GROUP
#      application: seata-server
#  config:
#    type: nacos
#    nacos:
#      server-addr: *************:18848
#      namespace: f5415cdb-1ebe-405d-aef0-50c7be56f4b4
#      group: SEATA_GROUP
    #mqtt:
    #  server:
    #    enabled: true               # 是否开启服务端，默认：true
  #    ip: 0.0.0.0                 # 服务端 ip 默认为空，0.0.0.0，建议不要设置
#    port: 1883                  # 端口，默认：1883
#    name: Cloud-Mqtt-Server      # 名称，默认：Cloud-Mqtt-Server
#    buffer-allocator: HEAP      # 堆内存和堆外内存，默认：堆内存
#    heartbeat-timeout: 120000   # 心跳超时，单位毫秒，默认: 1000 * 120
#    read-buffer-size: 8092      # 接收数据的 buffer size，默认：8092
#    max-bytes-in-message: 8092  # 消息解析最大 bytes 长度，默认：8092
#    debug: true                 # 如果开启 prometheus 指标收集，建议关闭
#    web-port: 8083              # http、websocket 端口，默认：8083
#    websocket-enable: true      # 是否开启 websocket，默认： true
#    http-enable: false          # 是否开启 http api，默认： false
#    http-basic-auth:
#      enable: false             # 是否开启 http basic auth，默认： false
#      username: "cloud"          # http basic auth 用户名
#      password: "cloud"          # http basic auth 密码
enc:
  enable-sensitive: true
#  enable-encrypt-all: true
---

spring:
  application:
    name: @artifactId@-dev
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS}
        namespace: ${NACOS_NAMESPACE}
        metadata:
          group: cgroup
          version: 3.8.2-dev
        group: ${NACOS_REG_GROUP:DEFAULT_GROUP}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        shared-configs: application-prod.${spring.cloud.nacos.config.file-extension}
        namespace: ${NACOS_NAMESPACE}
        group: ${NACOS_CFG_GROUP:DEFAULT_GROUP}
        context-path: ${NACOS_CONTEXTPATH:}
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
  config:
    activate:
      on-profile: dev
---
#nacos:
#  server-addr: ${NACOS}
spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS}
        namespace: ${NACOS_NAMESPACE}
        metadata:
          group: cgroup
          version: 3.8.2
        group: ${NACOS_REG_GROUP:DEFAULT_GROUP}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        shared-configs: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        namespace: ${NACOS_NAMESPACE}
        group: ${NACOS_CFG_GROUP:DEFAULT_GROUP}
        context-path: ${NACOS_CONTEXTPATH:}
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
  config:
    activate:
      on-profile: prod
cloud:
  #  xsequence:
  #    uid:
  #      enabled: true
  log:
    request:
      enabled: false
      level: none




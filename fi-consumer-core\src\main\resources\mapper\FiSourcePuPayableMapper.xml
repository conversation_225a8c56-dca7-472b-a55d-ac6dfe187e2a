<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloud.ficonsumer.mapper.FiSourcePuPayableMapper">

    <select id="beforeConvertDataPage" resultType="com.cloud.ficonsumer.vo.FiSourcePuPayableVO">
        SELECT t.*
        FROM fi_source_pu_payable t
        WHERE t.del_flag = 0
        <if test="param.pkPayablebill != null and param.pkPayablebill != ''">
            AND t.pk_payablebill LIKE CONCAT('%', #{param.pkPayablebill}, '%')
        </if>
        <if test="param.billno != null and param.billno != ''">
            AND t.billno LIKE CONCAT('%', #{param.billno}, '%')
        </if>
        <if test="param.pkOrgList != null and param.pkOrgList.size() > 0">
            AND t.pk_org in
            <foreach collection="param.pkOrgList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.groupCodeFlag != null and param.groupCodeFlag != ''">
            AND t.pk_org in (select pk_org from fi_org_compare where group_code = #{param.groupCodeFlag} and del_flag = 0)
        </if>
        <if test="param.pushStatus != null and param.pushStatus != ''">
            AND t.push_status = #{param.pushStatus}
        </if>
        <if test="param.returnStatus != null and param.returnStatus != ''">
            AND t.return_status = #{param.returnStatus}
        </if>
        <if test="param.pushStatusList != null and param.pushStatusList.size() > 0">
            AND t.push_status in
            <foreach collection="param.pushStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.pkTradetype != null and param.pkTradetype != ''">
            AND t.pk_tradetype = #{param.pkTradetype}
        </if>
        <if test="param.createTimeStart != null and param.createTimeStart != ''">
            and t.create_time >= #{param.createTimeStart}
        </if>
        <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
            and #{param.createTimeEnd >= t.create_time
        </if>
        ORDER BY t.create_time DESC
    </select>

    <select id="getBeforeConvertDataPage" resultType="com.cloud.ficonsumer.vo.FiSourcePuPayableDetailVO">
        SELECT t.*
        FROM fi_source_pu_payable_detail t
        WHERE t.del_flag = 0
          and t.pk_payablebill = #{param.pkPayablebill}
    </select>

    <select id="getConvertData" resultType="com.cloud.ficonsumer.vo.FiConvertPuPayableDetailVO">
        SELECT t.*
        FROM fi_convert_pu_payable t
        WHERE t.del_flag = 0
          AND t.pk_payablebill = #{pkPayablebill}
    </select>

    <!-- 根据来源单据ID查询不为已挂账的应付单明细 -->
    <select id="getNoPostedBySrcBillIds" resultType="com.cloud.ficonsumer.vo.FiSourcePuPayableDetailVO">
        SELECT
        d.src_billid as srcBillid,
        d.pk_payablebill as pkPayablebill,
        h.billno
        FROM fi_source_pu_payable_detail d
        LEFT JOIN fi_source_pu_payable h ON d.pk_payablebill = h.pk_payablebill
        WHERE d.del_flag = 0
        AND h.del_flag = 0
        AND (h.return_status != 2 or h.return_status is null)
        <if test="srcBillIds != null and srcBillIds.size() > 0">
            AND d.src_billid IN
            <foreach collection="srcBillIds" item="billId" open="(" separator="," close=")">
                #{billId}
            </foreach>
        </if>
    </select>
</mapper>

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/25.
 */
@Data
public class InvoiceFileVO {

    /**
     * 来源id
     */
    @ApiModelProperty(value="来源id")
    private String sourceId;

    /**
     * 发票类型
     */
    @ApiModelProperty(value="发票类型")
    private String invoType;

    /**
     * 发票代码
     */
    @ApiModelProperty(value="发票代码")
    private String invoCode;

    /**
     * 开票日期
     */
    @ApiModelProperty(value="开票日期")
    private String invDate;

    /**
     * 发票号码
     */
    @ApiModelProperty(value="发票号码")
    private String invoiceNo;
}

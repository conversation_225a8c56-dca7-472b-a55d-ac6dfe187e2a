package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertProjEstiPayableDetail;
import com.cloud.ficonsumer.mapper.FiConvertProjEstiPayableDetailMapper;
import com.cloud.ficonsumer.service.FiConvertProjEstiPayableDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiConvertProjEstiPayableDetailServiceImpl extends ServiceImpl<FiConvertProjEstiPayableDetailMapper, FiConvertProjEstiPayableDetail> implements FiConvertProjEstiPayableDetailService {

}

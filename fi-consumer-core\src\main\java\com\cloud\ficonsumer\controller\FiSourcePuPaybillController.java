package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillDetailQueryDTO;
import com.cloud.ficonsumer.entity.FiSourcePuPaybill;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourcePuPaybillService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiConvertPuPaybillVO;
import com.cloud.ficonsumer.vo.FiConvertPuPaybillDetailVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourcePuPaybillVO;
import com.cloud.ficonsumer.vo.FiSourcePuPaybillDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 采购付款源数据主表
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/puPaybill")
@Api(value = "puPaybill", tags = "采购付款源数据主表管理")
public class FiSourcePuPaybillController {

    private final FiSourcePuPaybillService fiSourcePuPaybillService;

    /**
     * 报错重推
     */
    @ApiOperation(value = "报错重推", notes = "报错重推")
    @GetMapping("/pushAgain/{pkSettlement}")
    public R<Boolean> pushAgain(@PathVariable("pkSettlement") String pkSettlement) {
        return R.ok(fiSourcePuPaybillService.push(pkSettlement));
    }

    /**
     * 转换前分页查询
     */
    @ApiOperation(value = "转换前分页查询", notes = "转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiSourcePuPaybillVO>> beforeConvertDataPage(Page<FiSourcePuPaybillVO> page, FiSourcePuPaybillQueryDTO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        return R.ok(fiSourcePuPaybillService.beforeConvertDataPage(page, queryVO));
    }

    /**
     * 转换前数据详情查询
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage")
    public R<Page<FiSourcePuPaybillDetailVO>> getBeforeConvertDataPage(Page<FiSourcePuPaybillDetailVO> page, FiSourcePuPaybillDetailQueryDTO queryDTO) {
        return R.ok(fiSourcePuPaybillService.getBeforeConvertDataPage(page, queryDTO));
    }

    /**
     * 主体表转换后单条字段
     */
    @ApiOperation(value = "主体表转换后单条字段", notes = "主体表转换后单条字段")
    @GetMapping("/getConvertList")
    public R<List<FiConvertPuPaybillVO>> getConvertList(String pkSettlement) {
        return R.ok(fiSourcePuPaybillService.getConvertList(pkSettlement));
    }

    /**
     * 获取转换后详情分页数据
     */
    @ApiOperation(value = "获取转换后详情分页数据", notes = "获取转换后详情分页数据")
    @GetMapping("/getConvertData")
    public R<Page<FiConvertPuPaybillDetailVO>> getConvertData(Page<FiConvertPuPaybillDetailVO> page, String pkSettlement) {
        return R.ok(fiSourcePuPaybillService.getConvertData(page, pkSettlement));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result = fiSourcePuPaybillService.update(Wrappers.<FiSourcePuPaybill>lambdaUpdate()
                .set(FiSourcePuPaybill::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiSourcePuPaybill::getPushMsg, "成功")
                .in(FiSourcePuPaybill::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiSourcePuPaybillService.pushAgainBatch(queryVO));
    }
}

package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.common.FiOrgConvert;

import java.util.List;
import java.util.Set;

public interface FiOrgConvertService extends IService<FiOrgConvert> {

    /**
     * 同步ERP中需要同步智慧平台组织
     */
    void syncPushEnableOrg();

    /**
     * 根据传入的组织ID集合，筛选出需要同步的组织ID集合
     *
     * @param orgIds 待检查的组织ID集合
     * @return 需要同步的组织ID集合（value="Y"的组织）
     */
    Set<Long> listEnablePushOrgId(Set<Long> orgIds);

    /**
     * 组织是否需要同步智慧平台
     *
     * @param orgId 业+组织ID
     * @return 是否需要同步智慧平台
     */
    Boolean orgEnablePush(Long orgId);

    /**
     * 根据orgId获取mdmOrgCode
     *
     * @param orgId 业+组织ID
     * @return mdmOrgCode
     */
    String getMdmOrgCodeByOrgId(Long orgId);

    /**
     * 批量保存智慧平台组织转换
     *
     * @param orgList 智慧平台组织转换列表
     */
    void saveOrgConvertBatch(List<FiOrgConvert> orgList);
}
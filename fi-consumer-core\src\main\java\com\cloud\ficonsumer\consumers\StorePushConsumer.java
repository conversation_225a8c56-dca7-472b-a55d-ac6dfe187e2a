package com.cloud.ficonsumer.consumers;

import com.cloud.apiexchange.client.consumer.IMqConsumer;
import com.cloud.apiexchange.client.consumer.MqResult;
import com.cloud.apiexchange.client.consumer.annotation.MqConsumer;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.service.FiSourceStoreService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@MqConsumer(topic = Constants.TOPIC_STORE_TRANS,group = Constants.Group.group,transaction = false)
@Service
public class StorePushConsumer implements IMqConsumer {

    @Autowired
    private FiSourceStoreService fiSourceStoreService;


    /**
     *  采购入库进行转换推送
     * @param s  采购入库主键
     * @return
     * @throws Exception
     */
    @Override
    public MqResult consume(String s) throws Exception {
        LogUtil.info(log, " 采购入库进行转换推送开始:", s);
        try {
            fiSourceStoreService.pushAgain(s);
        } catch (Exception e) {
            LogUtil.info(log, " 采购入库进行转换推送失败:", e);
            return new MqResult(MqResult.FAIL_CODE, AdminUtils.truncateString(e.getMessage(),1000));
        }
        return new MqResult(MqResult.SUCCESS_CODE," 采购入库进行转换推送成功");
    }
}

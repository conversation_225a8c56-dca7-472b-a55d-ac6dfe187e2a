package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 销售订单VO
 */
@Data
@ApiModel(description = "销售订单视图对象")
public class YgSaleOrderVO {

    @NotBlank
    @Size(max = 4)
    @ApiModelProperty(value = "单位编码(MDM组织编码)", required = true, example = "0101")
    private String unitCode;

    @ApiModelProperty(value = "电商平台编码(财务平台枚举)", example = "1001")
    private Integer ecomPlatform;

    @Size(max = 8)
    @ApiModelProperty(value = "业务模式编码", example = "MODE001")
    private String busiMode;

    @NotNull
    @ApiModelProperty(value = "订单日期(YYYY-MM-DD)", required = true, example = "2023-01-01")
    private String orderTime;

    @NotBlank
    @Size(max = 36)
    @ApiModelProperty(value = "订单编号", required = true, example = "SO202301010001")
    private String orderId;

    @Size(max = 50)
    @ApiModelProperty(value = "商城订单编号", example = "MALL20230101001")
    private String mallOrderNo;

    @NotBlank
    @Size(max = 18)
    @ApiModelProperty(value = "客户ID(MDM客户编码)", required = true, example = "CUS20230101")
    private String customId;

    @ApiModelProperty(value = "销售员(ISC主账号)", example = "sales001")
    private String salesman;

    @Size(max = 10)
    @ApiModelProperty(value = "销售部门(MDM组织编码)", example = "SALE001")
    private String saleDept;

    @Size(max = 50)
    @ApiModelProperty(value = "合同ID(经法合同编码)", example = "CONTRACT20230101")
    private String contractId;

    @DecimalMax(value = "1.0000")
    @DecimalMin(value = "0.0000")
    @ApiModelProperty(value = "超额执行比例", example = "0.1")
    private BigDecimal excessExeRatio;

    @NotBlank
    @Size(max = 8)
    @ApiModelProperty(value = "订单交易状态", required = true,
            allowableValues = "00000001,00000002,00000003,00000004,00000005,00000006,00000007,00000008,00000009",
            example = "00000001")
    private String orderStatus;

    @Size(max = 8)
    @ApiModelProperty(value = "是否废旧物资销售", example = "00000000")
    private String isWasteMatSale;

    @Size(max = 10)
    @ApiModelProperty(value = "利润中心(MDM组织编码)", example = "PROF001")
    private String profCen;

    @Size(max = 10)
    @ApiModelProperty(value = "成本中心(MDM组织编码)", example = "COST001")
    private String costCenter;

    @Size(max = 40)
    @ApiModelProperty(value = "产品条线(MDM业务分类编码)", example = "LINE001")
    private String prdLine;

    @Size(max = 40)
    @ApiModelProperty(value = "产品服务(MDM业务分类编码)", example = "SER001")
    private String prodAndSer;

    @Size(max = 200)
    @ApiModelProperty(value = "备注")
    private String remark;

    @NotBlank
    @Size(max = 8)
    @ApiModelProperty(value = "单据状态", required = true,
            allowableValues = "00000001,00000002", example = "00000002")
    private String docuStatus;

    @NotNull
    @Digits(integer = 1, fraction = 0)
    @ApiModelProperty(value = "业务类型(0-新增 1-修改 2-冲销)", required = true, example = "0")
    private Integer biType;

    @Valid
    @NotEmpty
    @ApiModelProperty(value = "物料明细列表", required = true)
    private List<MaterialItemVO> materList;

    /**
     * 物料明细VO
     */
    @Data
    @ApiModel(description = "物料明细视图对象")
    public static class MaterialItemVO {

        @NotBlank
        @Size(max = 100)
        @ApiModelProperty(value = "外部订单行号", required = true, example = "D202301010001")
        private String extOrdDtlNo;

        @NotBlank
        @Size(max = 36)
        @ApiModelProperty(value = "物料编码(MDM3.0)", required = true, example = "MAT20230101")
        private String mt;

        @NotNull
        @Digits(integer = 20, fraction = 4)
        @ApiModelProperty(value = "数量", required = true, example = "10.0000")
        private BigDecimal quantity;

        @Digits(integer = 20, fraction = 4)
        @ApiModelProperty(value = "不含税单价", example = "100.5000")
        private BigDecimal unPrExcTax;

        @Digits(integer = 20, fraction = 2)
        @ApiModelProperty(value = "不含税金额", example = "1005.00")
        private BigDecimal amtExTax;

        @NotNull
        @Digits(integer = 3, fraction = 2)
        @ApiModelProperty(value = "税率", required = true, example = "0.13")
        private BigDecimal taxrt;

        @NotNull
        @Digits(integer = 20, fraction = 4)
        @ApiModelProperty(value = "含税单价", required = true, example = "113.5000")
        private BigDecimal taxIncPric;

        @NotNull
        @Digits(integer = 20, fraction = 2)
        @ApiModelProperty(value = "含税金额", required = true, example = "1135.00")
        private BigDecimal taxAmt;

        @Size(max = 50)
        @ApiModelProperty(value = "项目ID(储备库项目编码)", example = "PROJ20230101")
        private String projectId;

        @Size(max = 8)
        @ApiModelProperty(value = "是否需交货(50000001枚举)", example = "00000000")
        private String isNeedDlvy;

        @Size(max = 200)
        @ApiModelProperty(value = "备注")
        private String remark;
    }
}
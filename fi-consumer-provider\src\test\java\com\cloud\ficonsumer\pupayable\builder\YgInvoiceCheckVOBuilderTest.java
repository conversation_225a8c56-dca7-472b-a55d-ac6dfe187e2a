package com.cloud.ficonsumer.pupayable.builder;

import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.ficonsumer.builder.YgInvoiceCheckVOBuilder;
import com.cloud.ficonsumer.builder.context.YgInvoiceCheckContext;
import com.cloud.ficonsumer.entity.FiConvertPuPayable;
import com.cloud.ficonsumer.entity.FiConvertPuPayableDetail;
import com.cloud.ficonsumer.entity.IaI2billB;
import com.cloud.ficonsumer.entity.PoOrderB;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.service.FiSourceOrderService;
import com.cloud.ficonsumer.service.FiSourceStoreService;
import com.cloud.ficonsumer.service.YgInvoiceService;
import com.cloud.ficonsumer.vo.YgInvoiceCheckVO;
import com.cloud.ficonsumer.vo.YgInvoiceInfoVO;
import com.cloud.ficonsumer.vo.YgPaymentInfoVO;
import com.cloud.ficonsumer.vo.YgPurchaseOrderDetailVO;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class YgInvoiceCheckVOBuilderTest {

    private static MockedStatic<SpringContextHolder> springContextHolderMock;

    @BeforeAll
    static void setUpClass() {
        springContextHolderMock = mockStatic(SpringContextHolder.class);
    }

    @AfterAll
    static void tearDownClass() {
        springContextHolderMock.close();
    }

    @Mock
    private ApiCompareService apiCompareService;
    
    @Mock
    private YgInvoiceService ygInvoiceService;
    
    @Mock
    private DmMapper dmMapper;

    @Mock
    private FiSourceOrderService fiSourceOrderService;

    @Mock
    private FiSourceStoreService fiSourceStoreService;

    @InjectMocks
    private YgInvoiceCheckVOBuilder builder;

    private FiConvertPuPayable convert;
    private List<FiConvertPuPayableDetail> convertDetails;
    private YgInvoiceCheckContext context;

    @BeforeEach
    void setUp() {
        // 在创建context之前设置mock
        springContextHolderMock.when(() -> SpringContextHolder.getBean(ApiCompareService.class))
            .thenReturn(apiCompareService);
        springContextHolderMock.when(() -> SpringContextHolder.getBean(YgInvoiceService.class))
            .thenReturn(ygInvoiceService);
        springContextHolderMock.when(() -> SpringContextHolder.getBean(DmMapper.class))
            .thenReturn(dmMapper);
        springContextHolderMock.when(() -> SpringContextHolder.getBean(FiSourceOrderService.class))
            .thenReturn(fiSourceOrderService);
        springContextHolderMock.when(() -> SpringContextHolder.getBean(FiSourceStoreService.class))
            .thenReturn(fiSourceStoreService);

        // 准备测试数据
        convert = new FiConvertPuPayable();
        convert.setBillno("TEST001");
        convert.setPkOrg("ORG001");
        convert.setBillmaker("MAKER001");
        convert.setPkTradetype("1");  // 物资类型
        convert.setPkCurrtype("1");   // 人民币
        convert.setPkPayablebill("PAYABLE001");
        convert.setDef6("COST_TYPE_001");

        convertDetails = new ArrayList<>();
        FiConvertPuPayableDetail detail = new FiConvertPuPayableDetail();
        detail.setProject("PROJECT001");
        detail.setContractno("SG001");
        detail.setRate(BigDecimal.ONE);
        detail.setMoneyCr(new BigDecimal("1000.00"));
        detail.setSupplier("SUPPLIER001");
        convertDetails.add(detail);

        // 创建context
        context = new YgInvoiceCheckContext(convert, convertDetails);
    }

    @Test
    @DisplayName("测试build方法 - 参数为空")
    void testBuildWithNullParams() {
        assertThrows(CheckedException.class, () -> builder.build(null, null));
        assertThrows(CheckedException.class, () -> builder.build(convert, null));
        assertThrows(CheckedException.class, () -> builder.build(null, convertDetails));
    }

    @Test
    @DisplayName("测试buildBasicInfo")
    void testBuildBasicInfo() {
        // Mock服务调用
        when(apiCompareService.getTurnByType(anyString(), anyString()))
            .thenReturn("MOCK_CODE");

        // 准备context数据
        context.prepareData();

        YgInvoiceCheckVO vo = new YgInvoiceCheckVO();
        builder.buildBasicInfo(vo, context);

        // 验证结果
        assertEquals("ORG001", vo.getDwdh());
        assertEquals("MAKER001", vo.getHdlgNmId());
        assertEquals(3, vo.getBiType());
        assertEquals("ZJYJ", vo.getOuterCode());
        assertEquals("TEST001", vo.getDisCode());
        assertEquals("MOCK_CODE", vo.getEnginProje());
        assertEquals("SG001", vo.getConNam());
        assertEquals("1", vo.getOrderType());
        assertEquals("00000008", vo.getPayType());
        assertEquals(BigDecimal.ZERO , vo.getTotAmoPaid());
        assertEquals(LocalDate.now().plusDays(30).format(DateTimeFormatter.ofPattern("yyyyMMdd")), 
                    vo.getOrderPayDate());
        assertEquals("COST_TYPE_001", vo.getCosType());
        assertEquals("N", vo.getBackTag());
        assertEquals("1", vo.getTarnCurren());
        assertEquals(BigDecimal.ONE, vo.getExchRat());
    }

    @Test
    @DisplayName("测试buildInvoiceInfo")
    void  testBuildInvoiceInfo() {
        // 准备测试数据
        YgInvoiceCheckVO vo = new YgInvoiceCheckVO();
        List<YgInvoiceInfoVO> mockInvoiceList = Collections.singletonList(new YgInvoiceInfoVO());
        Map<String, List<YgInvoiceInfoVO>> mockInvoiceMap = new HashMap<>();
        mockInvoiceMap.put("TEST", mockInvoiceList);

        // Mock服务调用
        when(ygInvoiceService.batchProcessInvoiceInfo(anySet()))
            .thenReturn(mockInvoiceMap);

        // 准备context数据
        context.prepareData();

        // 执行测试
        builder.buildInvoiceInfo(vo, context);

        // 验证结果
        assertNotNull(vo.getSub1());
        assertEquals(1, vo.getSub1().size());
        verify(ygInvoiceService).batchProcessInvoiceInfo(anySet());
    }

    @Test
    @DisplayName("测试buildPaymentInfo")
    void testBuildPaymentInfo() {
        // Mock服务调用
        when(apiCompareService.getTurnByType(anyString(), anyString()))
            .thenReturn("MOCK_CODE");

        // 准备context数据
        context.prepareData();

        YgInvoiceCheckVO vo = new YgInvoiceCheckVO();
        builder.buildPaymentInfo(vo, context);

        // 验证结果
        assertNotNull(vo.getSub3());
        assertEquals(1, vo.getSub3().size());
        YgPaymentInfoVO paymentInfo = vo.getSub3().get(0);
        assertEquals("MOCK_CODE", paymentInfo.getSupp());
        assertEquals("MOCK_CODE", paymentInfo.getPrjName());
        assertEquals(new BigDecimal("1000.00"), paymentInfo.getCurwriOffAdvPmtTax());
    }

    @Test
    @DisplayName("测试buildPurchaseOrderInfo")
    void testBuildPurchaseOrderInfo() {
        // 准备测试数据
        YgInvoiceCheckVO vo = new YgInvoiceCheckVO();
        convertDetails.get(0).setSrcBilltype("21");
        convertDetails.get(0).setSrcItemid("ORDER_DETAIL_001");
        convertDetails.get(0).setSrcBillid("ORDER_001");

        // Mock采购订单明细
        PoOrderB poOrderB = new PoOrderB();
        poOrderB.setPkOrderB("ORDER_001");
        poOrderB.setVbillcode("PO001");
        poOrderB.setCrowno("1");
        poOrderB.setPkMaterial("MATERIAL001");

        // Mock入库单明细
        IaI2billB iaI2billB = new IaI2billB();
        iaI2billB.setCfirstid("ORDER_001");
        iaI2billB.setVbillcode("IN001");
        iaI2billB.setCrowno("1");
        iaI2billB.setNnum(new BigDecimal("10"));

        when(dmMapper.listPoOrderBByPkB(anyList()))
            .thenReturn(Collections.singletonList(poOrderB));
        when(dmMapper.listIaI2billBByBids(anyList()))
            .thenReturn(Collections.singletonList(iaI2billB));

        // 准备context数据
        context.prepareData();

        // 执行测试
        builder.buildPurchaseOrderInfo(vo, context);

        // 验证结果
        assertNotNull(vo.getSub6());
        assertEquals(1, vo.getSub6().size());
        YgPurchaseOrderDetailVO detail = vo.getSub6().get(0);
        assertEquals("PO001", detail.getPurOrdNum());
        assertEquals("1", detail.getLineItem());
        assertEquals("MATERIAL001", detail.getMtCode());
        assertEquals("IN001", detail.getMtDocNum());
        assertEquals("1", detail.getMtDocNumLine());
        assertEquals(new BigDecimal("10"), detail.getPurNum());
    }

    @Test
    @DisplayName("测试完整build流程")
    void testFullBuildProcess() {
        // 设置采购订单相关的数据
        convertDetails.get(0).setSrcBilltype("21");
        convertDetails.get(0).setSrcItemid("ORDER_DETAIL_001");
        convertDetails.get(0).setSrcBillid("ORDER_001");

        // Mock所有必要的依赖
        when(apiCompareService.getTurnByType(anyString(), anyString()))
            .thenReturn("MOCK_CODE");
            
        // Mock采购订单明细
        PoOrderB poOrderB = new PoOrderB();
        poOrderB.setPkOrderB("ORDER_001");
        poOrderB.setVbillcode("PO001");
        poOrderB.setCrowno("1");
        poOrderB.setPkMaterial("MATERIAL001");
        when(dmMapper.listPoOrderBByPkB(anyList()))
            .thenReturn(Collections.singletonList(poOrderB));
            
        // Mock入库单明细
        IaI2billB iaI2billB = new IaI2billB();
        iaI2billB.setCfirstid("ORDER_001");
        iaI2billB.setVbillcode("IN001");
        iaI2billB.setCrowno("1");
        iaI2billB.setNnum(new BigDecimal("10"));
        when(dmMapper.listIaI2billBByBids(anyList()))
            .thenReturn(Collections.singletonList(iaI2billB));
            
        when(ygInvoiceService.batchProcessInvoiceInfo(anySet()))
            .thenReturn(new HashMap<>());

        // 执行测试
        YgInvoiceCheckVO result = builder.build(convert, convertDetails);

        // 验证基本信息
        assertNotNull(result);
        assertEquals("TEST001", result.getDisCode());
        assertEquals("MOCK_CODE", result.getEnginProje());
        
        // 验证各个子对象
        assertNotNull(result.getSub1());  // 发票信息
        assertNotNull(result.getSub3());  // 付款信息
        assertNotNull(result.getSub6());  // 采购订单明细
        assertNotNull(result.getSub7());  // 影像信息
    }
}
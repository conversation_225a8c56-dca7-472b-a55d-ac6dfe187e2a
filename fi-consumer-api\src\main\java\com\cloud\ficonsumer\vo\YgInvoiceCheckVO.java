package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "发票校验申请服务")
public class YgInvoiceCheckVO {

    @ApiModelProperty(value = "应用ID，默认值19773")
    private String bizappid;

    @ApiModelProperty(value = "发起单位", required = true, example = "1234")
    private String dwdh;
    
    @ApiModelProperty(value = "经办人", required = true, example = "USER123")
    private String hdlgNmId;
    
    @ApiModelProperty(value = "部门级成本中心", example = "COST123")
    private String deptCostCenter;
    
    @ApiModelProperty(value = "业务类型(3-发票校验)", required = true, example = "3")
    private Integer biType;
    
    @ApiModelProperty(value = "订单类型(1-物资,2-服务,3-寄售)", required = true, example = "1")
    private String orderType;
    
    @ApiModelProperty(value = "来源系统(QKL-浙江区块链,SAP-SAP系统)", required = true, example = "SAP")
    private String outerCode;
    
    @ApiModelProperty(value = "前端系统单据编号", required = true)
    private String disCode;
    
    @ApiModelProperty(value = "项目编码")
    private String enginProje;
    
    @ApiModelProperty(value = "合同编码")
    private String conNam;
    
    @ApiModelProperty(value = "采购订单", required = true)
    private String purOrdNo;
    
    @ApiModelProperty(value = "付款金额", required = true, example = "2000.00")
    private BigDecimal totAmoPaid;
    
    @ApiModelProperty(value = "预约付款时间(YYYYMMDD)", required = true, example = "20230531")
    private String orderPayDate;
    
    @ApiModelProperty(value = "最晚付款日期(YYYYMMDD)", example = "20240723")
    private String dateExpiry;
    
    @ApiModelProperty(value = "付款性质", required = true, example = "00000001")
    private String payType;
    
    @ApiModelProperty(value = "费用类型")
    private String cosType;

    @ApiModelProperty(value = "明细费用类型")
    private String detCosType;

    @ApiModelProperty(value = "明细费用类型编码")
    private String detCosTypeCode;

    @ApiModelProperty(value = "研发类型(00000001-开发阶段,00000002-研究阶段)")
    private String devType;
    
    @ApiModelProperty(value = "资金属性")
    private String moneyAttr;
    
    @ApiModelProperty(value = "批量流水号")
    private String pllsh;
    
    @ApiModelProperty(value = "无纸化单据标识(00000000-否,00000001-是)")
    private String docuStatus;
    
    @ApiModelProperty(value = "附件张数", example = "2")
    private Integer fdzs;
    
    @ApiModelProperty(value = "验审信息", example = "20231123201134张三")
    private String verInfo;
    
    @ApiModelProperty(value = "是否退回重传标识", required = true, example = "N")
    private String backTag;
    
    @ApiModelProperty(value = "是否自动传递(0-不自动传递,1-自动传递)", example = "0")
    private String autoPass;

    @ApiModelProperty(value = "交易币种(默认1-人民币)", example = "1")
    private String tarnCurren;

    @ApiModelProperty(value = "交易币种对应本位币汇率", example = "1.0000")
    private BigDecimal exchRat;
    
    @ApiModelProperty(value = "发票信息列表")
    private List<YgInvoiceInfoVO> sub1;
    
    @ApiModelProperty(value = "WBS信息列表")
    private List<YgWbsInfoVO> sub2;
    
    @ApiModelProperty(value = "付款信息列表")
    private List<YgPaymentInfoVO> sub3;
    
    @ApiModelProperty(value = "预付款冲销信息列表")
    private List<YgPrepaymentInfoVO> sub4;
    
    @ApiModelProperty(value = "结算信息列表")
    private List<YgSettlementInfoVO> sub5;
    
    @ApiModelProperty(value = "采购订单入库明细信息列表")
    private List<YgPurchaseOrderDetailVO> sub6;
    
    @ApiModelProperty(value = "原始凭据列表")
    private List<ImageDetail> sub7;
    
    @ApiModelProperty(value = "审批轨迹列表")
    private List<ApprvDetails> apprv;

    @ApiModelProperty(value = "部门分类")
    private String mainDeptType;
}
# YgInvoiceCheckContext 重构说明

## 概述

本次重构将 `YgInvoiceCheckContext` 从构造函数模式改为Builder模式，并添加了应付单类型枚举，支持逐步构建Context，特别适合需要获取上游订单数据的场景。

## 主要变更

### 1. 新增应付单类型枚举

**文件**: `PayableType.java`

```java
public enum PayableType {
    PURCHASE("F1-Cxx-02", "采购应付"),
    PROJECT("F1-Cxx-01", "项目应付");
}
```

### 2. Context类重构

**主要变更**:
- 添加了 `PayableType payableType` 字段
- 改为私有构造函数 + Builder模式
- 添加了便利方法：`isPurchasePayable()`, `isProjectPayable()`, `getPayableTypeName()`
- 将数据准备方法拆分为更细粒度的方法

### 3. Builder模式支持

**基础构建**:
```java
YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
    .withConvertData(convert, convertDetails)
    .build();
```

**完整构建**:
```java
YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
    .withConvertData(convert, convertDetails)
    .withBasicInfo()           // 基础信息（组织、供应商、项目等）
    .withCosTypeInfo()         // 费用类型信息
    .withInvoiceInfo()         // 发票信息
    .withAccountInfo()         // 账户信息
    .withTopBill()             // 上游单据信息
    .withSourceBill()          // 源头单据信息
    .withImageInfo()           // 影像信息
    .withValidation()          // 业务校验
    .build();
```

## 单据类型说明

### 采购应付 (PURCHASE)
- **上游单据**: 采购订单
- **源头单据**: 采购订单（与上游相同）
- **补充信息**: 入库单

### 项目应付 (PROJECT)
- **上游单据**: 收票登记
- **源头单据**: 付款合同或费用结算单

## Builder方法说明

| 方法 | 说明 | 适用场景 |
|------|------|----------|
| `withConvertData()` | 设置转换数据（必需） | 所有场景 |
| `withSourceData()` | 设置源数据（可选） | 需要源数据的场景 |
| `withBasicInfo()` | 准备基础信息 | 需要组织、供应商、项目信息 |
| `withCosTypeInfo()` | 准备费用类型信息 | 需要费用类型数据 |
| `withInvoiceInfo()` | 准备发票信息 | 需要发票数据 |
| `withAccountInfo()` | 准备账户信息 | 需要银行账户数据 |
| `withTopBill()` | 准备上游单据信息 | 需要上游单据数据 |
| `withSourceBill()` | 准备源头单据信息 | 需要源头单据数据 |
| `withImageInfo()` | 准备影像信息 | 需要附件数据 |
| `withValidation()` | 执行业务校验 | 需要校验业务规则 |

## 使用示例

### 示例1：最小化构建（仅获取上游订单数据）
```java
YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
    .withConvertData(convert, convertDetails)
    .withBasicInfo()    // 基础信息
    .withTopBill()      // 上游单据信息
    .build();

// 获取数据
if (context.isPurchasePayable()) {
    Map<String, PoOrderB> orders = context.getPoOrderBMap();
} else if (context.isProjectPayable()) {
    Map<String, PmContr> contracts = context.getPmContrMap();
}
```

### 示例2：完整构建（用于发票校验）
```java
YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
    .withConvertData(convert, convertDetails)
    .withBasicInfo()
    .withCosTypeInfo()
    .withInvoiceInfo()
    .withAccountInfo()
    .withTopBill()
    .withSourceBill()
    .withImageInfo()
    .withValidation()
    .build();
```

## 向后兼容性

为了保持向后兼容，保留了以下已废弃的方法：
- `prepareSrcPoInfo()` → 使用 `preparePurchaseOrderInfo()` + `prepareStoreInfo()`
- `prepareSrcPmInfo()` → 使用 `prepareProjectSourceInfo()`
- `updateSourcePuPayableDetail()` → 使用具体的更新方法

## 优势

1. **类型安全**: 使用枚举替代硬编码字符串
2. **逐步构建**: 支持按需构建，提高性能
3. **代码可读性**: 方法名更清晰，易于理解
4. **扩展性**: 便于添加新的应付单类型
5. **灵活性**: 可以根据不同场景选择性构建数据

## 注意事项

1. `withTopBill()` 和 `withSourceBill()` 的行为取决于应付单类型
2. 对于采购应付，`withSourceBill()` 主要准备入库单信息
3. 对于项目应付，需要实现收票登记信息的准备逻辑（目前为TODO）
4. 建议在service层使用新的Builder模式，逐步替换旧的构造函数调用

package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.lang.UUID;
import com.alibaba.excel.EasyExcel;
import com.cloud.ficonsumer.listener.ImportApiCompareListener;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.service.ImportService;
import com.cloud.ficonsumer.utils.ExcelUtils;
import com.cloud.ficonsumer.vo.ExcelImportStatusVO;
import com.cloud.ficonsumer.vo.ImportApiCompareVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.time.Duration;
import java.util.Map;

/**
 * @Author: zheng<PERSON>jin
 * @Descrition: 导入excel文件 service
 * @Date: 2020/11/11
 */
@Service
@Slf4j
@AllArgsConstructor
public class ImportServiceImpl implements ImportService {

    private final RedisTemplate redisTemplate;

    private final ApiCompareService apiCompareService;


    @Async
    @Transactional
    @Override
    public String apiCompareImport(InputStream is, UUID id, Map<String, String> collect) {
        log.info("start import excel apiCompareImport...");
        int headNum = 1;
        String redisKey = ExcelUtils.EXCEL_IMPORT + id;
        ExcelImportStatusVO excelStatusVO = new ExcelImportStatusVO();
        ValueOperations valueOperations = redisTemplate.opsForValue();
        valueOperations.set(redisKey, excelStatusVO, Duration.ofDays(1));
        ImportApiCompareListener importListener = new ImportApiCompareListener(redisKey, excelStatusVO, redisTemplate,apiCompareService,collect);
        EasyExcel.read(is, ImportApiCompareVO.class, importListener).sheet().headRowNumber(headNum).doRead();
        log.info("end import excel apiCompareImport...");
        return id.toString();
    }

}

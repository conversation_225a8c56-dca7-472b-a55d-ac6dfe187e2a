package com.cloud.ficonsumer.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class FidocheaderVO {

    /**
     * SAP凭证ID
     */
    @ApiModelProperty(value="SAP凭证ID")
    private String sapdocid;


    /**
     * 凭证类型
     */
    @ApiModelProperty(value="凭证类型")
    private String documenttype;

    /**
     * 公司代码
     */
    @ApiModelProperty(value="公司代码")
    private String company;


    /**
     * 凭证摘要
     */
    @ApiModelProperty(value="凭证摘要")
    private String summary;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value="凭证日期")
    private String documentdate;

    /**
     * 过账日期
     */
    @ApiModelProperty(value="过账日期")
    private String postingdate;

    /**
     * 过账期间
     */
    @ApiModelProperty(value="过账期间")
    private String perioddate;

    /**
     * 业务经办人
     */
    @ApiModelProperty(value="业务经办人")
    private String docapplyer;

    /**
     * 财务制证人（与sap用户一致）
     */
    @ApiModelProperty(value="财务制证人（与sap用户一致）")
    private String creator;

    /**
     * sap用户姓名名称
     */
    @ApiModelProperty(value="sap用户姓名名称")
    private String username;

    /**
     * 审核人
     */
    @ApiModelProperty(value="审核人")
    private String checker;

    /**
     * 财务主管
     */
    @ApiModelProperty(value="财务主管")
    private String financer;

    /**
     * 出纳
     */
    @ApiModelProperty(value="出纳")
    private String cashier;

    /**
     * 货币类型
     */
    @ApiModelProperty(value="货币类型")
    private String currency;

    /**
     * 附件张数
     */
    @ApiModelProperty(value="附件张数")
    private Integer reference;

    /**
     * 会计年度
     */
    @ApiModelProperty(value="会计年度")
    private Integer zyear;

//
//    /**
//     * 凭证货币
//     */
//    @ApiModelProperty(value="凭证货币")
//    private String forcur;

    /**
     * 换算汇率
     */
    @ApiModelProperty(value="换算汇率")
    private String rate;

    /**
     * 调整期凭证标识
     */
    @ApiModelProperty(value="调整期凭证标识")
    private String ranged;

    /**
     * 被冲销凭证号
     */
    @ApiModelProperty(value="被冲销凭证号")
    private String negdocument;

    /**
     * 被冲销年份
     */
    @ApiModelProperty(value="被冲销年份")
    private String negyear;

    /**
     * 利润中心
     */
    @ApiModelProperty(value="利润中心")
    private String profcenter;

    /**
     * 单据编号
     */
    @ApiModelProperty(value="单据编号")
    private String ywids;

    /**
     * 协同业务类型
     */
    @ApiModelProperty(value="协同业务类型")
    private String zxtype;

    /**
     * 冲销标志
     */
    @ApiModelProperty(value="冲销标志")
    private String xreversal;

    /**
     * 经办人编号
     */
    @ApiModelProperty(value="经办人编号")
    private String managerid;

    /**
     * 应付凭证单据编号
     */
    @ApiModelProperty(value="应付凭证单据编号")
    private String accpayvoudocno;


    /**
     * 抵消标志
     */
    @ApiModelProperty(value="抵消标志")
    private String xtsign;

}

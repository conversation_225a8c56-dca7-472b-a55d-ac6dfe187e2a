
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertAccountingDocfree;
import com.cloud.ficonsumer.mapper.FiConvertAccountingDocfreeMapper;
import com.cloud.ficonsumer.service.FiConvertAccountingDocfreeService;
import org.springframework.stereotype.Service;

/**
 * 采购入库详情数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:46
 */
@Service
public class FiConvertAccountingDocfreeServiceImpl extends ServiceImpl<FiConvertAccountingDocfreeMapper, FiConvertAccountingDocfree> implements FiConvertAccountingDocfreeService {

}

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@TableName("fi_convert_pu_paybill")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购付款转换数据详情")
public class FiConvertPuPaybill extends BaseEntity<FiConvertPuPaybill> {
    private static final long serialVersionUID = -1L;

    @TableId
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty("结算单主键")
    private String pkSettlement;

    @ApiModelProperty("财务组织")
    private String pkOrg;

    @ApiModelProperty(value = "应付财务组织编码", required = true)
    private String pkOrgCode;

    @ApiModelProperty(value = "应付财务组织名称", required = true)
    private String pkOrgName;

    @ApiModelProperty("业务单据编号")
    private String billcode;

    @ApiModelProperty("订单编号（采购使用）")
    private String vbillcode;

    @ApiModelProperty("合同号（项目付款使用）")
    private String cbillcode;

    @ApiModelProperty("业务单据录入人")
    private String pkBilloperator;

    @ApiModelProperty("原币金额")
    private BigDecimal primal;

    @ApiModelProperty("费用类型")
    private String expenseType;

    @ApiModelProperty("应付类型编码")
    private String pkTradetype;

    @ApiModelProperty(value = "是否退回重传标识，Y-是，N-否；标识默认为N，回退成功后，更新为Y；单据集成成功后，重置为N")
    private String backTag;

    @ApiModelProperty(value = "付款性质")
    private String def47;

    @ApiModelProperty(value = "部门分类")
    private String def52;
}

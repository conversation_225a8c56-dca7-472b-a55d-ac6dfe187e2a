
package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiConvertCostOrderDTO;
import com.cloud.ficonsumer.dto.FiConvertCostOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceCostOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceCostOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiConvertCostOrder;
import com.cloud.ficonsumer.entity.FiConvertCostOrderDetail;
import com.cloud.ficonsumer.entity.FiSourceAccounting;
import com.cloud.ficonsumer.entity.FiSourceCostOrder;
import com.cloud.ficonsumer.entity.FiSourceCostOrderDetail;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceCostOrderMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;


/**
 * 费用结算单采购订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:00
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiSourceCostOrderServiceImpl extends ServiceImpl<FiSourceCostOrderMapper, FiSourceCostOrder> implements FiSourceCostOrderService {

    private final FiSourceCostOrderDetailService fiSourceCostOrderDetailService;
    private final FiConvertCostOrderService fiConvertCostOrderService;
    private final FiConvertCostOrderDetailService fiConvertCostOrderDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;
    private final YgConfig ygConfig;

    /**
     * 费用结算单采购订单重新推送
     * @param
     */
    @Override
    public boolean pushAgain(String pk) {
        FiSourceCostOrder sourceMainData = this.getOne(Wrappers.<FiSourceCostOrder>lambdaQuery().eq(FiSourceCostOrder::getPkFeebalance, pk));
        List<FiSourceCostOrderDetail> details = fiSourceCostOrderDetailService.list(Wrappers.<FiSourceCostOrderDetail>lambdaQuery().eq(FiSourceCostOrderDetail::getPkFeebalance, pk));
        //重新转换
        try {
            this.turnData(sourceMainData,details);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            this.updateById(sourceMainData);
            LogUtil.info(log, "费用结算单采购订单集成信息转换失败:", e);
            throw new CheckedException("费用结算单采购订单集成信息转换失败:"+pushMsg);
        }
        //重新推送智慧平台
        sourceMainData.setPushTime(DateUtil.formatDateTime(new Date()));
        try {
            YgResult ygResult = this.pushData(pk);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
            sourceMainData.setPushMsg("");
            sourceMainData.setReadiness(1l);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk, Constants.TradeType.costOrder);
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000000");
                updatePlatFormLog.setMsg("");
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TradeType.costOrder);
                platFormLogVO.setCode("000000");
                platFormLogVO.setMsg("");
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,Constants.TradeType.costOrder);
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000001");
                updatePlatFormLog.setMsg(pushMsg);
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TradeType.costOrder);
                platFormLogVO.setCode("000001");
                platFormLogVO.setMsg(pushMsg);
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
            LogUtil.info(log, "费用结算单采购订单集成信息推送失败:", e);
            throw new CheckedException("费用结算单采购订单集成信息推送失败:"+pushMsg);
        }
        return true;
    }

    /**
     * 数据推送
     *
     * @param pk
     * @return
     * @throws Exception
     */
    private YgResult pushData(String pk) throws Exception {
        YgResult ygResult = new YgResult();
        if(ygConfig.getEnable()) {
            FiConvertCostOrder convertMainData = fiConvertCostOrderService.getOne(Wrappers.<FiConvertCostOrder>lambdaQuery().eq(FiConvertCostOrder::getPkFeebalance, pk));
            List<FiConvertCostOrderDetail> details = fiConvertCostOrderDetailService.list(Wrappers.<FiConvertCostOrderDetail>lambdaQuery().eq(FiConvertCostOrderDetail::getPkFeebalance,pk));
            YgOrderVO request = this.turnYgData(convertMainData,details);
            ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000011.getServCode());
            String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(request));
            ygResult = JSONObject.parseObject(result,YgResult.class);
            //000000：成功 其它：失败
            if(!ygResult.getCode().equals("000000")) {
                throw new CheckedException(ygResult.getmessage());
            }
        }
        return ygResult;
    }

    /**
     * 将转换后的数据用远光提供的格式推送
     *
     * @param convertMainData
     * @param details
     * @return
     */
    private YgOrderVO turnYgData(FiConvertCostOrder convertMainData, List<FiConvertCostOrderDetail> details) {
        YgOrderVO ygOrderVO = new YgOrderVO();
        String pkOrgV = apiCompareService.getTurnByType(convertMainData.getPkOrg(),Constants.PkOrg);
        if(StrUtil.isBlank(pkOrgV)) {
            throw new CheckedException("单位代号不可为空");
        }
        List<YgOrderDetailVO> items = new ArrayList<>();
        String prvMkCode = "00003010";
        ygOrderVO.setPrvMkCode(prvMkCode);
        ygOrderVO.setPurOrdNo(convertMainData.getBillCode());
        ygOrderVO.setAflUnit(pkOrgV);
        ygOrderVO.setCreTime(convertMainData.getBillmaketime());
        String purOrdTyp = "0L08";
        ygOrderVO.setPurOrdTyp(purOrdTyp);
        String contractNo = null;
        if(StringUtils.isNotEmpty(convertMainData.getBillCode()) && convertMainData.getBillCode().startsWith("SG")) {
            contractNo = convertMainData.getBillCode();
        }
        ygOrderVO.setContractNo(contractNo);
//        ygOrderVO.setCrePersNm(convertMainData.getBillmaker());
        String pkSupplier = apiCompareService.getTurnByType(convertMainData.getPkSupplier(), Constants.PkSupplier);
        ygOrderVO.setSupCod(pkSupplier);
        ygOrderVO.setTotAmExTax(convertMainData.getCurrMny());
        ygOrderVO.setPurOrdTaxAmount(convertMainData.getTax());
//        ygOrderVO.setPurOrdTypDesc(purOrdTypDesc);
        String transmissionDate = DateUtil.formatDate(new Date());
        ygOrderVO.setTransmissionDate(transmissionDate);
//        ygOrderVO.setProCen(pkOrgV);
        ygOrderVO.setDataSouSys("ZJYJ");
        for(FiConvertCostOrderDetail convertDetail : details) {
            YgOrderDetailVO ygOrderDetailVO = new YgOrderDetailVO();
            ygOrderDetailVO.setPurOrdNo(convertDetail.getBillCode());
            ygOrderDetailVO.setPurOrdItem(convertDetail.getProtocolList());
            ygOrderDetailVO.setPrjCode(apiCompareService.getProjectCode(convertDetail.getPkProject()));
            ygOrderDetailVO.setItTotAmExTax(convertDetail.getCurrMny());
            BigDecimal ntaxrate = BigDecimal.ZERO;
            if(StrUtil.isNotBlank(convertDetail.getTaxrate())) {
                ntaxrate = new BigDecimal(convertDetail.getTaxrate());
                ntaxrate = ntaxrate.divide(new BigDecimal(100),2, RoundingMode.HALF_UP);
            }
            ygOrderDetailVO.setTaxrt(ntaxrate.toPlainString());
            ygOrderDetailVO.setTaxAmount(convertDetail.getTax());
            ygOrderDetailVO.setItTotAmIncTax(convertDetail.getCurrTaxmny());
            ygOrderDetailVO.setCeleIdInPurVou("");
            ygOrderDetailVO.setUnitPrice(convertDetail.getTaxprice());
            ygOrderDetailVO.setAmt(convertDetail.getCurrNum());
//            String pkOrgDetail = apiCompareService.getTurnByType(convertDetail.getPkOrg(),Constants.PkOrg);
            ygOrderDetailVO.setPrvMkCode(prvMkCode);
//            ygOrderDetailVO.setPricUni(convertDetail.getPkMeasdoc());
            ygOrderDetailVO.setTransmissionDate(transmissionDate);
//            ygOrderDetailVO.setMtCode(convertDetail.getMaterialCode());
//            ygOrderDetailVO.setMtName(convertDetail.getName());
//            ygOrderDetailVO.setProCen("");
            ygOrderDetailVO.setRecepId("否");
            ygOrderDetailVO.setRecepInvVerSign("否");
            ygOrderDetailVO.setSerInvVerSign("是");
            items.add(ygOrderDetailVO);
        }
        ygOrderVO.setItems(items);
        return ygOrderVO;
    }

    /**
     * 数据转换
     *
     * @param sourceMainData
     * @param details
     */
    private void turnData(FiSourceCostOrder sourceMainData, List<FiSourceCostOrderDetail> details) throws Exception {
        String pk = sourceMainData.getPkFeebalance();
        FiConvertCostOrder fiConvertCostOrder = new FiConvertCostOrder();
        apiCompareCache.convertData(sourceMainData,fiConvertCostOrder, BillTypeEnum.COST_ORDER.getCode());
        FiConvertCostOrder convertMainData = fiConvertCostOrderService.getOne(Wrappers.<FiConvertCostOrder>lambdaQuery().eq(FiConvertCostOrder::getPkFeebalance, pk));
        fiConvertCostOrder.setId(null);
        if(convertMainData != null) {
            fiConvertCostOrder.setId(convertMainData.getId());
            fiConvertCostOrderDetailService.remove(Wrappers.<FiConvertCostOrderDetail>lambdaQuery().eq(FiConvertCostOrderDetail::getPkFeebalance,pk));
        }
        List<FiConvertCostOrderDetail> convertDetailList = new ArrayList<>();
        for(FiSourceCostOrderDetail sourceDetailData : details) {
            FiConvertCostOrderDetail fiConvertCostOrderDetail = new FiConvertCostOrderDetail();
            apiCompareCache.convertData(sourceDetailData,fiConvertCostOrderDetail,BillTypeEnum.COST_ORDER_DETAIL.getCode());
            fiConvertCostOrderDetail.setId(null);
            convertDetailList.add(fiConvertCostOrderDetail);
        }
        fiConvertCostOrderService.saveOrUpdate(fiConvertCostOrder);
        fiConvertCostOrderDetailService.saveOrUpdateBatch(convertDetailList);
    }

    @Override
    public Page<FiSourceCostOrderDTO> beforeConvertDataPage(Page page, FiSourceCostOrderQueryVO queryVO) {
        Page<FiSourceCostOrderDTO> result = this.baseMapper.beforeConvertDataPage(page,queryVO);
        if (CollectionUtil.isNotEmpty(result.getRecords())){
            for (FiSourceCostOrderDTO apiSource:result.getRecords()){
                if(StrUtil.isNotBlank(apiSource.getPkSupplier())) {
                    apiSource.setPkSupplierName(dmMapper.getSupplierName(apiSource.getPkSupplier()));
                }
            }
        }
        return result;
    }

    @Override
    public Page<FiSourceCostOrderDetailDTO> getBeforeConvertDataPage(Page page, FiSourceCostOrderQueryVO queryVO) {
        Page<FiSourceCostOrderDetailDTO> result = this.baseMapper.getBeforeConvertDataPage(page,queryVO);
        return result;
    }

    @Override
    public List<FiConvertCostOrderDTO> getConvertList(String pk) {
        List<FiConvertCostOrderDTO> convertDataDTOList = new ArrayList<>();
        FiConvertCostOrderDTO convertDataDTO = new FiConvertCostOrderDTO();
        FiConvertCostOrder convertDataOld = fiConvertCostOrderService.getOne(Wrappers.<FiConvertCostOrder>lambdaQuery().eq(FiConvertCostOrder::getPkFeebalance, pk));
        if(null != convertDataOld) {
            BeanUtil.copyProperties(convertDataOld,convertDataDTO);
            convertDataDTOList.add(convertDataDTO);
        }
        return convertDataDTOList;
    }

    @Override
    public Page<FiConvertCostOrderDetailDTO> getConvertData(Page page, String pk) {
        Page<FiConvertCostOrderDetailDTO> result = this.baseMapper.getConvertData(page,pk);
        return result;
    }

    @Override
    public void checkSync(String pkFeebalance) {
        FiSourceCostOrder fiSourceCostOrder = this.getOne(Wrappers.<FiSourceCostOrder>lambdaQuery()
                .eq(FiSourceCostOrder::getPkFeebalance, pkFeebalance));

        if (Objects.isNull(fiSourceCostOrder)) {
            throw new CheckedException(
                    MessageFormat.format("费用结算单未同步成功, 费用结算单不存在，费用结算单ID: {0}", pkFeebalance));
        }

        if (Objects.isNull(fiSourceCostOrder.getPushStatus()) ||
                ObjectUtil.notEqual(PushStatusEnum.PUSH_SUCCESS.getCode(), fiSourceCostOrder.getPushStatus())) {
            throw new CheckedException(
                    MessageFormat.format("费用结算单未同步成功, pushStatus: {0}，费用结算单编号: {1}",
                            fiSourceCostOrder.getPushStatus(), fiSourceCostOrder.getBillCode()));
        }
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourceCostOrder entity = this.getById(id);
            try {
                this.pushAgain(entity.getPkFeebalance());
            } catch (Exception e) {
                resultList.add(entity.getBillCode()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}

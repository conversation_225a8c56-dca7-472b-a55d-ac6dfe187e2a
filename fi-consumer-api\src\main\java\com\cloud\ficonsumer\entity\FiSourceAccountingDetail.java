

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 会计凭证详情数据源主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:41
 */
@Data
@TableName("fi_source_accounting_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "会计凭证详情数据源主表")
public class FiSourceAccountingDetail extends BaseEntity<FiSourceAccountingDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 凭证主键
     */
    @ApiModelProperty(value="凭证主键")
    private String pkVoucher;

    /**
     * 凭证编码
     */
    @ApiModelProperty(value="凭证编码")
    private Integer num;

    /**
     * 科目
     */
    @ApiModelProperty(value="科目")
    private String pkAccasoa;

    /**
     * 借贷标识
     */
    @ApiModelProperty(value="借贷标识")
    private String crde;

    /**
     * 分录序号
     */
    @ApiModelProperty(value="分录序号")
    private String flxh;


    /**
     * 原币
     */
    @ApiModelProperty(value="原币")
    private BigDecimal amount;

    /**
     * 业务日期
     */
    @ApiModelProperty(value="业务日期")
    private String busidate;

    /**
     * 辅助核算
     */
    @ApiModelProperty(value="辅助核算")
    private String assid;

    /**
     * 分录主键
     */
    @ApiModelProperty(value="分录主键")
    private String pkDetail;

}

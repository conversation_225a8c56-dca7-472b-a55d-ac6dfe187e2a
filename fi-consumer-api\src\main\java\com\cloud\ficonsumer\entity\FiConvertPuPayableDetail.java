package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@TableName("fi_convert_pu_payable_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购应付转换数据详情")
public class FiConvertPuPayableDetail extends BaseEntity<FiConvertPuPayableDetail> {

    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value = "部门", required = true)
    private String pkDeptidV;

    @ApiModelProperty(value = "合同号", required = true)
    private String contractno;

    @ApiModelProperty(value = "订单号")
    private String purchaseorder;

    @ApiModelProperty(value = "发票号")
    private String invoiceno;

    @ApiModelProperty(value = "出库单号")
    private String outstoreno;

    @ApiModelProperty(value = "供应商", required = true)
    private String supplier;

    @ApiModelProperty(value = "物料", required = true)
    private String material;

    @ApiModelProperty(value = "贷方数量", required = true)
    private BigDecimal quantityCr;

    @ApiModelProperty(value = "贷方原币金额", required = true)
    private BigDecimal moneyCr;

    @ApiModelProperty(value = "贷方原币无税金额")
    private BigDecimal notaxCr;

    @ApiModelProperty(value = "组织本币金额")
    private BigDecimal localMoneyCr;

    @ApiModelProperty(value = "组织本币无税金额")
    private BigDecimal localNotaxCr;

    @ApiModelProperty(value = "税额")
    private BigDecimal localTaxCr;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxrate;

    @ApiModelProperty(value = "收支项目", required = true)
    private String pkSubjcode;

    @ApiModelProperty(value = "应付单行标识")
    private String pkPayableitem;

    @ApiModelProperty(value = "应付单主键")
    private String pkPayablebill;

    @ApiModelProperty(value = "项目")
    private String project;

    @ApiModelProperty(value = "汇率")
    private BigDecimal rate;

    @ApiModelProperty(value = "采购订单行号")
    private String crowno;

    @ApiModelProperty(value = "应付单行号")
    private String rowno;

    @ApiModelProperty(value = "采购入库单号")
    private String storeBillno;

    @ApiModelProperty(value = "采购入库单行号")
    private String storeRowno;

    @ApiModelProperty(value = "上层单据类型")
    private String topBilltype;

    @ApiModelProperty(value = "上层单据主键")
    private String topBillid;

    @ApiModelProperty(value = "上层单据行主键")
    private String topItemid;

    @ApiModelProperty(value = "源头单据类型")
    private String srcBilltype;

    @ApiModelProperty(value = "源头交易类型")
    private String srcTradetype;

    @ApiModelProperty(value = "源头单据主键")
    private String srcBillid;

    @ApiModelProperty(value = "源头单据行主键")
    private String srcItemid;

    @ApiModelProperty(value = "方向，1=借方，-1=贷方")
    private Integer direction;

    @ApiModelProperty(value = "行号（从1开始递增）")
    private Integer lineNumber;

    @ApiModelProperty(value = "当期核销预付款含税金额")
    private String def24;

    @ApiModelProperty(value = "质保金金额")
    private String def38;

    @ApiModelProperty(value = "收款银行账户")
    private String recaccount;
}

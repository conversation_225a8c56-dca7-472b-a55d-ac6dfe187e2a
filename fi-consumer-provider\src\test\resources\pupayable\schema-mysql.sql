CREATE TABLE `fi_source_pu_payable`
(
    `id`                 bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_org`             varchar(20)    DEFAULT NULL COMMENT '应付财务组织',
    `billmaker`          varchar(20)    DEFAULT NULL COMMENT '制单人',
    `pk_payablebill`     varchar(40)    DEFAULT NULL COMMENT '应付单标识',
    `def6`               varchar(20)    DEFAULT NULL COMMENT '费用类型',
    `pk_tradetypeid`     varchar(20)    DEFAULT NULL COMMENT '应付类型',
    `pk_tradetype`       varchar(20)    DEFAULT NULL COMMENT '应付类型编码',
    `pk_currtype`        varchar(20)    DEFAULT NULL COMMENT '币种',
    `money`              decimal(28, 8) DEFAULT NULL COMMENT '原币金额',
    `billstatus`         int(11)        DEFAULT NULL COMMENT '单据状态',
    `billno`             varchar(40)    DEFAULT NULL COMMENT '单据号',
    `def47`              varchar(255)   DEFAULT NULL COMMENT '付款性质',
    `def52`              varchar(255)   DEFAULT NULL COMMENT '部门分类',
    `integration_status` varchar(128)   DEFAULT NULL COMMENT '集成状态',
    `push_status`        tinyint(4)     DEFAULT NULL COMMENT '4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败',
    `push_msg`           varchar(1024)  DEFAULT NULL COMMENT '推送成功失败信息',
    `push_time`          varchar(50)    DEFAULT NULL COMMENT '推送时间',
    `return_msg`         varchar(255)   DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      varchar(16)    DEFAULT NULL COMMENT '返回同步状态',
    `return_bill`        varchar(255)   DEFAULT NULL COMMENT '返回同步消息',
    `return_time`        varchar(50)    DEFAULT NULL COMMENT '返回接收时间',
    `try_number`         bigint(32)     DEFAULT '0' COMMENT '重试次数',
    `readiness`          bigint(32)     DEFAULT '0' COMMENT '0 未就绪 1 就绪',
    `back_tag`           varchar(1)     DEFAULT 'N' COMMENT '是否退回重传标识，Y-是，N-否；标识默认为N，回退成功后，更新为Y；单据集成成功后，重置为N',
    `ywid`               varchar(255)   DEFAULT NULL COMMENT '远光回调ID',
    `create_by`          varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='采购应付源数据详情';

CREATE TABLE `fi_source_pu_payable_detail`
(
    `id`             bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_deptid_v`    varchar(20)    DEFAULT NULL COMMENT '部门',
    `contractno`     varchar(40)    DEFAULT NULL COMMENT '合同号',
    `purchaseorder`  varchar(40)    DEFAULT NULL COMMENT '订单号',
    `invoiceno`      varchar(40)    DEFAULT NULL COMMENT '发票号',
    `outstoreno`     varchar(40)    DEFAULT NULL COMMENT '出库单号',
    `supplier`       varchar(20)    DEFAULT NULL COMMENT '供应商版本',
    `material`       varchar(40)    DEFAULT NULL COMMENT '物料',
    `quantity_cr`    decimal(28, 8) DEFAULT NULL COMMENT '贷方数量',
    `money_cr`       decimal(28, 8) DEFAULT NULL COMMENT '贷方原币金额',
    `notax_cr`       decimal(28, 8) DEFAULT NULL COMMENT '贷方原币无税金额',
    `local_money_cr` decimal(28, 8) DEFAULT NULL COMMENT '组织本币金额',
    `local_notax_cr` decimal(28, 8) DEFAULT NULL COMMENT '组织本币无税金额',
    `local_tax_cr`   decimal(28, 8) DEFAULT NULL COMMENT '税额',
    `taxrate`        decimal(28, 8) DEFAULT NULL COMMENT '税率',
    `pk_subjcode`    varchar(20)    DEFAULT NULL COMMENT '收支项目',
    `pk_payableitem` varchar(40)    DEFAULT NULL COMMENT '应付单行标识',
    `pk_payablebill` varchar(40)    DEFAULT NULL COMMENT '应付单表示',
    `rate`           decimal(28, 8) DEFAULT NULL COMMENT '税率',
    `project`        varchar(40)    DEFAULT NULL COMMENT '项目',
    `crowno`         varchar(40)    DEFAULT NULL COMMENT '采购订单行号',
    `rowno`          varchar(40)    DEFAULT NULL COMMENT '行号',
    `store_billno`   varchar(40)    DEFAULT NULL COMMENT '入库单编号',
    `store_rowno`    varchar(40)    DEFAULT NULL COMMENT '入库单行号',
    `top_billtype`   varchar(40)    DEFAULT NULL COMMENT '上层单据类型',
    `top_billid`     varchar(40)    DEFAULT NULL COMMENT '上层单据主键',
    `top_itemid`     varchar(40)    DEFAULT NULL COMMENT '上层单据行主键',
    `src_billtype`   varchar(40)    DEFAULT NULL COMMENT '源头单据类型',
    `src_tradetype`  varchar(40)    DEFAULT NULL COMMENT '源头交易类型',
    `src_billid`     varchar(40)    DEFAULT NULL COMMENT '源头单据主键',
    `src_itemid`     varchar(40)    DEFAULT NULL COMMENT '源头单据行主键',
    `direction`      int            DEFAULT '-1' COMMENT '方向 1=借方,-1=贷方',
    `line_number`    int            DEFAULT '-1' COMMENT '行号',
    `create_by`      varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`      varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`    datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`       bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='采购应付明细源数据详情';

CREATE TABLE `fi_convert_pu_payable`
(
    `id`                 bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_org`             varchar(20)    DEFAULT NULL COMMENT '应付财务组织',
    `billmaker`          varchar(20)    DEFAULT NULL COMMENT '制单人',
    `pk_payablebill`     varchar(40)    DEFAULT NULL COMMENT '应付单标识',
    `def6`               varchar(20)    DEFAULT NULL COMMENT '费用类型',
    `pk_tradetypeid`     varchar(20)    DEFAULT NULL COMMENT '应付类型',
    `pk_tradetype`       varchar(20)    DEFAULT NULL COMMENT '应付类型编码',
    `pk_currtype`        varchar(20)    DEFAULT NULL COMMENT '币种',
    `money`              decimal(28, 8) DEFAULT NULL COMMENT '原币金额',
    `billstatus`         int(11)        DEFAULT NULL COMMENT '单据状态',
    `billno`             varchar(40)    DEFAULT NULL COMMENT '单据号',
    `def47`              varchar(255)   DEFAULT NULL COMMENT '付款性质',
    `def52`              varchar(255)   DEFAULT NULL COMMENT '部门分类',
    `integration_status` varchar(128)   DEFAULT NULL COMMENT '集成状态',
    `push_status`        tinyint(4)     DEFAULT NULL COMMENT '4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败',
    `push_msg`           varchar(1024)  DEFAULT NULL COMMENT '推送成功失败信息',
    `push_time`          varchar(50)    DEFAULT NULL COMMENT '推送时间',
    `return_msg`         varchar(255)   DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      varchar(16)    DEFAULT NULL COMMENT '返回同步状态',
    `return_bill`        varchar(255)   DEFAULT NULL COMMENT '返回同步消息',
    `return_time`        varchar(50)    DEFAULT NULL COMMENT '返回接收时间',
    `try_number`         bigint(32)     DEFAULT '0' COMMENT '重试次数',
    `readiness`          bigint(32)     DEFAULT '0' COMMENT '0 未就绪 1 就绪',
    `back_tag`           varchar(1)     DEFAULT 'N' COMMENT '是否退回重传标识，Y-是，N-否；标识默认为N，回退成功后，更新为Y；单据集成成功后，重置为N',
    `create_by`          varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='采购应付转换数据主表';

CREATE TABLE `fi_convert_pu_payable_detail`
(
    `id`             bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_deptid_v`    varchar(20)    DEFAULT NULL COMMENT '部门',
    `contractno`     varchar(40)    DEFAULT NULL COMMENT '合同号',
    `purchaseorder`  varchar(40)    DEFAULT NULL COMMENT '订单号',
    `invoiceno`      varchar(40)    DEFAULT NULL COMMENT '发票号',
    `outstoreno`     varchar(40)    DEFAULT NULL COMMENT '出库单号',
    `supplier`       varchar(20)    DEFAULT NULL COMMENT '供应商版本',
    `material`       varchar(40)    DEFAULT NULL COMMENT '物料',
    `quantity_cr`    decimal(28, 8) DEFAULT NULL COMMENT '贷方数量',
    `money_cr`       decimal(28, 8) DEFAULT NULL COMMENT '贷方原币金额',
    `notax_cr`       decimal(28, 8) DEFAULT NULL COMMENT '贷方原币无税金额',
    `local_money_cr` decimal(28, 8) DEFAULT NULL COMMENT '组织本币金额',
    `local_notax_cr` decimal(28, 8) DEFAULT NULL COMMENT '组织本币无税金额',
    `local_tax_cr`   decimal(28, 8) DEFAULT NULL COMMENT '税额',
    `taxrate`        decimal(28, 8) DEFAULT NULL COMMENT '税率',
    `pk_subjcode`    varchar(20)    DEFAULT NULL COMMENT '收支项目',
    `pk_payableitem` varchar(40)    DEFAULT NULL COMMENT '应付单行标识',
    `pk_payablebill` varchar(40)    DEFAULT NULL COMMENT '应付单表示',
    `rate`           decimal(28, 8) DEFAULT NULL COMMENT '税率',
    `project`        varchar(40)    DEFAULT NULL COMMENT '项目',
    `crowno`         varchar(40)    DEFAULT NULL COMMENT '采购订单行号',
    `rowno`          varchar(40)    DEFAULT NULL COMMENT '行号',
    `store_billno`   varchar(40)    DEFAULT NULL COMMENT '入库单编号',
    `store_rowno`    varchar(40)    DEFAULT NULL COMMENT '入库单行号',
    `top_billtype`   varchar(40)    DEFAULT NULL COMMENT '上层单据类型',
    `top_billid`     varchar(40)    DEFAULT NULL COMMENT '上层单据主键',
    `top_itemid`     varchar(40)    DEFAULT NULL COMMENT '上层单据行主键',
    `src_billtype`   varchar(40)    DEFAULT NULL COMMENT '源头单据类型',
    `src_tradetype`  varchar(40)    DEFAULT NULL COMMENT '源头交易类型',
    `src_billid`     varchar(40)    DEFAULT NULL COMMENT '源头单据主键',
    `src_itemid`     varchar(40)    DEFAULT NULL COMMENT '源头单据行主键',
    `direction`      int            DEFAULT '-1' COMMENT '方向 1=借方,-1=贷方',
    `line_number`    int            DEFAULT '-1' COMMENT '行号',
    `create_by`      varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`      varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`    datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`       bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='采购应付明细转换数据详情';
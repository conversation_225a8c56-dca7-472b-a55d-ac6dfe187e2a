

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会计凭证源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:35
 */
@Data
@TableName("fi_source_accounting")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "会计凭证源数据主表")
public class FiSourceAccounting extends BaseEntity<FiSourceAccounting> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 凭证主键
     */
    @ApiModelProperty(value="凭证主键")
    private String pkVoucher;

    /**
     * 凭证编码
     */
    @ApiModelProperty(value="凭证编码")
    private Integer num;

    /**
     * 凭证类别
     */
    @ApiModelProperty(value="凭证类别")
    private String pkVouchertype;

    /**
     * 财务组织
     */
    @ApiModelProperty(value="财务组织")
    private String pkOrg;

    /**
     * 财务组织编码
     */
    @ApiModelProperty(value="财务组织编码")
    private String pkOrgCode;

    /**
     * 财务组织名称
     */
    @ApiModelProperty(value="财务组织名称")
    private String pkOrgName;

    /**
     * 业务单元
     */
    @ApiModelProperty(value="业务单元")
    private String free5;

    /**
     * 摘要
     */
    @ApiModelProperty(value="摘要")
    private String explanation;

    /**
     * 制单日期
     */
    @ApiModelProperty(value="制单日期")
    private String prepareddate;

    /**
     * 记账日期
     */
    @ApiModelProperty(value="记账日期")
    private String tallydate;
    /**
     * 会计期间
     */
    @ApiModelProperty(value="会计期间")
    private String period;

    /**
     * 制单人
     */
    @ApiModelProperty(value="制单人")
    private String pkPrepared;

    /**
     * 审核人
     */
    @ApiModelProperty(value="审核人")
    private String pkChecked;

    /**
     * 币种
     */
    @ApiModelProperty(value="币种")
    private String pkCurrtype;


    /**
     * 附单据数
     */
    @ApiModelProperty(value="附单据数")
    private Integer attachment;

    /**
     * 会计年度
     */
    @ApiModelProperty(value="会计年度")
    private String year;

    /**
     * 调整期间
     */
    @ApiModelProperty(value="调整期间")
    private String adjustperiod;

    /**
     * 冲销凭证
     */
    @ApiModelProperty(value="冲销凭证")
    private String offervoucher;

    /**
     * 成功失败标识，0是成功；1是失败
     */
    @ApiModelProperty(value="成功失败标识，0是成功；1是失败")
    private Integer code;

    /**
     * 集成状态
     */
    @ApiModelProperty(value="集成状态")
    private String integrationStatus;

    /**
     * 推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中
     */
    @ApiModelProperty(value="推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中")
    private Integer pushStatus;

    /**
     * 推送成功失败信息
     */
    @ApiModelProperty(value="推送成功失败信息")
    private String pushMsg;

    /**
     * 推送时间
     */
    @ApiModelProperty(value="推送时间")
    private String pushTime;

    /**
     * 1 推送中 2推送成功 3推送失败
     */
    @ApiModelProperty(value="1 推送中 2推送成功 3推送失败")
    private Integer pushOrderStatus;

    /**
     * 推送成功失败信息
     */
    @ApiModelProperty(value="推送成功失败信息")
    private String pushOrderMsg;

    /**
     * 返回同步消息
     */
    @ApiModelProperty(value="返回同步消息")
    private String returnMsg;

    /**
     * 返回同步状态
     */
    @ApiModelProperty(value="返回同步状态")
    private String returnStatus;

    /**
     * 重试次数
     */
    @ApiModelProperty(value="重试次数")
    private Long tryNumber;

    /**
     * 0 未就绪 1 就绪
     */
    @ApiModelProperty(value="0 未就绪 1 就绪")
    private Long readiness;

    /**
     * 中台凭证号
     */
    private String findocid;

}

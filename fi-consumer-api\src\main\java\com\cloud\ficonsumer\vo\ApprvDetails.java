package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/24.
 */
@Data
public class ApprvDetails {

    /**
     * 审批岗位
     */
    @ApiModelProperty(value="审批岗位")
    private String apprvPost;

    /**
     * 审批人
     */
    @ApiModelProperty(value="审批人")
    private String aprver;

    /**
     * 审批意见
     */
    @ApiModelProperty(value="审批意见")
    private String aprvMsg;

    /**
     * 审批日期
     */
    @ApiModelProperty(value="审批日期")
    private String aprvDate;

    /**
     * 印章格式
     */
    @ApiModelProperty(value="印章格式")
    private String sealFormat;

    /**
     * 印章资源
     */
    @ApiModelProperty(value="印章资源")
    private String sealSrc;

    /**
     * 是否需要外部审批
     */
    @ApiModelProperty(value="是否需要外部审批")
    private String needExternalApproval;

}

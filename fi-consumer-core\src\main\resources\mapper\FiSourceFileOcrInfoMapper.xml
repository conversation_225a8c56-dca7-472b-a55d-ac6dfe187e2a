<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloud.ficonsumer.mapper.FiSourceFileOcrInfoMapper">

    <select id="beforeConvertDataPage" resultType="com.cloud.ficonsumer.vo.FiSourceFileOcrInfoVO">
        SELECT *
        FROM fi_source_file_ocr_info t
        LEFT JOIN fi_source_file_info td ON t.file_id = td.file_manage_id
        WHERE t.del_flag = 0 and td.del_flag = 0
        <if test="param.pushStatus != null and param.pushStatus != ''">
            AND t.push_status = #{param.pushStatus}
        </if>
        <if test="param.pushStatusList != null and param.pushStatusList.size() > 0">
            AND t.push_status in
            <foreach collection="param.pushStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.uniquecodeofinvoice != null and param.uniquecodeofinvoice != ''">
            AND t.uniquecodeofinvoice LIKE CONCAT('%', #{param.uniquecodeofinvoice}, '%')
        </if>
        <if test="param.orgId != null">
            AND td.org_id = #{param.orgId}
        </if>
        <if test="param.createTimeStart != null and param.createTimeStart != ''">
            and t.create_time >= #{param.createTimeStart}
        </if>
        <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
            and #{param.createTimeEnd} >= t.create_time
        </if>
    </select>

    <select id="getBeforeConvertDataPage" resultType="com.cloud.ficonsumer.vo.FiSourceFileInfoVO">
        SELECT td.*
        FROM fi_source_file_ocr_info t
                 LEFT JOIN fi_source_file_info td ON t.file_id = td.file_manage_id
        WHERE t.del_flag = 0
          AND td.del_flag = 0
          AND t.id = #{param.id}
    </select>

    <select id="getConvertData" resultType="com.cloud.ficonsumer.vo.FiConvertFileInfoVO">
        SELECT td.*
        FROM fi_convert_file_ocr_info t
                 LEFT JOIN fi_convert_file_info td ON t.file_id = td.file_manage_id
        WHERE t.del_flag = 0
          AND td.del_flag = 0
          AND t.uniquecodeofinvoice = #{uniquecodeofinvoice}
    </select>
</mapper>


package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.*;
import com.cloud.ficonsumer.entity.FiSourceAccounting;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceAccountingQueryVO;

import java.util.List;

/**
 * 采购入库源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:35
 */
public interface FiSourceAccountingService extends IService<FiSourceAccounting> {

    /**
     * 会计凭证重新推送
     * @param s
     */
    boolean pushAgain(String s);

    Page<FiSourceAccountingDTO> beforeConvertDataPage(Page page, FiSourceAccountingQueryVO queryVO);

    Page<FiSourceAccountingDetailDTO> getBeforeConvertDataPage(Page page, FiSourceAccountingQueryVO queryVO);

    List<FiConvertAccountingDTO> getConvertList(String pk);

    Page<FiConvertAccountingDetailDTO> getConvertData(Page page, String pk);


    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);

}

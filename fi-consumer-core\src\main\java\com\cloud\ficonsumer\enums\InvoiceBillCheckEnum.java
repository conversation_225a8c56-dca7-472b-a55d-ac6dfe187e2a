package com.cloud.ficonsumer.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 发票入池前，校验发票所属业务单据状态枚举
 */
@Getter
public enum InvoiceBillCheckEnum {

    /**
     * 应付单
     * 表名：ap_payablebill
     * 状态字段：billstatus
     * 有效状态：1(审批通过)，8(签字)
     */
    AP_PAYABLE_BILL("F1", "ap_payablebill", "billstatus", Arrays.asList("1", "8")),

    /**
     * 采购发票
     * 表名：po_invoice
     * 状态字段：fbillstatus
     * 有效状态：3(审批通过)
     */
    PO_INVOICE("25", "po_invoice", "fbillstatus", Collections.singletonList("3")),

    /**
     * 收票登记
     * 表名：pm_receiptreg
     * 状态字段：bill_status
     * 有效状态：1(审批通过)，3(已提交)
     */
    PM_RECEIPT_REG("4D53", "pm_receiptreg", "bill_status", Arrays.asList("1", "3")),

    /**
     * 其他业务类型
     * 不校验状态，直接通过
     */
    OTHER("OTHER", null, null, Collections.emptyList());

    /** 业务代码 */
    private final String businessCode;
    /** 表名 */
    private final String tableName;
    /** 状态字段名 */
    private final String statusField;
    /** 有效的状态值列表 */
    private final List<String> validStatuses;

    InvoiceBillCheckEnum(String businessCode, String tableName, String statusField, List<String> validStatuses) {
        this.businessCode = businessCode;
        this.tableName = tableName;
        this.statusField = statusField;
        this.validStatuses = validStatuses;
    }

    /**
     * 根据业务代码获取对应的枚举值
     *
     * @param businessCode 业务代码
     * @return 对应的枚举值，如果未找到则返回 OTHER
     */
    public static InvoiceBillCheckEnum getByBusinessCode(String businessCode) {
        return Arrays.stream(values())
                .filter(e -> e.getBusinessCode().equals(businessCode))
                .findFirst()
                .orElse(OTHER);
    }
}
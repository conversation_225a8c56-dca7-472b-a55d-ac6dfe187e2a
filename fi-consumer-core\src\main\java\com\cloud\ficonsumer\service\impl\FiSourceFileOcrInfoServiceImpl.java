package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.common.UCN;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.BillStatusDTO;
import com.cloud.ficonsumer.dto.FiSourceFileInfoQueryDTO;
import com.cloud.ficonsumer.dto.FiSourceFileOcrInfoQueryDTO;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.enums.*;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceFileOcrInfoMapper;
import com.cloud.ficonsumer.mapper.FileManageMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.UdsUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import com.sgcc.uds.cloud.sdk.action.result.CommonActionResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourceFileOcrInfoServiceImpl extends ServiceImpl<FiSourceFileOcrInfoMapper, FiSourceFileOcrInfo> implements FiSourceFileOcrInfoService {

    private final FiSourceFileInfoService detailService;
    private final FiConvertFileOcrInfoService convertService;
    private final FiConvertFileInfoService convertDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final FileManageMapper fileManageMapper;
    private final YgConfig ygConfig;
    private final FiOrgConvertService fiOrgConvertService;
    private final DmMapper dmMapper;
    private final PlatformLogService platformLogService;

    @Override
    public boolean push(String uniquecodeofinvoice) {
        FiSourceFileOcrInfo source = this.getOne(Wrappers.<FiSourceFileOcrInfo>lambdaQuery()
                .eq(FiSourceFileOcrInfo::getUniquecodeofinvoice, uniquecodeofinvoice));
        if (Objects.isNull(source)) {
            throw new CheckedException(MessageFormat.format("进项发票集成信息不存在,单据号:{0}", uniquecodeofinvoice));
        }

        List<FiSourceFileInfo> sourceDetails = detailService.list(Wrappers.<FiSourceFileInfo>lambdaQuery()
                .eq(FiSourceFileInfo::getFileManageId, source.getFileId()));
        if (CollectionUtil.isEmpty(sourceDetails)) {
            throw new CheckedException(MessageFormat.format("进项发票集成信息明细不存在,单据号:{0}", uniquecodeofinvoice));
        }

        // 1. 转换数据
        try {
            convertData(source, sourceDetails);
        } catch (Exception e) {
            handleConversionException(source, e);
        }

        try {
            // 获取创建人所属部门
            UCN dept = getDepartmentInfo(sourceDetails);
            // 推送数据到远光平台
            pushToYgPlatform(source, dept);
            // 更新推送状态
            updatePushSuccess(source);
        } catch (Exception e) {
            handlePushException(source, e);
        }

        return true;
    }

    @Override
    public Page<FiSourceFileOcrInfoVO> beforeConvertDataPage(Page<FiSourceFileOcrInfoVO> page, FiSourceFileOcrInfoQueryDTO queryDTO) {
        return baseMapper.beforeConvertDataPage(page, queryDTO);
    }

    @Override
    public Page<FiSourceFileInfoVO> getBeforeConvertDataPage(Page<FiSourceFileInfoVO> page, FiSourceFileInfoQueryDTO queryDTO) {
        return baseMapper.getBeforeConvertDataPage(page, queryDTO);
    }

    @Override
    public List<FiConvertFileOcrInfoVO> getConvertList(String uniquecodeofinvoice) {
        List<FiConvertFileOcrInfoVO> convertDataList = new ArrayList<>();
        FiConvertFileOcrInfoVO convertDataVO = new FiConvertFileOcrInfoVO();
        FiConvertFileOcrInfo convertDataOld = convertService.getOne(Wrappers.<FiConvertFileOcrInfo>lambdaQuery()
                .eq(FiConvertFileOcrInfo::getUniquecodeofinvoice, uniquecodeofinvoice));
        if (null != convertDataOld) {
            BeanUtil.copyProperties(convertDataOld, convertDataVO);
            convertDataList.add(convertDataVO);
        }
        return convertDataList;
    }

    @Override
    public Page<FiConvertFileInfoVO> getConvertData(Page<FiConvertFileInfoVO> page, String uniquecodeofinvoice) {
        Page<FiConvertFileInfoVO> result = this.baseMapper.getConvertData(page, uniquecodeofinvoice);
        if (CollectionUtil.isNotEmpty(result.getRecords())) {
            for (FiConvertFileInfoVO data : result.getRecords()) {

            }
        }
        return result;
    }

    @Override
    public Map<String, Integer> listOcrPushStatus(List<String> fileManageIds) {
        Map<String, Integer> result = new HashMap<>();
        List<FiSourceFileOcrInfo> list = this.list(Wrappers.<FiSourceFileOcrInfo>lambdaQuery()
                .in(FiSourceFileOcrInfo::getFileManageId, fileManageIds));
        for (FiSourceFileOcrInfo source : list) {
            result.put(source.getFileManageId(), source.getPushStatus());
        }
        return result;
    }

    @Override
    public List<FiSourceFileOcrInfoVO> filterValidBillStatus(Map<InvoiceBillCheckEnum, List<FiSourceFileOcrInfoVO>> businessCodeGroup) {
        if (CollectionUtils.isEmpty(businessCodeGroup)) {
            return Collections.emptyList();
        }

        return businessCodeGroup.entrySet().stream()
                .filter(entry -> !CollectionUtils.isEmpty(entry.getValue()))
                .flatMap(entry -> processBusinessGroup(entry.getKey(), entry.getValue()).stream())
                .collect(Collectors.toList());
    }

    /**
     * 处理单个业务类型组的单据
     *
     * @param billCheckEnum 业务类型枚举
     * @param groupList 该业务类型的单据列表
     * @return 有效单据列表
     */
    private List<FiSourceFileOcrInfoVO> processBusinessGroup(InvoiceBillCheckEnum billCheckEnum, List<FiSourceFileOcrInfoVO> groupList) {
        // 其他业务类型直接返回
        if (billCheckEnum == InvoiceBillCheckEnum.OTHER) {
            return groupList;
        }

        // 提取单据ID
        List<String> billIds = extractValidBillIds(groupList);
        if (CollectionUtils.isEmpty(billIds)) {
            return Collections.emptyList();
        }

        // 获取有效状态的单据
        Map<String, BillStatusDTO> statusMap = getBillStatusMap(billCheckEnum, billIds);

        // 只保留状态存在的单据
        return groupList.stream()
                .filter(bill -> statusMap.containsKey(bill.getBillId()))
                .collect(Collectors.toList());
    }

    /**
     * 根据业务类型获取单据状态
     */
    private Map<String, BillStatusDTO> getBillStatusMap(InvoiceBillCheckEnum billCheck, List<String> billIds) {
        if (billCheck == null || CollectionUtils.isEmpty(billIds)) {
            return Collections.emptyMap();
        }

        switch (billCheck) {
            case AP_PAYABLE_BILL:
                return dmMapper.batchGetApPayablebillStatus(billIds, billCheck.getValidStatuses());
            case PO_INVOICE:
                return dmMapper.batchGetPoInvoiceStatus(billIds, billCheck.getValidStatuses());
            case PM_RECEIPT_REG:
                return dmMapper.batchGetPmReceiptregStatus(billIds, billCheck.getValidStatuses());
            default:
                log.warn("未支持的业务类型: {}", billCheck);
                return Collections.emptyMap();
        }
    }

    /**
     * 提取有效的单据ID
     */
    private List<String> extractValidBillIds(List<FiSourceFileOcrInfoVO> groupList) {
        return groupList.stream()
                .map(FiSourceFileOcrInfoVO::getBillId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
    }

    private UCN getDepartmentInfo(List<FiSourceFileInfo> sourceDetails) {
        String createBy = sourceDetails.get(0).getCreateBy();
        if (StringUtils.isEmpty(createBy)) {
            throw new CheckedException("创建人信息不能为空");
        }
        List<UCN> depts = dmMapper.getDeptByUserCode(createBy);
        if (CollectionUtil.isEmpty(depts)) {
            // 如果未找到用户部门信息，返回"其他部门"
            UCN otherDept = new UCN();
            otherDept.setCode("OTHER_DEPT");
            otherDept.setName("其他部门");
            return otherDept;
        }
        return depts.get(0);
    }

    private void updatePushSuccess(FiSourceFileOcrInfo source) {
        source.setIntegrationStatus("0");
        source.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
        source.setPushMsg("");
        source.setReadiness(1L);
        source.setPushMsg("");
        this.updateById(source);

        platformLogService.recordLog(
            source.getUniquecodeofinvoice(),
            BillTypeEnum.FILE_OCR_INFO.getCode(),
            Constants.SUCCESS,
            "【进项发票信息接收服务】集成财务中台成功"
        );
    }

    private void handleConversionException(FiSourceFileOcrInfo source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        platformLogService.recordLog(
            source.getUniquecodeofinvoice(),
            BillTypeEnum.FILE_OCR_INFO.getCode(),
            Constants.FAIL,
            pushMsg
        );
        log.warn("进项发票集成信息转换失败:", e);
        throw new CheckedException("进项发票集成信息转换失败:" + pushMsg);
    }

    private void handlePushException(FiSourceFileOcrInfo source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        platformLogService.recordLog(
            source.getUniquecodeofinvoice(),
            BillTypeEnum.FILE_OCR_INFO.getCode(),
            Constants.FAIL,
            pushMsg
        );
        log.warn("进项发票集成信息推送失败:", e);
        throw new CheckedException("进项发票集成信息推送失败:" + pushMsg);
    }

    private Boolean pushToYgPlatform(FiSourceFileOcrInfo source, UCN dept) {
        if (!ygConfig.getEnable()) {
            return Boolean.FALSE;
        }

        //获取转换后的数据
        FiConvertFileOcrInfo convert = convertService.getOne(Wrappers.<FiConvertFileOcrInfo>lambdaQuery()
                .eq(FiConvertFileOcrInfo::getFileManageId, source.getFileManageId()));
        List<FiConvertFileInfo> convertDetails = convertDetailService.list(Wrappers.<FiConvertFileInfo>lambdaQuery()
                .eq(FiConvertFileInfo::getFileManageId, source.getFileId()));

        YgInvoicePooingVO ygInvoicePooingVO = createYgInvoicePooingVO(convert, convertDetails, dept);
        ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000028.getServCode());

        // 发票入池，首先上传发票附件到UDS
        CommonActionResult udsResult;
        for (FiConvertFileInfo convertDetail : convertDetails) {
            udsResult = UdsUtil.pushFileToUdsFromOSS(convertDetail.getBucketName(),
                    convertDetail.getOriginal(), convertDetail.getFileName());
            if (Objects.isNull(udsResult)) {
                throw new CheckedException(MessageFormat.format("上传UDS失败,bucketName:{0},original:{1},fileName:{2}",
                        convertDetail.getBucketName(),
                        convertDetail.getOriginal(),
                        convertDetail.getFileName()));
            }
            // 根据UDS返回信息填充电子凭证信息
            ygInvoicePooingVO.setElecBillInvDoc(udsResult.getFileName());
            ygInvoicePooingVO.setElecBillInvType("xml");
            ygInvoicePooingVO.setElecBillUdsResId(udsResult.getDocumentId());
        }
        // 然后调用发票入池接口
        Map<String, Object> data = new HashMap<>();
        data.put("list", Collections.singletonList(ygInvoicePooingVO));
        String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(data));
        if (StringUtils.isEmpty(result)) {
            throw new CheckedException("响应失败，响应为空。");
        }

        YgResult<YgInvoiceDataResult> ygResult = JSONObject.parseObject(result,
                new TypeReference<YgResult<YgInvoiceDataResult>>() {
                });
        if (Objects.isNull(ygResult)) {
            throw new CheckedException("响应失败，响应结果为: " + result);
        }

        return handleYgResponse(ygResult, source.getFileManageId());
    }

    private Boolean handleYgResponse(YgResult<YgInvoiceDataResult> ygResult, String fileManageId) {
        String respCode = ygResult.getCode();
        // 处理成功或部分成功的情况
        if ("6100000".equals(respCode) || "6710300".equals(respCode)) {
            YgInvoiceDataResult data = ygResult.getData();
            if (Objects.isNull(data)) {
                fileManageMapper.updateFileOcrInfoStatus(fileManageId, 2, "响应数据为空");
                throw new CheckedException("响应数据为空");
            }

            List<YgInvoiceProcResult> procSucc = data.getProcSucc();
            List<YgInvoiceProcResult> procFail = data.getProcFail();

            // 记录成功数据
            if (!CollectionUtils.isEmpty(procSucc)) {
                log.info("进项发票入池成功数据: {}", JSON.toJSONString(procSucc));
                fileManageMapper.updateFileOcrInfoStatus(fileManageId, 1, "入池成功");
            }

            // 处理失败数据
            if (!CollectionUtils.isEmpty(procFail)) {
                log.error("进项发票入池失败数据: {}", JSON.toJSONString(procFail));
                StringBuilder errorMsg = new StringBuilder("进项发票入池失败: ");
                for (int i = 0; i < procFail.size(); i++) {
                    YgInvoiceProcResult failItem = procFail.get(i);
                    errorMsg.append(String.format("发票号码:%s,发票代码:%s,失败代码:%s,失败原因:%s,subGid:%s",
                            failItem.getInvoiceNo(),
                            failItem.getInvoCode(),
                            failItem.getProcCode(),
                            failItem.getProcMsg(),
                            failItem.getSubGid()));
                    if (i < procFail.size() - 1) {
                        errorMsg.append("; ");
                    }
                }
                String pushMsg = AdminUtils.truncateString(errorMsg.toString(), 1000);
                fileManageMapper.updateFileOcrInfoStatus(fileManageId, 2, pushMsg);
                throw new CheckedException(errorMsg.toString());
            }

            return Boolean.TRUE;
        }
        // 处理明确的失败情况
        else if ("6990399".equals(respCode)) {
            String errorMsg = "响应失败，错误代码: " + respCode + ", 响应结果: " + JSON.toJSONString(ygResult);
            fileManageMapper.updateFileOcrInfoStatus(fileManageId, 2, 
                AdminUtils.truncateString(errorMsg, 1000));
            throw new CheckedException(errorMsg);
        }
        // 处理其他未知情况
        else {
            String errorMsg = "未知响应代码: " + respCode + ", 响应结果: " + JSON.toJSONString(ygResult);
            fileManageMapper.updateFileOcrInfoStatus(fileManageId, 2, 
                AdminUtils.truncateString(errorMsg, 1000));
            throw new CheckedException(errorMsg);
        }
    }

    private void convertData(FiSourceFileOcrInfo source, List<FiSourceFileInfo> sourceDetails) {
        String fileManageId = source.getFileManageId();

        FiConvertFileOcrInfo fiConvert = new FiConvertFileOcrInfo();
        apiCompareCache.convertData(source, fiConvert, BillTypeEnum.FILE_OCR_INFO.getCode());
        fiConvert.setCreateBy(source.getCreateBy());
        fiConvert.setUpdateBy(source.getUpdateBy());
        FiConvertFileOcrInfo existing = convertService.getOne(Wrappers.<FiConvertFileOcrInfo>lambdaQuery()
                .eq(FiConvertFileOcrInfo::getFileManageId, fileManageId));
        if (Objects.nonNull(existing)) {
            fiConvert.setId(existing.getId());
            convertDetailService.remove(Wrappers.<FiConvertFileInfo>lambdaQuery()
                    .eq(FiConvertFileInfo::getFileManageId, source.getFileId()));
        }

        List<FiConvertFileInfo> convertDetailList = new ArrayList<>();
        for (FiSourceFileInfo sourceDetail : sourceDetails) {
            FiConvertFileInfo convertDetail = new FiConvertFileInfo();
            apiCompareCache.convertData(sourceDetail, convertDetail, BillTypeEnum.FILE_INFO.getCode());
            convertDetail.setCreateBy(sourceDetail.getCreateBy());
            convertDetail.setUpdateBy(sourceDetail.getUpdateBy());
            convertDetail.setId(null);
            convertDetailList.add(convertDetail);
        }

        convertService.saveOrUpdate(fiConvert);
        convertDetailService.saveOrUpdateBatch(convertDetailList);
    }

    private YgInvoicePooingVO createYgInvoicePooingVO(FiConvertFileOcrInfo convert, List<FiConvertFileInfo> details, UCN dept) {
        YgInvoicePooingVO ygInvoicePooingVO = new YgInvoicePooingVO();

        // 设置主表必填信息
        Long orgId = details.get(0).getOrgId();
        String mdmOrgCode = fiOrgConvertService.getMdmOrgCodeByOrgId(orgId);
        ygInvoicePooingVO.setUnitCode(mdmOrgCode); // 发票归属单位ID

        // 生成提交gid：yyyyMMddHHmmss+UUID去除"-"前8位
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        ygInvoicePooingVO.setSubGid(timestamp + uuid);

        // 上传人信息
        ygInvoicePooingVO.setManagerNm(details.get(0).getCreateByName()); // 上传人名称
        ygInvoicePooingVO.setHdlgDept(dept.getName()); // 上传人部门名称
        ygInvoicePooingVO.setFileSource("UDS"); // 来源文件存储系统类型
        ygInvoicePooingVO.setFromFileSystemId("uds_14508_14"); // 来源文件存储系统ID

        // 设置发票详情信息
        ygInvoicePooingVO.setInvoType(convert.getInvoiceTypeCode()); // 发票类型编码
        ygInvoicePooingVO.setInvoCode(convert.getEinvoicecode()); // 发票代码
        ygInvoicePooingVO.setInvoiceNo(convert.getEinvoicenumber()); // 发票号码
        ygInvoicePooingVO.setElecInvoiceNo(convert.getElecInvoiceNo()); // 全电发票号码

        // 开票日期处理
        if (StringUtils.isNotBlank(convert.getInvoicedate())) {
            ygInvoicePooingVO.setInvDate(convert.getInvoicedate()); // 开票日期
        }

        // 购方和销方信息
        ygInvoicePooingVO.setBuyerName(convert.getBuyername()); // 购方名称
        ygInvoicePooingVO.setBuyTxPyrNo(convert.getBuyertaxno()); // 购方税号
        ygInvoicePooingVO.setSellerName(convert.getSalername()); // 销方名称
        ygInvoicePooingVO.setSellerTin(convert.getSalertaxno()); // 销方税号

        // 金额相关信息
        ygInvoicePooingVO.setAmouInTot(convert.getInvoiceamount()); // 合计金额
        ygInvoicePooingVO.setTotalTax(convert.getTaxamount()); // 合计税额
        ygInvoicePooingVO.setTotPriTax(convert.getTotalamount()); // 价税合计
        ygInvoicePooingVO.setCheckCode(convert.getCheckcode()); // 校验码

        // 发票状态，0正常，1失控，2作废，3红冲，4异常，5-其他，6-部分红冲
        int invoStatus = 4;
        String invoiceStatus = convert.getInvoiceStatus();
        if (StringUtils.isNotEmpty(invoiceStatus)) {
            if (invoiceStatus.matches("\\d+")) {
                invoStatus = Integer.parseInt(invoiceStatus);
            } else {
                invoStatus = StringUtils.equals("Y", invoiceStatus) ? 0 : 3;
            }
        }
        ygInvoicePooingVO.setInvoStatus(invoStatus);

        // 设置开票人
        ygInvoicePooingVO.setDParty(convert.getDparty()); // 开票人

        // 设置发票明细列表
        List<FiFileOcrInfoItem> fiFileOcrInfoItems = parseFiFileOcrInfoItem(convert.getBody());

        List<YgInvoicePooingDetail> ygInvoicePooingDetails = new ArrayList<>();
        for (FiFileOcrInfoItem item : fiFileOcrInfoItems) {
            YgInvoicePooingDetail detail = new YgInvoicePooingDetail();
            detail.setUnit(item.getMeaUnits());
            detail.setUnitPrice(item.getUnPrice());
            detail.setAmount(item.getAmount());
            detail.setTaxAmount(item.getComTaxAm());
            detail.setTaxrt(item.getTaxRate());
            detail.setTaxClaCode(item.getTaxClassificationCode());
            detail.setPrjName(item.getItemName());

            ygInvoicePooingDetails.add(detail);
        }
        ygInvoicePooingVO.setInvoiceDetailList(ygInvoicePooingDetails);

        return ygInvoicePooingVO;
    }

    public List<FiFileOcrInfoItem> parseFiFileOcrInfoItem(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new ArrayList<>();
        }

        return JSON.parseObject(json, new TypeReference<List<FiFileOcrInfoItem>>() {
        });
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourceFileOcrInfo entity = this.getById(id);
            try {
                this.push(entity.getUniquecodeofinvoice());
            } catch (Exception e) {
                resultList.add(entity.getUniquecodeofinvoice()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}
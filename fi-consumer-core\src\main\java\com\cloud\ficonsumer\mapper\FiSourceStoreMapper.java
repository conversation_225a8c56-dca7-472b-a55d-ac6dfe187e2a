

package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiConvertStoreDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceStoreDTO;
import com.cloud.ficonsumer.dto.FiSourceStoreDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceStore;
import com.cloud.ficonsumer.vo.FiSourceStoreQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 采购入库源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:35
 */
@Mapper
public interface FiSourceStoreMapper extends CloudBaseMapper<FiSourceStore> {

    @Select("<script>SELECT * " +
            " FROM fi_source_store t " +
            " WHERE t.del_flag = 0" +
            "<if test=\"param.cbillid !=null and param.cbillid !=''\"> " +
            " and cbillid = #{param.cbillid} " +
            "</if> " +
            "<if test=\"param.vbillcode !=null and param.vbillcode !=''\"> " +
            " and vbillcode like CONCAT('%',#{param.vbillcode},'%') " +
            "</if> " +
            "<if test=\"param.pkOrgList != null and param.pkOrgList.size() > 0\"> " +
            "   AND pk_org in " +
            "   <foreach collection=\"param.pkOrgList\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "      #{item} " +
            "   </foreach>" +
            " </if> " +
            "<if test=\"param.groupCodeFlag !=null and param.groupCodeFlag !=''\"> " +
            " and pk_org in (select pk_org " +
            "       from fi_org_compare " +
            "       where group_code = #{param.groupCodeFlag} and del_flag =0) " +
            "</if> " +
            "<if test=\"param.pushStatus !=null and param.pushStatus !=''\"> " +
            " and push_status = #{param.pushStatus} " +
            "</if> " +
            "<if test=\"param.pushStatusList != null and param.pushStatusList.size() > 0\">"+
            " and push_status in " +
            "<foreach collection=\"param.pushStatusList\" item=\"item\" open=\"(\" separator=\",\" close=\")\">"+
            "#{item}" +
            "</foreach>" +
            "</if> " +
            "<if test=\"param.createTimeStart !=null and param.createTimeStart !=''\"> " +
            " and create_time >= #{param.createTimeStart} " +
            "</if> " +
            "<if test=\"param.createTimeEnd !=null and param.createTimeEnd !=''\"> " +
            " and #{param.createTimeEnd} >= create_time " +
            "</if> " +
            " order by create_time desc  " +
            "</script>")
    Page<FiSourceStoreDTO> beforeConvertDataPage(Page page, @Param("param") FiSourceStoreQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_source_store t " +
            "    LEFT JOIN fi_source_store_detail td ON t.cbillid = td.cbillid " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.cbillid = #{param.cbillid}" +
            "</script>")
    Page<FiSourceStoreDetailDTO> getBeforeConvertDataPage(Page page, @Param("param") FiSourceStoreQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_convert_store t " +
            "    LEFT JOIN fi_convert_store_detail td ON t.cbillid = td.cbillid " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.cbillid = #{pk}" +
            "</script>")
    Page<FiConvertStoreDetailDTO> getConvertData(Page page, @Param("pk") String pk);
}

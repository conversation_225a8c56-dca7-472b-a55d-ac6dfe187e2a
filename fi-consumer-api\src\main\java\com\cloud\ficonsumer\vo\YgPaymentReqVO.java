package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "预付/付款申请服务")
public class YgPaymentReqVO {

    @ApiModelProperty(value = "应用ID")
    private String bizappid;

    @ApiModelProperty(value = "发起单位", required = true)
    private String dwdh;

//    @ApiModelProperty(value = "部门级成本中心", required = true)
    private String deptCostCenter;

    @ApiModelProperty(value = "经办人", required = true)
    private String hdlgNmId;

    @ApiModelProperty(value = "业务类型（1：预付款，2：付款申请）", required = true)
    private int biType;

    @ApiModelProperty(value = "来源系统", required = true)
    private String outerCode;

    @ApiModelProperty(value = "前端系统单据编号", required = true)
    private String disCode;

    @ApiModelProperty(value = "采购订单", required = true)
    private String purOrdNo;

    @ApiModelProperty(value = "内部订单")
    private String inteOrdNNum;

    @ApiModelProperty(value = "事由")
    private String cause;

    @ApiModelProperty(value = "项目编码")
    private String enginProje;

    @ApiModelProperty(value = "合同编码")
    private String conNam;

    @ApiModelProperty(value = "支付比例")
    private String pyRtName;

    @ApiModelProperty(value = "预约付款时间", required = true)
    private String orderPayDate;

    @ApiModelProperty(value = "最晚付款日期")
    private String dateExpiry;

    @ApiModelProperty(value = "付款金额", required = true)
    private BigDecimal totAmoPaid;

    @ApiModelProperty(value = "付款性质", required = true)
    private String payType;

    @ApiModelProperty(value = "归口部门")
    private String conmandep;

    @ApiModelProperty(value = "费用类型")
    private String cosType;

    @ApiModelProperty(value = "明细费用类型")
    private String detCosType;

    @ApiModelProperty(value = "明细费用类型编码")
    private String detCosTypeCode;

    @ApiModelProperty(value = "资金属性")
    private String moneyAttr;

    @ApiModelProperty(value = "无纸化单据标识")
    private String docuStatus;

    @ApiModelProperty(value = "附件张数", required = true)
    private int fdzs;

    @ApiModelProperty(value = "是否退回重传标识", required = true)
    private String backTag;

    @ApiModelProperty(value = "部门分类")
    private String mainDeptType;

    @ApiModelProperty(value = "验审信息")
    private String verInfo;

    @ApiModelProperty(value = "原系统单据编号")
    private String sourBusId;

    @ApiModelProperty(value = "修改单标识", required = true)
    private String modifyBillFlag;

    @ApiModelProperty(value = "是否自动传递")
    private String autoPass;

    @ApiModelProperty(value = "发票校验号")
    private String invVerNo;

    @ApiModelProperty(value = "交易币种")
    private String tarnCurren;

    @ApiModelProperty(value = "汇率")
    private BigDecimal exchRat;

    @ApiModelProperty(value = "付款信息列表", required = true)
    private List<Payable> payabl;

    @ApiModelProperty(value = "WBS信息列表")
    private List<WBS> wbs;

    @ApiModelProperty(value = "原始凭据列表")
    private List<ImageDetail> cred;

    @ApiModelProperty(value = "审批信息列表")
    private List<ApprvDetails> apprv;

    @Data
    @ApiModel(description = "付款信息")
    public static class Payable {

        @ApiModelProperty(value = "供应商编码", required = true)
        private String supp;

        @ApiModelProperty(value = "是否民工工资账户", required = true)
        private String migrantWorkerWageAccount;

        @ApiModelProperty(value = "供应商银行账号", required = true)
        private String rAcc;

        @ApiModelProperty(value = "供应商银行账户名称", required = true)
        private String rcvrAccName;

        @ApiModelProperty(value = "供应商账户开户行", required = true)
        private String bank;

        @ApiModelProperty(value = "票据收款方账户")
        private String billRcvrAcc;

        @ApiModelProperty(value = "票据收款方账户名称")
        private String billRcvrAccName;

        @ApiModelProperty(value = "票据银行账号开户行")
        private String billPayBank;

        @ApiModelProperty(value = "本次付款金额", required = true)
        private BigDecimal amtThisPay;

        @ApiModelProperty(value = "原支付计划号")
        private String payPlanNo;

        @ApiModelProperty(value = "供应链融资方式")
        private String finMode;

        @ApiModelProperty(value = "项目名称")
        private String prjName;

        @ApiModelProperty(value = "往来台账ID")
        private String associatId;

        @ApiModelProperty(value = "供应商数字钱包账户ID")
        private String collWalId;

        @ApiModelProperty(value = "供应商数字钱包开户行ID")
        private String collWalOpenBankId;

        @ApiModelProperty(value = "预留质保金本位币")
        private BigDecimal RevDepCy;

        @ApiModelProperty(value = "本次核销预付款本位币金额")
        private BigDecimal writeOffAdvTotAmtCy;

        @ApiModelProperty(value = "费用类型")
        private String cosType;

        @ApiModelProperty(value = "明细费用类型")
        private String detCosType;

        @ApiModelProperty(value = "明细费用类型编码")
        private String detCosTypeCode;

        @ApiModelProperty(value = "部门分类")
        private String mainDeptType;

        @ApiModelProperty(value = "本次预留质保金金额")
        private BigDecimal revDep;

        @ApiModelProperty(value = "本次核销预付金额")
        private BigDecimal curwriOffAdvPmtTax;
    }

    @Data
    @ApiModel(description = "WBS信息")
    public static class WBS {

        @ApiModelProperty(value = "WBS")
        private String wbsElement;

        @ApiModelProperty(value = "预付金额")
        private BigDecimal advPmlAmt;
    }
}

package com.cloud.ficonsumer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 费用结算单实体
 */
@Data
@ApiModel(value = "费用结算单")
public class PmFeebalanceB implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String pkFeebalance;

    @ApiModelProperty(value = "费用结算明细主键")
    private String pkFeebalanceB;

    @ApiModelProperty(value = "单据号")
    private String billCode;

    @ApiModelProperty(value = "同步集成中台状态")
    private String def8;
}
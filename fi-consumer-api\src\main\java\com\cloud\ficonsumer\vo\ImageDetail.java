package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/24.
 */
@Data
public class ImageDetail {

    /**
     * 影像分类
     */
    @ApiModelProperty(value="影像分类")
    private String imageType;

    /**
     * UDS文件ID
     */
    @ApiModelProperty(value="UDS文件ID")
    private String fileResId;

    /**
     * 文件名
     */
    @ApiModelProperty(value="文件名")
    private String fileName;

    /**
     * 来源文件存储系统类型
     */
    @ApiModelProperty(value="来源文件存储系统类型")
    private String fileSource;

    /**
     * 来源文件存储系统ID
     * systemcode，固定的14508   deploycode 浙江是14，总部是99
     */
    @ApiModelProperty(value="来源文件存储系统ID")
    private String fromFileSystemId;

}

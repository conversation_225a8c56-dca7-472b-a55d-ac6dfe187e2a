package com.cloud.ficonsumer;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.entity.FiSourceSaleOrder;
import com.cloud.ficonsumer.entity.FiSourceSaleOrderDetail;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.service.FiSourceSaleOrderDetailService;
import com.cloud.ficonsumer.service.FiSourceSaleOrderService;
import com.cloud.ficonsumer.vo.FiSourceSaleOrderVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class SaleOrderIntegTest {

    @SpyBean
    private DmMapper dmMapper;

    @SpyBean
    private ApiProjectService apiProjectService;

    @SpyBean
    private FiSourceSaleOrderService fiSourceSaleOrderService;

    @SpyBean
    private FiSourceSaleOrderDetailService fiSourceSaleOrderDetailService;

    @BeforeEach
    public void initData() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);

        // 初始化 erp 库数据:  销售订单数据 (oracle 数据库)
        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator erpPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("saleorder/schema-oracle.sql"),
                    new ClassPathResource("saleorder/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(erpPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 初始化 mysql 库数据：销售订单数据 (mysql 数据库)
        DynamicDataSourceContextHolder.push("mysql");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("saleorder/schema-mysql.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    @Test
    public void testProcessSaleOrder() {
        // 1. 从 ERP 数据库中获取销售订单数据
        List<FiSourceSaleOrderVO> fiSourceSaleOrderVOS = dmMapper.listSaleOrder();

        // 2. 验证获取到的数据量
        assertEquals(3, fiSourceSaleOrderVOS.size(), "ERP 销售订单表中应存在三条数据");

        // 3. 循环处理每个销售订单
        for (FiSourceSaleOrderVO fiSourceSaleOrderVO : fiSourceSaleOrderVOS) {
            apiProjectService.processSaleOrder(fiSourceSaleOrderVO);
        }

        // 4. 从 MySQL 数据库中获取处理后的销售订单和明细数据
        List<FiSourceSaleOrder> saleOrderList = fiSourceSaleOrderService.list();
        List<FiSourceSaleOrderDetail> saleOrderDetailList = fiSourceSaleOrderDetailService.list();

        // 5. 验证MySQL数据库中的数据量
        assertEquals(3, saleOrderList.size(), "MySQL 销售订单表中应存在三条数据");
        assertEquals(3, saleOrderDetailList.size(), "MySQL 销售订单明细表中应存在三条数据");

        // 6. 再次从 ERP 数据库中获取数据，验证同步状态
        fiSourceSaleOrderVOS = dmMapper.listSaleOrder();
        for (FiSourceSaleOrderVO fiSourceSaleOrder : fiSourceSaleOrderVOS) {
            assertEquals(fiSourceSaleOrder.getVdef20(), "1", "ERP 销售订单表中接收状态已变更为1");
        }
    }
}
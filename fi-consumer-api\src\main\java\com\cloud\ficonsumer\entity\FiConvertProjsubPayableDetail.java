package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName("fi_convert_projsub_payable_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目分包应付明细转换数据详情")
public class FiConvertProjsubPayableDetail extends BaseEntity<FiConvertProjsubPayableDetail> {
    private static final long serialVersionUID = -1L;

    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value = "应付单标识")
    private String pkPayablebill;

    @ApiModelProperty(value = "应付单行标识")
    private String pkPayableitem;
}

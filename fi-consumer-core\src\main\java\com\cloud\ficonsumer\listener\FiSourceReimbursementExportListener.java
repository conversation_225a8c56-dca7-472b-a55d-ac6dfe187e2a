package com.cloud.ficonsumer.listener;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementExcelVO;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceReimbursementService;
import com.cloud.ficonsumer.vo.FiSourceReimbursementQueryVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/9.
 */
@Slf4j
public class FiSourceReimbursementExportListener extends AbstractExportListener<FiSourceReimbursementExcelVO> {

    private final FiSourceReimbursementService fiSourceReimbursementService;
    private final FiSourceReimbursementQueryVO queryDTO;
    private final Page<FiSourceReimbursementDTO> page;
    private static final int PAGE_SIZE = 5000;

    public FiSourceReimbursementExportListener(UUID id, FiSourceReimbursementService fiSourceReimbursementService,
                                               Page<FiSourceReimbursementDTO> page,
                                               FiSourceReimbursementQueryVO queryDTO) {
        super(id, "导出通用报销单查询列表");
        this.page = page;
        this.fiSourceReimbursementService = fiSourceReimbursementService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiSourceReimbursementExcelVO> doHandleData() {
        List<FiSourceReimbursementDTO> vos = new ArrayList<>();
        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiSourceReimbursementDTO> pageResult = fiSourceReimbursementService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiSourceReimbursementDTO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiSourceReimbursementDTO> pageResult;
            do {
                pageResult = fiSourceReimbursementService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }
        List<FiSourceReimbursementExcelVO> result = BeanUtil.copyToList(vos, FiSourceReimbursementExcelVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}

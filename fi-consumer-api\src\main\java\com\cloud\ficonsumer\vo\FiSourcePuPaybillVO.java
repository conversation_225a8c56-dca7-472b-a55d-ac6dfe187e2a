package com.cloud.ficonsumer.vo;

import com.cloud.ficonsumer.entity.FiSourcePuPaybill;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购付款源数据VO")
public class FiSourcePuPaybillVO extends FiSourcePuPaybill {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "接收状态")
    private String def20;

    private String billno;

    private String pkBilltype;

    private String pkPaybill;

    /**
     * 返回状态
     */
    private String returnStatusName;

    @ApiModelProperty(value = "付款类型名称")
    private String pkTradetypeName;

    @ApiModelProperty(value="供应商名称")
    private String pkSupplierName;

    @ApiModelProperty(value = "业务单据录入人")
    private String pkBilloperatorName;

    @ApiModelProperty(value = "币种")
    private String pkCurrtypeLastName;
}
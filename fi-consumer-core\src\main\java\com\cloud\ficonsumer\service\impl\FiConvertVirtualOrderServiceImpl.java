
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertVirtualOrder;
import com.cloud.ficonsumer.mapper.FiConvertVirtualOrderMapper;
import com.cloud.ficonsumer.service.FiConvertVirtualOrderService;
import org.springframework.stereotype.Service;

/**
 * 虚拟采购订单转换数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:10
 */
@Service
public class FiConvertVirtualOrderServiceImpl extends ServiceImpl<FiConvertVirtualOrderMapper, FiConvertVirtualOrder> implements FiConvertVirtualOrderService {

}

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/17.
 */
@ApiModel("响应信息主体")
public class YgResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("返回标记：成功标记=0，失败标记=1")
    private String code;
    @ApiModelProperty("返回信息")
    private String message;
    @ApiModelProperty("是否加密")
    private boolean en;
    @ApiModelProperty("数据")
    private T data;

    public static <T> YgResult<T> ok() {
        return restResult(null, "000000", (String)null);
    }

    public static <T> YgResult<T> ok(T data) {
        return restResult(data, "000000", (String)null);
    }

    public static <T> YgResult<T> ok(T data, String message) {
        return restResult(data, "000000", message);
    }

    public static <T> YgResult<T> failed() {
        return restResult(null, "000001", (String)null);
    }

    public static <T> YgResult<T> failed(String message) {
        return restResult(null, "000001", message);
    }

    public static <T> YgResult<T> failed(T data) {
        return restResult(data, "000001", (String)null);
    }

    public static <T> YgResult<T> failed(T data, String message) {
        return restResult(data, "000001", message);
    }

    private static <T> YgResult<T> restResult(T data, String code, String message) {
        YgResult<T> apiResult = new YgResult();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setmessage(message);
        return apiResult;
    }

    public static <T> YgResult.RBuildeYgResult<T> buildeYgResult() {
        return new YgResult.RBuildeYgResult();
    }

    public String toString() {
        return "YgResult(code=" + this.getCode() + ", message=" + this.getmessage() + ", en=" + this.isEn() + ", data=" + this.getData() + ")";
    }

    public YgResult() {
    }

    public YgResult(String code, String message, boolean en, T data) {
        this.code = code;
        this.message = message;
        this.en = en;
        this.data = data;
    }

    public String getCode() {
        return this.code;
    }

    public YgResult<T> setCode(String code) {
        this.code = code;
        return this;
    }

    public String getmessage() {
        return this.message;
    }

    public YgResult<T> setmessage(String message) {
        this.message = message;
        return this;
    }

    public boolean isEn() {
        return this.en;
    }

    public YgResult<T> setEn(boolean en) {
        this.en = en;
        return this;
    }

    public T getData() {
        return this.data;
    }

    public YgResult<T> setData(T data) {
        this.data = data;
        return this;
    }

    public static class RBuildeYgResult<T> {
        private String code;
        private String message;
        private boolean en;
        private T data;

        RBuildeYgResult() {
        }

        public YgResult.RBuildeYgResult<T> code(String code) {
            this.code = code;
            return this;
        }

        public YgResult.RBuildeYgResult<T> message(String message) {
            this.message = message;
            return this;
        }

        public YgResult.RBuildeYgResult<T> en(boolean en) {
            this.en = en;
            return this;
        }

        public YgResult.RBuildeYgResult<T> data(T data) {
            this.data = data;
            return this;
        }

        public YgResult<T> build() {
            return new YgResult(this.code, this.message, this.en, this.data);
        }

        public String toString() {
            return "YgResult.RBuildeYgResult(code=" + this.code + ", message=" + this.message + ", en=" + this.en + ", data=" + this.data + ")";
        }
    }
    
}

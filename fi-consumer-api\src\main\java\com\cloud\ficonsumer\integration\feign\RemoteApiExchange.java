package com.cloud.ficonsumer.integration.feign;

import com.cloud.cloud.common.core.constant.SecurityConstants;
import com.cloud.ficonsumer.integration.dto.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(contextId = "remoteApiExchange", value = "apiexchange-provider")
public interface RemoteApiExchange {

    @PostMapping("/exchange/inner")
    String exchange(@RequestBody Param params, @RequestHeader(SecurityConstants.FROM) String from);
}

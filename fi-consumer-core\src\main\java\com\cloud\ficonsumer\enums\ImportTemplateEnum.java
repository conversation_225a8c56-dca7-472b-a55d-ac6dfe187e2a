package com.cloud.ficonsumer.enums;

/**
 * 文件模板类型
 *
 * <AUTHOR>
 * @date 2021/3/16 14:50
 */
public enum ImportTemplateEnum {

    COMPARE_IMPORT("compare_import_template", "对照表导入模板"),
   ;

    private String index;
    private String name;

    ImportTemplateEnum(String index, String name) {
        this.index = index;
        this.name = name;
    }

    public static String getName(String index) {
        for (ImportTemplateEnum item : ImportTemplateEnum.values()) {
            if (item.getIndex().equals(index)) {
                return item.name;
            }
        }
        return null;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

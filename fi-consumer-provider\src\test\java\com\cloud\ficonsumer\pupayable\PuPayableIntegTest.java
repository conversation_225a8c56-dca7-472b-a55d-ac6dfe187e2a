package com.cloud.ficonsumer.pupayable;

import com.cloud.ficonsumer.dto.FiSourcePuPayableQueryDTO;
import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import com.cloud.ficonsumer.entity.FiSourcePuPayableDetail;
import com.cloud.ficonsumer.vo.FiSourcePuPayableVO;
import org.junit.jupiter.api.Test;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class PuPayableIntegTest extends BasePuPayableTest {

    @Test
    public void testProcessPuPayable() {
        List<FiSourcePuPayableVO> fiSourcePuPayableList = dmMapper.listPuPayable(new FiSourcePuPayableQueryDTO());
        assertEquals(3, fiSourcePuPayableList.size(), "ERP采购应付表 中应存在三条数据");

        // 在测试方法中调用同步
        syncErpToMysql();

        List<FiSourcePuPayable> list = fiSourcePuPayableService.list();
        List<FiSourcePuPayableDetail> detailList = fiSourcePuPayableDetailService.list();

        assertEquals(3, list.size(), "MySQL采购应付 中应存在三条数据");
        assertEquals(3, detailList.size(), "MySQL采购应付明细 表中应存在三条数据");

        fiSourcePuPayableList = dmMapper.listPuPayable(new FiSourcePuPayableQueryDTO());
        assertEquals(0, fiSourcePuPayableList.size(), "ERP采购应付表 已完成同步到FiConsumer");
    }
}
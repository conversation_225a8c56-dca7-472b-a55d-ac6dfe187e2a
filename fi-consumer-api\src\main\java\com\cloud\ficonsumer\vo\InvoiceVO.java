package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/24.
 */
@Data
public class InvoiceVO {


    /**
     * 发票类型
     */
    @ApiModelProperty(value="发票类型")
    private String invoType;

    /**
     * 发票代码
     */
    @ApiModelProperty(value="发票代码")
    private String invoCode;

    /**
     * 开票日期
     */
    @ApiModelProperty(value="开票日期")
    private String invDate;

    /**
     * 发票号码
     */
    @ApiModelProperty(value="发票号码")
    private String invoiceNo;

    /**
     * 销售方名称
     */
    @ApiModelProperty(value="销售方名称")
    private String sellerName;

    /**
     * 发票金额
     */
    @ApiModelProperty(value="发票金额")
    private BigDecimal invoiceVal;

    /**
     * 发票税额
     */
    @ApiModelProperty(value="发票税额")
    private BigDecimal invTax;

    /**
     * 发票金额不含税
     */
    @ApiModelProperty(value="发票金额不含税")
    private BigDecimal invAmtWt;

}

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/20.
 */
@Data
public class YgReceivableVO {

    /**
     * 详情
     */
    @ApiModelProperty(value="详情")
    private List<YgReceivableDetailVO> settDet;

    /**
     * 详情
     */
    @ApiModelProperty(value="详情")
    private List<AdvClOffVO> advClOff;

    /**
     * 发票信息
     */
    @ApiModelProperty(value="发票信息")
    private List<InvoiceVO> invDet;

    /**
     * 影像信息
     */
    @ApiModelProperty(value="影像信息")
    private List<ImageDetail> sub2;

    /**
     * 应用ID
     */
    @ApiModelProperty(value="应用ID")
    private String bizappid;

    /**
     * 发起单位
     */
    @ApiModelProperty(value="发起单位")
    private String dwdh;

    /**
     * 经办人
     */
    @ApiModelProperty(value="经办人")
    private String hdlgNmId;

    /**
     * 经办部门
     */
    @ApiModelProperty(value="经办部门")
    private String hdlgDept;

    /**
     * 收款类型
     */
    @ApiModelProperty(value="收款类型")
    private String collecType;

    /**
     * 业务类型
     */
    @ApiModelProperty(value="业务类型")
    private int biType;

    /**
     * 来源系统
     */
    @ApiModelProperty(value="来源系统")
    private String outerCode;

    /**
     * 前端系统单据编号
     */
    @ApiModelProperty(value="前端系统单据编号")
    private String disCode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value="客户名称")
    private String cusName;

    /**
     * 收入类型
     */
    @ApiModelProperty(value="收入类型")
    private String incTyp;

    /**
     * 明细业务事项
     */
    @ApiModelProperty(value="明细业务事项")
    private String detCosType;

    /**
     * 明细业务事项编码
     */
    @ApiModelProperty(value="明细业务事项编码")
    private String detCosTypeCode;

    /**
     * 合同名称
     */
    @ApiModelProperty(value="合同名称")
    private String conNam;

    /**
     * 项目编码
     */
    @ApiModelProperty(value="项目编码")
    private String prjCode;

    /**
     * 内部订单
     */
    @ApiModelProperty(value="内部订单")
    private String intOrder;

    /**
     * WBS编码
     */
    @ApiModelProperty(value="WBS编码")
    private String wbsCod;

    /**
     * 事由
     */
    @ApiModelProperty(value="事由")
    private String cause;

    /**
     * 发票类型
     */
    @ApiModelProperty(value="发票类型")
    private String invoType;

    /**
     * 是否在线开票
     */
    @ApiModelProperty(value="是否在线开票")
    private String invSign;

    /**
     * 开票类型
     */
    @ApiModelProperty(value="开票类型")
    private String invongType;

    /**
     * 红冲类型
     */
    @ApiModelProperty(value="红冲类型")
    private String funcType;

    /**
     * 交易币种
     */
    @ApiModelProperty(value="交易币种")
    private String tranCurren;

    /**
     * 购方单位
     */
    @ApiModelProperty(value="购方单位")
    private String receivingu;

    /**
     * 是否显示购方信息
     */
    @ApiModelProperty(value="是否显示购方信息")
    private String isshowbuybankinfo;

    /**
     * 是否显示销方信息
     */
    @ApiModelProperty(value="是否显示销方信息")
    private String isshowsalebankinfo;

    /**
     * 应收金额
     */
    @ApiModelProperty(value="应收金额")
    private String totalIncm;

    /**
     * 无纸化单据标识
     */
    @ApiModelProperty(value="无纸化单据标识")
    private String docuStatus;

    /**
     * 附件张数
     */
    @ApiModelProperty(value="附件张数")
    private String fdzs;

    /**
     * 是否退回重传标识
     */
    @ApiModelProperty(value="是否退回重传标识")
    private String backTag;

}

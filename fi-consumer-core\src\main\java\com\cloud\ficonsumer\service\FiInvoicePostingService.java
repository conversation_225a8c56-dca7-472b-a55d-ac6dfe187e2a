

package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiInvoicePostingDTO;
import com.cloud.ficonsumer.dto.FiInvoicePostingItemDTO;
import com.cloud.ficonsumer.dto.YgInvoiceStatus;
import com.cloud.ficonsumer.dto.YgRequestInvoicePosting;
import com.cloud.ficonsumer.entity.FiInvoicePosting;
import com.cloud.ficonsumer.vo.FiInvoicePostingQueryVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;

import java.util.List;

/**
 * 采购应付源数据详情
 *
 * <AUTHOR>
 * @date 2025-04-22 10:04:33
 */
public interface FiInvoicePostingService extends IService<FiInvoicePosting> {

    List<YgInvoiceStatus> saveInvoicePosting(List<YgRequestInvoicePosting> list);

    boolean pushHistory(String busibillno);

    Page<FiInvoicePostingDTO> beforeConvertDataPage(Page page, FiInvoicePostingQueryVO queryVO);

    Page<FiInvoicePostingItemDTO> getBeforeConvertDataPage(Page page, FiInvoicePostingQueryVO queryVO);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}

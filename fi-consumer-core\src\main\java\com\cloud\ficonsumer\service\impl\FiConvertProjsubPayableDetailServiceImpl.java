package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertProjsubPayableDetail;
import com.cloud.ficonsumer.mapper.FiConvertProjsubPayableDetailMapper;
import com.cloud.ficonsumer.service.FiConvertProjsubPayableDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiConvertProjsubPayableDetailServiceImpl extends ServiceImpl<FiConvertProjsubPayableDetailMapper, FiConvertProjsubPayableDetail> implements FiConvertProjsubPayableDetailService {

}



package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织对照关系
 *
 * <AUTHOR>
 * @date 2025-04-27 10:55:07
 */
@Data
@TableName("fi_org_compare")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "组织对照关系")
public class FiOrgCompare extends BaseEntity<FiOrgCompare> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 业+组织ID
     */
    @ApiModelProperty(value="业+组织ID")
    private Long orgId;

    /**
     * 业+组织编码
     */
    @ApiModelProperty(value="业+组织编码")
    private Long upmsCode;

    /**
     * 业+组织名称
     */
    @ApiModelProperty(value="业+组织名称")
    private Long upmsName;

    /**
     * 集团主键
     */
    @ApiModelProperty(value="集团主键")
    private String pkGroup;

    /**
     * 集团编码
     */
    @ApiModelProperty(value="集团编码")
    private String groupCode;

    /**
     * 集团名称
     */
    @ApiModelProperty(value="集团名称")
    private String groupName;

    /**
     * 组织主键
     */
    @ApiModelProperty(value="组织主键")
    private String pkOrg;

    /**
     * 组织编码
     */
    @ApiModelProperty(value="组织编码")
    private String orgCode;

    /**
     * 组织名称
     */
    @ApiModelProperty(value="组织名称")
    private String orgName;

    /**
     * MDM组织编码
     */
    @ApiModelProperty(value="MDM组织编码")
    private String mdmOrgCode;

    /**
     * 组织级别 0单位 1集团 2顶部组织
     */
    @ApiModelProperty(value="组织级别 0单位 1集团 2顶部组织")
    private String level;

    /**
     * 排序
     */
    @ApiModelProperty(value="排序")
    private Integer sort;

}

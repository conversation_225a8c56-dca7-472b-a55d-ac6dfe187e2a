
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceAccountingDetail;
import com.cloud.ficonsumer.entity.FiSourceAccountingDocfree;
import com.cloud.ficonsumer.mapper.FiSourceAccountingDetailMapper;
import com.cloud.ficonsumer.mapper.FiSourceAccountingDocfreeMapper;
import com.cloud.ficonsumer.service.FiSourceAccountingDetailService;
import com.cloud.ficonsumer.service.FiSourceAccountingDocfreeService;
import org.springframework.stereotype.Service;

/**
 * 采购入库详情数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:41
 */
@Service
public class FiSourceAccountingDocfreeServiceImpl extends ServiceImpl<FiSourceAccountingDocfreeMapper, FiSourceAccountingDocfree> implements FiSourceAccountingDocfreeService {

}

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/24.
 */
@Data
public class PayDetVO {

    /**
     * 收款人编码
     */
    @ApiModelProperty(value="收款人编码")
    private String payeeCod;

    /**
     * 付款金额
     */
    @ApiModelProperty(value="付款金额")
    private BigDecimal payAmo;

    /**
     * 收款方账户
     */
    @ApiModelProperty(value="收款方账户")
    private String rcvrAcc;

    /**
     * 票据收款方账户
     */
    @ApiModelProperty(value="票据收款方账户")
    private String billRcvrAcc;

    /**
     * 明细付款业务类型
     */
    @ApiModelProperty(value="明细付款业务类型")
    private String detPayBizType;

    /**
     * 支付方式
     */
    @ApiModelProperty(value="支付方式")
    private String pyMethod;

    /**
     * 起租编号
     */
    @ApiModelProperty(value="起租编号")
    private String hiredCode;

    /**
     * 付款方银行账户
     */
    @ApiModelProperty(value="付款方银行账户")
    private String pyrBkAcct;

}

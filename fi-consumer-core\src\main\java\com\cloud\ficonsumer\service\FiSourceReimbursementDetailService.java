

package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.entity.FiSourceReimbursementDetail;
import com.cloud.ficonsumer.vo.BillStatisticsVO;

/**
 * 通用报销单详情源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:40
 */
public interface FiSourceReimbursementDetailService extends IService<FiSourceReimbursementDetail> {

    /**
     * 统计某一集团下的组织的成功率
     * @param pkGroup
     * @return
     */
    BillStatisticsVO getStatisticsByGroup(String pkGroup);
}

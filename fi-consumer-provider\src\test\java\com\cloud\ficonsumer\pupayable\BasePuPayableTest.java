package com.cloud.ficonsumer.pupayable;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.ficonsumer.BaseIntegrationTest;
import com.cloud.ficonsumer.dto.FiSourcePuPayableQueryDTO;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.service.FiSourcePuPayableDetailService;
import com.cloud.ficonsumer.service.FiSourcePuPayableService;
import com.cloud.ficonsumer.vo.FiSourcePuPayableVO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.Statement;
import java.util.List;

public abstract class BasePuPayableTest extends BaseIntegrationTest {

    @SpyBean
    protected DmMapper dmMapper;

    @SpyBean
    protected ApiProjectService apiProjectService;

    @SpyBean
    protected FiSourcePuPayableService fiSourcePuPayableService;

    @SpyBean
    protected FiSourcePuPayableDetailService fiSourcePuPayableDetailService;

    protected DataSource dynamicDataSource;

    @BeforeEach
    public void baseInitData() {
        dynamicDataSource = SpringContextHolder.getBean(DataSource.class);
        initErpDatabase();
        initFiConsumerDatabase();
    }

    protected void initErpDatabase() {
        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator erpPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("basic/schema-oracle.sql"),
                    new ClassPathResource("basic/data-oracle.sql"),
                    new ClassPathResource("pupayable/schema-oracle.sql"),
                    new ClassPathResource("pupayable/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(erpPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    protected void initFiConsumerDatabase() {
        DynamicDataSourceContextHolder.push("apiexchange");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("pupayable/schema-mysql.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    protected void syncErpToMysql() {
        List<FiSourcePuPayableVO> fiSourcePuPayableList = dmMapper.listPuPayable(new FiSourcePuPayableQueryDTO());
        for (FiSourcePuPayableVO fiSourcePuPayable : fiSourcePuPayableList) {
            apiProjectService.processPuPayable(fiSourcePuPayable);
        }
    }

    @AfterEach
    public void baseCleanData() {
        DynamicRoutingDataSource dynamicDataSource = (DynamicRoutingDataSource) SpringContextHolder.getBean(DataSource.class);
        cleanDatabase(dynamicDataSource.getDataSource("erp"));
        cleanDatabase(dynamicDataSource.getDataSource("apiexchange"));
    }

    private void cleanDatabase(DataSource dataSource) {
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理数据库失败", e);
        }
    }
}
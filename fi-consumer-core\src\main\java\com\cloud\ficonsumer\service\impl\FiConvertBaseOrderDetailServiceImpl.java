
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertBaseOrderDetail;
import com.cloud.ficonsumer.mapper.FiConvertBaseOrderDetailMapper;
import com.cloud.ficonsumer.service.FiConvertBaseOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * 订单源数据转换详情表
 *
 * <AUTHOR>
 * @date 2025-06-03 11:10:12
 */
@Service
public class FiConvertBaseOrderDetailServiceImpl extends ServiceImpl<FiConvertBaseOrderDetailMapper, FiConvertBaseOrderDetail> implements FiConvertBaseOrderDetailService {

}

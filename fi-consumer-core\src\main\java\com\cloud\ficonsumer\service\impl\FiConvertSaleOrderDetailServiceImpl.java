package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertSaleOrderDetail;
import com.cloud.ficonsumer.mapper.FiConvertSaleOrderDetailMapper;
import com.cloud.ficonsumer.service.FiConvertSaleOrderDetailService;
import org.springframework.stereotype.Service;

@Service
public class FiConvertSaleOrderDetailServiceImpl extends ServiceImpl<FiConvertSaleOrderDetailMapper, FiConvertSaleOrderDetail> implements FiConvertSaleOrderDetailService {

}

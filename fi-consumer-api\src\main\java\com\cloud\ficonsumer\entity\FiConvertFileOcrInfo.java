package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "进项发票转换数据详情")
@TableName("fi_convert_file_ocr_info")
public class FiConvertFileOcrInfo extends BaseEntity<FiConvertFileOcrInfo> {

    @TableId
    @ApiModelProperty(value = "主键")
    private String id;

    @ApiModelProperty(value = "FileManage系統中表主键")
    private String fileManageId;

    @ApiModelProperty(value = "发票ID")
    private String einvoiceid;

    @ApiModelProperty(value = "发票唯一标识")
    private String uniquecodeofinvoice;

    @ApiModelProperty(value = "数据插入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createtime;

    @ApiModelProperty(value = "公司代码")
    private String bukrs;

    @ApiModelProperty(value = "发票号码")
    private String einvoicenumber;

    @ApiModelProperty(value = "发票代码")
    private String einvoicecode;

    @ApiModelProperty(value = "开票日期")
    private String invoicedate;

    @ApiModelProperty(value = "发票类型")
    private String invoiceTypeCode;

    @ApiModelProperty(value = "校验码")
    private String checkcode;

    @ApiModelProperty(value = "销方名称")
    private String salername;

    @ApiModelProperty(value = "销方税号")
    private String salertaxno;

    @ApiModelProperty(value = "销方地址、电话")
    private String saleraddressphone;

    @ApiModelProperty(value = "销方开户行及账号")
    private String saleraccount;

    @ApiModelProperty(value = "购方名称")
    private String buyername;

    @ApiModelProperty(value = "购方税号")
    private String buyertaxno;

    @ApiModelProperty(value = "购方地址、电话")
    private String buyeraddressphone;

    @ApiModelProperty(value = "购方开户行及账号")
    private String buyeraccount;

    @ApiModelProperty(value = "发票金额(不含税)")
    private String invoiceamount;

    @ApiModelProperty(value = "发票税额")
    private String taxamount;

    @ApiModelProperty(value = "价税合计")
    private String totalamount;

    @ApiModelProperty(value = "dt")
    private String dt;

    @ApiModelProperty(value = "文件id")
    private Long fileId;

    @ApiModelProperty(value = "同步状态")
    private String syncStatus;

    @ApiModelProperty(value = "全电发票号码")
    private String elecInvoiceNo;

    @ApiModelProperty(value = "发票状态")
    private String invoiceStatus;

    @ApiModelProperty(value = "开票人")
    private String dparty;

    @ApiModelProperty(value = "税收分类编码")
    private String taxCalCode;

    @ApiModelProperty(value = "特殊政策标识 0：否 1：是")
    private Integer specialPolicySign;

    @ApiModelProperty(value = "零税率标识")
    private Integer taxRateFlag;

    @ApiModelProperty(value = "发票明细数据(JSON)")
    private String body;

    @ApiModelProperty(value = "发票使用单位")
    private String unitCode;

    @ApiModelProperty("部门主键")
    private String pkDept;

    @ApiModelProperty(value = "集成状态")
    private String integrationStatus;

    @ApiModelProperty(value = "4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败")
    private Integer pushStatus;

    @ApiModelProperty(value = "推送成功失败信息")
    private String pushMsg;

    @ApiModelProperty(value = "返回同步消息")
    private String returnMsg;

    @ApiModelProperty(value = "返回同步状态")
    private String returnStatus;

    @ApiModelProperty(value = "重试次数")
    private Long tryNumber;

    @ApiModelProperty(value = "0 未就绪 1 就绪")
    private Long readiness;
}
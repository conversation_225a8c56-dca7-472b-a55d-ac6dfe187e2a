package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiSourceProjEstiPayableDetailQueryDTO;
import com.cloud.ficonsumer.dto.FiSourceProjEstiPayableQueryDTO;
import com.cloud.ficonsumer.entity.FiSourceProjEstiPayable;
import com.cloud.ficonsumer.vo.FiConvertProjEstiPayableDetailVO;
import com.cloud.ficonsumer.vo.FiConvertProjEstiPayableVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceProjEstiPayableDetailVO;
import com.cloud.ficonsumer.vo.FiSourceProjEstiPayableVO;

import java.util.List;

public interface FiSourceProjEstiPayableService extends IService<FiSourceProjEstiPayable> {

    /**
     * 推送数据
     */
    boolean push(String pkEstipayablebill);

    /**
     * 转换前分页查询
     */
    Page<FiSourceProjEstiPayableVO> beforeConvertDataPage(Page<FiSourceProjEstiPayableVO> page, FiSourceProjEstiPayableQueryDTO queryDTO);

    /**
     * 转换前数据详情查询
     */
    Page<FiSourceProjEstiPayableDetailVO> getBeforeConvertDataPage(Page<FiSourceProjEstiPayableDetailVO> page, FiSourceProjEstiPayableDetailQueryDTO queryDTO);

    /**
     * 获取转换后主表数据
     */
    List<FiConvertProjEstiPayableVO> getConvertList(String pkEstipayablebill);

    /**
     * 获取转换后详情分页数据
     */
    Page<FiConvertProjEstiPayableDetailVO> getConvertData(Page<FiConvertProjEstiPayableDetailVO> page, String pkEstipayablebill);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}

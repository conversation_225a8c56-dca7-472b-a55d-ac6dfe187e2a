package com.cloud.ficonsumer.factory;

import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.service.BillStatisticsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class BillStatisticsFactory {
    
    private final List<BillStatisticsService> statisticsServices;
    private final Map<BillTypeEnum, BillStatisticsService> serviceMap = new EnumMap<>(BillTypeEnum.class);
    private BillStatisticsService defaultService;

    @PostConstruct
    public void init() {
        statisticsServices.forEach(service -> {
            if (service.getSupportedBillType() != null) {
                serviceMap.put(service.getSupportedBillType(), service);
            } else {
                defaultService = service;
            }
        });
    }

    public BillStatisticsService getService(BillTypeEnum billType) {
        return serviceMap.getOrDefault(billType, defaultService);
    }
}
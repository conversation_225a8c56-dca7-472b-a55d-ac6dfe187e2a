package com.cloud.ficonsumer.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceSaleOrderDetail;
import com.cloud.ficonsumer.mapper.FiSourceSaleOrderDetailMapper;
import com.cloud.ficonsumer.service.FiSourceSaleOrderDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourceSaleOrderDetailServiceImpl extends ServiceImpl<FiSourceSaleOrderDetailMapper, FiSourceSaleOrderDetail> implements FiSourceSaleOrderDetailService {

}
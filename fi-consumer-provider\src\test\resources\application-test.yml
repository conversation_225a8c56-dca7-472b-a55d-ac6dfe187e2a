jasypt:
  encryptor:
    password: cloudcloudcloudx
    bean: sm4encraypt
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      remoteApiExchange:
        connectTimeout: 1500000
        readTimeout: 1500000
security:
  oauth2:
    client:
      ignore-urls:
        - /error
        - /actuator/**
      client-id: groupit
      client-secret: groupit
      # user-authorization-uri: http://*************:3000/oauth/authorize
      # access-token-uri: http://*************:3000/oauth/token
      scope: server
    # resource:
    #   loadBalanced: true
    #   token-info-uri: http://cloud-auth/oauth/check_token
# 数据源
spring:
  redis:
    host: 127.0.0.1
    port: 6379
  datasource:
    dynamic:
      druid:
        initialSize: 5
        minIdle: 5
        maxActive: 20
        maxWait: 60000
        filters: stat
      primary: apiexchange
      strict: false
      datasource:
        # 模拟 MySQL 数据源（可以直接使用 H2 的默认模式）
        apiexchange:
          driver-class-name: org.h2.Driver
          url: jdbc:h2:mem:mysql;MODE=MySQL;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
          username: sa
          password:
        # 模拟 Oracle 数据源（可以直接使用 H2= 的 Oracle 模式）
        erp:
          driver-class-name: org.h2.Driver
          url: jdbc:h2:mem:oracle;MODE=Oracle;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=true
          username: sa
          password:
        upms:
          driver-class-name: org.h2.Driver
          url: jdbc:h2:mem:mysql;MODE=MySQL;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
          username: sa
          password:
        filemanage:
          driver-class-name: org.h2.Driver
          url: jdbc:h2:mem:filemanage;MODE=MySQL;DB_CLOSE_DELAY=-1;DATABASE_TO_UPPER=false
          username: sa
          password:
  # Logger Config
logging:
  level:
    com.alibaba.nacos.client.naming: warn
minio:
  url: http://*************:9000
  access-key: admin
  secret-key: ${MINIO-PWD:edcrfv@34}
oss:
  enable: true
  endpoint: http://*************:9000
  accessKey: admin
  secretKey: edcrfv@34
  pathStyleAccess: true
xxl:
  job:
    enabled: true
    admin:
      address: http://*************:30080/xxl-job-admin
      addresses: http://*************:30080/xxl-job-admin
      access-token: a152022f-7c85-498b-9759-200329de948h
    executor:
      app-name: fi-cal
      appname: fi-cal
      accessToken: a152022f-7c85-498b-9759-200329de948h
mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  configuration-properties:
    blobType: BLOB
    boolValue: TRUE
    prefix:
  global-config:
    banner: false
    db-config:
      id-type: auto
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
cloud:
  mq:
    admin:
      address: http://***********:28088/capi/apiexchange/mq
    accessToken: zzzzzzzzzzzzzzzzzzzzzzzzssssssssssssss123s

flowable:
  activityFontName: \u5B8B\u4F53
  labelFontName: \u5B8B\u4F53
  annotationFontName: \u5B8B\u4F53
  #关闭定时任务JOB
  async-executor-activate: false
  database-schema-update: true
  common:
    app:
      idm-url: localhost:11111
yg:
  enable: true
  ak: 7870b7ce8b33217d1ad86cddfab0fc4aa1919da2b53e45e176ca6e66d5d21341
  sk: 7870b7ce8b33217d1ad86cddfab0fc4a4c71a4c8a5880b74c9e7451c518a226c
  loginOrgCode: ?ecp.mapp=true&loginOrgCode=00003010
  gatewayHost: *************:18446
uds:
  apiServiceUrl: http://*************:18082
  accessKey: CkMRfj8zpk8qEZnL
  secretKey: OnBULx97HcONVyhUQDlS6Oz9
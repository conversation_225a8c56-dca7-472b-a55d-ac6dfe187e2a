package com.cloud.ficonsumer.consumers;

import com.cloud.apiexchange.client.consumer.IMqConsumer;
import com.cloud.apiexchange.client.consumer.MqResult;
import com.cloud.apiexchange.client.consumer.annotation.MqConsumer;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.service.FiSourceProjEstiPayableService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.cloud.ficonsumer.enums.Constants.TOPIC_PROJ_ESTI_PAYABLE_TRANS;

@Slf4j
@MqConsumer(topic = TOPIC_PROJ_ESTI_PAYABLE_TRANS, group = Constants.Group.group, transaction = false)
@Component
@AllArgsConstructor
public class ProjEstiPayablePushConsumer implements IMqConsumer {

    private FiSourceProjEstiPayableService fiSourceProjEstiPayableService;

    @Override
    public MqResult consume(String pkEstipayablebill) {
        LogUtil.info(log, "项目暂估应付处理开始:", pkEstipayablebill);
        try {
            fiSourceProjEstiPayableService.push(pkEstipayablebill);
        } catch (Exception e) {
            LogUtil.error(log, "项目暂估应付处理失败:", e);
            return new MqResult(MqResult.FAIL_CODE, AdminUtils.truncateString(e.getMessage(), 1000));
        }
        return new MqResult(MqResult.SUCCESS_CODE, "项目暂估应付处理成功");
    }
}

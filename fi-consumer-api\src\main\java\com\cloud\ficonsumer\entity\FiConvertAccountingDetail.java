

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 会计凭证详情数据转换详情表
 *
 */
@Data
@TableName("fi_convert_accounting_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "会计凭证详情数据转换详情表")
public class FiConvertAccountingDetail extends BaseEntity<FiConvertAccountingDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 凭证主键
     */
    @ApiModelProperty(value="凭证主键")
    private String pkVoucher;

    /**
     * 凭证编码
     */
    @ApiModelProperty(value="凭证编码")
    private Integer num;

    /**
     * 借贷标识
     */
    @ApiModelProperty(value="借贷标识")
    private String crde;

    /**
     * 分录序号
     */
    @ApiModelProperty(value="分录序号")
    private String flxh;

    /**
     * 科目
     */
    @ApiModelProperty(value="科目")
    private String pkAccasoa;

    /**
     * 原币
     */
    @ApiModelProperty(value="原币")
    private BigDecimal amount;

    /**
     * 业务日期
     */
    @ApiModelProperty(value="业务日期")
    private String busidate;

    /**
     * 辅助核算
     */
    @ApiModelProperty(value="辅助核算")
    private String assid;

    /**
     * 分录主键
     */
    @ApiModelProperty(value="分录主键")
    private String pkDetail;
}

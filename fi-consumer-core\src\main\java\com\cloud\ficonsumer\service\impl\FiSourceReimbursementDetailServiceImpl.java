
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceReimbursementDetail;
import com.cloud.ficonsumer.mapper.FiSourceReimbursementDetailMapper;
import com.cloud.ficonsumer.service.FiSourceReimbursementDetailService;
import com.cloud.ficonsumer.vo.BillStatisticsVO;
import org.springframework.stereotype.Service;

/**
 * 通用报销单详情源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:40
 */
@Service
public class FiSourceReimbursementDetailServiceImpl extends ServiceImpl<FiSourceReimbursementDetailMapper, FiSourceReimbursementDetail> implements FiSourceReimbursementDetailService {

    /**
     * 统计某一集团下的组织的成功率
     * @param pkGroup
     * @return
     */
    @Override
    public BillStatisticsVO getStatisticsByGroup(String pkGroup) {
        return this.baseMapper.getStatisticsByGroup(pkGroup);
    }
}

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/5 15:12
 */
@Data
@ApiModel(value = "excel导入状态实体")
public class ExcelImportStatusVO implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(value = "校验名字")
    private String checkName;
    @ApiModelProperty(value = "校验数")
    private Integer checkNum = 0;
    @ApiModelProperty(value = "总数")
    private Integer sum = 0;
    @ApiModelProperty(value = "处理数")
    private Integer handle = 0;
    @ApiModelProperty(value = "错误信息")
    private List<String> errorMsg = new ArrayList<>();
    @ApiModelProperty(value = "是否结束")
    private Boolean isFinish = false;
    @ApiModelProperty(value = "是否成功")
    private Boolean result = false;
}

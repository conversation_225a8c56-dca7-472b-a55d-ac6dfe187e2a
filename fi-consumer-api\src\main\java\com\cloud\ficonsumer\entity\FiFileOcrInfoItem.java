package com.cloud.ficonsumer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "进项发票发票明细项")
public class FiFileOcrInfoItem {

    @ApiModelProperty(value = "商品名称")
    private String ItemName;

    @ApiModelProperty(value = "规格型号")
    private String SpecMod;

    @ApiModelProperty(value = "计量单位")
    private String MeaUnits;

    @ApiModelProperty(value = "数量")
    private String Quantity;

    @ApiModelProperty(value = "单价")
    private String UnPrice;

    @ApiModelProperty(value = "金额")
    private String Amount;

    @ApiModelProperty(value = "税率")
    private String TaxRate;

    @ApiModelProperty(value = "税额")
    private String ComTaxAm;

    @ApiModelProperty(value = "含税金额")
    private String TotaltaxIncludedAmount;

    @ApiModelProperty(value = "税收分类编码")
    private String TaxClassificationCode;
}

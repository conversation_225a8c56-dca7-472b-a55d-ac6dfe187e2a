package com.cloud.ficonsumer.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.FiSourceProjEstiPayableQueryDTO;
import com.cloud.ficonsumer.dto.FiSourceProjEstiPayableDetailQueryDTO;
import com.cloud.ficonsumer.entity.FiSourceProjEstiPayable;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceProjEstiPayableService;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiConvertProjEstiPayableVO;
import com.cloud.ficonsumer.vo.FiConvertProjEstiPayableDetailVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceProjEstiPayableVO;
import com.cloud.ficonsumer.vo.FiSourceProjEstiPayableDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目预估应付源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-07 15:52:38
 */
@RestController
@AllArgsConstructor
@RequestMapping("/projEstiPayable")
@Api(value = "projEstiPayable", tags = "项目预估应付源数据主表管理")
public class FiSourceProjEstiPayableController {

    private final FiSourceProjEstiPayableService fiSourceProjEstiPayableService;

    /**
     * 报错重推
     */
    @ApiOperation(value = "报错重推", notes = "报错重推")
    @GetMapping("/pushAgain/{pkEstipayablebill}")
    public R<Boolean> pushAgain(@PathVariable("pkEstipayablebill") String pkEstipayablebill) {
        return R.ok(fiSourceProjEstiPayableService.push(pkEstipayablebill));
    }

    /**
     * 转换前分页查询
     */
    @ApiOperation(value = "转换前分页查询", notes = "转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiSourceProjEstiPayableVO>> beforeConvertDataPage(Page<FiSourceProjEstiPayableVO> page, FiSourceProjEstiPayableQueryDTO queryDTO) {
        return R.ok(fiSourceProjEstiPayableService.beforeConvertDataPage(page, queryDTO));
    }

    /**
     * 转换前数据详情查询
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage")
    public R<Page<FiSourceProjEstiPayableDetailVO>> getBeforeConvertDataPage(Page<FiSourceProjEstiPayableDetailVO> page, FiSourceProjEstiPayableDetailQueryDTO queryDTO) {
        return R.ok(fiSourceProjEstiPayableService.getBeforeConvertDataPage(page, queryDTO));
    }

    /**
     * 主体表转换后单条字段
     */
    @ApiOperation(value = "主体表转换后单条字段", notes = "主体表转换后单条字段")
    @GetMapping("/getConvertList")
    public R<List<FiConvertProjEstiPayableVO>> getConvertList(String pkEstipayablebill) {
        return R.ok(fiSourceProjEstiPayableService.getConvertList(pkEstipayablebill));
    }

    /**
     * 获取转换后详情分页数据
     */
    @ApiOperation(value = "获取转换后详情分页数据", notes = "获取转换后详情分页数据")
    @GetMapping("/getConvertData")
    public R<Page<FiConvertProjEstiPayableDetailVO>> getConvertData(Page<FiConvertProjEstiPayableDetailVO> page, String pkEstipayablebill) {
        return R.ok(fiSourceProjEstiPayableService.getConvertData(page, pkEstipayablebill));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result = fiSourceProjEstiPayableService.update(Wrappers.<FiSourceProjEstiPayable>lambdaUpdate()
                .set(FiSourceProjEstiPayable::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiSourceProjEstiPayable::getPushMsg, "成功")
                .in(FiSourceProjEstiPayable::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiSourceProjEstiPayableService.pushAgainBatch(queryVO));
    }
}
package com.cloud.ficonsumer.builder.context;

import com.cloud.ficonsumer.entity.FiConvertPuPayable;
import com.cloud.ficonsumer.entity.FiConvertPuPayableDetail;
import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import com.cloud.ficonsumer.entity.FiSourcePuPayableDetail;
import com.cloud.ficonsumer.enums.PayableType;

import java.util.List;

/**
 * YgInvoiceCheckContext使用示例
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class YgInvoiceCheckContextExample {

    /**
     * 示例1：仅使用转换数据构建Context
     */
    public void example1(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 使用Builder模式逐步构建Context
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .build();
        
        // 准备所有数据
        context.prepareData();
        
        // 使用便利方法判断应付单类型
        if (context.isPurchasePayable()) {
            System.out.println("这是采购应付单: " + context.getPayableTypeName());
        } else if (context.isProjectPayable()) {
            System.out.println("这是项目应付单: " + context.getPayableTypeName());
        }
        
        // 获取应付单类型枚举
        PayableType payableType = context.getPayableType();
        System.out.println("应付单类型编码: " + payableType.getCode());
        System.out.println("应付单类型名称: " + payableType.getName());
    }

    /**
     * 示例2：同时使用源数据和转换数据构建Context
     */
    public void example2(FiSourcePuPayable source, List<FiSourcePuPayableDetail> sourceDetails,
                        FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 使用Builder模式逐步构建Context，包含源数据
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withSourceData(source, sourceDetails)
                .withConvertData(convert, convertDetails)
                .build();
        
        // 准备所有数据
        context.prepareData();
        
        // 现在可以访问源数据和转换数据
        System.out.println("源数据单据号: " + context.getSource().getBillno());
        System.out.println("转换数据单据号: " + context.getConvert().getBillno());
        System.out.println("应付单类型: " + context.getPayableTypeName());
    }

    /**
     * 示例3：使用新的Builder模式分步构建Context
     */
    public void example3(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 使用新的Builder模式，一次性构建完整的Context
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .withBasicInfo()           // 准备基础信息（组织、供应商、项目等）
                .withCosTypeInfo()         // 准备费用类型信息
                .withInvoiceInfo()         // 准备发票信息
                .withAccountInfo()         // 准备账户信息
                .withTopBill()             // 准备上游单据信息
                .withSourceBill()          // 准备源头单据信息
                .withImageInfo()           // 准备影像信息
                .withValidation()          // 执行业务校验
                .build();

        System.out.println("Context构建完成，应付单类型: " + context.getPayableTypeName());
    }

    /**
     * 示例4：分步构建Context（用于特定场景）
     */
    public void example4(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 第一步：创建基础Context，只准备必要的基础信息
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .withBasicInfo()
                .build();

        // 第二步：根据业务需要，选择性准备数据
        if (context.isPurchasePayable()) {
            // 采购应付：只准备上游单据（采购订单）
            context = YgInvoiceCheckContext.builder()
                    .withConvertData(convert, convertDetails)
                    .withBasicInfo()
                    .withTopBill()  // 准备采购订单信息
                    .build();
            System.out.println("准备了采购订单信息");
        } else if (context.isProjectPayable()) {
            // 项目应付：准备上游单据（收票登记）和源头单据（付款合同/费用结算）
            context = YgInvoiceCheckContext.builder()
                    .withConvertData(convert, convertDetails)
                    .withBasicInfo()
                    .withTopBill()      // 准备收票登记信息
                    .withSourceBill()   // 准备付款合同或费用结算信息
                    .build();
            System.out.println("准备了收票登记和源头单据信息");
        }

        System.out.println("分步构建完成，应付单类型: " + context.getPayableTypeName());
    }

    /**
     * 示例5：最小化构建（仅用于获取上游订单数据）
     */
    public void example5(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 最小化构建：只准备获取上游订单数据所需的信息
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .withBasicInfo()    // 基础信息（组织、供应商等）
                .withTopBill()      // 上游单据信息
                .build();

        // 现在可以获取上游订单数据
        if (context.isPurchasePayable()) {
            System.out.println("采购订单数量: " + context.getPoOrderBMap().size());
        } else if (context.isProjectPayable()) {
            System.out.println("项目合同数量: " + context.getPmContrMap().size());
            System.out.println("费用结算单数量: " + context.getPmFeebalanceBMap().size());
        }
    }
}

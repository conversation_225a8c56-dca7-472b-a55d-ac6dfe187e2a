package com.cloud.ficonsumer.builder.context;

import com.cloud.ficonsumer.entity.FiConvertPuPayable;
import com.cloud.ficonsumer.entity.FiConvertPuPayableDetail;
import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import com.cloud.ficonsumer.entity.FiSourcePuPayableDetail;
import com.cloud.ficonsumer.enums.PayableType;

import java.util.List;

/**
 * YgInvoiceCheckContext使用示例
 * 展示新的Builder模式和分阶段构建的用法
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class YgInvoiceCheckContextExample {

    /**
     * 示例1：基础构建（仅使用转换数据）
     */
    public void example1(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 使用Builder模式构建Context
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .build();
        
        // 使用便利方法判断应付单类型
        if (context.isPurchasePayable()) {
            System.out.println("这是采购应付单: " + context.getPayableTypeName());
        } else if (context.isProjectPayable()) {
            System.out.println("这是项目应付单: " + context.getPayableTypeName());
        }
        
        // 获取应付单类型枚举
        PayableType payableType = context.getPayableType();
        System.out.println("应付单类型编码: " + payableType.getCode());
        System.out.println("应付单类型名称: " + payableType.getName());
    }

    /**
     * 示例2：完整构建（用于发票校验）
     */
    public void example2(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 使用新的Builder模式，一次性构建完整的Context
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .withBasicInfo()           // 准备基础信息（组织、供应商、项目等）
                .withCosTypeInfo()         // 准备费用类型信息
                .withInvoiceInfo()         // 准备发票信息
                .withAccountInfo()         // 准备账户信息
                .withTopBill()             // 准备上游单据信息
                .withSourceBill()          // 准备源头单据信息
                .withImageInfo()           // 准备影像信息
                .withValidation()          // 执行业务校验
                .build();
        
        System.out.println("Context构建完成，应付单类型: " + context.getPayableTypeName());
    }

    /**
     * 示例3：继续构建模式（分阶段构建）
     */
    public void example3(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 第一步：创建基础Context
        YgInvoiceCheckContext baseContext = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .withBasicInfo()
                .build();
        
        // 第二步：继续构建，添加更多信息
        YgInvoiceCheckContext extendedContext = baseContext.continueWith()
                .withTopBill()      // 添加上游单据信息
                .withSourceBill()   // 添加源头单据信息
                .build();
        
        // 第三步：再次继续构建，完成所有信息
        YgInvoiceCheckContext completeContext = extendedContext.continueWith()
                .withCosTypeInfo()
                .withInvoiceInfo()
                .withAccountInfo()
                .withImageInfo()
                .withValidation()
                .build();
        
        System.out.println("分阶段构建完成，应付单类型: " + completeContext.getPayableTypeName());
    }

    /**
     * 示例4：FiSourcePuPayableServiceImpl中的实际使用场景
     */
    public void example4ServiceUsage(FiSourcePuPayable source, List<FiSourcePuPayableDetail> sourceDetails,
                                    FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        
        // 第一阶段：订单同步（仅项目应付需要）
        YgInvoiceCheckContext initialContext = YgInvoiceCheckContext.builder()
                .withSourceData(source, sourceDetails)
                .build();

        if (initialContext.isProjectPayable()) {
            // 继续构建，添加订单同步所需的信息
            YgInvoiceCheckContext orderSyncContext = initialContext.continueWith()
                    .withConvertData(convert, convertDetails)  // 添加转换数据
                    .withBasicInfo()        // 基础信息（组织、供应商、项目等）
                    .withTopBill()          // 上游单据信息（收票登记）
                    .withSourceBill()       // 源头单据信息（付款合同或费用结算单）
                    .build();

            System.out.println("订单同步Context构建完成，应付单类型: " + orderSyncContext.getPayableTypeName());
            System.out.println("付款合同数量: " + orderSyncContext.getPmContrMap().size());
            System.out.println("费用结算单数量: " + orderSyncContext.getPmFeebalanceBMap().size());
            
            // 使用orderSyncContext进行fi_source_base_order的加工处理
            // processBaseOrderSync(orderSyncContext);
        }

        // 第二阶段：应付单推送
        // 在同步完订单后，继续构建为完整的Context，用于构造同步应付单的VO
        YgInvoiceCheckContext completeContext = initialContext.continueWith()
                .withConvertData(convert, convertDetails)  // 确保有转换数据
                .withBasicInfo()        // 基础信息
                .withCosTypeInfo()      // 费用类型信息
                .withInvoiceInfo()      // 发票信息
                .withAccountInfo()      // 账户信息
                .withTopBill()          // 上游单据信息
                .withSourceBill()       // 源头单据信息
                .withImageInfo()        // 影像信息
                .withValidation()       // 业务校验
                .build();

        System.out.println("完整Context构建完成，准备推送应付单");
        
        // 使用completeContext构建VO并推送
        // YgInvoiceCheckVO vo = ygInvoiceCheckVOBuilder.buildWithContext(completeContext);
    }

    /**
     * 示例5：最小化构建（仅用于获取上游订单数据）
     */
    public void example5(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 最小化构建：只准备获取上游订单数据所需的信息
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .withBasicInfo()    // 基础信息（组织、供应商等）
                .withTopBill()      // 上游单据信息
                .build();
        
        // 现在可以获取上游订单数据
        if (context.isPurchasePayable()) {
            System.out.println("采购订单数量: " + context.getPoOrderBMap().size());
        } else if (context.isProjectPayable()) {
            System.out.println("项目合同数量: " + context.getPmContrMap().size());
            System.out.println("费用结算单数量: " + context.getPmFeebalanceBMap().size());
        }
    }

    /**
     * 示例6：错误处理和状态检查
     */
    public void example6ErrorHandling(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        try {
            // 构建Context时可能出现的异常
            YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                    .withConvertData(convert, convertDetails)
                    .withBasicInfo()
                    .withTopBill()
                    .withSourceBill()
                    .withValidation()  // 这里可能抛出业务校验异常
                    .build();
            
            System.out.println("Context构建成功");
            
        } catch (Exception e) {
            System.err.println("Context构建失败: " + e.getMessage());
            // 根据异常类型进行不同的处理
            if (e.getMessage().contains("采购订单不存在")) {
                System.err.println("这是订单同步失败");
            } else if (e.getMessage().contains("构建参数不能为空")) {
                System.err.println("这是转换失败");
            } else {
                System.err.println("这是其他类型的失败");
            }
        }
    }
}

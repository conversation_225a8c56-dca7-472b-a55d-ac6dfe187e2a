package com.cloud.ficonsumer.builder.context;

import com.cloud.ficonsumer.entity.FiConvertPuPayable;
import com.cloud.ficonsumer.entity.FiConvertPuPayableDetail;
import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import com.cloud.ficonsumer.entity.FiSourcePuPayableDetail;
import com.cloud.ficonsumer.enums.PayableType;

import java.util.List;

/**
 * YgInvoiceCheckContext使用示例
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public class YgInvoiceCheckContextExample {

    /**
     * 示例1：仅使用转换数据构建Context
     */
    public void example1(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 使用Builder模式逐步构建Context
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .build();
        
        // 准备所有数据
        context.prepareData();
        
        // 使用便利方法判断应付单类型
        if (context.isPurchasePayable()) {
            System.out.println("这是采购应付单: " + context.getPayableTypeName());
        } else if (context.isProjectPayable()) {
            System.out.println("这是项目应付单: " + context.getPayableTypeName());
        }
        
        // 获取应付单类型枚举
        PayableType payableType = context.getPayableType();
        System.out.println("应付单类型编码: " + payableType.getCode());
        System.out.println("应付单类型名称: " + payableType.getName());
    }

    /**
     * 示例2：同时使用源数据和转换数据构建Context
     */
    public void example2(FiSourcePuPayable source, List<FiSourcePuPayableDetail> sourceDetails,
                        FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 使用Builder模式逐步构建Context，包含源数据
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withSourceData(source, sourceDetails)
                .withConvertData(convert, convertDetails)
                .build();
        
        // 准备所有数据
        context.prepareData();
        
        // 现在可以访问源数据和转换数据
        System.out.println("源数据单据号: " + context.getSource().getBillno());
        System.out.println("转换数据单据号: " + context.getConvert().getBillno());
        System.out.println("应付单类型: " + context.getPayableTypeName());
    }

    /**
     * 示例3：分步构建Context（用于获取上游订单数据）
     */
    public void example3(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        // 第一步：创建基础Context
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .build();
        
        // 第二步：准备基础数据（用于获取上游订单信息）
        context.prepareOrgCode();
        context.prepareSupplierCode();
        context.prepareProjectCode();
        
        // 第三步：根据需要准备特定数据
        if (context.isPurchasePayable()) {
            // 采购应付：准备采购订单相关数据
            context.prepareSrcPoInfo();
        } else if (context.isProjectPayable()) {
            // 项目应付：准备项目合同相关数据
            context.prepareSrcPmInfo();
        }
        
        // 第四步：准备其他数据
        context.prepareCosTypeInfo();
        context.prepareInvoiceInfo();
        context.prepareAccountInfo();
        context.prepareImageInfo();
        
        // 第五步：执行业务校验
        context.validateBusinessRules();
        
        System.out.println("Context构建完成，应付单类型: " + context.getPayableTypeName());
    }
}

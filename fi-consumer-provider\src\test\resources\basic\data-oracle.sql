INSERT INTO BD_SUPPLIER
(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CODE, CO<PERSON><PERSON><PERSON><PERSON>R, CORPADDRESS, CREATIONTI<PERSON>, CREATOR, DATAOR<PERSON><PERSON><PERSON>AG, DEF1, DEF10, DEF11, <PERSON>F12, <PERSON>F13, <PERSON>F14, <PERSON>F15, <PERSON>F16, <PERSON><PERSON>17, <PERSON><PERSON>18, <PERSON>F19, <PERSON>F2, <PERSON>F20, <PERSON>F21, <PERSON>F22, <PERSON>F23, <PERSON>F24, <PERSON>F25, <PERSON>F26, <PERSON>F27, DEF28, DEF29, DEF3, DEF30, DEF4, DEF5, DEF6, DEF7, DEF8, DEF9, DELETESTATE, DELPERSON, DELTIME, DR, ECOTYPESINCEVFIVE, EMAIL, ENABLESTATE, ENAME, ESTABLISHDATE, FAX1, <PERSON>X2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>CU<PERSON>OMER, ISF<PERSON>ECUST, ISMOBILECOOPERTIVE, ISOUTCHECK, ISVAT, LEGALBODY, MEMO, MNECODE, MODIFIEDTI<PERSON>, <PERSON>OD<PERSON>IER, NAME, <PERSON><PERSON>E2, <PERSON><PERSON>E3, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>UNTR<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>_<PERSON>INANCEORG, PK_FORMAT, PK_GROUP, PK_OLDSUPPLIER, PK_ORG, PK_SUPPLIER, PK_SUPPLIER_MAIN, PK_SUPPLIER_PF, PK_SUPPLIERCLASS, PK_SUPTAXES, PK_TIMEZONE, REGISTERFUND, SHORTNAME, SUPPROP, SUPSTATE, TAXPAYERID, TEL1, TEL2, TEL3, TRADE, TS, URL, VATCODE, ZIPCODE)
VALUES(NULL, 'S03000001', '1001A1100000000I8JIF', '~', '2020-12-27 12:28:16', '0001A110000000000CF3', 0, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '1000066412', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', NULL, 0, '1001A1100000000DP1H1', NULL, 2, NULL, NULL, NULL, NULL, 'N', 'Y', 'N', 'N', 'N', 'N', NULL, NULL, NULL, '2025-04-07 15:37:27', '1001A710000000JAFQ2S', '***************司', NULL, NULL, NULL, NULL, NULL, '~', '~', '0001Z010000000079UJJ', '~', '~', 'FMT0Z000000000000000', '0001A110000000000CE4', '1001A1100000000I8JIF', 'GLOBLE00000000000000', '1001A1100000000I8JIF', '~', NULL, '1001A1100000000000P6', '~', '0001Z010000000079U2P', NULL, NULL, 0, 1, '110*********85X', NULL, NULL, NULL, '~', '2025-04-07 15:37:43', NULL, NULL, NULL);

INSERT INTO BD_PROJECT
(ACTU_FINISH_DATE, ACTU_START_DATE, ACTUDURATION, BEGIN_FLAG, BILL_TYPE, CHECK_DATE, CREATIONSTATE, CREATIONTIME, CREATOR, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF6, DEF7, DEF8, DEF9, DELETESTATE, DELPERSON, DELTIME, DR, ENABLESTATE, ESTIMATE_GLOBAL, ESTIMATE_GROUP, ESTIMATE_MNY, FINISH_WORK_DATE, GENERAL_GLOBAL, GENERAL_GROUP, GENERAL_MNY, HDEF100, HDEF51, HDEF52, HDEF53, HDEF54, HDEF55, HDEF56, HDEF57, HDEF58, HDEF59, HDEF60, HDEF61, HDEF62, HDEF63, HDEF64, HDEF65, HDEF66, HDEF67, HDEF68, HDEF69, HDEF70, HDEF71, HDEF72, HDEF73, HDEF74, HDEF75, HDEF76, HDEF77, HDEF78, HDEF79, HDEF80, HDEF81, HDEF82, HDEF83, HDEF84, HDEF85, HDEF86, HDEF87, HDEF88, HDEF89, HDEF90, HDEF91, HDEF92, HDEF93, HDEF94, HDEF95, HDEF96, HDEF97, HDEF98, HDEF99, MEMO, MODIFIEDTIME, MODIFIER, ORDER_FINISH_DATE, ORDER_START_DATE, ORDERDURATION, ORDERMETHOD, PK_BUSITYPE, PK_CURRTYPE, PK_DUTIER, PK_DUTY_DEPT, PK_DUTY_DEPT_V, PK_DUTY_ORG, PK_DUTY_ORG_V, PK_EPS, PK_GROUP, PK_ORG, PK_ORG_V, PK_PARENTPRO, PK_PARENTTASK, PK_PLANMAKER, PK_PROJECT, PK_PROJECTCLASS, PK_PROJECTSTATE, PK_TRANSITYPE, PK_WBSTEMPLATE, PK_WORKCALENDAR, PK_YEARPLAN_B, PLAN_AUTH, PLAN_FINISH_DATE, PLAN_START_DATE, PLANDURATION, PLANMODEL, PLANPRIORITY, PROJECT_CODE, PROJECT_NAME, PROJECT_NAME2, PROJECT_NAME3, PROJECT_NAME4, PROJECT_NAME5, PROJECT_NAME6, PROJECT_OT_NAME, PROJECT_SH_NAME, REQ_FINISH_DATE, REQ_START_DATE, REQDURATION, SRC_BILL_CODE, SRC_BILL_TYPE, SRC_PK_BILL, SRC_PK_TRANSITYPE, SRC_TRANSI_TYPE, START_WORK_DATE, STATUS_DATE, TAX_FLAG, TRANSI_TYPE, TS, UPLOAD_FLAG, LATITUDE, LONGITUDE, PK_PROJECTLIB, PMC_FLAG, POINT_FLAG, "POSITION", PRE_REPEAT_DATE, REPEAT_WORK_DATE, STOP_WORK_DATE, STOP_WORK_DAYS, PK_PARENTORG_NEW, PROJECT_VISIBILITY, PK_PARENTPRO_NEW, T_CREATIONSTATE)
VALUES(NULL, NULL, NULL, 'Y', '4D10', NULL, '1001A110000000002TUP', '2025-04-08 16:33:38', '1001A110000000DPFZBQ', '~', '~', '~', NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', '~', '~', NULL, '~', NULL, 0, 2, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '2025-04-09 16:26:35', '1001A110000000DPFZD5', NULL, NULL, NULL, 1, '0001A1100000000029TP', '1002Z0100000000001K1', '~', '~', '~', '0001A110000000004GXW', '0001A110000000004GXV', '1001O610000000H83CAS', '0001A110000000000CE4', '0001A110000000004GXW', '0001A110000000004GXV', '~', '~', '~', '1001A710000000LD3VKV', '~', '1001A110000000002TUP', '0001A110000000002A33', '~', '1001A1100000007Z70RI', NULL, NULL, '2027-04-09 23:59:59', '2025-04-08 16:32:49', 523, NULL, 1, '870000Z202504002', '测试财务单据', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-04-08 16:32:49', NULL, 'XMLX202504080002', '4D12', '1001A710000000LD3VIA', '0001A110000000002A32', '4D12-01', NULL, '2025-04-08 16:35:49', 'N', '4D10-01', '2025-04-09 16:26:35', 'N', NULL, NULL, '~', 'N', 'N', NULL, NULL, NULL, NULL, NULL, '~', 1, '~', NULL);

INSERT INTO PUB_SYSINIT (CONTROLFLAG, DATAORIGINFLAG, DR, EDITFLAG, INITCODE, INITNAME, PK_ORG, PK_SYSINIT, SYSINIT, `VALUE`
) VALUES ('Y', 0, 0, 'Y', 'PT01', '组织推送启用标识', '0001A110000000003ZM0', '0001PT01', 'PT03', 'Y');
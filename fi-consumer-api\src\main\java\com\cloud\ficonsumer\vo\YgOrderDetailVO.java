package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025/1/20.
 */
@Data
public class YgOrderDetailVO {

    /**
     * 采购订单号
     */
    @ApiModelProperty(value="采购订单号")
    private String purOrdNo;

    /**
     * 订单行号
     */
    @ApiModelProperty(value="订单行号")
    private String purOrdItem;

    /**
     * 项目编码
     */
    @ApiModelProperty(value="项目编码")
    private String prjCode;

    /**
     * 订单行不含税金额
     */
    @ApiModelProperty(value="订单行不含税金额")
    private String itTotAmExTax;

    /**
     * 税率
     */
    @ApiModelProperty(value="税率")
    private String taxrt;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String taxAmount;

    /**
     * 订单行含税金额
     */
    @ApiModelProperty(value="订单行含税金额")
    private String itTotAmIncTax;

    /**
     * 所属WBS元素
     */
    @ApiModelProperty(value="所属WBS元素")
    private String wbsCod;

    /**
     * 删除标识
     */
    @ApiModelProperty(value="删除标识")
    private String celeIdInPurVou;

    /**
     * 成本中心
     */
    @ApiModelProperty(value="成本中心")
    private String costCenterCode;

    /**
     * 单价
     */
    @ApiModelProperty(value="单价")
    private String unitPrice;

    /**
     * 数量
     */
    @ApiModelProperty(value="数量")
    private String amt;

    /**
     * MDM基础组织编码
     */
    @ApiModelProperty(value="MDM基础组织编码")
    private String prvMkCode;

    /**
     * 工单号
     */
    @ApiModelProperty(value="工单号")
    private String workOrdNo;

    /**
     * 净单价
     */
    @ApiModelProperty(value="净单价")
    private String netUnitPrice;

    /**
     * 是否退货项目
     */
    @ApiModelProperty(value="是否退货项目")
    private String isReturnIteems;

    /**
     * 每
     */
    @ApiModelProperty(value="每")
    private String per;

    /**
     * 价格单位
     */
    @ApiModelProperty(value="价格单位")
    private String pricUni;

    /**
     * 传输日期
     */
    @ApiModelProperty(value="传输日期")
    private String transmissionDate;

    /**
     * 工厂
     */
    @ApiModelProperty(value="工厂")
    private String facNum;

    /**
     * 项目类别
     */
    @ApiModelProperty(value="项目类别")
    private String prjType;

    /**
     * 科目分配类别
     */
    @ApiModelProperty(value="科目分配类别")
    private String accZssigCategory;

    /**
     * 物料编号
     */
    @ApiModelProperty(value="物料编号")
    private String mtCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value="物料名称")
    private String mtName;

    /**
     * 是否业务外包
     */
    @ApiModelProperty(value="是否业务外包")
    private String isOutsou;

    /**
     * 是否安全费用支出
     */
    @ApiModelProperty(value="是否安全费用支出")
    private String isSafeExpend;

    /**
     * 安全费用类型
     */
    @ApiModelProperty(value="安全费用类型")
    private String safeExpendType;

    /**
     * 利润中心
     */
    @ApiModelProperty(value="利润中心")
    private String proCen;

    /**
     * 请购单号
     */
    @ApiModelProperty(value="请购单号")
    private String purReq;

    /**
     * 采购方式
     */
    @ApiModelProperty(value="采购方式")
    private String proMet;

    /**
     * 软件包编号
     */
    @ApiModelProperty(value="软件包编号")
    private String packageNumber;

    /**
     * 订单类别
     */
    @ApiModelProperty(value="订单类别")
    private String ordTp;

    /**
     * 年份
     */
    @ApiModelProperty(value="年份")
    private String Year;

    /**
     * 业务专业分类
     */
    @ApiModelProperty(value="业务专业分类")
    private String busProfType;

    /**
     * 典型外包业务
     */
    @ApiModelProperty(value="典型外包业务")
    private String tpOutsou;

    /**
     * 收货，非评估
     */
    @ApiModelProperty(value="收货，非评估")
    private String gRNoAssessment;

    /**
     * 收货标识
     */
    @ApiModelProperty(value="收货标识")
    private String recepId;

    /**
     * 基于收货的发票校验标识
     */
    @ApiModelProperty(value="基于收货的发票校验标识")
    private String recepInvVerSign;

    /**
     * 基于服务的发票校验标识
     */
    @ApiModelProperty(value="基于服务的发票校验标识")
    private String serInvVerSign;

    /**
     * 发票收据标识
     */
    @ApiModelProperty(value="发票收据标识")
    private String invVerSign;

    /**
     * 账户分配的分配标识
     */
    @ApiModelProperty(value="账户分配的分配标识")
    private String accAllSign;

}

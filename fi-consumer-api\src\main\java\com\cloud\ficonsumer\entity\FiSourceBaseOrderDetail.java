

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单源数据转换详情表
 *
 * <AUTHOR>
 * @date 2025-06-03 11:10:03
 */
@Data
@TableName("fi_source_base_order_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "订单源数据转换详情表")
public class FiSourceBaseOrderDetail extends BaseEntity<FiSourceBaseOrderDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 采购订单号
     */
    @ApiModelProperty(value="采购订单号")
    private String purOrdno;

    /**
     * 订单行号
     */
    @ApiModelProperty(value="订单行号")
    private String purOrdItem;

    /**
     * 项目编码
     */
    @ApiModelProperty(value="项目编码")
    private String prjCode;

    /**
     * 订单行不含税金额
     */
    @ApiModelProperty(value="订单行不含税金额")
    private String itTotAmExTax;

    /**
     * 税率
     */
    @ApiModelProperty(value="税率")
    private String taxrt;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String taxAmount;

    /**
     * 订单行含税金额
     */
    @ApiModelProperty(value="订单行含税金额")
    private String itTotAmIncTax;

    /**
     * 删除标识
     */
    @ApiModelProperty(value="删除标识")
    private String celeIdInPurVou;

    /**
     * 成本中心
     */
    @ApiModelProperty(value="成本中心")
    private String costCenterCode;

    /**
     * 单价
     */
    @ApiModelProperty(value="单价")
    private String unitPrice;

    /**
     * 数量
     */
    @ApiModelProperty(value="数量")
    private String amt;

    /**
     * MDM基础组织编码
     */
    @ApiModelProperty(value="MDM基础组织编码")
    private String prvMkCode;

    /**
     * 传输日期
     */
    @ApiModelProperty(value="传输日期")
    private String transmissionDate;

    /**
     * 物料编号
     */
    @ApiModelProperty(value="物料编号")
    private String mtCode;

    /**
     * 收货标识
     */
    @ApiModelProperty(value="收货标识")
    private String recepId;

    /**
     * 主键值
     */
    @ApiModelProperty(value="主键值")
    private String pkMain;

}

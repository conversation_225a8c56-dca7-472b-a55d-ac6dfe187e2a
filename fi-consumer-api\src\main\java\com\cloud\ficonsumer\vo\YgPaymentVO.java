package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/20.
 */
@Data
public class YgPaymentVO {

    /**
     * 详情
     */
    @ApiModelProperty(value="详情")
    private List<YgPaymentDetailVO> payDetails;

    /**
     * 经法合同编号集合
     */
    @ApiModelProperty(value="经法合同编号集合")
    private List<YgPaymentContractDetailVO> contractDetails;

    /**
     * 影像集合
     */
    @ApiModelProperty(value="影像集合")
    private List<ImageDetail> imageDetails;

    /**
     * 审批轨迹详情
     */
    @ApiModelProperty(value="审批轨迹详情")
    private List<ApprvDetails> apprv;

    /**
     * 单据类型ID
     */
    @ApiModelProperty(value="单据类型ID")
    private String billTypeid;

    /**
     * 业务系统单号
     */
    @ApiModelProperty(value="业务系统单号")
    private String externalDocNo;

    /**
     * 发起单位
     */
    @ApiModelProperty(value="发起单位")
    private String dwdh;

    /**
     * 待办用户ID
     */
    @ApiModelProperty(value="待办用户ID")
    private String yhdm;

    /**
     * 单据流程ID
     */
    @ApiModelProperty(value="单据流程ID")
    private String flowid;

    /**
     * 应用ID
     */
    @ApiModelProperty(value="应用ID")
    private String appId;

    /**
     * 是否退回重传
     */
    @ApiModelProperty(value="是否退回重传")
    private String backTag;

    /**
     * 是否自动传递
     */
    @ApiModelProperty(value="是否自动传递")
    private String autoPass;

    /**
     * 经办人
     */
    @ApiModelProperty(value="经办人")
    private String hdlgNmId;

    /**
     * 来源模块
     */
    @ApiModelProperty(value="来源模块")
    private String outerCode;

    /**
     * 业务类型
     */
    @ApiModelProperty(value="业务类型")
    private String biType;

    /**
     * 来源业务主键
     */
    @ApiModelProperty(value="来源业务主键")
    private String sourBusId;

    /**
     * 修改单标识
     */
    @ApiModelProperty(value="修改单标识")
    private String modifyBillFlag;

    /**
     * 填报部门
     */
    @ApiModelProperty(value="填报部门")
    private String dept;

    /**
     * 部门级成本中心
     */
    @ApiModelProperty(value="部门级成本中心")
    private String deptCostCenter;

    /**
     * 利润中心
     */
    @ApiModelProperty(value="利润中心")
    private String profCen;

    /**
     * 业务事项
     */
    @ApiModelProperty(value="业务事项")
    private String cosType;

    /**
     * 明细业务事项名称
     */
    @ApiModelProperty(value="明细业务事项名称")
    private String detCosType;

    /**
     * 明细业务事项编码
     */
    @ApiModelProperty(value="明细业务事项编码")
    private String detCosTypeCode;

    /**
     * 租赁方式
     */
    @ApiModelProperty(value="租赁方式")
    private String leaseMet;

    /**
     * 租赁方式细分
     */
    @ApiModelProperty(value="租赁方式细分")
    private String leaseMetDetail;

    /**
     * 是否内部客户
     */
    @ApiModelProperty(value="是否内部客户")
    private String innerTransSign;

    /**
     * 是否存量
     */
    @ApiModelProperty(value="是否存量")
    private String stockSit;

    /**
     * 支付方式
     */
    @ApiModelProperty(value="支付方式")
    private String pyMethod;

    /**
     * 付款账户
     */
    @ApiModelProperty(value="付款账户")
    private String payAccNum;

    /**
     * 支付总金额
     */
    @ApiModelProperty(value="支付总金额")
    private String totAmoPaid;

    /**
     * 预约支付时间
     */
    @ApiModelProperty(value="预约支付时间")
    private String orderpaydate;

    /**
     * 币种
     */
    @ApiModelProperty(value="币种")
    private String tranCurren;

    /**
     * 汇率
     */
    @ApiModelProperty(value="汇率")
    private String exchRat;

    /**
     * 事由
     */
    @ApiModelProperty(value="事由")
    private String cause;

    /**
     * 支付处理模式
     */
    @ApiModelProperty(value="支付处理模式")
    private String payProMode;

}

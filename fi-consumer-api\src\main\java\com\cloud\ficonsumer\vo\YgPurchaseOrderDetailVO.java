package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "采购订单入库明细信息")
public class YgPurchaseOrderDetailVO {

    @ApiModelProperty(value = "订单编号", required = true)
    private String purOrdNum;
    
    @ApiModelProperty(value = "行项目")
    private String lineItem;
    
    @ApiModelProperty(value = "物料编号")
    private String mtCode;
    
    @ApiModelProperty(value = "物料描述")
    private String mtDesc;
    
    @ApiModelProperty(value = "计量单位")
    private String measurUnit;
    
    @ApiModelProperty(value = "税项")
    private String taxItem;
    
    @ApiModelProperty(value = "物料凭证编号", required = true)
    private String mtDocNum;
    
    @ApiModelProperty(value = "物料凭证中的行项目编号")
    private String mtDocNumLine;
    
    @ApiModelProperty(value = "入库允许过账数量")
    private BigDecimal purNum;
    
    @ApiModelProperty(value = "入库允许过账金额")
    private BigDecimal purExTaxAmount;
    
    @ApiModelProperty(value = "入库明细含税金额")
    private BigDecimal purAmount;
    
    @ApiModelProperty(value = "凭证中的过账日期")
    private String posDatInTheDoc;
    
    @ApiModelProperty(value = "参考凭证")
    private String refDocNum;
    
    @ApiModelProperty(value = "参考凭证年度")
    private Integer mtdocYear;
    
    @ApiModelProperty(value = "参考凭证行项目")
    private String referDocNo;

    @ApiModelProperty(value = "收货标识", example = "00000001", notes = "是:'00000001', 否:'00000000'")
    private String recepId;
}
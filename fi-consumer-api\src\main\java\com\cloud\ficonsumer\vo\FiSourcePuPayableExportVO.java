package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购应付导出VO
 */
@Data
@ColumnWidth(25)
public class FiSourcePuPayableExportVO {

    @ExcelProperty(value = "应付类型名称")
    private String pkTradetypeName;

    @ExcelProperty(value = "单据号")
    private String billno;

    @ExcelProperty(value = "应付财务组织名称")
    private String pkOrgName;

    @ExcelProperty(value="供应商名称")
    private String pkSupplierName;

    @ExcelProperty(value = "制单人名称")
    private String billmakerName;

    @ExcelProperty(value = "币种")
    private String pkCurrtypeName;

    @ExcelProperty(value = "原币金额")
    private BigDecimal money;

    @ExcelProperty(value = "订单编号")
    private String billcode;

    @ExcelProperty(value = "推送时间")
    private String pushTime;

    @ExcelProperty(value = "同步状态")
    private String pushStatusName;

    @ExcelProperty(value = "同步消息")
    private String pushMsg;

    @ExcelProperty(value = "返回状态")
    private String returnStatusName;

    @ExcelProperty(value = "返回消息")
    private String returnMsg;

    @ExcelProperty(value = "返回单号")
    private String returnBill;

    @ExcelProperty(value = "返回接收时间")
    private String returnTime;

    /**
     * 创建时间
     */
    @ExcelProperty(value="拉取时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ExcelIgnore
    private Integer pushStatus;
}
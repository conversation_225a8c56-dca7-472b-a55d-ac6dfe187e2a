

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采购应付明细源数据详情
 *
 * <AUTHOR>
 * @date 2025-04-22 10:04:42
 */
@Data
@TableName("fi_invoice_posting_item")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购应付明细源数据详情")
public class FiInvoicePostingItem extends BaseEntity<FiInvoicePostingItem> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 业务单据ID
     */
    @ApiModelProperty(value="业务单据ID")
    private String busibillno;

    /**
     * 发票行项目
     */
    @ApiModelProperty(value="发票行项目")
    private String invoicedocitem;

    /**
     * 凭证编号
     */
    @ApiModelProperty(value="凭证编号")
    private String accvouno;

    /**
     * 财年
     */
    @ApiModelProperty(value="财年")
    private String fisyea;

    /**
     * 采购订单号
     */
    @ApiModelProperty(value="采购订单号")
    private String purordno;

    /**
     * 采购凭证的项目编号
     */
    @ApiModelProperty(value="采购凭证的项目编号")
    private String ordprojid;

    /**
     * 物料
     */
    @ApiModelProperty(value="物料")
    private String mtcode;

    /**
     * 公司代码
     */
    @ApiModelProperty(value="公司代码")
    private String comcod;

    /**
     * 工厂
     */
    @ApiModelProperty(value="工厂")
    private String facnum;

    /**
     * 税率代码
     */
    @ApiModelProperty(value="税率代码")
    private String taxrtcd;

    /**
     * 金额
     */
    @ApiModelProperty(value="金额")
    private String purorditemamt;

    /**
     * 数量
     */
    @ApiModelProperty(value="数量")
    private String ordqua;

    /**
     * 参考凭证
     */
    @ApiModelProperty(value="参考凭证")
    private String refdocnum;

    /**
     * 参考凭证财年
     */
    @ApiModelProperty(value="参考凭证财年")
    private String mtdocyear;

    /**
     * 参考凭证行项目
     */
    @ApiModelProperty(value="参考凭证行项目")
    private String referdocno;

    /**
     * 行项目文本
     */
    @ApiModelProperty(value="行项目文本")
    private String instruction;

    /**
     * 对账编号
     */
    @ApiModelProperty(value="对账编号")
    private String chkid;

    /**
     * 内部订单
     */
    @ApiModelProperty(value="内部订单")
    private String inteordcode;

}

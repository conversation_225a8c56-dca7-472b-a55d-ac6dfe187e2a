package com.cloud.ficonsumer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "影像结果")
public class BdUdsResult {

    @ApiModelProperty(value = "主键")
    private String pkUdsresult;

    @ApiModelProperty(value = "业务单据主键")
    private String pkBill;

    @ApiModelProperty(value = "业务单据类型")
    private String pkBilltype;

    @ApiModelProperty(value = "NCC文件主键")
    private String pkDoc;

    @ApiModelProperty(value = "UDS主键")
    private String documentId;

    @ApiModelProperty(value = "版本ID")
    private String versionId;

    @ApiModelProperty(value = "文件名")
    private String fileName;

    @ApiModelProperty(value = "删除标志")
    private Integer dr;

    @ApiModelProperty(value = "自定义字段1")
    private String def1;

    @ApiModelProperty(value = "自定义字段2")
    private String def2;

    @ApiModelProperty(value = "自定义字段3")
    private String def3;

    @ApiModelProperty(value = "自定义字段4")
    private String def4;

    @ApiModelProperty(value = "自定义字段5")
    private String def5;

    @ApiModelProperty(value = "自定义字段6")
    private String def6;

    @ApiModelProperty(value = "自定义字段7")
    private String def7;

    @ApiModelProperty(value = "自定义字段8")
    private String def8;

    @ApiModelProperty(value = "自定义字段9")
    private String def9;

    @ApiModelProperty(value = "自定义字段10")
    private String def10;

    @ApiModelProperty(value = "时间戳")
    private String ts;
}
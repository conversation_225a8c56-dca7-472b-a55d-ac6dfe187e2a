package com.cloud.ficonsumer.consumers;

import com.cloud.apiexchange.client.consumer.IMqConsumer;
import com.cloud.apiexchange.client.consumer.MqResult;
import com.cloud.apiexchange.client.consumer.annotation.MqConsumer;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.service.FiSourceSaleOrderService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import static com.cloud.ficonsumer.enums.Constants.TOPIC_SALE_ORDER_TRANS;

@Slf4j
@MqConsumer(topic = TOPIC_SALE_ORDER_TRANS, group = Constants.Group.group, transaction = false)
@Component
@AllArgsConstructor
public class SaleOrderPushConsumer implements IMqConsumer {


    private FiSourceSaleOrderService fiSourceSalesOrderService;

    @Override
    public MqResult consume(String salesOrderNo) {
        LogUtil.info(log, "【销售订单接口-接收销售订单信息】处理开始:", salesOrderNo);
        try {
            fiSourceSalesOrderService.push(salesOrderNo);
        } catch (Exception e) {
            LogUtil.error(log, "【销售订单接口-接收销售订单信息】处理失败:", e);
            return new MqResult(MqResult.FAIL_CODE, AdminUtils.truncateString(e.getMessage(), 1000));
        }
        return new MqResult(MqResult.SUCCESS_CODE, "【销售订单接口-接收销售订单信息】处理成功");
    }
}
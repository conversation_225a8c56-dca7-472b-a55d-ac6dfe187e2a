

package com.cloud.ficonsumer.mapper;

import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.entity.FiOrgCompare;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 组织对照关系
 *
 * <AUTHOR>
 * @date 2025-04-27 10:55:07
 */
@Mapper
public interface FiOrgCompareMapper extends CloudBaseMapper<FiOrgCompare> {

    @Select("<script>  select pk_group,group_name " +
            "      from fi_org_compare " +
            "      where level!=0 and del_flag =0 " +
            "      order by sort </script>")
    List<FiOrgCompare> getGroupMap();
}

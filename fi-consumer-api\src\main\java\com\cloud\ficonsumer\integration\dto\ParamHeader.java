package com.cloud.ficonsumer.integration.dto;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 参数格式
 */
@Data
public class ParamHeader {
    /**
     * 请求流水号
     */
    @NotNull
    @ApiModelProperty("请求流水号")
    private String seqNo;
    /**
     * 调用系统编码
     */
    @ApiModelProperty("调用端系统代码")
    private String systemCode;
    /**
     * 服务端编码
     */
    @NotNull
    @ApiModelProperty("服务端接口编码")
    private String servCode;
    /**
     * 密钥
     */
    @NotNull
    @ApiModelProperty("服务端接入密钥")
    private String accessKey;
    /**
     * 调用端的请求日期 YYYY-MM-DD
     */
    @ApiModelProperty("请求日期")
    private String requestDate;
    /**
     * 调用端时间 24hh:mm:ss SSS
     */
    @ApiModelProperty("请求时间")
    private String requestTime;

    /**
     * 响应是否成功 10000成功 20000失败
     */
    @ApiModelProperty("返回代码，成功=10000，失败=20000")
    private String responseCode;

    @ApiModelProperty("返回说明")
    private String responseMsg;

    @ApiModelProperty("返回日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String responseDate;

    @ApiModelProperty("返回时间")
    private String responseTime;

//    @ApiModelProperty("动作码(1增2改0删)")
//    private String actionCode;


    public ParamHeader() {
        this.seqNo = IdUtil.simpleUUID();
        this.systemCode = "fi-consumer";
        Date date = new Date();
        this.responseDate = DateUtil.format(date, "yyyy-MM-dd");
        this.responseTime = DateUtil.format(date, "HH:mm:ss.SSS");
    }



}

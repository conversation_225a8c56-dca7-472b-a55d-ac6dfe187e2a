CREATE TABLE `fi_source_order`
(
    `id`                 bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_order`           varchar(40)   DEFAULT NULL COMMENT '订单主键',
    `vbillcode`          varchar(40)   DEFAULT NULL COMMENT '订单编号',
    `pk_org_v`           varchar(20)   DEFAULT NULL COMMENT '采购组织',
    `dmakedate`          varchar(19)   DEFAULT NULL COMMENT '制单日期',
    `billmaker`          varchar(20)   DEFAULT NULL COMMENT '制单人',
    `pk_supplier`        varchar(20)   DEFAULT NULL COMMENT '供应商',
    `corigcurrencyid`    varchar(20)   DEFAULT NULL COMMENT '币种',
    `vcontractcode`      varchar(40)   DEFAULT NULL COMMENT '合同号',
    `norigmny`           varchar(32)   DEFAULT NULL COMMENT '无税金额',
    `nexchangerate`      varchar(32)   DEFAULT NULL COMMENT '调整方式',
    `ntax`               varchar(32)   DEFAULT NULL COMMENT '税额',
    `breturn`            varchar(32)   DEFAULT NULL COMMENT '退货',
    `ctrantypeid`        varchar(32)   DEFAULT NULL COMMENT '订单类型',
    `vtrantypecode`      varchar(32)   DEFAULT NULL COMMENT '单据类型编码',
    `integration_status` varchar(128)  DEFAULT NULL COMMENT '集成状态',
    `push_status`        tinyint(4)    DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
    `push_msg`           varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`         varchar(256)  DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      varchar(16)   DEFAULT NULL COMMENT '返回同步状态',
    `try_number`         bigint(32)    DEFAULT '0' COMMENT '重试次数',
    `readiness`          bigint(32)    DEFAULT '0' COMMENT '0 未就绪 1 就绪',
    `create_by`          varchar(50)   DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)   DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)    DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='订单源数据主表';

CREATE TABLE `fi_convert_order`
(
    `id`                 bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_order`           varchar(40)   DEFAULT NULL COMMENT '订单主键',
    `vbillcode`          varchar(40)   DEFAULT NULL COMMENT '订单编号',
    `pk_org_v`           varchar(20)   DEFAULT NULL COMMENT '采购组织',
    `dmakedate`          varchar(19)   DEFAULT NULL COMMENT '制单日期',
    `billmaker`          varchar(20)   DEFAULT NULL COMMENT '制单人',
    `pk_supplier`        varchar(20)   DEFAULT NULL COMMENT '供应商',
    `corigcurrencyid`    varchar(20)   DEFAULT NULL COMMENT '币种',
    `vcontractcode`      varchar(40)   DEFAULT NULL COMMENT '合同号',
    `norigmny`           varchar(32)   DEFAULT NULL COMMENT '无税金额',
    `nexchangerate`      varchar(32)   DEFAULT NULL COMMENT '调整方式',
    `ntax`               varchar(32)   DEFAULT NULL COMMENT '税额',
    `breturn`            varchar(32)   DEFAULT NULL COMMENT '退货',
    `ctrantypeid`        varchar(32)   DEFAULT NULL COMMENT '订单类型',
    `vtrantypecode`      varchar(32)   DEFAULT NULL COMMENT '单据类型编码',
    `integration_status` varchar(128)  DEFAULT NULL COMMENT '集成状态',
    `push_status`        tinyint(4)    DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
    `push_msg`           varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`         varchar(256)  DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      varchar(16)   DEFAULT NULL COMMENT '返回同步状态',
    `try_number`         bigint(32)    DEFAULT '0' COMMENT '重试次数',
    `readiness`          bigint(32)    DEFAULT '0' COMMENT '0 未就绪 1 就绪',
    `create_by`          varchar(50)   DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime      DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)   DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)    DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='订单转换数据主表';

CREATE TABLE `fi_source_order_detail`
(
    `id`              bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_order`        varchar(40) DEFAULT NULL COMMENT '订单主键',
    `pk_order_b`      varchar(40) DEFAULT NULL COMMENT '订单主键',
    `vbillcode`       varchar(40) DEFAULT NULL COMMENT '订单编号',
    `crowno`          varchar(20) DEFAULT NULL COMMENT '行号',
    `cprojectid`      varchar(20) DEFAULT NULL COMMENT '项目',
    `norigmny`        varchar(32) DEFAULT NULL COMMENT '无税金额',
    `nexchangerate`   varchar(32) DEFAULT NULL COMMENT '调整方式',
    `ntax`            varchar(32) DEFAULT NULL COMMENT '税额',
    `norigtaxmny`     varchar(32) DEFAULT NULL COMMENT '价税合计',
    `pk_dept_v`       varchar(20) DEFAULT NULL COMMENT '采购部门',
    `nqtorigtaxprice` varchar(32) DEFAULT NULL COMMENT '含税单价',
    `nastnum`         varchar(32) DEFAULT NULL COMMENT '数量',
    `pk_org_v`        varchar(20) DEFAULT NULL COMMENT '采购组织',
    `breturn`         varchar(20) DEFAULT NULL COMMENT '退货',
    `castunitid`      varchar(20) DEFAULT NULL COMMENT '单位',
    `pk_material`     varchar(20) DEFAULT NULL COMMENT '物料版本信息',
    `name`            varchar(20) DEFAULT NULL COMMENT '物料名称',
    `ctrantypeid`     varchar(20) DEFAULT NULL COMMENT '订单类型',
    `dbilldate`       varchar(19) DEFAULT NULL COMMENT '订单日期',
    `nqtorigprice`    varchar(32) DEFAULT NULL COMMENT '无税单价',
    `ntotalorigmny`   varchar(32) DEFAULT NULL COMMENT '价税合计',
    `ntaxmny`         varchar(32) DEFAULT NULL COMMENT '本币价税合计',
    `nmny`            varchar(32) DEFAULT NULL COMMENT '本币无税金额',
    `forderstatus`    varchar(32) DEFAULT NULL COMMENT '单据状态',
    `create_by`       varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time`     datetime    DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`       varchar(50) DEFAULT NULL COMMENT '更新人',
    `update_time`     datetime    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`        bigint(32)  DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='订单源数据详情表';


CREATE TABLE `fi_convert_order_detail`
(
    `id`              bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_order`        varchar(40) DEFAULT NULL COMMENT '订单主键',
    `pk_order_b`      varchar(40) DEFAULT NULL COMMENT '订单主键',
    `vbillcode`       varchar(40) DEFAULT NULL COMMENT '订单编号',
    `crowno`          varchar(20) DEFAULT NULL COMMENT '行号',
    `cprojectid`      varchar(20) DEFAULT NULL COMMENT '项目',
    `norigmny`        varchar(32) DEFAULT NULL COMMENT '无税金额',
    `nexchangerate`   varchar(32) DEFAULT NULL COMMENT '调整方式',
    `ntax`            varchar(32) DEFAULT NULL COMMENT '税额',
    `norigtaxmny`     varchar(32) DEFAULT NULL COMMENT '价税合计',
    `pk_dept_v`       varchar(20) DEFAULT NULL COMMENT '采购部门',
    `nqtorigtaxprice` varchar(32) DEFAULT NULL COMMENT '含税单价',
    `nastnum`         varchar(32) DEFAULT NULL COMMENT '数量',
    `pk_org_v`        varchar(20) DEFAULT NULL COMMENT '采购组织',
    `breturn`         varchar(20) DEFAULT NULL COMMENT '退货',
    `castunitid`      varchar(20) DEFAULT NULL COMMENT '单位',
    `pk_material`     varchar(20) DEFAULT NULL COMMENT '物料版本信息',
    `name`            varchar(20) DEFAULT NULL COMMENT '物料名称',
    `ctrantypeid`     varchar(20) DEFAULT NULL COMMENT '订单类型',
    `dbilldate`       varchar(19) DEFAULT NULL COMMENT '订单日期',
    `nqtorigprice`    varchar(32) DEFAULT NULL COMMENT '无税单价',
    `ntotalorigmny`   varchar(32) DEFAULT NULL COMMENT '价税合计',
    `ntaxmny`         varchar(32) DEFAULT NULL COMMENT '本币价税合计',
    `nmny`            varchar(32) DEFAULT NULL COMMENT '本币无税金额',
    `forderstatus`    varchar(32) DEFAULT NULL COMMENT '单据状态',
    `create_by`       varchar(50) DEFAULT NULL COMMENT '创建人',
    `create_time`     datetime    DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`       varchar(50) DEFAULT NULL COMMENT '更新人',
    `update_time`     datetime    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`        bigint(32)  DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='订单转换数据详情表';

CREATE TABLE `fi_source_pu_payable`
(
    `id`                 bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_org_v`           varchar(20)    DEFAULT NULL COMMENT '应付财务组织',
    `billmaker`          varchar(20)    DEFAULT NULL COMMENT '制单人',
    `pk_payablebill`     varchar(40)    DEFAULT NULL COMMENT '应付单标识',
    `def6`               varchar(20)    DEFAULT NULL COMMENT '费用类型',
    `pk_tradetypeid`     varchar(20)    DEFAULT NULL COMMENT '应付类型',
    `pk_currtype`        varchar(20)    DEFAULT NULL COMMENT '币种',
    `money`              decimal(28, 8) DEFAULT NULL COMMENT '原币金额',
    `billstatus`         int(11)        DEFAULT NULL COMMENT '单据状态',
    `billno`             varchar(40)    DEFAULT NULL COMMENT '单据号',
    `integration_status` varchar(128)   DEFAULT NULL COMMENT '集成状态',
    `push_status`        tinyint(4)     DEFAULT NULL COMMENT '4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败',
    `push_msg`           varchar(1024)  DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`         varchar(256)   DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      varchar(16)    DEFAULT NULL COMMENT '返回同步状态',
    `try_number`         bigint(32)     DEFAULT '0' COMMENT '重试次数',
    `readiness`          bigint(32)     DEFAULT '0' COMMENT '0 未就绪 1 就绪',
    `create_by`          varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='采购应付源数据详情';

CREATE TABLE `fi_source_pu_payable_detail`
(
    `id`             bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_deptid_v`    varchar(20)    DEFAULT NULL COMMENT '部门',
    `contractno`     varchar(40)    DEFAULT NULL COMMENT '合同号',
    `invoiceno`      varchar(40)    DEFAULT NULL COMMENT '发票号',
    `supplier_v`     varchar(20)    DEFAULT NULL COMMENT '供应商版本',
    `material`       varchar(40)    DEFAULT NULL COMMENT '物料',
    `quantity_cr`    decimal(28, 8) DEFAULT NULL COMMENT '贷方数量',
    `money_cr`       decimal(28, 8) DEFAULT NULL COMMENT '贷方原币金额',
    `notax_cr`       decimal(28, 8) DEFAULT NULL COMMENT '贷方原币无税金额',
    `local_money_cr` decimal(28, 8) DEFAULT NULL COMMENT '组织本币金额',
    `local_notax_cr` decimal(28, 8) DEFAULT NULL COMMENT '组织本币无税金额',
    `local_tax_cr`   decimal(28, 8) DEFAULT NULL COMMENT '税额',
    `taxrate`        decimal(28, 8) DEFAULT NULL COMMENT '税率',
    `pk_subjcode`    varchar(20)    DEFAULT NULL COMMENT '收支项目',
    `pk_payableitem` varchar(40)    DEFAULT NULL COMMENT '应付单行标识',
    `pk_payablebill` varchar(40)    DEFAULT NULL COMMENT '应付单表示',
    `create_by`      varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`      varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`    datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`       bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='采购应付明细源数据详情';

CREATE TABLE `fi_convert_pu_payable`
(
    `id`                 bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_org_v`           varchar(20)    DEFAULT NULL COMMENT '应付财务组织',
    `billmaker`          varchar(20)    DEFAULT NULL COMMENT '制单人',
    `pk_payablebill`     varchar(40)    DEFAULT NULL COMMENT '应付单标识',
    `def6`               varchar(20)    DEFAULT NULL COMMENT '费用类型',
    `pk_tradetypeid`     varchar(20)    DEFAULT NULL COMMENT '应付类型',
    `pk_currtype`        varchar(20)    DEFAULT NULL COMMENT '币种',
    `money`              decimal(28, 8) DEFAULT NULL COMMENT '原币金额',
    `billstatus`         int(11)        DEFAULT NULL COMMENT '单据状态',
    `billno`             varchar(40)    DEFAULT NULL COMMENT '单据号',
    `integration_status` varchar(128)   DEFAULT NULL COMMENT '集成状态',
    `push_status`        tinyint(4)     DEFAULT NULL COMMENT '4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败',
    `push_msg`           varchar(1024)  DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`         varchar(256)   DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      varchar(16)    DEFAULT NULL COMMENT '返回同步状态',
    `try_number`         bigint(32)     DEFAULT '0' COMMENT '重试次数',
    `readiness`          bigint(32)     DEFAULT '0' COMMENT '0 未就绪 1 就绪',
    `create_by`          varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='采购应付源数据详情';

CREATE TABLE `fi_convert_pu_payable_detail`
(
    `id`             bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_deptid_v`    varchar(20)    DEFAULT NULL COMMENT '部门',
    `contractno`     varchar(40)    DEFAULT NULL COMMENT '合同号',
    `invoiceno`      varchar(40)    DEFAULT NULL COMMENT '发票号',
    `supplier_v`     varchar(20)    DEFAULT NULL COMMENT '供应商版本',
    `material`       varchar(40)    DEFAULT NULL COMMENT '物料',
    `quantity_cr`    decimal(28, 8) DEFAULT NULL COMMENT '贷方数量',
    `money_cr`       decimal(28, 8) DEFAULT NULL COMMENT '贷方原币金额',
    `notax_cr`       decimal(28, 8) DEFAULT NULL COMMENT '贷方原币无税金额',
    `local_money_cr` decimal(28, 8) DEFAULT NULL COMMENT '组织本币金额',
    `local_notax_cr` decimal(28, 8) DEFAULT NULL COMMENT '组织本币无税金额',
    `local_tax_cr`   decimal(28, 8) DEFAULT NULL COMMENT '税额',
    `taxrate`        decimal(28, 8) DEFAULT NULL COMMENT '税率',
    `pk_subjcode`    varchar(20)    DEFAULT NULL COMMENT '收支项目',
    `pk_payableitem` varchar(40)    DEFAULT NULL COMMENT '应付单行标识',
    `pk_payablebill` varchar(40)    DEFAULT NULL COMMENT '应付单表示',
    `create_by`      varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`    datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`      varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`    datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`       bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 0
  DEFAULT CHARSET = utf8 COMMENT ='采购应付明细源数据详情';

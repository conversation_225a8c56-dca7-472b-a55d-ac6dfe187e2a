create table SO_<PERSON>LEORDER
(
    APPROVER         VARCHAR2(20),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>      CHAR,
    BARSETTLEF<PERSON><PERSON>    CHAR,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>    CHAR,
    BC<PERSON><PERSON>ET<PERSON><PERSON>LAG  CHAR,
    BF<PERSON><PERSON><PERSON><PERSON><PERSON>G    CHAR,
    BILLMAKER        VARCHAR2(20),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   CHAR,
    B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>      CHAR,
    BOUTEN<PERSON><PERSON><PERSON>      CHAR,
    BPOCOOPTOME<PERSON>AG  CHAR,
    BPRECEIVEFL<PERSON>    CHAR,
    BSENDENDFL<PERSON>     CHAR,
    CARSUBTYPEID     VARCHAR2(20),
    CBALANCETYPEID   VARCHAR2(20),
    CBILLSRCID       VARCHAR2(20),
    CBIZTYPEID       VARCHAR2(20),
    CCHANNELTYPEID   VARCHAR2(20),
    CCUSTBANKACCID   VARCHAR2(20),
    CCUSTBANKID      VARCHAR2(20),
    CCUSTOMERID      VARCHAR2(20),
    CDEPTID          VARCHAR2(20),
    CDEPTVID         VARCHAR2(20),
    CEMPLOYEEID      VARCHAR2(20),
    CF<PERSON><PERSON>USTID      VARCHAR2(20),
    CHRECEIVEADDID   VARCHAR2(20),
    CH<PERSON>CEIVECUSTID  VARCHAR2(20),
    <PERSON><PERSON><PERSON><PERSON>ECUSTID   VARCHAR2(20),
    CORIGCURRENCYID  VARCHAR2(20),
    CPAYTERMID       VARCHAR2(20),
    CREATIONTIME     CHAR(19),
    CREATOR          CHAR(20),
    CREVISERID       VARCHAR2(20),
    CSALEORDERID     CHAR(20) not null
        constraint PK_SO_SALEORDER
            primary key,
    CTRADEWORDID     VARCHAR2(20),
    CTRANSPORTTYPEID VARCHAR2(20),
    CTRANTYPEID      VARCHAR2(20),
    DBILLDATE        CHAR(19),
    DMAKEDATE        CHAR(19),
    DR               NUMBER(10) default 0,
    FPFSTATUSFLAG    NUMBER(38),
    FSTATUSFLAG      NUMBER(38),
    IPRINTCOUNT      NUMBER(38),
    IVERSION         NUMBER(38),
    MODIFIEDTIME     CHAR(19),
    MODIFIER         VARCHAR2(20),
    NDISCOUNTRATE    NUMBER(28, 8),
    NLRGTOTALORIGMNY NUMBER(28, 8),
    NPRECEIVEMNY     NUMBER(28, 8),
    NPRECEIVEQUOTA   NUMBER(28, 8),
    NPRECEIVERATE    NUMBER(28, 8),
    NRECEIVEDMNY     NUMBER(28, 8),
    NTOTALNUM        NUMBER(28, 8),
    NTOTALORIGMNY    NUMBER(28, 8),
    NTOTALORIGSUBMNY NUMBER(28, 8),
    NTOTALPIECE      NUMBER(28, 8),
    NTOTALVOLUME     NUMBER(28, 8),
    NTOTALWEIGHT     NUMBER(28, 8),
    PK_GROUP         VARCHAR2(20),
    PK_ORG           VARCHAR2(20),
    PK_ORG_V         VARCHAR2(20),
    TAUDITTIME       VARCHAR2(19),
    TREVISETIME      CHAR(19),
    TS               CHAR(19)   default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VBILLCODE        VARCHAR2(40),
    VBILLSRCTYPE     VARCHAR2(20),
    VCOOPPOHCODE     VARCHAR2(40),
    VCREDITNUM       VARCHAR2(20),
    VDEF1            VARCHAR2(300),
    VDEF10           VARCHAR2(101),
    VDEF11           VARCHAR2(101),
    VDEF12           VARCHAR2(101),
    VDEF13           VARCHAR2(101),
    VDEF14           VARCHAR2(101),
    VDEF15           VARCHAR2(101),
    VDEF16           VARCHAR2(101),
    VDEF17           VARCHAR2(101),
    VDEF18           VARCHAR2(101),
    VDEF19           VARCHAR2(101),
    VDEF2            VARCHAR2(101),
    VDEF20           VARCHAR2(101),
    VDEF3            VARCHAR2(101),
    VDEF4            VARCHAR2(101),
    VDEF5            VARCHAR2(101),
    VDEF6            VARCHAR2(101),
    VDEF7            VARCHAR2(101),
    VDEF8            VARCHAR2(101),
    VDEF9            VARCHAR2(101),
    VNOTE            VARCHAR2(2000),
    VREVISEREASON    VARCHAR2(181),
    VTRANTYPECODE    VARCHAR2(20),
    SAGA_BTXID       VARCHAR2(64),
    SAGA_FROZEN      NUMBER(38) default 0,
    SAGA_GTXID       VARCHAR2(64),
    SAGA_STATUS      NUMBER(38) default 0
);

create table SO_SALEORDER_B
(
    BARRANGEDFLAG      CHAR,
    BBARSETTLEFLAG     CHAR,
    BBCOSTSETTLEFLAG   CHAR,
    BBINDFLAG          CHAR,
    BBINVOICENDFLAG    CHAR,
    BBOUTENDFLAG       CHAR,
    BBSENDENDFLAG      CHAR,
    BDISCOUNTFLAG      CHAR,
    BJCZXSFLAG         CHAR,
    BLABORFLAG         CHAR,
    BLARGESSFLAG       CHAR,
    BLRGCASHFLAG       CHAR,
    BTRIATRADEFLAG     CHAR,
    CARORGID           VARCHAR2(20),
    CARORGVID          VARCHAR2(20),
    CARRANGEPERSONID   VARCHAR2(20),
    CASTUNITID         VARCHAR2(20),
    CBINDSRCID         VARCHAR2(20),
    CBUYLARGESSACTID   VARCHAR2(20),
    CBUYLARGESSID      VARCHAR2(20),
    CBUYPROMOTTYPEID   VARCHAR2(20),
    CCTMANAGEBID       VARCHAR2(20),
    CCTMANAGEID        VARCHAR2(20),
    CCURRENCYID        VARCHAR2(20),
    CCUSTMATERIALID    VARCHAR2(20),
    CEXCHANGESRCRETID  VARCHAR2(20),
    CFACTORYID         VARCHAR2(20),
    CFIRSTBID          VARCHAR2(20),
    CFIRSTID           VARCHAR2(20),
    CLARGESSSRCID      VARCHAR2(20),
    CMATERIALID        VARCHAR2(20),
    CMATERIALVID       VARCHAR2(20),
    CMFFILEID          VARCHAR2(20),
    CORIGAREAID        VARCHAR2(20),
    CORIGCOUNTRYID     VARCHAR2(20),
    CPRCPROMOTTYPEID   VARCHAR2(20),
    CPRICEFORMID       VARCHAR2(20),
    CPRICEITEMID       VARCHAR2(20),
    CPRICEITEMTABLEID  VARCHAR2(20),
    CPRICEPOLICYID     VARCHAR2(20),
    CPRICEPROMTACTID   VARCHAR2(20),
    CPRODLINEID        VARCHAR2(20),
    CPRODUCTORID       VARCHAR2(20),
    CPROFITCENTERID    VARCHAR2(20),
    CPROFITCENTERVID   VARCHAR2(20),
    CPROJECTID         VARCHAR2(20),
    CPROMOTPRICEID     VARCHAR2(20),
    CQTUNITID          VARCHAR2(20),
    CQUALITYLEVELID    VARCHAR2(20),
    CRECECOUNTRYID     VARCHAR2(20),
    CRECEIVEADDDOCID   VARCHAR2(20),
    CRECEIVEADDRID     VARCHAR2(20),
    CRECEIVEAREAID     VARCHAR2(20),
    CRECEIVECUSTID     VARCHAR2(20),
    CRETPOLICYID       VARCHAR2(20),
    CRETREASONID       VARCHAR2(20),
    CROWNO             VARCHAR2(20),
    CSALEORDERBID      CHAR(20) not null
        constraint PK_SO_SALEORDER_B
            primary key,
    CSALEORDERID       CHAR(20),
    CSENDCOUNTRYID     VARCHAR2(20),
    CSENDSTOCKORGID    VARCHAR2(20),
    CSENDSTOCKORGVID   VARCHAR2(20),
    CSENDSTORDOCID     VARCHAR2(20),
    CSETTLEORGID       VARCHAR2(20),
    CSETTLEORGVID      VARCHAR2(20),
    CSPROFITCENTERID   VARCHAR2(20),
    CSPROFITCENTERVID  VARCHAR2(20),
    CSRCBID            VARCHAR2(20),
    CSRCID             VARCHAR2(20),
    CTAXCODEID         VARCHAR2(20),
    CTAXCOUNTRYID      VARCHAR2(20),
    CTRAFFICORGID      VARCHAR2(20),
    CTRAFFICORGVID     VARCHAR2(20),
    CUNITID            VARCHAR2(20),
    CVENDORID          VARCHAR2(20),
    DBILLDATE          CHAR(19),
    DR                 NUMBER(10) default 0,
    DRECEIVEDATE       CHAR(19),
    DSENDDATE          CHAR(19),
    FBUYSELLFLAG       NUMBER(38),
    FLARGESSTYPEFLAG   NUMBER(38),
    FRETEXCHANGE       NUMBER(38),
    FROWSTATUS         NUMBER(38),
    FTAXTYPEFLAG       NUMBER(38),
    NACCPRICE          NUMBER(28, 8),
    NASKQTORIGNETPRICE NUMBER(28, 8),
    NASKQTORIGPRICE    NUMBER(28, 8),
    NASKQTORIGTAXPRC   NUMBER(28, 8),
    NASKQTORIGTXNTPRC  NUMBER(28, 8),
    NASTNUM            NUMBER(28, 8),
    NCALTAXMNY         NUMBER(28, 8),
    NDISCOUNT          NUMBER(28, 8),
    NDISCOUNTRATE      NUMBER(28, 8),
    NEXCHANGERATE      NUMBER(28, 8),
    NGLOBALEXCHGRATE   NUMBER(28, 8),
    NGLOBALMNY         NUMBER(28, 8),
    NGLOBALTAXMNY      NUMBER(28, 8),
    NGROUPEXCHGRATE    NUMBER(28, 8),
    NGROUPMNY          NUMBER(28, 8),
    NGROUPTAXMNY       NUMBER(28, 8),
    NITEMDISCOUNTRATE  NUMBER(28, 8),
    NLARGESSMNY        NUMBER(28, 8),
    NLARGESSTAXMNY     NUMBER(28, 8),
    NMFFILEPRICE       NUMBER(28, 8),
    NMNY               NUMBER(28, 8),
    NNETPRICE          NUMBER(28, 8),
    NNUM               NUMBER(28, 8),
    NORIGDISCOUNT      NUMBER(28, 8),
    NORIGMNY           NUMBER(28, 8),
    NORIGNETPRICE      NUMBER(28, 8),
    NORIGPRICE         NUMBER(28, 8),
    NORIGTAXMNY        NUMBER(28, 8),
    NORIGTAXNETPRICE   NUMBER(28, 8),
    NORIGTAXPRICE      NUMBER(28, 8),
    NPIECE             NUMBER(28, 8),
    NPRICE             NUMBER(28, 8),
    NQTNETPRICE        NUMBER(28, 8),
    NQTORIGNETPRICE    NUMBER(28, 8),
    NQTORIGPRICE       NUMBER(28, 8),
    NQTORIGTAXNETPRC   NUMBER(28, 8),
    NQTORIGTAXPRICE    NUMBER(28, 8),
    NQTPRICE           NUMBER(28, 8),
    NQTTAXNETPRICE     NUMBER(28, 8),
    NQTTAXPRICE        NUMBER(28, 8),
    NQTUNITNUM         NUMBER(28, 8),
    NTAX               NUMBER(28, 8),
    NTAXMNY            NUMBER(28, 8),
    NTAXNETPRICE       NUMBER(28, 8),
    NTAXPRICE          NUMBER(28, 8),
    NTAXRATE           NUMBER(28, 8),
    NVOLUME            NUMBER(28, 8),
    NWEIGHT            NUMBER(28, 8),
    PK_BATCHCODE       VARCHAR2(20),
    PK_GROUP           VARCHAR2(20),
    PK_ORG             VARCHAR2(20),
    TLASTARRANGETIME   CHAR(19),
    TS                 CHAR(19)   default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VBATCHCODE         VARCHAR2(40),
    VBDEF1             VARCHAR2(101),
    VBDEF10            VARCHAR2(101),
    VBDEF11            VARCHAR2(101),
    VBDEF12            VARCHAR2(101),
    VBDEF13            VARCHAR2(101),
    VBDEF14            VARCHAR2(101),
    VBDEF15            VARCHAR2(101),
    VBDEF16            VARCHAR2(101),
    VBDEF17            VARCHAR2(101),
    VBDEF18            VARCHAR2(101),
    VBDEF19            VARCHAR2(101),
    VBDEF2             VARCHAR2(101),
    VBDEF20            VARCHAR2(101),
    VBDEF3             VARCHAR2(101),
    VBDEF4             VARCHAR2(101),
    VBDEF5             VARCHAR2(101),
    VBDEF6             VARCHAR2(101),
    VBDEF7             VARCHAR2(101),
    VBDEF8             VARCHAR2(500),
    VBDEF9             VARCHAR2(101),
    VBREVISEREASON     VARCHAR2(181),
    VCHANGERATE        VARCHAR2(60),
    VCLOSEREASON       VARCHAR2(181),
    VCTCODE            VARCHAR2(40),
    VCUSTOMBILLCODE    VARCHAR2(40),
    VFIRSTCODE         VARCHAR2(40),
    VFIRSTROWNO        VARCHAR2(20),
    VFIRSTTRANTYPE     VARCHAR2(20),
    VFIRSTTYPE         VARCHAR2(20),
    VFREE1             VARCHAR2(101),
    VFREE10            VARCHAR2(101),
    VFREE2             VARCHAR2(101),
    VFREE3             VARCHAR2(101),
    VFREE4             VARCHAR2(101),
    VFREE5             VARCHAR2(101),
    VFREE6             VARCHAR2(101),
    VFREE7             VARCHAR2(101),
    VFREE8             VARCHAR2(101),
    VFREE9             VARCHAR2(101),
    VQTUNITRATE        VARCHAR2(60),
    VRETURNMODE        VARCHAR2(181),
    VROWNOTE           VARCHAR2(181),
    VSRCCODE           VARCHAR2(40),
    VSRCROWNO          VARCHAR2(20),
    VSRCTRANTYPE       VARCHAR2(20),
    VSRCTYPE           VARCHAR2(20)
);

create table BD_PLATFORMLOG
(
    PK_LOG      VARCHAR2(40) not null,
    PK_BILL     VARCHAR2(101),
    PK_BILLTYPE VARCHAR2(101),
    TYPEID      VARCHAR2(101),
    DISCODE     VARCHAR2(101),
    CODE        VARCHAR2(20),
    MESSAGE     VARCHAR2(2000),
    DEF1        VARCHAR2(101),
    DEF2        VARCHAR2(101),
    DEF3        VARCHAR2(101),
    DEF4        VARCHAR2(101),
    DEF5        VARCHAR2(101),
    DEF6        VARCHAR2(101),
    DEF7        VARCHAR2(101),
    DEF8        VARCHAR2(101),
    DEF9        VARCHAR2(101),
    DEF10       VARCHAR2(101),
    TS          CHAR(19) default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss')
);
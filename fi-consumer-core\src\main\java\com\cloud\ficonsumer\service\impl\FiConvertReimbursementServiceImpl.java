
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertReimbursement;
import com.cloud.ficonsumer.mapper.FiConvertReimbursementMapper;
import com.cloud.ficonsumer.service.FiConvertReimbursementService;
import org.springframework.stereotype.Service;

/**
 * 通用报销单转换数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:59
 */
@Service
public class FiConvertReimbursementServiceImpl extends ServiceImpl<FiConvertReimbursementMapper, FiConvertReimbursement> implements FiConvertReimbursementService {

}

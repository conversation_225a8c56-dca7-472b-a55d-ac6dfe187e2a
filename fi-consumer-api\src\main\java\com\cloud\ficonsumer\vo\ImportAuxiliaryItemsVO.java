package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/8/3.
 */
@Data
@ColumnWidth(25)
public class ImportAuxiliaryItemsVO {

    /**
     * 科目编码
     */
    @NotBlank(message = "科目编码不可为空")
    @ExcelProperty(value="科目编码")
    private String itemNumber;

    /**
     * 科目名称
     */
    @ExcelProperty(value="科目名称")
    private String itemName;

    /**
     * 辅助核算
     */
    @ExcelProperty(value="辅助核算")
    private String auxiliary;

    /**
     * 方向
     */
    @ApiModelProperty(value="方向")
    private String direction;

    /**
     * 期初余额
     */
    @ApiModelProperty(value="期初余额")
    private String startBalance;

    /**
     * 本期借方
     */
    @ApiModelProperty(value="本期借方")
    private String borrow;

    /**
     * 本期贷方
     */
    @ApiModelProperty(value="本期贷方")
    private String loan;

    /**
     * 借方累计
     */
    @ApiModelProperty(value="借方累计")
    private String borrowTotal;

    /**
     * 贷方累计
     */
    @ApiModelProperty(value="贷方累计")
    private String loanTotal;

    /**
     * 期末方向
     */
    @ApiModelProperty(value="期末方向")
    private String endDirection;

    /**
     * 期末余额
     */
    @ApiModelProperty(value="期末余额")
    private String endBalance;
}

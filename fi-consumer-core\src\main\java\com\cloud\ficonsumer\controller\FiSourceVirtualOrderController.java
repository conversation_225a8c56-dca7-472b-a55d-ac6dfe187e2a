

package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.FiConvertVirtualOrderDTO;
import com.cloud.ficonsumer.dto.FiConvertVirtualOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceVirtualOrder;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceVirtualOrderService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceVirtualOrderQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 虚拟采购订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/virtualOrder" )
@Api(value = "virtualOrder", tags = "虚拟采购订单源数据主表管理")
public class FiSourceVirtualOrderController {

    private final FiSourceVirtualOrderService fiSourceVirtualOrderService;

    /**
     * 报错重推
     *
     * @return
     */
    @ApiOperation(value = "报错重推", notes = "报错重推")
    @GetMapping("/pushAgain/{pk}")
    public R<Boolean> pushAgain(@PathVariable("pk") String pk) {
        return R.ok(fiSourceVirtualOrderService.pushAgain(pk));
    }

    /**
     * 订单转换前分页查询
     *
     * @return
     */
    @ApiOperation(value = "订单转换前分页查询", notes = "订单转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiSourceVirtualOrderDTO>> beforeConvertDataPage(Page page, FiSourceVirtualOrderQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        return R.ok(fiSourceVirtualOrderService.beforeConvertDataPage(page,queryVO));
    }

    /**
     * 转换前数据
     * @return
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage" )
    public R<Page<FiSourceVirtualOrderDetailDTO>> getBeforeConvertDataPage(Page page, FiSourceVirtualOrderQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getPkContr(), "主键不能为空");
        return R.ok(fiSourceVirtualOrderService.getBeforeConvertDataPage(page,queryVO));
    }

    /**
     * 主体表转换后单条字段
     * @return
     */
    @ApiOperation(value = "主体表转换后单条字段", notes = "主体表转换后单条字段")
    @GetMapping("/getConvertList" )
    public R<List<FiConvertVirtualOrderDTO>> getConvertList(String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceVirtualOrderService.getConvertList(pk));
    }
    /**
     * 获取转换后详情分页数据
     * @return
     */
    @ApiOperation(value = "获取转换后详情分页数据", notes = "获取转换后详情分页数据")
    @GetMapping("/getConvertData" )
    public R<Page<FiConvertVirtualOrderDetailDTO>> getConvertData(Page page, String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceVirtualOrderService.getConvertData(page,pk));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result = fiSourceVirtualOrderService.update(Wrappers.<FiSourceVirtualOrder>lambdaUpdate()
                .set(FiSourceVirtualOrder::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiSourceVirtualOrder::getPushMsg, "成功")
                .in(FiSourceVirtualOrder::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiSourceVirtualOrderService.pushAgainBatch(queryVO));
    }
}



package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.FiInvoicePostingDTO;
import com.cloud.ficonsumer.dto.FiInvoicePostingItemDTO;
import com.cloud.ficonsumer.entity.FiInvoicePosting;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiInvoicePostingService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiInvoicePostingQueryVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;


/**
 * 服务物资发票过账
 *
 * <AUTHOR>
 * @date 2025-04-22 10:04:33
 */
@RestController
@AllArgsConstructor
@RequestMapping("/invoicePosting" )
@Api(value = "invoicePosting", tags = "采购应付源数据详情管理")
public class FiInvoicePostingController {

    private final  FiInvoicePostingService fiInvoicePostingService;

    /**
     * 转换前分页查询
     *
     * @return
     */
    @ApiOperation(value = "转换前分页查询", notes = "转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiInvoicePostingDTO>> beforeConvertDataPage(Page page, FiInvoicePostingQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        return R.ok(fiInvoicePostingService.beforeConvertDataPage(page,queryVO));
    }

    /**
     * 转换前数据
     * @return
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage" )
    public R<Page<FiInvoicePostingItemDTO>> getBeforeConvertDataPage(Page page, FiInvoicePostingQueryVO queryVO) {
//        AssertUtils.notBlank(queryVO.getPkPayablebill(), "主键不能为空");
        return R.ok(fiInvoicePostingService.getBeforeConvertDataPage(page,queryVO));
    }

    /**
     * 报错重推
     *
     * @return
     */
    @ApiOperation(value = "报错重推", notes = "报错重推")
    @GetMapping("/pushAgain/{busibillno}")
    public R<Boolean> pushAgain(@PathVariable("busibillno") String busibillno) {
        return R.ok(fiInvoicePostingService.pushHistory(busibillno));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result = fiInvoicePostingService.update(Wrappers.<FiInvoicePosting>lambdaUpdate()
                .set(FiInvoicePosting::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiInvoicePosting::getPushMsg, "成功")
                .in(FiInvoicePosting::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiInvoicePostingService.pushAgainBatch(queryVO));
    }
}


package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceBaseOrderDetail;
import com.cloud.ficonsumer.mapper.FiSourceBaseOrderDetailMapper;
import com.cloud.ficonsumer.service.FiSourceBaseOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * 订单源数据转换详情表
 *
 * <AUTHOR>
 * @date 2025-06-03 11:10:03
 */
@Service
public class FiSourceBaseOrderDetailServiceImpl extends ServiceImpl<FiSourceBaseOrderDetailMapper, FiSourceBaseOrderDetail> implements FiSourceBaseOrderDetailService {

}

package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertProjsubPayable;
import com.cloud.ficonsumer.mapper.FiConvertProjsubPayableMapper;
import com.cloud.ficonsumer.service.FiConvertProjsubPayableService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiConvertProjsubPayableServiceImpl extends ServiceImpl<FiConvertProjsubPayableMapper, FiConvertProjsubPayable> implements FiConvertProjsubPayableService {

}

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/2/28.
 */
@Data
public class StatBatchPayVO {

    /**
     * 收款方银行联行号
     */
    @ApiModelProperty(value="收款方银行联行号")
    private String rcvrBId;

    /**
     * 收款方开户行名称
     */
    @ApiModelProperty(value="收款方开户行名称")
    private String bnAccBkNm;

    /**
     * 收款方名称
     */
    @ApiModelProperty(value="收款方名称")
    private String rcptName;

    /**
     * 收款方银行名称
     */
    @ApiModelProperty(value="收款方银行名称")
    private String beneBankName;

    /**
     * 支付摘要
     */
    @ApiModelProperty(value="支付摘要")
    private String pmtSummary;

    /**
     * 支付金额
     */
    @ApiModelProperty(value="支付金额")
    private BigDecimal pyAmt;

    /**
     * 收款方银行编码
     */
    @ApiModelProperty(value="收款方银行编码")
    private String beneBankCode;

    /**
     * 身份证号码
     */
    @ApiModelProperty(value="身份证号码")
    private String idNum;

    /**
     * 收款方银行账号
     */
    @ApiModelProperty(value="收款方银行账号")
    private String rcvrBAcc;

}

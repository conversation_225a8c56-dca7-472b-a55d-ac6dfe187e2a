package com.cloud.ficonsumer.config;

import com.cloud.apiexchange.client.factory.impl.CloudMqSpringClientFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/7/15.
 */
@Configuration
public class MQClient {

    @Value("${cloud.mq.admin.address}")
    private String adminAddress;
    @Value("${cloud.mq.accessToken}")
    private String accessToken;

    @Bean
    public CloudMqSpringClientFactory getCloudMqConsumer(){

        CloudMqSpringClientFactory cloudMqSpringClientFactory = new CloudMqSpringClientFactory();
        cloudMqSpringClientFactory.setAdminAddress(adminAddress);
        cloudMqSpringClientFactory.setAccessToken(accessToken);

        return cloudMqSpringClientFactory;
    }
}


package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceReceivableDetail;
import com.cloud.ficonsumer.mapper.FiSourceReceivableDetailMapper;
import com.cloud.ficonsumer.service.FiSourceReceivableDetailService;
import org.springframework.stereotype.Service;

/**
 * 项目应收详情单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:35
 */
@Service
public class FiSourceReceivableDetailServiceImpl extends ServiceImpl<FiSourceReceivableDetailMapper, FiSourceReceivableDetail> implements FiSourceReceivableDetailService {

}

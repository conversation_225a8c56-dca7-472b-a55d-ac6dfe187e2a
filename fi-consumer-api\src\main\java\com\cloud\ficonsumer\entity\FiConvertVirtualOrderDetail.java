

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 虚拟采购订单转换数据详情表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:08
 */
@Data
@TableName("fi_convert_virtual_order_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "虚拟采购订单转换数据详情表")
public class FiConvertVirtualOrderDetail extends BaseEntity<FiConvertVirtualOrderDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 单据详情主键
     */
    @ApiModelProperty(value="单据详情主键")
    private String pkContrWorks;

    /**
     * 单据主键
     */
    @ApiModelProperty(value="单据主键")
    private String pkContr;

    /**
     * 订单编号
     */
    @ApiModelProperty(value="订单编号")
    private String billCode;

    /**
     * 行号
     */
    @ApiModelProperty(value="行号")
    private String rowno;

    /**
     * 项目
     */
    @ApiModelProperty(value="项目")
    private String pkProject;

    /**
     * 不含税合同金额
     */
    @ApiModelProperty(value="不含税合同金额")
    private String currMny;

    /**
     * 税率
     */
    @ApiModelProperty(value="税率")
    private String taxrate;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String tax;

    /**
     * 含税金额
     */
    @ApiModelProperty(value="含税金额")
    private String currTaxmny;

    /**
     * 含税单价
     */
    @ApiModelProperty(value="含税单价")
    private String taxprice;

    /**
     * 数量
     */
    @ApiModelProperty(value="数量")
    private String currNum;

    /**
     * 组织
     */
    @ApiModelProperty(value="组织")
    private String pkOrg;

    /**
     * 采购组织
     */
    @ApiModelProperty(value="采购组织")
    private String pkOrgV;

    /**
     * 单位
     */
    @ApiModelProperty(value="单位")
    private String pkMeasdoc;

    /**
     * 物料版本信息
     */
    @ApiModelProperty(value="物料版本信息")
    private String pkMaterial;

    /**
     * 物料编码
     */
    @ApiModelProperty(value="物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value="物料名称")
    private String name;

}

package com.cloud.ficonsumer.vo;

import com.cloud.ficonsumer.entity.FiSourcePuPaybillDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购付款明细源数据详情VO")
public class FiSourcePuPaybillDetailVO extends FiSourcePuPaybillDetail {
    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "单据号")
    private String billno;
}

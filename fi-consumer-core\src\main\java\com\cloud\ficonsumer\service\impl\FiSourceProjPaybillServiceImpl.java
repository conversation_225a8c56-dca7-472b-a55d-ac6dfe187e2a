package com.cloud.ficonsumer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceProjPaybillMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourceProjPaybillServiceImpl extends ServiceImpl<FiSourceProjPaybillMapper, FiSourceProjPaybill>
        implements FiSourceProjPaybillService {

    private final FiSourceProjPaybillDetailService detailService;
    private final FiConvertProjPaybillService convertService;
    private final FiConvertProjPaybillDetailService convertDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;
    private final YgConfig ygConfig;

    @Override
    public boolean push(String pkPayablebill) {
        FiSourceProjPaybill source = this.getOne(Wrappers.<FiSourceProjPaybill>lambdaQuery()
                .eq(FiSourceProjPaybill::getPkPaybill, pkPayablebill));
        List<FiSourceProjPaybillDetail> sourceDetails = detailService.list(Wrappers.<FiSourceProjPaybillDetail>lambdaQuery()
                .eq(FiSourceProjPaybillDetail::getPkPaybill, pkPayablebill));

        // 1. 转换数据
        try {
            convertData(source, sourceDetails);
        } catch (Exception e) {
            handleConversionException(source, e);
        }

        // 2. 推送数据到智慧平台
        try {
            pushToYgPlatform(source);
            updatePushSuccess(source);
        } catch (Exception e) {
            handlePushException(source, e);
        }

        return true;
    }

    /**
     * 更新主单推送成功的状态，并保存成功日志
     */
    private void updatePushSuccess(FiSourceProjPaybill source) {
        source.setIntegrationStatus("0");
        source.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
        source.setPushMsg("");
        source.setReadiness(1L);
        this.updateById(source);

        // 保存成功日志
        insertPlatformLog(source, Constants.SUCCESS, "【项目付款接收服务】集成财务中台成功");
    }

    /**
     * 记录转换异常，更新状态，并抛出 CheckedException
     */
    private void handleConversionException(FiSourceProjPaybill source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        // 保存转换失败日志
        insertPlatformLog(source, Constants.FAIL, pushMsg);

        LogUtil.info(log, "项目付款集成信息转换失败:", e);
        throw new CheckedException("项目付款集成信息转换失败:" + pushMsg);
    }

    /**
     * 记录推送异常，更新状态，并抛出 CheckedException
     */
    private void handlePushException(FiSourceProjPaybill source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        // 保存推送失败日志
        insertPlatformLog(source, Constants.FAIL, pushMsg);

        LogUtil.info(log, "项目付款集成信息推送失败:", e);
        throw new CheckedException("项目付款集成信息推送失败:" + pushMsg);
    }

    /**
     * 插入平台日志
     */
    private void insertPlatformLog(FiSourceProjPaybill source, String code, String msg) {
        PlatFormLogVO platFormLogVO = new PlatFormLogVO();
        platFormLogVO.setPkLog(UUID.randomUUID().toString());
        platFormLogVO.setPkBill(source.getPkPaybill());
        platFormLogVO.setPkBillType(BillTypeEnum.PROJ_PAYBILL.getCode());
        platFormLogVO.setCode(code);
        platFormLogVO.setMsg(msg);
        dmMapper.insertPlatFormLog(platFormLogVO);
    }

    /**
     * 推送数据至智慧平台
     */
    private void pushToYgPlatform(FiSourceProjPaybill source) throws Exception {
        if (!ygConfig.getEnable()) {
            return;
        }

        FiConvertProjPaybill convert = convertService.getOne(
                Wrappers.<FiConvertProjPaybill>lambdaQuery()
                        .eq(FiConvertProjPaybill::getPkPaybill, source.getPkPaybill()));
        List<FiConvertProjPaybillDetail> details = convertDetailService.list(
                Wrappers.<FiConvertProjPaybillDetail>lambdaQuery()
                        .eq(FiConvertProjPaybillDetail::getPkPaybill, source.getPkPaybill()));

        YgReimbursementVO ygReimbursementVO = createYgReimbursementVO(convert, details);
        ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000012.getServCode());

        String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(ygReimbursementVO));
        if (StringUtils.isEmpty(result)) {
            throw new CheckedException("响应失败，响应为空。");
        }

        YgResult ygResult = JSONObject.parseObject(result, YgResult.class);
        if (!Constants.SUCCESS.equals(ygResult.getCode())) {
            throw new CheckedException("推送失败，响应结果: " + JSON.toJSONString(ygResult));
        }
    }

    /**
     * 转换数据
     */
    private void convertData(FiSourceProjPaybill sourceMainData, List<FiSourceProjPaybillDetail> details) throws Exception {
        String pkPaybill = sourceMainData.getPkPaybill();
        String billType = BillTypeEnum.PROJ_PAYBILL.getCode();

        // 转换主信息
        FiConvertProjPaybill fiConvert = new FiConvertProjPaybill();
        apiCompareCache.convertData(sourceMainData, fiConvert, billType);

        // 如若已存在转换数据，则使用已有主键并清空老明细数据
        FiConvertProjPaybill existing = convertService.getOne(
                Wrappers.<FiConvertProjPaybill>lambdaQuery()
                        .eq(FiConvertProjPaybill::getPkPaybill, pkPaybill));
        if (existing != null) {
            fiConvert.setId(existing.getId());
            convertDetailService.remove(
                    Wrappers.<FiConvertProjPaybillDetail>lambdaQuery()
                            .eq(FiConvertProjPaybillDetail::getPkPaybill, pkPaybill));
        }

        // 转换明细数据
        List<FiConvertProjPaybillDetail> convertDetailList = new ArrayList<>();
        for (FiSourceProjPaybillDetail sourceDetail : details) {
            FiConvertProjPaybillDetail convertDetail = new FiConvertProjPaybillDetail();
            apiCompareCache.convertData(sourceDetail, convertDetail, billType);
            convertDetailList.add(convertDetail);
        }

        convertService.saveOrUpdate(fiConvert);
        convertDetailService.saveOrUpdateBatch(convertDetailList);
    }

    private YgReimbursementVO createYgReimbursementVO(FiConvertProjPaybill convert, List<FiConvertProjPaybillDetail> details) {
        YgReimbursementVO reimbursementVO = new YgReimbursementVO();

        // 设置主表字段
        reimbursementVO.setDwdh(convert.getPkOrgV());          // 单位代号 -> 应付财务组织版本
        reimbursementVO.setHdlgNm(convert.getBillmaker());     // 经办人 -> 制单人
        reimbursementVO.setCosType(convert.getDef6());        // 费用类型 -> def6
        reimbursementVO.setAmount(String.valueOf(convert.getLocalMoney())); // 金额 -> 组织本币金额
        reimbursementVO.setMoneyDm(convert.getPkCurrtype());  // 币种 -> 币种

        // 设置基础明细数据（取第一条明细的公共信息）
        if (!details.isEmpty()) {
            FiConvertProjPaybillDetail firstDetail = details.get(0);
            reimbursementVO.setClaimant(firstDetail.getPkPsndoc());        // 报销人 -> 业务员
            reimbursementVO.setSettleObj(firstDetail.getSupplier());       // 结算对象 -> 供应商
            reimbursementVO.setTProject(firstDetail.getProject());         // 项目 -> 项目
            reimbursementVO.setAmount(String.valueOf(firstDetail.getLocalMoney())); // 金额 -> 组织本币金额
            reimbursementVO.setBeDept(firstDetail.getPkDeptid());         // 职工所属部门 -> 部门
            reimbursementVO.setPrjCode(firstDetail.getProject());     // 项目编码 -> 项目
        }

        // 处理发票信息
        List<InvoiceVO> invoiceList = new ArrayList<>();
        // 处理付款信息
        List<PayDetVO> payDetList = new ArrayList<>();
        // 处理费用明细信息
        List<YgReimbursementDetailVO> costDetList = new ArrayList<>();

        // 遍历明细，构建相关VO
        for (FiConvertProjPaybillDetail detail : details) {
            // 1. 构建发票信息
            InvoiceVO invoiceVO = new InvoiceVO();
            invoiceVO.setInvoiceNo(detail.getInvoiceNo());              // 发票号码
            invoiceList.add(invoiceVO);

            // 2. 构建付款信息
            PayDetVO payDetVO = new PayDetVO();
            payDetVO.setDetPayBizType(detail.getDef35());                 // 明细付款业务类型 -> 款项性质
            payDetVO.setPyMethod(detail.getPkBalatype());          // 支付方式 -> 结算方式
            payDetVO.setPayAmo(detail.getLocalMoney()); // 本次应付金额本位币
            payDetList.add(payDetVO);

            // 3. 构建费用明细信息
            YgReimbursementDetailVO costDetVO = new YgReimbursementDetailVO();
            costDetVO.setAmount(detail.getMoneyDe());        // 金额 -> 借方原币金额
            costDetVO.setAmtExTaxCy(detail.getLocalNotaxDe()); // 不含税金额 -> 组织本币无税金额
            costDetVO.setCosType(detail.getDef6());                        // 费用类型
            costDetVO.setCosTypeCode(detail.getDef6());                    // 费用类型编码
            costDetVO.setDetCosType(detail.getPkSubjcode());           // 明细费用类型 -> 收支项目
            costDetVO.setReimbAmt(detail.getLocalMoney()); // 报销金额
            costDetVO.setVatAmt(detail.getLocalTaxDe()); // 增值税税额 -> 税额
            costDetVO.setTProject(detail.getProject());                     // 项目
            costDetList.add(costDetVO);
        }

        // 设置所有明细列表
        reimbursementVO.setGenerbillistatInvoice(invoiceList);       // 发票信息列表
        reimbursementVO.setGenerbillistatPayDet(payDetList);         // 付款信息列表
        reimbursementVO.setGenerbillistatCostDet(costDetList);       // 费用明细列表

        return reimbursementVO;
    }

}


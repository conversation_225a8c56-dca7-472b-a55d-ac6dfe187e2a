package com.cloud.ficonsumer.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiSourcePuPayableQueryDTO;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourcePuPayableService;
import com.cloud.ficonsumer.vo.FiSourcePuPayableExportVO;
import com.cloud.ficonsumer.vo.FiSourcePuPayableVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class FiSourcePuPayableExportListener extends AbstractExportListener<FiSourcePuPayableExportVO> {

    private final FiSourcePuPayableService fiSourcePuPayableService;
    private final FiSourcePuPayableQueryDTO queryDTO;
    private final Page<FiSourcePuPayableVO> page;
    private static final int PAGE_SIZE = 1000;

    public FiSourcePuPayableExportListener(UUID id,
                                         FiSourcePuPayableService fiSourcePuPayableService,
                                         Page<FiSourcePuPayableVO> page,
                                         FiSourcePuPayableQueryDTO queryDTO) {
        super(id, "采购应付转换前数据");
        this.page = page;
        this.fiSourcePuPayableService = fiSourcePuPayableService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiSourcePuPayableExportVO> doHandleData() {
        List<FiSourcePuPayableVO> vos = new ArrayList<>();

        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiSourcePuPayableVO> pageResult = fiSourcePuPayableService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiSourcePuPayableVO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiSourcePuPayableVO> pageResult;
            do {
                pageResult = fiSourcePuPayableService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }

        List<FiSourcePuPayableExportVO> result = BeanUtil.copyToList(vos, FiSourcePuPayableExportVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}
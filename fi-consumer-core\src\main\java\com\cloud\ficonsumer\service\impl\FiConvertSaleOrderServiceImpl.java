package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertSaleOrder;
import com.cloud.ficonsumer.mapper.FiConvertSaleOrderMapper;
import com.cloud.ficonsumer.service.FiConvertSaleOrderService;
import org.springframework.stereotype.Service;

@Service
public class FiConvertSaleOrderServiceImpl extends ServiceImpl<FiConvertSaleOrderMapper, FiConvertSaleOrder> implements FiConvertSaleOrderService {

}


package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.client.message.CloudMqMessage;
import com.cloud.apiexchange.client.producer.CloudMqProducer;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.dto.FiInvoicePostingDTO;
import com.cloud.ficonsumer.dto.FiInvoicePostingItemDTO;
import com.cloud.ficonsumer.dto.YgInvoiceStatus;
import com.cloud.ficonsumer.dto.YgRequestInvoicePosting;
import com.cloud.ficonsumer.dto.YgRequestInvoicePostingItem;
import com.cloud.ficonsumer.entity.FiInvoicePosting;
import com.cloud.ficonsumer.entity.FiInvoicePostingItem;
import com.cloud.ficonsumer.entity.FiOrgCompare;
import com.cloud.ficonsumer.entity.FiSourceFileOcrInfo;
import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import com.cloud.ficonsumer.entity.FiSourcePuPayableDetail;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.FiInvoicePostingMapper;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.service.FiInvoicePostingItemService;
import com.cloud.ficonsumer.service.FiInvoicePostingService;
import com.cloud.ficonsumer.service.FiOrgCompareService;
import com.cloud.ficonsumer.service.FiSourcePuPayableDetailService;
import com.cloud.ficonsumer.service.FiSourcePuPayableService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.FiInvoicePostingQueryVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.HistoryOrderListVO;
import com.cloud.ficonsumer.vo.HistoryOrderVO;
import com.cloud.ficonsumer.vo.YgResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 采购应付源数据详情
 *
 * <AUTHOR>
 * @date 2025-04-22 10:04:33
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiInvoicePostingServiceImpl extends ServiceImpl<FiInvoicePostingMapper, FiInvoicePosting> implements FiInvoicePostingService {

    private final FiInvoicePostingItemService fiInvoicePostingItemService;
    private final FiSourcePuPayableService fiSourcePuPayableService;
    private final FiSourcePuPayableDetailService fiSourcePuPayableDetailService;
    private final ApiCompareService apiCompareService;
    private final FiOrgCompareService fiOrgCompareService;

    /**
     * 挂账的时候回传“采购/项目应付单更新历史订单转换查询”
     *
     * @param list
     * @return
     */
    @Override
    public List<YgInvoiceStatus> saveInvoicePosting(List<YgRequestInvoicePosting> list) {
        List<YgInvoiceStatus> dataList = new ArrayList<>();
        for(YgRequestInvoicePosting data : list) {
            YgInvoiceStatus source = new YgInvoiceStatus();
            source.setAppid(data.getAppid());
            source.setTypeid(data.getTypeid());
            source.setMktdocnbr(data.getBusibillno());
            source.setSapdocid(data.getBusibillno());
            source.setBukrs(data.getComcod());
            source.setType("S");
            source.setMessage("成功");
            source.setPrctr(data.getPrctr());
            List<YgRequestInvoicePostingItem> item = data.getItem();
            if(CollectionUtil.isNotEmpty(item)) {
                source.setGjahr(item.get(0).getFisyea());
            }else {
                throw new CheckedException("订单明细详情不能为空");
            }
            dataList.add(source);
            //采购/项目应付单更新历史订单转换查询
            String busibillno = data.getBusibillno();
            if(StrUtil.isBlank(busibillno)) {
                throw new CheckedException("业务单据ID不能为空");
            }
            FiInvoicePosting fiInvoicePosting = new FiInvoicePosting();
            BeanUtil.copyProperties(data,fiInvoicePosting);
            if(StrUtil.isNotBlank(fiInvoicePosting.getComcod())) {
                FiOrgCompare fiOrgCompare = fiOrgCompareService.getOne(Wrappers.<FiOrgCompare>lambdaQuery()
                        .eq(FiOrgCompare::getMdmOrgCode, fiInvoicePosting.getComcod()));
                if(null != fiOrgCompare) {
                    fiInvoicePosting.setPkOrg(fiOrgCompare.getPkOrg());
                    fiInvoicePosting.setPkOrgName(fiOrgCompare.getOrgName());
                }
            }
            List<FiInvoicePostingItem> fiInvoicePostingItemList = new ArrayList<>();
            for(YgRequestInvoicePostingItem orderItem : item) {
                FiInvoicePostingItem fiInvoicePostingItem = new FiInvoicePostingItem();
                BeanUtil.copyProperties(orderItem,fiInvoicePostingItem);
                fiInvoicePostingItem.setBusibillno(busibillno);
                if(StrUtil.isBlank(fiInvoicePostingItem.getPurordno())) {
                    throw new CheckedException("采购订单号不能为空");
                }
                if(StrUtil.isBlank(fiInvoicePostingItem.getOrdprojid())) {
                    throw new CheckedException("采购凭证的项目编号不能为空");
                }
                fiInvoicePostingItemList.add(fiInvoicePostingItem);
            }
            FiInvoicePosting sourceEntity = this.getOne(Wrappers.<FiInvoicePosting>lambdaQuery().eq(FiInvoicePosting::getBusibillno, busibillno));
            if (sourceEntity != null) {
                fiInvoicePosting.setId(sourceEntity.getId());
                fiInvoicePostingItemService.remove(Wrappers.<FiInvoicePostingItem>lambdaQuery().eq(FiInvoicePostingItem::getBusibillno, busibillno));
            }
            fiInvoicePosting.setPushStatus(PushStatusEnum.PUSH_DING.getCode());
            this.saveOrUpdate(fiInvoicePosting);
            fiInvoicePostingItemService.saveOrUpdateBatch(fiInvoicePostingItemList);
            CloudMqMessage cloudMqMessage = new CloudMqMessage();
            cloudMqMessage.setTopic(Constants.TOPIC_INVOICE_POSTING_TRANS);
            cloudMqMessage.setMsgGroup(Constants.Group.group);
            cloudMqMessage.setMsgData(busibillno);
            CloudMqProducer.produce(cloudMqMessage);
        }
        return dataList;
    }

    /**
     * 服务物资发票过账推送开始
     * @return
     */
    @Override
    public boolean pushHistory(String busibillno) {
        //重新推送智慧平台
        FiInvoicePosting sourceMainData = this.getOne(Wrappers.<FiInvoicePosting>lambdaQuery().eq(FiInvoicePosting::getBusibillno, busibillno));
        try {
            List<FiInvoicePostingItem> fiInvoicePostingItemList = fiInvoicePostingItemService.list(Wrappers.<FiInvoicePostingItem>lambdaQuery().eq(FiInvoicePostingItem::getBusibillno, busibillno));
            HistoryOrderListVO request = new HistoryOrderListVO();
            List<HistoryOrderVO> dataNew = new ArrayList<>();

            FiSourcePuPayable fiSourcePuPayable = fiSourcePuPayableService.getOne(Wrappers.<FiSourcePuPayable>lambdaQuery().eq(FiSourcePuPayable::getYwid, busibillno));
            if(null == fiSourcePuPayable) {
                throw new CheckedException("应付单不存在:"+busibillno);
            }
            List<FiSourcePuPayableDetail> fiSourcePuPayableDetailList = fiSourcePuPayableDetailService.list(Wrappers.<FiSourcePuPayableDetail>lambdaQuery().eq(FiSourcePuPayableDetail::getPkPayablebill, fiSourcePuPayable.getPkPayablebill()));
            Map<String, FiSourcePuPayableDetail> map = new HashMap<>();
            for(FiSourcePuPayableDetail fiSourcePuPayableDetail : fiSourcePuPayableDetailList) {
                map.put(fiSourcePuPayableDetail.getPurchaseorder()+fiSourcePuPayableDetail.getCrowno(),fiSourcePuPayableDetail);
            }
            for(FiInvoicePostingItem orderItem : fiInvoicePostingItemList) {
                HistoryOrderVO historyOrderVO = new HistoryOrderVO();
                historyOrderVO.setPrvMkCode(orderItem.getComcod());
                historyOrderVO.setPurOrdNo(orderItem.getPurordno());
                historyOrderVO.setOrdProjId(orderItem.getOrdprojid());
                historyOrderVO.setMtdocYear(orderItem.getMtdocyear());
//                historyOrderVO.setMtDocNum(orderItem.getAccvouno());
//                historyOrderVO.setMtDocNumLine(orderItem.getInvoicedocitem());
//                historyOrderVO.setPurReqQty(orderItem.getOrdqua());
//                historyOrderVO.setDifAmoInLocCur(orderItem.getPurorditemamt());
//                historyOrderVO.setMtCode(orderItem.getMtcode());
                FiSourcePuPayableDetail detail = map.get(orderItem.getPurordno() + orderItem.getOrdprojid());
                if(null == detail) {
                    throw new CheckedException("行数据错误");
                }
                historyOrderVO.setMtDocNum(fiSourcePuPayable.getBillno());
                historyOrderVO.setMtDocNumLine(detail.getRowno());
                historyOrderVO.setPurReqQty(ObjectUtils.isNull(detail.getQuantityCr())?null:detail.getQuantityCr().toString());
                historyOrderVO.setDifAmoInLocCur(ObjectUtils.isNull(detail.getLocalNotaxCr())?null:detail.getLocalNotaxCr().toString());
//                historyOrderVO.setMtCode(detail.getMaterial());
//                historyOrderVO.setReturnsItem(ObjectUtils.isNull(detail.getDirection())?null:detail.getDirection().toString());
                historyOrderVO.setReturnsItem("H");
                historyOrderVO.setSeqNoAccAssig("01");
                historyOrderVO.setPurOrdHisCat("Q");
                historyOrderVO.setTraTypPurOrdHis("2");
                historyOrderVO.setInputTime(new SimpleDateFormat("HH-mm-ss").format(new Date()));
                dataNew.add(historyOrderVO);
            }
            request.setList(dataNew);
            ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000029.getServCode());
            String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(request));
            YgResult ygResult = JSONObject.parseObject(result, YgResult.class);
            //000000：成功 其它：失败
            if (!ygResult.getCode().equals("000000")) {
                throw new CheckedException(ygResult.getmessage());
            }
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
            sourceMainData.setPushMsg("");
            sourceMainData.setReadiness(1l);
            this.updateById(sourceMainData);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            this.updateById(sourceMainData);
            LogUtil.info(log, "服务物资发票过账推送失败:", e);
            throw new CheckedException("服务物资发票过账推送失败:"+pushMsg);
        }
        return true;
    }

    @Override
    public Page<FiInvoicePostingDTO> beforeConvertDataPage(Page page, FiInvoicePostingQueryVO queryVO) {
        Page<FiInvoicePostingDTO> result = this.baseMapper.beforeConvertDataPage(page,queryVO);
        if (CollectionUtil.isNotEmpty(result.getRecords())){
            for (FiInvoicePostingDTO data : result.getRecords()) {
                data.setPushStatusName(PushStatusEnum.getByCode(data.getPushStatus()).getName());
            }
        }
        return result;
    }

    @Override
    public Page<FiInvoicePostingItemDTO> getBeforeConvertDataPage(Page page, FiInvoicePostingQueryVO queryVO) {
        Page<FiInvoicePostingItemDTO> result = this.baseMapper.getBeforeConvertDataPage(page,queryVO);
        return result;
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiInvoicePosting entity = this.getById(id);
            try {
                this.pushHistory(entity.getBusibillno());
            } catch (Exception e) {
                resultList.add(entity.getBusibillno()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}

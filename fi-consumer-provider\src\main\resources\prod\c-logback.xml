<?xml version="1.0" encoding="UTF-8"?>


<!--
    小技巧: 在根pom里面设置统一存放路径，统一管理方便维护
    1. 其他模块加日志输出，直接copy本文件放在resources 目录即可
    2. 注意修改 <property name="${log-path}/log.path" value=""/> 的value模块
-->
<configuration debug="false" scan="false">
	<property name="log.path" value="logs/${project.artifactId}"/>
	<property name="appname" value="${project.artifactId}"/>
	<!-- 彩色日志格式 -->
	<property name="CONSOLE_LOG_PATTERN"
			  value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
	<!-- 彩色日志依赖的渲染类 -->
	<conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
	<conversionRule conversionWord="wex"
					converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
	<conversionRule conversionWord="wEx"
					converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

	<!--nacos 心跳 INFO 屏蔽-->
<!--	<logger name="com.alibaba.nacos" level="OFF">-->
<!--		<appender-ref ref="error"/>-->
<!--	</logger>-->

	<appender name="sentinelConsole" class="ch.qos.logback.core.ConsoleAppender">
		<layout class="ch.qos.logback.classic.PatternLayout">
			<pattern>%-5level %logger - %msg%n</pattern>
		</layout>
	</appender>

	<appender name="sentinelRecord" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${log.path}/sentinel-record.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${log.path}/%d{yyyy-MM, aux}/sentinel-record.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
			<maxFileSize>50MB</maxFileSize>
			<maxHistory>5</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%-5level %logger - %msg%n</pattern>
		</encoder>
	</appender>
	<appender name="sentinelCommandCenter" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${log.path}/sentinel-command-center.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${log.path}/%d{yyyy-MM, aux}/sentinel-command-center.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
			<maxFileSize>50MB</maxFileSize>
			<maxHistory>5</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%-5level %logger - %msg%n</pattern>
		</encoder>
	</appender>

	<logger name="sentinelRecordLogger" level="trace">
		<appender-ref ref="sentinelConsole" />
		<appender-ref ref="sentinelRecord" />
	</logger>

	<logger name="sentinelCommandCenterLogger" level="trace">
		<appender-ref ref="sentinelConsole" />
		<appender-ref ref="sentinelCommandCenter" />
	</logger>
	<!-- 导入 -->
	<include resource="com/cloud/cloud/common/log/logback-console.xml"/>
	<include resource="com/cloud/cloud/common/log/logback-log-file.xml"/>

	<!-- 开发输出级别 -->
	<root level="INFO">
		<appender-ref ref="console"/>
		<appender-ref ref="asyncFileInfoLog"/>
	</root>
</configuration>
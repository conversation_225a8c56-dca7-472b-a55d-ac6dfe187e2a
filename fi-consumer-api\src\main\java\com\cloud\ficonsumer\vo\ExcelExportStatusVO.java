package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/12 15:12
 */
@Data
@ApiModel(value = "excel导出状态实体")
public class ExcelExportStatusVO implements Serializable {
    @ApiModelProperty(value = "总数")
    private Integer sum = 0;
    @ApiModelProperty(value = "处理数")
    private Integer handle = 0;
    @ApiModelProperty(value = "错误信息")
    private List<String> errorMsg = new ArrayList<>();
    @ApiModelProperty(value = "是否结束")
    private Boolean isFinish = false;
    @ApiModelProperty(value = "是否成功")
    private Boolean result = false;
    @ApiModelProperty(value = "文件路径")
    private String url;
}

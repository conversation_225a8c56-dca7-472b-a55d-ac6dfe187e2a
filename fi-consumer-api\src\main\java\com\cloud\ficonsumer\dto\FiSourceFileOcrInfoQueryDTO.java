package com.cloud.ficonsumer.dto;

import com.cloud.ficonsumer.entity.FiSourceFileOcrInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class FiSourceFileOcrInfoQueryDTO extends FiSourceFileOcrInfo {

    @ApiModelProperty("业+组织ID")
    private Long orgId;

    @ApiModelProperty("业+组织编码")
    private String orgCode;

    /**
     * 推送状态列表，用于多选查询
     */
    @ApiModelProperty(value = "推送状态列表，用于多选查询")
    private List<Integer> pushStatusList;

    /**
     * 拉取起始时间
     */
    @ApiModelProperty(value="拉取起始时间")
    private String createTimeStart;


    /**
     * 拉取结束时间
     */
    @ApiModelProperty(value="拉取结束时间")
    private String createTimeEnd;
}

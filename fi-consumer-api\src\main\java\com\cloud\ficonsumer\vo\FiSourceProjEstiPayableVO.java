package com.cloud.ficonsumer.vo;

import com.cloud.ficonsumer.entity.FiSourceProjEstiPayable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目暂估应付", description = "项目暂估应付信息")
public class FiSourceProjEstiPayableVO extends FiSourceProjEstiPayable {

}

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/3/12.
 */
@Data
public class StoreDetailVO {

    /**
     * 采购订单号
     */
    @ApiModelProperty(value="采购订单号")
    private String purOrd;

    /**
     * 采购订单行号
     */
    @ApiModelProperty(value="采购订单行号")
    private String orderLineNo;

    /**
     * 入库数量
     */
    @ApiModelProperty(value="入库数量")
    private BigDecimal inWhsQty;

    /**
     * 含税单价
     */
    @ApiModelProperty(value="含税单价")
    private BigDecimal inWhsUnitPriceIncTax;

    /**
     * 不含税单价
     */
    @ApiModelProperty(value="不含税单价")
    private BigDecimal inWhsUnitPriceExcTax;

    /**
     * 税率
     */
    @ApiModelProperty(value="税率")
    private BigDecimal taxrt;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private BigDecimal taxAmount;

    /**
     * 含税金额
     */
    @ApiModelProperty(value="含税金额")
    private BigDecimal taxAmt;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value="不含税金额")
    private BigDecimal amtExTax;

    /**
     * 批号
     */
    @ApiModelProperty(value="批号")
    private String batNum;

    /**
     * 项目编码
     */
    @ApiModelProperty(value="项目编码")
    private String prjCode;

    /**
     * 物料编码
     */
    @ApiModelProperty(value="物料编码")
    private String mtCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value="物料名称")
    private String MtName;

    /**
     * 备注信息
     */
    @ApiModelProperty(value="备注信息")
    private String Note;

    /**
     * 入库单行号
     */
    @ApiModelProperty(value="入库单行号")
    private String lineNo;

    /**
     * WBS编号
     */
    @ApiModelProperty(value="WBS编号")
    private String wbsCod;

    /**
     * 用途
     * 00000001-项目
     * 00000004-检修运维
     * 00000013-销售
     * 00000018-零购资产
     * 00000022-劳动保护
     * 00000023-低值易耗品
     * 00000024-办公用品及杂费
     */
    @ApiModelProperty(value="用途")
    private String purpose;

    /**
     * 库存类型
     */
    @ApiModelProperty(value="库存类型")
    private String inveTyp;

}

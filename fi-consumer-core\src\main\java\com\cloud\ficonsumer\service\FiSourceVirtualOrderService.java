

package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiConvertVirtualOrderDTO;
import com.cloud.ficonsumer.dto.FiConvertVirtualOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceVirtualOrder;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceVirtualOrderQueryVO;

import java.util.List;

/**
 * 虚拟采购订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:26
 */
public interface FiSourceVirtualOrderService extends IService<FiSourceVirtualOrder> {

    boolean pushAgain(String s);

    Page<FiSourceVirtualOrderDTO> beforeConvertDataPage(Page page, FiSourceVirtualOrderQueryVO queryVO);

    Page<FiSourceVirtualOrderDetailDTO> getBeforeConvertDataPage(Page page, FiSourceVirtualOrderQueryVO queryVO);

    List<FiConvertVirtualOrderDTO> getConvertList(String pk);

    Page<FiConvertVirtualOrderDetailDTO> getConvertData(Page page, String pk);

    /**
     * 校验付款合同同步状态
     *
     * @param pkContr 付款合同主键
     */
    void checkSync(String pkContr);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}

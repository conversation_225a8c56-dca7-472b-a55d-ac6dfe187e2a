

package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiConvertOrderDTO;
import com.cloud.ficonsumer.dto.FiConvertOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceOrder;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceOrderQueryVO;

import java.util.List;

/**
 * 订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-16 16:53:16
 */
public interface FiSourceOrderService extends IService<FiSourceOrder> {

    /**
     * 订单重新转换推送
     * @param pkOrder
     * @return
     */
    boolean pushAgain(String pkOrder);

    /**
     * 查询订单是否已经推送智慧平台（推送了后续单子才能继续）
     * @param pkOrder
     * @return
     */
    boolean getOrderReadiness(String pkOrder);

    /**
     * 订单转换前分页查询
     *
     * @return
     */
    Page<FiSourceOrderDTO> beforeConvertDataPage(Page page, FiSourceOrderQueryVO queryVO);

    /**
     * 转换前详情查询
     *
     * @param page
     * @param queryVO
     * @return
     */
    Page<FiSourceOrderDetailDTO> getBeforeConvertDataPage(Page page, FiSourceOrderQueryVO queryVO);

    /**
     * 主体表转换后单条字段
     *
     * @param pk
     * @return
     */
    List<FiConvertOrderDTO> getConvertList(String pk);

    /**
     * 获取转换后详情分页数据
     *
     * @param page
     * @param pk
     * @return
     */
    Page<FiConvertOrderDetailDTO> getConvertData(Page page, String pk);

    /**
     * 校验采购订单同步状态
     *
     * @param pkOrder 采购订单主键
     */
    void checkSync(String pkOrder);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}

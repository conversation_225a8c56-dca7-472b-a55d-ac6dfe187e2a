package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/27.
 */
@Data
public class BillStatisticsVO {

    /**
     * 集团名称
     */
    @ApiModelProperty(value="集团名称")
    private String pkGroupName;

    /**
     * 业+推送数据量
     */
    @ApiModelProperty(value="业+推送数据量")
    private String sumNum;

    /**
     * 推送失败数量
     */
    @ApiModelProperty(value="推送失败数量")
    private String failNum;

    /**
     * 推送成功数量
     */
    @ApiModelProperty(value="推送成功数量")
    private String successNum;

    /**
     * 业+推送成功率
     */
    @ApiModelProperty(value="业+推送成功率")
    private String successRate;
}

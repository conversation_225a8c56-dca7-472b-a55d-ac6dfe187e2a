package com.cloud.ficonsumer;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.entity.FiSourceFileOcrInfo;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.FileManageMapper;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.service.FiSourceFileOcrInfoService;
import com.cloud.ficonsumer.utils.UdsUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.FiSourceFileInfoVO;
import com.cloud.ficonsumer.vo.FiSourceFileOcrInfoVO;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import com.sgcc.uds.cloud.sdk.action.result.CommonActionResult;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.Connection;
import java.sql.Statement;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class FileOcrInfoPushTest {

    @SpyBean
    private FileManageMapper fileManageMapper;

    @SpyBean
    private ApiProjectService apiProjectService;

    @SpyBean
    private FiSourceFileOcrInfoService fiSourceFileOcrInfoService;

    @SpyBean
    private ApiCompareCache apiCompareCache;

    private String ygInvoiceSuccessResp;
    private String ygInvoiceFailResp;

    @BeforeAll
    public void mockResp() throws Exception {
        // 加载模拟的远光响应
        ygInvoiceSuccessResp = new String(Files.readAllBytes(new ClassPathResource("fileocrinfo/YgInvoiceSuccessResp.json").getFile().toPath()), StandardCharsets.UTF_8);
        ygInvoiceFailResp = new String(Files.readAllBytes(new ClassPathResource("fileocrinfo/YgInvoiceFailResp.json").getFile().toPath()), StandardCharsets.UTF_8);
    }

    @BeforeEach
    public void initData() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);

        // 初始化 filemanage 数据库数据
        DynamicDataSourceContextHolder.push("filemanage");
        try {
            ResourceDatabasePopulator filemanagePopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("fileocrinfo/schema-filemanage.sql"),
                    new ClassPathResource("fileocrinfo/data-filemanage.sql")
            );
            DatabasePopulatorUtils.execute(filemanagePopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 初始化 mysql 库数据
        DynamicDataSourceContextHolder.push("mysql");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("fileocrinfo/schema-ficonsumer.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 同步 filemanage 进项发票到 MySQL
        List<FiSourceFileOcrInfoVO> fileOcrInfoVOS = fileManageMapper.listFileOrcInfo(null);
        for (FiSourceFileOcrInfoVO fileOcrInfoVO : fileOcrInfoVOS) {
            List<FiSourceFileInfoVO> fileInfoVOS = fileManageMapper.getFileInfos(fileOcrInfoVO.getFileId());
            apiProjectService.processFileOcrInfo(fileOcrInfoVO, fileInfoVOS);
        }
    }

    @AfterEach
    public void cleanData() {
        DynamicRoutingDataSource dynamicDataSource = (DynamicRoutingDataSource) SpringContextHolder.getBean(DataSource.class);

        // 清理 filemanage 库
        DataSource filemanageDataSource = dynamicDataSource.getDataSource("filemanage");
        try (Connection conn = filemanageDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 filemanage 数据库失败", e);
        }

        // 清理 mysql 库
        DataSource mysqlDataSource = dynamicDataSource.getDataSource("mysql");
        try (Connection conn = mysqlDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 MySQL 数据库失败", e);
        }
    }

    @Test
    public void testPushSuccess() {
        // 模拟文件ID, 需要替换为实际测试数据中存在的id
        String uniquecodeofinvoice = "4650174102263300232130";

        try (MockedStatic<YgUtil> ygUtil = mockStatic(YgUtil.class);
             MockedStatic<UdsUtil> udsUtil = mockStatic(UdsUtil.class)) {
            // 模拟 YgUtil.sendToYg 方法，返回成功响应
            ygUtil.when(() -> YgUtil.sendToYg(any(), any())).thenReturn(ygInvoiceSuccessResp);
            // 模拟 UdsUtil.pushFileToUdsFromOSS，返回null
            CommonActionResult commonActionResult = new CommonActionResult();
            commonActionResult.setFileName("test.xml");
            commonActionResult.setDocumentId("testDocumentId");
            udsUtil.when(() -> UdsUtil.pushFileToUdsFromOSS(any(), any(), any())).thenReturn(commonActionResult);

            // 执行推送
            boolean result = fiSourceFileOcrInfoService.push(uniquecodeofinvoice);
            assertTrue(result, "推送成功");

            // 验证数据库状态
            FiSourceFileOcrInfo updatedSource = fiSourceFileOcrInfoService.getOne(
                    Wrappers.<FiSourceFileOcrInfo>lambdaQuery()
                            .eq(FiSourceFileOcrInfo::getUniquecodeofinvoice, uniquecodeofinvoice)
            );
            assertEquals("0", updatedSource.getIntegrationStatus(), "集成状态应为成功");
            assertEquals(PushStatusEnum.PUSH_SUCCESS.getCode(), updatedSource.getPushStatus(), "推送状态应为成功");

            // 验证平台日志
            List<PlatFormLogVO> logs = fileManageMapper.selectPlatFormLog(uniquecodeofinvoice, "fi_source_file_ocr_info");
            assertFalse(logs.isEmpty(), "平台日志为空");
            assertEquals(Constants.SUCCESS, logs.get(0).getCode(), "日志状态应为成功");
        }
    }

    @Test
    public void testConvertFail() {
        // 模拟文件ID, 需要替换为实际测试数据中存在的id
        String uniquecodeofinvoice = "4650174102263300232130";

        // 模拟数据转换异常
        doThrow(new CheckedException("进项发票信息转换失败"))
                .when(apiCompareCache).convertData(any(), any(), any());

        // 执行推送并验证异常
        CheckedException exception = assertThrows(CheckedException.class,
                () -> fiSourceFileOcrInfoService.push(uniquecodeofinvoice));
        assertTrue(exception.getMessage().contains("进项发票集成信息转换失败"), "异常消息不匹配");

        // 验证数据库状态
        FiSourceFileOcrInfo fileOcrInfo = fiSourceFileOcrInfoService.getOne(
                Wrappers.<FiSourceFileOcrInfo>lambdaQuery()
                        .eq(FiSourceFileOcrInfo::getUniquecodeofinvoice, uniquecodeofinvoice));
        assertEquals("1", fileOcrInfo.getIntegrationStatus(), "集成状态不正确");
        assertEquals(PushStatusEnum.CONVERT_FAIL.getCode(), fileOcrInfo.getPushStatus(), "推送状态应为转换失败");
        assertNotNull(fileOcrInfo.getPushMsg(), "推送错误消息应不为空");
    }

    @Test
    public void testPushFail() {
        // 模拟文件ID, 需要替换为实际测试数据中存在的id
        String uniquecodeofinvoice = "4650174102263300232130";

        try (MockedStatic<YgUtil> ygUtil = mockStatic(YgUtil.class);
             MockedStatic<UdsUtil> udsUtil = mockStatic(UdsUtil.class)) {
            // 模拟 YgUtil.sendToYg 方法，返回失败响应
            ygUtil.when(() -> YgUtil.sendToYg(any(), any())).thenReturn(ygInvoiceFailResp);
            // 模拟 UdsUtil.pushFileToUdsFromOSS，返回null
            // 模拟 UdsUtil.pushFileToUdsFromOSS，返回null
            CommonActionResult commonActionResult = new CommonActionResult();
            commonActionResult.setFileName("test.xml");
            commonActionResult.setDocumentId("testDocumentId");
            udsUtil.when(() -> UdsUtil.pushFileToUdsFromOSS(any(), any(), any())).thenReturn(commonActionResult);

            // 执行推送并验证异常
            CheckedException exception = assertThrows(CheckedException.class,
                    () -> fiSourceFileOcrInfoService.push(uniquecodeofinvoice));
            assertTrue(exception.getMessage().contains("进项发票集成信息推送失败"), "异常消息不匹配");

            // 验证数据库状态
            FiSourceFileOcrInfo updatedOrder = fiSourceFileOcrInfoService.getOne(
                    Wrappers.<FiSourceFileOcrInfo>lambdaQuery()
                            .eq(FiSourceFileOcrInfo::getUniquecodeofinvoice, uniquecodeofinvoice)
            );
            assertEquals("1", updatedOrder.getIntegrationStatus(), "集成状态不正确");
            assertEquals(PushStatusEnum.PUSH_FAIL.getCode(), updatedOrder.getPushStatus(), "推送状态应为推送失败");

            // 验证平台日志
            List<PlatFormLogVO> logs = fileManageMapper.selectPlatFormLog(uniquecodeofinvoice, "fi_source_file_ocr_info");
            assertFalse(logs.isEmpty(), "平台日志为空");
            assertEquals(Constants.FAIL, logs.get(0).getCode(), "日志CODE不正确");
            assertTrue(logs.get(0).getMsg().contains("入池失败:更新票面信息失败"), "日志信息为：" + logs.get(0).getMsg());
        }
    }
}
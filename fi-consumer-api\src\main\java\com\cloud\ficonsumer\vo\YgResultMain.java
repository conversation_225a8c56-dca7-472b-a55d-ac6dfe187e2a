package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/1/17.
 */
@Data
@ApiModel("响应信息主体")
public class YgResultMain implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("返回标记：成功标记=0，失败标记=1")
    private Integer httpStatusCode;
    @ApiModelProperty("返回信息")
    private String rawBody;
}

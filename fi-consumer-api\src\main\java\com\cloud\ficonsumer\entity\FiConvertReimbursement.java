

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用报销单转换数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:59
 */
@Data
@TableName("fi_convert_reimbursement")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "通用报销单转换数据主表")
public class FiConvertReimbursement extends BaseEntity<FiConvertReimbursement> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * pk主键
     */
    @ApiModelProperty(value="pk主键")
    private String pkPayablebill;

    /**
     * 编码
     */
    @ApiModelProperty(value="编码")
    private String billno;

    /**
     * 单据类型
     * F1-Cxx-06 通用报销单(报账)
     * F1-Cxx-02 采购应付
     */
    @ApiModelProperty(value="单据类型")
    private String pkTradetype;

    /**
     * 摘要
     */
    @ApiModelProperty(value="摘要")
    private String scomment;

    /**
     * 财务组织
     */
    @ApiModelProperty(value="财务组织")
    private String pkOrg;

    /**
     * 财务组织名称
     */
    @ApiModelProperty(value="财务组织名称")
    private String pkOrgName;

    /**
     * 应付财务组织版本
     */
    @ApiModelProperty(value="应付财务组织版本")
    private String pkOrgV;

    /**
     * 制单人  - 取MDM编码
     */
    @ApiModelProperty(value="制单人")
    private String billmaker;

    /**
     * 制单人 - 取ISC登录账号
     */
    @ApiModelProperty(value="制单人")
    private String billmakerT;

    /**
     * 收支项目
     */
    @ApiModelProperty(value="收支项目")
    private String pkSubjcode;

    /**
     * 是否退回重传标识
     * Y-是，N-否；标识默认为N，回退成功后，更新为Y；单据集成成功后，重置为N
     */
    @ApiModelProperty(value="是否退回重传标识")
    private String backTag;

    /**
     * 往来对象
     * 0=客户;
     * 1=供应商;
     * 2=部门;
     * 3=业务员;
     * objtype=0或1，传00000001；
     *
     * objtype=3，传00000000；
     */
    @ApiModelProperty(value="往来对象")
    private String objtype;

    /**
     * 业务员
     */
    @ApiModelProperty(value="业务员")
    private String pkPsndoc;

    /**
     * 合同类型
     */
    @ApiModelProperty(value="合同类型")
    private String def5;

    /**
     * 付款方式
     */
    @ApiModelProperty(value="付款方式")
    private String def2;

    /**
     * 平台业务事项
     */
    @ApiModelProperty(value="平台业务事项")
    private String def47;

    /**
     * 部门分类
     */
    @ApiModelProperty(value="部门分类")
    private String def52;

    /**
     * 币种
     */
    @ApiModelProperty(value="币种")
    private String pkCurrtype;

    /**
     * 项目
     */
    @ApiModelProperty(value="项目")
    private String project;

    /**
     * 供应商
     */
    @ApiModelProperty(value="供应商")
    private String supplier;

    /**
     * 组织本币金额
     */
    @ApiModelProperty(value="组织本币金额")
    private String localMoney;

    /**
     * 部门
     */
    @ApiModelProperty(value="部门")
    private String pkDeptid;

    /**
     * 部门版本
     */
    @ApiModelProperty(value="部门版本")
    private String pkDeptidV;

}

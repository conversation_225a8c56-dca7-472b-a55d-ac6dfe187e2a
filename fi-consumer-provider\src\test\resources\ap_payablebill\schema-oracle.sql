create table AP_PAYABLEBILL
(
    ACCESSORYNUM      NUMBER(38),
    APPROVEDATE       CHAR(19),
    APPROVER          VARCHAR2(20)  default '~',
    APPROVESTATUS     NUMBER(38)    default -1   not null,
    BILLCLASS         VARCHAR2(2)   default 'yf' not null,
    BILLDATE          CHAR(19)                   not null,
    BILLMAKER         CHAR(20)                   not null,
    B<PERSON><PERSON>NO            VARCHAR2(40),
    BILLPERIOD        VARCHAR2(2),
    B<PERSON>LSTATUS        NUMBER(38)                 not null,
    BILLYEAR          VARCHAR2(4),
    CONFIRMUSER       CHAR(20),
    COORDFLAG         NUMBER(38),
    CREATIONTIME      CHAR(19)                   not null,
    CREATOR           CHAR(20)                   not null,
    DEF1              VARCHAR2(101),
    DEF10             VARCHAR2(101),
    DEF11             VARCHAR2(101),
    DEF12             VARCHAR2(101),
    DEF13             VARCHAR2(101),
    DEF14             VARCHAR2(101),
    DEF15             VARCHAR2(101),
    <PERSON>F16             VARCHAR2(101),
    DEF17             VARCHAR2(101),
    DEF18             VARCHAR2(101),
    DEF19             VARCHAR2(101),
    DEF2              VARCHAR2(101),
    DEF20             VARCHAR2(101),
    DEF21             VARCHAR2(101),
    DEF22             VARCHAR2(101),
    DEF23             VARCHAR2(101),
    DEF24             VARCHAR2(101),
    DEF25             VARCHAR2(101),
    DEF26             VARCHAR2(101),
    DEF27             VARCHAR2(101),
    DEF28             VARCHAR2(101),
    DEF29             VARCHAR2(101),
    DEF3              VARCHAR2(101),
    DEF30             VARCHAR2(101),
    DEF31             VARCHAR2(101),
    DEF32             VARCHAR2(101),
    DEF33             VARCHAR2(101),
    DEF34             VARCHAR2(101),
    DEF35             VARCHAR2(101),
    DEF36             VARCHAR2(101),
    DEF37             VARCHAR2(101),
    DEF38             VARCHAR2(101),
    DEF39             VARCHAR2(101),
    DEF4              VARCHAR2(101),
    DEF40             VARCHAR2(101),
    DEF41             VARCHAR2(101),
    DEF42             VARCHAR2(101),
    DEF43             VARCHAR2(101),
    DEF44             VARCHAR2(101),
    DEF45             VARCHAR2(101),
    DEF46             VARCHAR2(101),
    DEF47             VARCHAR2(101),
    DEF48             VARCHAR2(101),
    DEF49             VARCHAR2(101),
    DEF5              VARCHAR2(101),
    DEF50             VARCHAR2(101),
    DEF51             VARCHAR2(101),
    DEF52             VARCHAR2(101),
    DEF53             VARCHAR2(101),
    DEF54             VARCHAR2(101),
    DEF55             VARCHAR2(101),
    DEF56             VARCHAR2(101),
    DEF57             VARCHAR2(101),
    DEF58             VARCHAR2(101),
    DEF59             VARCHAR2(101),
    DEF6              VARCHAR2(101),
    DEF60             VARCHAR2(101),
    DEF61             VARCHAR2(101),
    DEF62             VARCHAR2(4000),
    DEF63             VARCHAR2(101),
    DEF64             VARCHAR2(101),
    DEF65             VARCHAR2(101),
    DEF66             VARCHAR2(101),
    DEF67             VARCHAR2(101),
    DEF68             VARCHAR2(101),
    DEF69             VARCHAR2(101),
    DEF7              VARCHAR2(101),
    DEF70             VARCHAR2(101),
    DEF71             VARCHAR2(101),
    DEF72             VARCHAR2(101),
    DEF73             VARCHAR2(101),
    DEF74             VARCHAR2(101),
    DEF75             VARCHAR2(101),
    DEF76             VARCHAR2(101),
    DEF77             VARCHAR2(101),
    DEF78             VARCHAR2(101),
    DEF79             VARCHAR2(101),
    DEF8              VARCHAR2(101),
    DEF80             VARCHAR2(101),
    DEF9              VARCHAR2(101),
    DR                NUMBER(10)    default 0,
    EFFECTDATE        CHAR(19),
    EFFECTSTATUS      NUMBER(38),
    EFFECTUSER        VARCHAR2(20)  default '~',
    GLOBALLOCAL       NUMBER(28, 8),
    GROUPLOCAL        NUMBER(28, 8),
    INVOICENO         VARCHAR2(100),
    ISFLOWBILL        CHAR,
    ISINIT            CHAR,
    ISPUADJUST        CHAR,
    ISREDED           CHAR,
    ISREFUND          CHAR          default 'N',
    ISURGENT          CHAR          default 'N',
    LASTADJUSTUSER    VARCHAR2(20)  default '~',
    LASTAPPROVEID     VARCHAR2(20)  default '~',
    LOCAL_MONEY       NUMBER(28, 8),
    MODIFIEDTIME      CHAR(19),
    MODIFIER          VARCHAR2(20)  default '~',
    MONEY             NUMBER(28, 8),
    OFFICIALPRINTDATE CHAR(19),
    OFFICIALPRINTUSER VARCHAR2(20)  default '~',
    OUTBUSITYPE       CHAR(20),
    PK_BALATYPE       VARCHAR2(20),
    PK_BILLTYPE       VARCHAR2(20)               not null,
    PK_BUSITYPE       VARCHAR2(20)  default '~',
    PK_CURRTYPE       VARCHAR2(20)  default '~',
    PK_FIORG          VARCHAR2(20)  default '~',
    PK_FIORG_V        VARCHAR2(20)  default '~',
    PK_GROUP          CHAR(20)                   not null,
    PK_ORG            CHAR(20)                   not null,
    PK_ORG_V          VARCHAR2(20),
    PK_PAYABLEBILL    CHAR(20)                   not null
        constraint PK_AP_PAYABLEBILL
            primary key,
    PK_PCORG          VARCHAR2(20)  default '~',
    PK_TRADETYPE      VARCHAR2(20)               not null,
    PK_TRADETYPEID    VARCHAR2(20)  default '~',
    RECECOUNTRYID     VARCHAR2(20),
    SCOMMENT          VARCHAR2(800) default '~',
    SETT_ORG          VARCHAR2(20)  default '~',
    SETT_ORG_V        VARCHAR2(20)  default '~',
    SRC_SYSCODE       NUMBER(38),
    START_PERIOD      VARCHAR2(20),
    SUBJCODE          VARCHAR2(20)  default '~',
    SYSCODE           NUMBER(38)    default 1    not null,
    TAXCOUNTRYID      VARCHAR2(20),
    TOTAL_PERIOD      NUMBER(38),
    TS                CHAR(19)      default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VID_PCORG         VARCHAR2(20)  default '~',
    SAGA_BTXID        VARCHAR2(64),
    SAGA_FROZEN       NUMBER(38)    default 0    not null,
    SAGA_GTXID        VARCHAR2(64),
    SAGA_STATUS       NUMBER(38)    default 0    not null
);

create table AP_PAYABLEITEM
(
    ASSETPACTNO     VARCHAR2(80),
    BANKROLLPROJET  VARCHAR2(20)  default '~',
    BILLCLASS       VARCHAR2(2)   default 'yf' not null,
    BILLDATE        CHAR(19)                   not null,
    BILLNO          VARCHAR2(40),
    BUSIDATE        CHAR(19),
    BUYSELLFLAG     NUMBER(38),
    CALTAXMNY       NUMBER(28, 8),
    CASHITEM        VARCHAR2(20)  default '~',
    CBS             VARCHAR2(20),
    CHECKELEMENT    VARCHAR2(20)  default '~',
    CHECKTYPE       VARCHAR2(20)  default '~',
    CONFERNUM       VARCHAR2(60),
    CONTRACTNO      VARCHAR2(40),
    COORDFLAG       NUMBER(38),
    COSTCENTER      VARCHAR2(20),
    CUSTOMER        VARCHAR2(20)  default '~',
    DEF1            VARCHAR2(101),
    DEF10           VARCHAR2(101),
    DEF11           VARCHAR2(101),
    DEF12           VARCHAR2(101),
    DEF13           VARCHAR2(101),
    DEF14           VARCHAR2(101),
    DEF15           VARCHAR2(101),
    DEF16           VARCHAR2(101),
    DEF17           VARCHAR2(101),
    DEF18           VARCHAR2(101),
    DEF19           VARCHAR2(101),
    DEF2            VARCHAR2(101),
    DEF20           VARCHAR2(101),
    DEF21           VARCHAR2(101),
    DEF22           VARCHAR2(101),
    DEF23           VARCHAR2(101),
    DEF24           VARCHAR2(101),
    DEF25           VARCHAR2(101),
    DEF26           VARCHAR2(101),
    DEF27           VARCHAR2(101),
    DEF28           VARCHAR2(101),
    DEF29           VARCHAR2(101),
    DEF3            VARCHAR2(101),
    DEF30           VARCHAR2(101),
    DEF31           VARCHAR2(101),
    DEF32           VARCHAR2(101),
    DEF33           VARCHAR2(101),
    DEF34           VARCHAR2(101),
    DEF35           VARCHAR2(101),
    DEF36           VARCHAR2(101),
    DEF37           VARCHAR2(101),
    DEF38           VARCHAR2(101),
    DEF39           VARCHAR2(101),
    DEF4            VARCHAR2(101),
    DEF40           VARCHAR2(101),
    DEF41           VARCHAR2(101),
    DEF42           VARCHAR2(101),
    DEF43           VARCHAR2(101),
    DEF44           VARCHAR2(101),
    DEF45           VARCHAR2(101),
    DEF46           VARCHAR2(101),
    DEF47           VARCHAR2(101),
    DEF48           VARCHAR2(101),
    DEF49           VARCHAR2(101),
    DEF5            VARCHAR2(101),
    DEF50           VARCHAR2(101),
    DEF51           VARCHAR2(101),
    DEF52           VARCHAR2(101),
    DEF53           VARCHAR2(101),
    DEF54           VARCHAR2(101),
    DEF55           VARCHAR2(101),
    DEF56           VARCHAR2(101),
    DEF57           VARCHAR2(101),
    DEF58           VARCHAR2(101),
    DEF59           VARCHAR2(101),
    DEF6            VARCHAR2(101),
    DEF60           VARCHAR2(101),
    DEF61           VARCHAR2(101),
    DEF62           VARCHAR2(4000),
    DEF63           VARCHAR2(101),
    DEF64           VARCHAR2(101),
    DEF65           VARCHAR2(101),
    DEF66           VARCHAR2(101),
    DEF67           VARCHAR2(101),
    DEF68           VARCHAR2(101),
    DEF69           VARCHAR2(101),
    DEF7            VARCHAR2(101),
    DEF70           VARCHAR2(101),
    DEF71           VARCHAR2(101),
    DEF72           VARCHAR2(101),
    DEF73           VARCHAR2(101),
    DEF74           VARCHAR2(101),
    DEF75           VARCHAR2(101),
    DEF76           VARCHAR2(101),
    DEF77           VARCHAR2(101),
    DEF78           VARCHAR2(101),
    DEF79           VARCHAR2(101),
    DEF8            VARCHAR2(255),
    DEF80           VARCHAR2(101),
    DEF9            VARCHAR2(101),
    DIRECTION       NUMBER(38)    default -1   not null,
    DR              NUMBER(10)    default 0,
    EQUIPMENTCODE   VARCHAR2(100),
    FACARD          VARCHAR2(50)  default '~',
    FREECUST        VARCHAR2(20)  default '~',
    GLOBALBALANCE   NUMBER(28, 8) default 0,
    GLOBALCREBIT    NUMBER(28, 8) default 0,
    GLOBALNOTAX_CRE NUMBER(28, 8) default 0,
    GLOBALRATE      NUMBER(15, 8) default 1,
    GLOBALTAX_CRE   NUMBER(28, 8) default 0,
    GROUPBALANCE    NUMBER(28, 8) default 0,
    GROUPCREBIT     NUMBER(28, 8) default 0,
    GROUPNOTAX_CRE  NUMBER(28, 8) default 0,
    GROUPRATE       NUMBER(15, 8) default 1,
    GROUPTAX_CRE    NUMBER(28, 8) default 0,
    INNERORDERNO    VARCHAR2(40),
    INVOICENO       VARCHAR2(140),
    ISURGENT        CHAR          default 'N',
    LOCAL_MONEY_BAL NUMBER(28, 8) default 0,
    LOCAL_MONEY_CR  NUMBER(28, 8) default 0,
    LOCAL_NOTAX_CR  NUMBER(28, 8) default 0,
    LOCAL_PRICE     NUMBER(28, 8) default 0,
    LOCAL_TAX_CR    NUMBER(28, 8) default 0,
    LOCAL_TAXPRICE  NUMBER(28, 8) default 0,
    MATERIAL        VARCHAR2(20)  default '~',
    MATERIAL_SRC    VARCHAR2(20),
    MONEY_BAL       NUMBER(28, 8) default 0,
    MONEY_CR        NUMBER(28, 8) default 0,
    NOSUBTAX        NUMBER(28, 8),
    NOSUBTAXRATE    NUMBER(28, 8),
    NOTAX_CR        NUMBER(28, 8) default 0,
    OBJTYPE         NUMBER(38),
    OCCUPATIONMNY   NUMBER(28, 8) default 0,
    OPPTAXFLAG      CHAR,
    ORDERCUBASDOC   VARCHAR2(20)  default '~',
    OUTSTORENO      VARCHAR2(40),
    PAUSETRANSACT   CHAR,
    PAYACCOUNT      VARCHAR2(20)  default '~',
    PK_BALATYPE     VARCHAR2(20),
    PK_BILLTYPE     VARCHAR2(20)               not null,
    PK_CURRTYPE     VARCHAR2(20)  default '~',
    PK_DEPTID       VARCHAR2(20)  default '~',
    PK_DEPTID_V     VARCHAR2(20)  default '~',
    PK_FIORG        VARCHAR2(20)  default '~',
    PK_FIORG_V      VARCHAR2(20)  default '~',
    PK_GROUP        CHAR(20)                   not null,
    PK_ORG          CHAR(20)                   not null,
    PK_ORG_V        VARCHAR2(20)  default '~',
    PK_PAYABLEBILL  CHAR(20),
    PK_PAYABLEITEM  CHAR(20)                   not null
        constraint PK_AP_PAYABLEITEM
            primary key,
    PK_PAYTERM      VARCHAR2(20)  default '~',
    PK_PCORG        VARCHAR2(20)  default '~',
    PK_PCORG_V      VARCHAR2(20)  default '~',
    PK_PSNDOC       VARCHAR2(20)  default '~',
    PK_SSITEM       CHAR(20),
    PK_SUBJCODE     VARCHAR2(20)  default '~',
    PK_TRADETYPE    VARCHAR2(20)               not null,
    PK_TRADETYPEID  VARCHAR2(20)  default '~',
    POSTPRICE       NUMBER(28, 8) default 0,
    POSTPRICENOTAX  NUMBER(28, 8) default 0,
    POSTQUANTITY    NUMBER(20, 8) default 0,
    POSTUNIT        VARCHAR2(40),
    PRICE           NUMBER(28, 8) default 0,
    PRODUCTLINE     VARCHAR2(20)  default '~',
    PROJECT         VARCHAR2(20),
    PROJECT_TASK    VARCHAR2(20),
    PU_DEPTID       VARCHAR2(20)  default '~',
    PU_DEPTID_V     VARCHAR2(20)  default '~',
    PU_ORG          VARCHAR2(20)  default '~',
    PU_ORG_V        CHAR(20),
    PU_PSNDOC       CHAR(20),
    PURCHASEORDER   VARCHAR2(40),
    QUANTITY_BAL    NUMBER(20, 8) default 0,
    QUANTITY_CR     NUMBER(20, 8) default 0,
    RATE            NUMBER(15, 8) default 1,
    RECACCOUNT      VARCHAR2(20)  default '~',
    ROWNO           NUMBER(38),
    ROWTYPE         NUMBER(38),
    SCOMMENT        VARCHAR2(800) default '~',
    SENDCOUNTRYID   VARCHAR2(20),
    SETT_ORG        VARCHAR2(20)  default '~',
    SETT_ORG_V      VARCHAR2(20)  default '~',
    SETTLECURR      VARCHAR2(20),
    SETTLEMONEY     NUMBER(28, 8),
    SETTLENO        VARCHAR2(20),
    SRC_BILLID      CHAR(20),
    SRC_BILLTYPE    VARCHAR2(20),
    SRC_ITEMID      CHAR(20),
    SRC_TRADETYPE   VARCHAR2(20),
    SUBJCODE        VARCHAR2(20)  default '~',
    SUPPLIER        VARCHAR2(20)  default '~',
    TAXCODEID       VARCHAR2(20),
    TAXNUM          VARCHAR2(30),
    TAXPRICE        NUMBER(28, 8) default 0,
    TAXRATE         NUMBER(9, 6)  default 0,
    TAXTYPE         NUMBER(38),
    TOP_BILLID      CHAR(20),
    TOP_BILLTYPE    VARCHAR2(20),
    TOP_ITEMID      CHAR(20),
    TOP_TRADETYPE   VARCHAR2(20),
    TS              CHAR(19)      default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VATCODE         VARCHAR2(50),
    VENDORVATCODE   VARCHAR2(50)
);

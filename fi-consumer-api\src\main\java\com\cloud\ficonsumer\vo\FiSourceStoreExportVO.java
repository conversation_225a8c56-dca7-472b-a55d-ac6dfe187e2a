package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 采购入库
 */
@Data
@ColumnWidth(25)
public class FiSourceStoreExportVO {


    @ExcelProperty(value="财务组织名称")
    private String pkOrgName;

    @ExcelProperty(value = "单据编号")
    private String vbillcode;

    @ExcelProperty(value = "制单日期")
    private String dmakedate;

    @ExcelProperty(value = "供应商名称")
    private String cvendorvidName;

    /**
     * 用途
     */
    @ExcelProperty(value="用途")
    private String vbdef7Name;

    @ExcelProperty(value = "暂估标志")
    private String bestimateflagName;

    @ExcelProperty(value = "订单编号")
    private String orderNum;

    @ExcelProperty(value = "推送时间")
    private String pushTime;

    @ExcelProperty(value = "同步状态")
    private String pushStatusName;

    @ExcelProperty(value = "同步消息")
    private String pushMsg;

    /**
     * 创建时间
     */
    @ExcelProperty(value="拉取时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ExcelIgnore
    private Integer pushStatus;


}
package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@TableName("fi_convert_pu_paybill_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购付款明细转换数据详情")
public class FiConvertPuPaybillDetail extends BaseEntity<FiConvertPuPaybillDetail> {
    private static final long serialVersionUID = -1L;

    @TableId
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("结算单主键")
    private String pkSettlement;

    @ApiModelProperty("部门")
    private String pkDeptidV;

    @ApiModelProperty("部门")
    private String pkDeptid;

    @ApiModelProperty("上游单据主键")
    private String srcBillid;

    @ApiModelProperty("付款性质")
    private String prepay;

    @ApiModelProperty("摘要")
    private String memo;

    @ApiModelProperty("项目")
    private String project;

    @ApiModelProperty("合同号")
    private String contractNo;

    @ApiModelProperty("付款业务类型")
    private String pkRecpaytype;

    @ApiModelProperty("供应商")
    private String supplier;

    @ApiModelProperty("对方账号")
    private String oppaccount;

    @ApiModelProperty("对方账户户名")
    private String oppaccname;

    @ApiModelProperty("对方银行")
    private String pkOppbank;

    @ApiModelProperty("付款原币金额")
    private BigDecimal pay;

    @ApiModelProperty("实付币种")
    private String pkCurrtypeLast;

    @ApiModelProperty("实付套汇汇率")
    private BigDecimal changerate;

    @ApiModelProperty(value = "质保金金额")
    private String def38;
}

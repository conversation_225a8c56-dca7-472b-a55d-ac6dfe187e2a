package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@TableName("fi_source_proj_paybill_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目付款明细源表")
public class FiSourceProjPaybillDetail extends BaseEntity<FiSourceProjPaybillDetail> {

    private static final long serialVersionUID = -1L;

    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value = "项目付款单标识")
    private String pkPaybill;

    @ApiModelProperty(value = "项目付款单行标识")
    private String pkPayitem;

    @ApiModelProperty(value = "业务员")
    private String pkPsndoc;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "项目")
    private String project;

    @ApiModelProperty(value = "组织本币金额")
    private BigDecimal localMoney;

    @ApiModelProperty(value = "部门")
    private String pkDeptid;

    @ApiModelProperty(value = "币种")
    private String pkCurrtype;

    @ApiModelProperty(value = "结算方式")
    private String pkBalatype;

    @ApiModelProperty(value = "合同号")
    private String contractno;

    @ApiModelProperty(value = "借方原币金额")
    private BigDecimal moneyDe;

    @ApiModelProperty(value = "组织本币无税金额")
    private BigDecimal localNotaxDe;

    @ApiModelProperty(value = "税额")
    private BigDecimal localTaxDe;

    @ApiModelProperty(value = "收支项目")
    private String pkSubjcode;

    @ApiModelProperty("款项性质")
    private String def35;

    @ApiModelProperty("费用类型")
    private String def6;
}

-- 创建 fi_source_sale_order 表
CREATE TABLE `fi_source_sale_order`
(
    `id`                 BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `csaleorderid`       VARCHAR(255) DEFAULT NULL COMMENT '销售订单主键',
    `pk_org_v`           VARCHAR(255) DEFAULT NULL COMMENT '组织版本主键',
    `ctrantypeid`        VARCHAR(255) DEFAULT NULL COMMENT '交易类型ID',
    `dbilldate`          DATETIME     DEFAULT NULL COMMENT '单据日期',
    `vbillcode`          VARCHAR(255) DEFAULT NULL COMMENT '单据号',
    `ccustomerid`        VARCHAR(255) DEFAULT NULL COMMENT '客户ID',
    `cemployeeid`        VARCHAR(255) DEFAULT NULL COMMENT '业务员ID',
    `cdeptvid`           VARCHAR(255) DEFAULT NULL COMMENT '部门版本ID',
    `cctmanageid`        VARCHAR(255) DEFAULT NULL COMMENT '合同管理ID',
    `vnote`              VARCHAR(255) DEFAULT NULL COMMENT '备注',
    `fstatusflag`        VARCHAR(255) DEFAULT NULL COMMENT '单据状态',
    `vbdef9`             VARCHAR(255) DEFAULT NULL COMMENT '自定义项9, 备用字段',
    `vdef20`              VARCHAR(255) DEFAULT NULL COMMENT '同步状态',
    `dr`                 INT          DEFAULT '0' COMMENT '删除状态,0未删除,1已删除',
    `ts`                 DATETIME     DEFAULT NULL COMMENT '时间戳',
    `integration_status` VARCHAR(255) DEFAULT NULL COMMENT '集成状态',
    `push_status`        INT          DEFAULT NULL COMMENT '4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败',
    `push_msg`           VARCHAR(1024) DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`         VARCHAR(255) DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      VARCHAR(255) DEFAULT NULL COMMENT '返回同步状态',
    `try_number`         BIGINT       DEFAULT NULL COMMENT '重试次数',
    `readiness`          BIGINT       DEFAULT NULL COMMENT '0 未就绪 1 就绪',
    `create_by`          varchar(50)  DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)  DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='销售订单源数据详情';

-- 创建 fi_source_sale_order_detail 表
CREATE TABLE `fi_source_sale_order_detail`
(
    `id`              BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `csaleorderid`    VARCHAR(255)   DEFAULT NULL COMMENT '销售订单主表ID',
    `csaleorderbid`   VARCHAR(255)   DEFAULT NULL COMMENT '销售订单子表ID',
    `crowno`          VARCHAR(255)   DEFAULT NULL COMMENT '行号',
    `cmaterialvid`    VARCHAR(255)   DEFAULT NULL COMMENT '物料ID',
    `nastnum`         DECIMAL(20, 2) DEFAULT NULL COMMENT '数量',
    `nqtorigprice`    DECIMAL(20, 6) DEFAULT NULL COMMENT '报价',
    `norigmny`        DECIMAL(20, 2) DEFAULT NULL COMMENT '金额',
    `ntaxrate`        DECIMAL(10, 6) DEFAULT NULL COMMENT '税率',
    `nqtorigtaxprice` DECIMAL(20, 6) DEFAULT NULL COMMENT '含税单价',
    `norigtaxmny`     DECIMAL(20, 2) DEFAULT NULL COMMENT '含税金额',
    `cprojectid`      VARCHAR(255)   DEFAULT NULL COMMENT '项目ID',
    `vbdef9`          VARCHAR(255)   DEFAULT NULL COMMENT '自定义项9,备用字段',
    `create_by`       varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`     datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`       varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`     datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='销售订单子表源数据详情';

-- 创建 fi_convert_sale_order 表
CREATE TABLE `fi_convert_sale_order`
(
    `id`                 BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `csaleorderid`       VARCHAR(255) DEFAULT NULL COMMENT '销售订单主键',
    `pk_org_v`           VARCHAR(255) DEFAULT NULL COMMENT '组织版本主键',
    `ctrantypeid`        VARCHAR(255) DEFAULT NULL COMMENT '交易类型ID',
    `dbilldate`          DATETIME     DEFAULT NULL COMMENT '单据日期',
    `vbillcode`          VARCHAR(255) DEFAULT NULL COMMENT '单据号',
    `ccustomerid`        VARCHAR(255) DEFAULT NULL COMMENT '客户ID',
    `cemployeeid`        VARCHAR(255) DEFAULT NULL COMMENT '业务员ID',
    `cdeptvid`           VARCHAR(255) DEFAULT NULL COMMENT '部门版本ID',
    `cctmanageid`        VARCHAR(255) DEFAULT NULL COMMENT '合同管理ID',
    `vnote`              VARCHAR(255) DEFAULT NULL COMMENT '备注',
    `fstatusflag`        VARCHAR(255) DEFAULT NULL COMMENT '单据状态',
    `vbdef9`             VARCHAR(255) DEFAULT NULL COMMENT '自定义项9, 备用字段',
    `vdef20`              VARCHAR(255) DEFAULT NULL COMMENT '同步状态',
    `dr`                 INT          DEFAULT '0' COMMENT '删除状态,0未删除,1已删除',
    `ts`                 DATETIME     DEFAULT NULL COMMENT '时间戳',
    `integration_status` VARCHAR(255) DEFAULT NULL COMMENT '集成状态',
    `push_status`        INT          DEFAULT NULL COMMENT '4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败',
    `push_msg`           VARCHAR(1024) DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`         VARCHAR(255) DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      VARCHAR(255) DEFAULT NULL COMMENT '返回同步状态',
    `try_number`         BIGINT       DEFAULT NULL COMMENT '重试次数',
    `readiness`          BIGINT       DEFAULT NULL COMMENT '0 未就绪 1 就绪',
    `create_by`          varchar(50)  DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)  DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='销售订单转换数据详情';

-- 创建 fi_convert_sale_order_detail 表
CREATE TABLE `fi_convert_sale_order_detail`
(
    `id`              BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    `csaleorderid`    VARCHAR(255)   DEFAULT NULL COMMENT '销售订单主表ID',
    `csaleorderbid`   VARCHAR(255)   DEFAULT NULL COMMENT '销售订单子表ID',
    `crowno`          VARCHAR(255)   DEFAULT NULL COMMENT '行号',
    `cmaterialvid`    VARCHAR(255)   DEFAULT NULL COMMENT '物料ID',
    `nastnum`         DECIMAL(20, 2) DEFAULT NULL COMMENT '数量',
    `nqtorigprice`    DECIMAL(20, 6) DEFAULT NULL COMMENT '报价',
    `norigmny`        DECIMAL(20, 2) DEFAULT NULL COMMENT '金额',
    `ntaxrate`        DECIMAL(10, 6) DEFAULT NULL COMMENT '税率',
    `nqtorigtaxprice` DECIMAL(20, 6) DEFAULT NULL COMMENT '含税单价',
    `norigtaxmny`     DECIMAL(20, 2) DEFAULT NULL COMMENT '含税金额',
    `cprojectid`      VARCHAR(255)   DEFAULT NULL COMMENT '项目ID',
    `vbdef9`          VARCHAR(255)   DEFAULT NULL COMMENT '自定义项9,备用字段',
    `create_by`       varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`     datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`       varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`     datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='销售订单子表转换数据详情';


package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiConvertStoreDTO;
import com.cloud.ficonsumer.dto.FiConvertStoreDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceStoreDTO;
import com.cloud.ficonsumer.dto.FiSourceStoreDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceStore;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceStoreQueryVO;

import java.util.List;

/**
 * 采购入库源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:35
 */
public interface FiSourceStoreService extends IService<FiSourceStore> {

    /**
     * 采购入库重新推送
     * @param s
     */
    boolean pushAgain(String s);

    Page<FiSourceStoreDTO> beforeConvertDataPage(Page page, FiSourceStoreQueryVO queryVO);

    Page<FiSourceStoreDetailDTO> getBeforeConvertDataPage(Page page, FiSourceStoreQueryVO queryVO);

    List<FiConvertStoreDTO> getConvertList(String pk);

    Page<FiConvertStoreDetailDTO> getConvertData(Page page, String pk);

    /**
     * 校验采购入库单同步状态
     *
     * @param cbillid 入库单主键
     */
    void checkSync(String cbillid);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}

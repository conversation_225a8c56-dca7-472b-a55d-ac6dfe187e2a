package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.util.R;
import com.cloud.cloud.common.log.annotation.SysLog;
import com.cloud.common.oss.OssProperties;
import com.cloud.common.oss.service.OssTemplate;
import com.cloud.ficonsumer.dto.FiSourceFileOcrInfoQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPayableQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillQueryDTO;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.listener.*;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.utils.ExcelUtils;
import com.cloud.ficonsumer.utils.RedisUtil;
import com.cloud.ficonsumer.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.time.Duration;

/**
 * @Author: cyz
 * @Descrition: 导出excel文件
 * @Date: 2023/07/03
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/exportNew")
@Api(value = "导出excel文件", tags = "导出excel文件")
public class ExportNewController {
    private final ExportService exportService;
    private final RedisTemplate redisTemplate;
    private final OssTemplate ossTemplate;
    private final OssProperties ossProperties;

    private final FiSourceFileOcrInfoService fiSourceFileOcrInfoService;
    private final FiSourceOrderService fiSourceOrderService;
    private final FiSourceReimbursementService fiSourceReimbursementService;
    private final FiSourceReceivableService fiSourceReceivableService;
    private final FiSourceCostOrderService fiSourceCostOrderService;
    private final FiSourceVirtualOrderService fiSourceVirtualOrderService;
    private final FiInvoicePostingService fiInvoicePostingService;
    private final FiSourcePuPayableService fiSourcePuPayableService;

    private final FiSourceStoreService fiSourceStoreService;
    private final FiSourcePuPaybillService fiSourcePuPaybillService;
    private final FiSourceAccountingService fiSourceAccountingService;


    /**
     * ExportService
     * 获取导出文件进度
     *
     * @param id
     */
    @ApiOperation(value = "异步导出获取导出文件进度", notes = "异步导出获取导出文件进度")
    @GetMapping("/progress/{id}")
    public R<ExcelExportStatusVO> getImportStatus(@PathVariable String id) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        ExcelExportStatusVO vo = (ExcelExportStatusVO) valueOperations.get(ExcelUtils.EXCEL_EXPORT + id);
        return R.ok(vo);
    }

    /**
     * 异步导出下载excel文件，同步不需要
     *
     * @param id       缓存key
     * @param fileName 文件名称
     * @param response
     * @return
     */
    @ApiOperation(value = "异步导出下载excel文件，同步不需要", notes = "异步导出下载excel文件，同步不需要")
    @PostMapping("/download/{id}/{fileName}")
    public void download(@PathVariable String id, @PathVariable String fileName, HttpServletResponse response) {
        try (InputStream inputStream = ossTemplate.getObject(ossProperties.getBucketName(), fileName).getObjectContent()) {
            response.setContentType("application/octet-stream; charset=UTF-8");
            IoUtil.copy(inputStream, response.getOutputStream());

            ossTemplate.removeObject(ossProperties.getBucketName(), fileName);
            redisTemplate.delete(id);
        } catch (Exception e) {
            log.error("文件已经从OSS中删除 ", e);
        }
    }

    /**
     * 异步导出对照表信息
     * @return
     */
    @ApiOperation(value = "异步导出对照表信息", notes = "异步导出对照表信息")
    @PostMapping("/exportCompareAsync")
    //@PreAuthorize("@pms.hasPermission(true,#request.orgCode,false,'pm_exportProjectMonitorAsync')")
    public R<String> exportCompareAsync(Page page, @RequestBody ApiCompareVO vo) {
        UUID id = UUID.randomUUID();
        ExcelExportStatusVO excelStatusVO = new ExcelExportStatusVO();
        RedisUtil.set(ExcelUtils.EXCEL_EXPORT + id, excelStatusVO, Duration.ofDays(1));
        exportService.exportCompareAsync(page, vo, id.toString());
        return R.ok(id.toString());
    }

    /**
     * 异步导出通用报销单
     * @return
     */
    @ApiOperation(value = "异步导出通用报销单", notes = "异步导出通用报销单")
    @PostMapping("/exportReimbursementAsync")
    public R<String> exportReimbursementAsync(Page page, @RequestBody FiSourceReimbursementQueryVO queryVO) {
        UUID id = UUID.randomUUID();
        ExcelExportStatusVO excelStatusVO = new ExcelExportStatusVO();
        RedisUtil.set(ExcelUtils.EXCEL_EXPORT + id, excelStatusVO, Duration.ofDays(1));
//        String pkOrg = AdminUtils.processToPkOrg(queryVO.getPkOrg());
//        queryVO.setPkOrg(pkOrg);
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        FiSourceReimbursementExportListener listener = new FiSourceReimbursementExportListener(
                id, fiSourceReimbursementService, page, queryVO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(id.toString());
    }

    /**
     * 异步导出应收单
     * @return
     */
    @ApiOperation(value = "异步导出通用报销单", notes = "异步导出通用报销单")
    @PostMapping("/exportReceivableAsync")
    public R<String> exportReceivableAsync(Page page, @RequestBody FiSourceReceivableQueryVO queryVO) {
        UUID id = UUID.randomUUID();
        ExcelExportStatusVO excelStatusVO = new ExcelExportStatusVO();
        RedisUtil.set(ExcelUtils.EXCEL_EXPORT + id, excelStatusVO, Duration.ofDays(1));
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        FiSourceReceivableExportListener listener = new FiSourceReceivableExportListener(
                id, fiSourceReceivableService, page, queryVO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(id.toString());
    }

    @ApiOperation(value = "异步导出采购订单", notes = "异步导出采购订单")
    @PostMapping("/exportOrderAsync")
    public R<String> exportOrderAsync(Page page, @RequestBody FiSourceOrderQueryVO queryVO) {
        UUID id = UUID.randomUUID();
        ExcelExportStatusVO excelStatusVO = new ExcelExportStatusVO();
        RedisUtil.set(ExcelUtils.EXCEL_EXPORT + id, excelStatusVO, Duration.ofDays(1));
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        FiSourceOrderExportListener listener = new FiSourceOrderExportListener(
                id, fiSourceOrderService, page, queryVO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(id.toString());
    }

    @ApiOperation(value = "异步导出费用结算单采购订单", notes = "异步导出费用结算单采购订单")
    @PostMapping("/exportCostOrderAsync")
    public R<String> exportCostOrderAsync(Page page, @RequestBody FiSourceCostOrderQueryVO queryVO) {
        UUID id = UUID.randomUUID();
        ExcelExportStatusVO excelStatusVO = new ExcelExportStatusVO();
        RedisUtil.set(ExcelUtils.EXCEL_EXPORT + id, excelStatusVO, Duration.ofDays(1));
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        FiSourceCostOrderExportListener listener = new FiSourceCostOrderExportListener(
                id, fiSourceCostOrderService, page, queryVO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(id.toString());
    }

    @ApiOperation(value = "异步导出付款合同采购订单", notes = "异步导出付款合同采购订单")
    @PostMapping("/exportVirtualOrderAsync")
    public R<String> exportVirtualOrderAsync(Page page, @RequestBody FiSourceVirtualOrderQueryVO queryVO) {
        UUID id = UUID.randomUUID();
        ExcelExportStatusVO excelStatusVO = new ExcelExportStatusVO();
        RedisUtil.set(ExcelUtils.EXCEL_EXPORT + id, excelStatusVO, Duration.ofDays(1));
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        FiSourceVirtualOrderExportListener listener = new FiSourceVirtualOrderExportListener(
                id, fiSourceVirtualOrderService, page, queryVO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(id.toString());
    }

    @SysLog("进项发票转换前数据导出")
    @ApiOperation(value = "进项发票转换前数据导出", notes = "进项发票转换前数据导出")
    @PostMapping("/fiSourceFileOcrInfo")
    public R<String> exportFiSourceFileOcrInfo(Page<FiSourceFileOcrInfoVO> page, @RequestBody FiSourceFileOcrInfoQueryDTO queryDTO) {
        UUID id = UUID.randomUUID();
        FiSourceFileOcrInfoExportListener listener = new FiSourceFileOcrInfoExportListener(
                id, fiSourceFileOcrInfoService, page, queryDTO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(String.valueOf(id));
    }

    @SysLog("采购应付转换前数据导出")
    @ApiOperation(value = "应付单转换前数据导出", notes = "应付单转换前数据导出")
    @PostMapping("/puPayable")
    public R<String> exportPuPayable(Page<FiSourcePuPayableVO> page, @RequestBody FiSourcePuPayableQueryDTO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        UUID id = UUID.randomUUID();
        FiSourcePuPayableExportListener listener = new FiSourcePuPayableExportListener(
                id, fiSourcePuPayableService, page, queryVO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(String.valueOf(id));
    }

    @SysLog("采购入库单转换前数据导出")
    @ApiOperation(value = "采购入库单转换前数据导出", notes = "采购入库单转换前数据导出")
    @PostMapping("/Store")
    public R<String> exportStore(Page<FiSourceStoreVO> page, @RequestBody FiSourceStoreQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        UUID id = UUID.randomUUID();
        FiSourceStoreExportListener listener = new FiSourceStoreExportListener(
                id, fiSourceStoreService, page, queryVO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(String.valueOf(id));
    }

    @SysLog("采购/项目付款转换前数据导出")
    @ApiOperation(value = "采购/项目付款转换前数据导出", notes = "采购/项目付款转换前数据导出")
    @PostMapping("/puPaybill")
    public R<String> exportPuPaybill(Page<FiSourcePuPaybillVO> page, @RequestBody FiSourcePuPaybillQueryDTO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        UUID id = UUID.randomUUID();
        FiSourcePuPaybillExportListener listener = new FiSourcePuPaybillExportListener(
                id, fiSourcePuPaybillService, page, queryVO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(String.valueOf(id));
    }

    @SysLog("凭证记账转换前数据导出")
    @ApiOperation(value = "凭证记账转换前数据导出", notes = "凭证记账转换前数据导出")
    @PostMapping("/accounting")
    public R<String> exportAccounting(Page<FiSourceAccountingVO> page, @RequestBody FiSourceAccountingQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        UUID id = UUID.randomUUID();
        FiSourceAccountingExportListener listener = new FiSourceAccountingExportListener(
                id, fiSourceAccountingService, page, queryVO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(String.valueOf(id));
    }

    @SysLog("服务物资发票过账导出")
    @ApiOperation(value = "服务物资发票过账导出", notes = "服务物资发票过账导出")
    @PostMapping("/invoicePosting")
    public R<String> exportPuPayable(Page page, @RequestBody FiInvoicePostingQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        UUID id = UUID.randomUUID();
        FiInvoicePostingExportListener listener = new FiInvoicePostingExportListener(
                id, fiInvoicePostingService, page, queryVO);
        exportService.exportBeforeConvertData(id, listener);
        return R.ok(String.valueOf(id));
    }

    /**
     * 导出通用报销单的统计
     * @return
     */
    @ApiOperation(value = "导出通用报销单的统计", notes = "导出通用报销单的统计")
    @PostMapping("/exportReimbursementStatistics")
    public void exportStatistics(HttpServletResponse response) {
        exportService.exportStatistics(BillTypeEnum.REIMBURSEMENT, response);
    }

    /**
     * 导出通用报销单的统计
     * @return
     */
    @ApiOperation(value = "导出业务单据的统计", notes = "导出业务单据的统计")
    @PostMapping("/exportStatistics/{billType}")
    public void exportStatistics(@PathVariable BillTypeEnum billType, HttpServletResponse response) {
        exportService.exportStatistics(billType, response);
    }
}

package com.cloud.ficonsumer.consumers;

import com.cloud.apiexchange.client.consumer.IMqConsumer;
import com.cloud.apiexchange.client.consumer.MqResult;
import com.cloud.apiexchange.client.consumer.annotation.MqConsumer;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.service.FiSourceProjPaybillService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

import static com.cloud.ficonsumer.enums.Constants.TOPIC_PROJ_PAYBILL_TRANS;

@Slf4j
@MqConsumer(topic = TOPIC_PROJ_PAYBILL_TRANS, group = Constants.Group.group, transaction = false)
@Component
@AllArgsConstructor
public class ProjPaybillPushConsumer implements IMqConsumer {


    private FiSourceProjPaybillService fiSourceProjPaybillService;

    @Override
    public MqResult consume(String pkSettlement) {
        LogUtil.info(log, "项目付款处理开始:", pkSettlement);
        try {
            fiSourceProjPaybillService.push(pkSettlement);
        } catch (Exception e) {
            LogUtil.error(log, "项目付款处理失败:", e);
            return new MqResult(MqResult.FAIL_CODE, AdminUtils.truncateString(e.getMessage(), 1000));
        }
        return new MqResult(MqResult.SUCCESS_CODE, "项目付款处理成功");
    }
}

package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertPuPayableDetail;
import com.cloud.ficonsumer.mapper.FiConvertPuPayableDetailMapper;
import com.cloud.ficonsumer.service.FiConvertPuPayableDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiConvertPuPayableDetailServiceImpl extends ServiceImpl<FiConvertPuPayableDetailMapper, FiConvertPuPayableDetail> implements FiConvertPuPayableDetailService {

}

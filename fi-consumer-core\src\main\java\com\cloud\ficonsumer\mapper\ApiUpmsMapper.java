

package com.cloud.ficonsumer.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.cloud.cloud.admin.api.entity.SysDictItem;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.entity.ApiCompare;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 对照表
 *
 * <AUTHOR> code generator
 * @date 2024-07-12 10:14:10
 */
@DS("upms")
@Mapper
public interface ApiUpmsMapper extends CloudBaseMapper<ApiCompare> {

    @Select("SELECT * FROM `sys_dict_item` WHERE TYPE='api_file_type' AND del_flag=0")
    List<SysDictItem> getFileTypeDictItems();

    @Select("SELECT * FROM `sys_dict_item` WHERE TYPE='api_auxiliary_item' AND del_flag=0")
    List<SysDictItem> getAuxiliaryDictItems();

    @Select("SELECT * FROM `sys_dict_item` WHERE TYPE=#{type} AND del_flag=0")
    List<SysDictItem> getItemListByType(@Param("type") String type);

}

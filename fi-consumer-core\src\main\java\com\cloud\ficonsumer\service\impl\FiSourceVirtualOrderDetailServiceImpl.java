
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceVirtualOrderDetail;
import com.cloud.ficonsumer.mapper.FiSourceVirtualOrderDetailMapper;
import com.cloud.ficonsumer.service.FiSourceVirtualOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * 虚拟采购订单源数据详情表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:31
 */
@Service
public class FiSourceVirtualOrderDetailServiceImpl extends ServiceImpl<FiSourceVirtualOrderDetailMapper, FiSourceVirtualOrderDetail> implements FiSourceVirtualOrderDetailService {

}

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("fi_source_pu_payable")
@ApiModel(value = "采购应付源数据详情")
public class FiSourcePuPayable extends BaseEntity<FiSourcePuPayable> {
    private static final long serialVersionUID = -1L;

    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value = "应付财务组织", required = true)
    private String pkOrg;

    @ApiModelProperty(value = "应付财务组织编码", required = true)
    private String pkOrgCode;

    @ApiModelProperty(value = "应付财务组织名称", required = true)
    private String pkOrgName;

    @ApiModelProperty(value = "制单人", required = true)
    private String billmaker;

    @ApiModelProperty(value = "应付单标识")
    private String pkPayablebill;

    @ApiModelProperty(value = "费用类型")
    private String def6;

    @ApiModelProperty(value = "应付类型")
    private String pkTradetypeid;

    @ApiModelProperty(value = "应付类型编码")
    private String pkTradetype;

    @ApiModelProperty(value = "币种")
    private String pkCurrtype;

    @ApiModelProperty(value = "原币金额", required = true)
    private BigDecimal money;

    @ApiModelProperty(value = "单据状态")
    private Integer billstatus;

    @ApiModelProperty(value = "单据号", required = true)
    private String billno;

    @ApiModelProperty(value = "付款性质")
    private String def47;

    @ApiModelProperty(value = "部门分类")
    private String def52;

    @ApiModelProperty(value="集成状态")
    private String integrationStatus;

    @ApiModelProperty(value="4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败")
    private Integer pushStatus;

    @ApiModelProperty(value="推送成功失败信息")
    private String pushMsg;

    @ApiModelProperty(value="推送时间")
    private String pushTime;

    @ApiModelProperty(value="返回同步消息")
    private String returnMsg;

    @ApiModelProperty(value="返回同步状态")
    private String returnStatus;

    @ApiModelProperty(value="返回单号")
    private String returnBill;

    @ApiModelProperty(value="返回接收时间")
    private String returnTime;

    @ApiModelProperty(value="重试次数")
    private Long tryNumber;

    @ApiModelProperty(value="0 未就绪 1 就绪")
    private Long readiness;

    @ApiModelProperty(value = "是否退回重传标识，Y-是，N-否；标识默认为N，回退成功后，更新为Y；单据集成成功后，重置为N")
    private String backTag;

    @ApiModelProperty(value = "远光回调ID")
    private String ywid;

    @ApiModelProperty(value = "订单编号")
    private String purchaseorder;
}

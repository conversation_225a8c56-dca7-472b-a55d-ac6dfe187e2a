
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertBaseOrder;
import com.cloud.ficonsumer.mapper.FiConvertBaseOrderMapper;
import com.cloud.ficonsumer.service.FiConvertBaseOrderService;
import org.springframework.stereotype.Service;

/**
 * 订单源数据转换主表
 *
 * <AUTHOR>
 * @date 2025-06-03 11:10:07
 */
@Service
public class FiConvertBaseOrderServiceImpl extends ServiceImpl<FiConvertBaseOrderMapper, FiConvertBaseOrder> implements FiConvertBaseOrderService {

}

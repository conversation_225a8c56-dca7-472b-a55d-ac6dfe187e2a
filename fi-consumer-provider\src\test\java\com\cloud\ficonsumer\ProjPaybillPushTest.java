package com.cloud.ficonsumer;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.entity.FiSourceProjPaybill;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.service.FiSourceProjPaybillDetailService;
import com.cloud.ficonsumer.service.FiSourceProjPaybillService;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.FiSourceProjPaybillVO;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.Connection;
import java.sql.Statement;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ProjPaybillPushTest {

    @SpyBean
    private DmMapper dmMapper;

    @SpyBean
    private ApiProjectService apiProjectService;

    @SpyBean
    private FiSourceProjPaybillService fiSourceProjPaybillService;

    @SpyBean
    private FiSourceProjPaybillDetailService fiSourceProjPaybillDetailService;

    @SpyBean
    private ApiCompareCache apiCompareCache;

    private String ygSuccessResp;
    private String ygFailResp;

    @BeforeAll
    public void mockResp() throws Exception {
        ygSuccessResp = new String(Files.readAllBytes(new ClassPathResource("YgSuccessResp.json").getFile().toPath()), StandardCharsets.UTF_8);
        ygFailResp = new String(Files.readAllBytes(new ClassPathResource("YgFailResp.json").getFile().toPath()), StandardCharsets.UTF_8);
    }

    @BeforeEach
    public void initData() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);

        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator erpPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("projpaybill/schema-oracle.sql"),
                    new ClassPathResource("projpaybill/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(erpPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        DynamicDataSourceContextHolder.push("mysql");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("projpaybill/schema-mysql.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 同步ERP项目付款到MySQL
        List<FiSourceProjPaybillVO> sourceList = dmMapper.listProjPaybill();
        for (FiSourceProjPaybillVO source : sourceList) {
            apiProjectService.processProjPaybill(source);
        }
    }

    @AfterEach
    public void cleanData() {
        DynamicRoutingDataSource dynamicDataSource = (DynamicRoutingDataSource) SpringContextHolder.getBean(DataSource.class);

        DataSource erpDataSource = dynamicDataSource.getDataSource("erp");
        try (Connection conn = erpDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 ERP 数据库失败", e);
        }

        DataSource mysqlDataSource = dynamicDataSource.getDataSource("mysql");
        try (Connection conn = mysqlDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 MySQL 数据库失败", e);
        }
    }

    @Test
    @DisplayName("测试推送成功场景")
    public void testPushSuccess() {
        String pkPaybill = "1005A1100000000TESTY";

        try (MockedStatic<YgUtil> ygUtil = mockStatic(YgUtil.class)) {
            ygUtil.when(() -> YgUtil.sendToYg(any(), any())).thenReturn(ygSuccessResp);

            // 执行测试
            boolean result = fiSourceProjPaybillService.push(pkPaybill);

            // 验证结果
            assertTrue(result, "推送应该成功");

            // 验证数据库状态
            FiSourceProjPaybill updatedSource = fiSourceProjPaybillService.getOne(
                    Wrappers.<FiSourceProjPaybill>lambdaQuery()
                            .eq(FiSourceProjPaybill::getPkPaybill, pkPaybill));

            assertEquals("0", updatedSource.getIntegrationStatus(), "集成状态应为成功");
            assertEquals(PushStatusEnum.PUSH_SUCCESS.getCode(), updatedSource.getPushStatus(), "推送状态应为成功");
            assertEquals(1L, updatedSource.getReadiness(), "readiness 值不正确");

            // 验证平台日志
            List<PlatFormLogVO> logs = dmMapper.selectPlatFormLog(pkPaybill, null);
            assertFalse(logs.isEmpty(), "平台日志不应为空");
            assertEquals(Constants.SUCCESS, logs.get(0).getCode(), "日志状态应为成功");
            assertEquals(BillTypeEnum.PROJ_PAYBILL.getCode(), logs.get(0).getPkBillType(), "日志单据类型不正确");
        }
    }

    @Test
    @DisplayName("测试转换失败场景")
    public void testConvertFail() {
        String pkPaybill = "1005A1100000000TESTY";

        // Mock转换异常
        doThrow(new CheckedException("项目付款集成信息转换失败"))
                .when(apiCompareCache).convertData(any(), any(), any());

        // 执行测试并验证异常
        CheckedException exception = assertThrows(CheckedException.class,
                () -> fiSourceProjPaybillService.push(pkPaybill));

        // 验证异常信息
        assertTrue(exception.getMessage().contains("项目付款集成信息转换失败"));

        // 验证数据库状态
        FiSourceProjPaybill source = fiSourceProjPaybillService.getOne(
                Wrappers.<FiSourceProjPaybill>lambdaQuery()
                        .eq(FiSourceProjPaybill::getPkPaybill, pkPaybill));

        assertEquals("1", source.getIntegrationStatus());
        assertEquals(PushStatusEnum.CONVERT_FAIL.getCode(), source.getPushStatus());
        assertNotNull(source.getPushMsg());

        // 验证平台日志
        List<PlatFormLogVO> logs = dmMapper.selectPlatFormLog(pkPaybill, null);
        assertFalse(logs.isEmpty(), "平台日志不应为空");
        assertEquals(Constants.FAIL, logs.get(0).getCode(), "日志状态应为失败");
        assertTrue(logs.get(0).getMsg().contains("项目付款集成信息转换失败"));
    }

    @Test
    @DisplayName("测试推送失败场景")
    public void testPushFail() {
        String pkPaybill = "1005A1100000000TESTY";

        try (MockedStatic<YgUtil> ygUtil = mockStatic(YgUtil.class)) {
            ygUtil.when(() -> YgUtil.sendToYg(any(), any())).thenReturn(ygFailResp);

            // 执行测试并验证异常
            CheckedException exception = assertThrows(CheckedException.class,
                    () -> fiSourceProjPaybillService.push(pkPaybill));

            // 验证异常信息
            assertTrue(exception.getMessage().contains("响应失败"));

            // 验证数据库状态
            FiSourceProjPaybill updatedSource = fiSourceProjPaybillService.getOne(
                    Wrappers.<FiSourceProjPaybill>lambdaQuery()
                            .eq(FiSourceProjPaybill::getPkPaybill, pkPaybill));

            assertEquals("1", updatedSource.getIntegrationStatus());
            assertEquals(PushStatusEnum.PUSH_FAIL.getCode(), updatedSource.getPushStatus());
            assertNotNull(updatedSource.getPushMsg());

            // 验证平台日志
            List<PlatFormLogVO> logs = dmMapper.selectPlatFormLog(pkPaybill, null);
            assertFalse(logs.isEmpty(), "平台日志不应为空");
            assertEquals(Constants.FAIL, logs.get(0).getCode(), "日志状态应为失败");
            assertEquals(BillTypeEnum.PROJ_PAYBILL.getCode(), logs.get(0).getPkBillType(), "日志单据类型不正确");
            assertTrue(logs.get(0).getMsg().contains("响应失败"));
        }
    }
}
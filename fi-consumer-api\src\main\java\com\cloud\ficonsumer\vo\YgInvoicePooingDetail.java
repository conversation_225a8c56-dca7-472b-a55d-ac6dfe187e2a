package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "InvoiceDetailList", description = "发票池发票明细")
public class YgInvoicePooingDetail {

    @ApiModelProperty(value = "行号", required = true)
    private Integer lineNumber;

    @ApiModelProperty(value = "项目名称", required = true)
    private String prjName;

    @ApiModelProperty(value = "规格型号")
    private String speAndMod;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "数量")
    private BigDecimal amt;

    @ApiModelProperty(value = "单价")
    private String unitPrice;

    @ApiModelProperty(value = "金额（不含税）", required = true)
    private String amount;

    @ApiModelProperty(value = "税率", required = true)
    private String taxrt;

    @ApiModelProperty(value = "税额", required = true)
    private String taxAmount;

    @ApiModelProperty(value = "税收分类编码", required = true)
    private String taxClaCode;

    @ApiModelProperty(value = "特殊政策标识")
    private Integer specialPolicySign;

    @ApiModelProperty(value = "零税率标识")
    private Integer taxRateFlag;
}

package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertStore;
import com.cloud.ficonsumer.mapper.FiConvertStoreMapper;
import com.cloud.ficonsumer.service.FiConvertStoreService;
import org.springframework.stereotype.Service;

/**
 * 采购入库源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:44
 */
@Service
public class FiConvertStoreServiceImpl extends ServiceImpl<FiConvertStoreMapper, FiConvertStore> implements FiConvertStoreService {

}

create table file_ocr_info_data_push
(
    id                  text                   null,
    einvoiceid          text                   null comment '发票ID',
    uniquecodeofinvoice varchar(100)           not null comment '发票唯一标识',
    createtime          datetime               null comment '数据插入时间',
    bukrs               text                   null comment '公司代码',
    einvoicenumber      text                   null comment '发票号码',
    einvoicecode        text                   null comment '发票代码',
    invoicedate         text                   null comment '开票日期',
    invoicetype         text                   null comment '发票类型',
    checkcode           text                   null comment '校验码',
    salername           text                   null comment '销方名称',
    salertaxno          text                   null comment '销方税号',
    saleraddressphone   text                   null comment '销方地址、电话',
    saleraccount        text                   null comment '销方开户行及账号',
    buyername           text                   null comment '购方名称',
    buyertaxno          text                   null comment '购方税号',
    buyeraddressphone   text                   null comment '购方地址、电话',
    buyeraccount        text                   null comment '购方开户行及账号',
    invoiceamount       text                   null comment '发票金额(不含税)',
    taxamount           text                   null comment '发票税额',
    totalamount         text                   null comment '价税合计',
    dt                  text                   null comment 'dt',
    file_id             int                    not null comment '文件id',
    sync_status         varchar(2) default '0' null comment '同步状态，0:未同步 1:集成成功 2:同步失败',
    invoice_type_code   varchar(10)            null comment '发票类型编码',
    elec_invoice_no     varchar(20)            null comment '全电发票号码',
    invoice_status      varchar(10)            null comment '发票状态',
    dparty              varchar(50)            null comment '开票人',
    tax_cal_code        varchar(50)            null comment '税收分类编码',
    special_policy_sign int(1)                 null comment '特殊政策标识 0：否 1：是',
    tax_rate_flag       int(1)                 null comment '零税率标识',
    body                varchar(1024)          null comment '发票表体',
    unit_code           varchar(100)           null comment '发票使用单位',
    pk_dept             varchar(100)           null comment '部门主键',
    pk_org              varchar(100)           null comment 'erp组织主键',
    constraint file_ocr_info_data_push_un
        unique (uniquecodeofinvoice)
)
    comment '其他地区产业单位进项发票推送表';

create table file_info
(
    id                 int auto_increment comment 'id'
        primary key,
    relation_id        int                     not null comment '关联id',
    extends_id         int                     null comment '继承id',
    image_code         varchar(50)             not null comment '影像类型编码',
    image_name         varchar(50)             not null comment '影像类型名称',
    is_upload          varchar(2)  default '0' not null comment '是否刚性控制 1-是 0-否',
    file_name          varchar(255)            null comment '文件名',
    bucket_name        varchar(255)            null comment '保存路径',
    original           varchar(255)            null comment '原文件名',
    file_type          varchar(255)            null comment '文件类型',
    file_size          varchar(50)             null comment '文件大小',
    upload_type        varchar(16)             null comment '上传方式（0-手动上传 1-扫描录入）',
    module_type        varchar(255)            null comment '模块对应的表',
    module_id          int                     null comment '模块业务id',
    type               varchar(10) default '0' null comment '模块子类型（0-未知，1-录音，2-图片）',
    show_name          varchar(255)            null comment '显示文件名',
    business_type      varchar(255)            null comment '业务类型',
    erp_file_check     varchar(2)              null comment '文件一致性校验结果 1-通过 0-不通过',
    create_time        datetime                not null comment '创建时间',
    update_time        datetime                null on update CURRENT_TIMESTAMP comment '最后修改时间',
    create_by          varchar(50)             not null comment '创建人',
    create_by_name     varchar(50)             null comment '创建人姓名',
    update_by          varchar(50)             null comment '最后修改人',
    update_by_name     varchar(50)             null comment '最后修改人姓名',
    del_flag           int         default 0   not null comment '删除标识',
    source_id          varchar(40)             null comment '来源主键id',
    source_relation_id varchar(40)             null comment '来源关联id',
    ocr_type           varchar(10)             not null comment 'OCR识别类型'
)
    comment '文件信息表' charset = utf8mb4
                         row_format = DYNAMIC;

create table file_image_upload_info
(
    id             int auto_increment comment 'id'
        primary key,
    system_source  varchar(8)    null comment '系统来源',
    org_id         int           null comment '单位id',
    org_name       varchar(50)   null comment '单位名称',
    group_org_id   int           null comment '组织id',
    group_org_name varchar(50)   null comment '组织名称',
    bill_id        varchar(50)   not null comment '单据主键',
    bill_code      varchar(100)  null comment '单据号',
    bill_name      varchar(100)  null comment '单据名称',
    project_name   varchar(100)  null comment '项目名称',
    business_time  date          null comment '制单日期',
    relation_code  varchar(100)  null comment '关联编码(来源系统_单据类型_单据主键)',
    business_code  varchar(50)   not null comment '单据类型编码',
    business_name  varchar(50)   null comment '单据类型名称',
    trade_code     varchar(50)   null comment '交易类型编码',
    trade_name     varchar(50)   null comment '交易类型名称',
    remark         varchar(100)  null comment '备注',
    del_flag       int default 0 not null comment '删除标识',
    create_time    datetime      null comment '创建时间',
    create_by      varchar(50)   null comment '创建人',
    create_by_name varchar(50)   null comment '创建人姓名',
    update_time    datetime      null on update CURRENT_TIMESTAMP comment '最后修改时间',
    update_by      varchar(50)   null comment '最后修改人',
    update_by_name varchar(50)   null comment '最后修改人姓名',
    source_id      varchar(40)   null comment '来源主键id',
    unit_code      varchar(100)  null comment '发票使用单位',
    pk_dept        varchar(100)  null comment '部门主键',
    pk_org         varchar(100)  null comment 'erp组织主键',
    constraint file_image_upload_info_relation_code_un
        unique (relation_code, business_code, del_flag)
)
    comment '影像上传信息表头' charset = utf8mb4
                               row_format = DYNAMIC;

create table bd_platformlog
(
    pk_log      varchar(40) not null,
    pk_bill     varchar(101)  default null,
    pk_billtype varchar(101)  default null,
    typeid      varchar(101)  default null,
    discode     varchar(101)  default null,
    code        varchar(20)   default null,
    message     varchar(2000) default null,
    def1        varchar(101)  default null,
    def2        varchar(101)  default null,
    def3        varchar(101)  default null,
    def4        varchar(101)  default null,
    def5        varchar(101)  default null,
    def6        varchar(101)  default null,
    def7        varchar(101)  default null,
    def8        varchar(101)  default null,
    def9        varchar(101)  default null,
    def10       varchar(101)  default null,
    ts          timestamp     default current_timestamp,
    primary key (pk_log)
)
    comment '平台集成日志' charset = utf8mb4
                           row_format = DYNAMIC;

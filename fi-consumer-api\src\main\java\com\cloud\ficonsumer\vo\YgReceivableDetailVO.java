package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/24.
 */
@Data
public class YgReceivableDetailVO {

    /**
     * 业务事项
     */
    @ApiModelProperty(value="业务事项")
    private String incTyp;

    /**
     * 明细业务事项
     */
    @ApiModelProperty(value="明细业务事项")
    private String detCosType;

    /**
     * 明细业务事项编码
     */
    @ApiModelProperty(value="明细业务事项编码")
    private String detCosTypeCode;

    /**
     * 合同编号
     */
    @ApiModelProperty(value="合同编号")
    private String conNam;

    /**
     * 项目编码
     */
    @ApiModelProperty(value="项目编码")
    private String prjCode;

    /**
     * 内部订单编号
     */
    @ApiModelProperty(value="内部订单编号")
    private String intOrder;

    /**
     * 明细业务事项编码
     */
    @ApiModelProperty(value="明细业务事项编码")
    private String wbsCod;

    /**
     * 工单编码
     */
    @ApiModelProperty(value="工单编码")
    private String workOrder;

    /**
     * 预留质保金
     */
    @ApiModelProperty(value="预留质保金")
    private String revDep;

    /**
     * 质保金到日期
     */
    @ApiModelProperty(value="质保金到日期")
    private String expiryDate;

    /**
     * 原发票号码
     */
    @ApiModelProperty(value="原发票号码")
    private String invoiceNo;

    /**
     * 原发票代码
     */
    @ApiModelProperty(value="原发票代码")
    private String invoCode;

    /**
     * 原开票日期
     */
    @ApiModelProperty(value="原开票日期")
    private String origInTime;

    /**
     * 原发票类型
     */
    @ApiModelProperty(value="原发票类型")
    private String origInType;

    /**
     * 冲红原因标识
     */
    @ApiModelProperty(value="冲红原因标识")
    private String redReasonTag;

    /**
     * 商品名称
     */
    @ApiModelProperty(value="商品名称")
    private String goodsDesc;

    /**
     * 数量
     */
    @ApiModelProperty(value="数量")
    private String amt;

    /**
     * 税率
     */
    @ApiModelProperty(value="税率")
    private String taxrt;

    /**
     * 价税合计
     */
    @ApiModelProperty(value="价税合计")
    private String totPriTax;

    /**
     * 本次入账金额
     */
    @ApiModelProperty(value="本次入账金额")
    private String curreIncom;

    /**
     * 本次入账税额
     */
    @ApiModelProperty(value="本次入账税额")
    private String curTaxPosted;

    /**
     * 本次入账含税金额
     */
    @ApiModelProperty(value="本次入账含税金额")
    private String recvAmt;

}

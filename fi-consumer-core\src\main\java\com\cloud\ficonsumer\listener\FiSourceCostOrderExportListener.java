package com.cloud.ficonsumer.listener;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiSourceCostOrderDTO;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceCostOrderService;
import com.cloud.ficonsumer.vo.FiSourceCostOrderExportVO;
import com.cloud.ficonsumer.vo.FiSourceCostOrderQueryVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/9.
 */
@Slf4j
public class FiSourceCostOrderExportListener extends AbstractExportListener<FiSourceCostOrderExportVO> {

    private final FiSourceCostOrderService fiSourceCostOrderService;
    private final FiSourceCostOrderQueryVO queryDTO;
    private final Page<FiSourceCostOrderDTO> page;
    private static final int PAGE_SIZE = 5000;

    public FiSourceCostOrderExportListener(UUID id, FiSourceCostOrderService fiSourceCostOrderService,
                                             Page<FiSourceCostOrderDTO> page,
                                           FiSourceCostOrderQueryVO queryDTO) {
        super(id, "付款合同采购订单导出");
        this.page = page;
        this.fiSourceCostOrderService = fiSourceCostOrderService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiSourceCostOrderExportVO> doHandleData() {
        List<FiSourceCostOrderDTO> vos = new ArrayList<>();
        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiSourceCostOrderDTO> pageResult = fiSourceCostOrderService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiSourceCostOrderDTO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiSourceCostOrderDTO> pageResult;
            do {
                pageResult = fiSourceCostOrderService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }
        List<FiSourceCostOrderExportVO> result = BeanUtil.copyToList(vos, FiSourceCostOrderExportVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}

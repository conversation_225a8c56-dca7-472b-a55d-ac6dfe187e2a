package com.cloud.ficonsumer.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "InvoiceFourFactorDTO", description = "发票四要素信息")
public class InvoiceFourFactorDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "合计金额", required = true, example = "1000.00")
    private String amouInTot;

    @ApiModelProperty(value = "校验码", required = true, example = "12345678")
    private String checkCode;

    @ApiModelProperty(value = "开票日期", required = true, example = "20240101")
    private String invDate;

    @ApiModelProperty(value = "发票代码", required = true, example = "044001900211")
    private String invoCode;

    @ApiModelProperty(value = "发票类型", required = true, example = "01")
    private String invoType;

    @ApiModelProperty(value = "发票号码", required = true, example = "12345678")
    private String invoiceNo;

    @ApiModelProperty(value = "是否个人", required = true, example = "0")
    private String isPersonal;

    @ApiModelProperty(value = "价税合计", required = true, example = "101.00")
    private String totPriTax;

    @ApiModelProperty(value = "购买方税号", required = true, example = "123456789012345")
    private String taxNo;
}
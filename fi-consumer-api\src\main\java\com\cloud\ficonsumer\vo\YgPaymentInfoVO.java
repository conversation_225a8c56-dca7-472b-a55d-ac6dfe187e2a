package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "付款信息")
public class  YgPaymentInfoVO {

    @ApiModelProperty(value = "供应商编码", required = true)
    private String supp;

    @ApiModelProperty(value = "本次付款金额本位币", example = "2000.00", notes = "仅报账但不付款传0.00；存在外币业务时传入")
    private BigDecimal pyAmtStdCy;
    
    @ApiModelProperty(value = "是否民工工资账户(********-否,********-是)")
    private String migrantWorkerWageAccount;
    
    @ApiModelProperty(value = "供应商银行账号", required = true)
    private String rAcc;
    
    @ApiModelProperty(value = "供应商银行账户名称", required = true)
    private String rcvrAccName;
    
    @ApiModelProperty(value = "供应商银行账号联行号", required = true)
    private String bank;
    
    @ApiModelProperty(value = "票据收款方账户")
    private String billRcvrAcc;
    
    @ApiModelProperty(value = "票据收款方账户名称")
    private String billRcvrAccName;
    
    @ApiModelProperty(value = "票据银行账号开户行")
    private String billPayBank;
    
    @ApiModelProperty(value = "本次付款金额", required = true, example = "2000.00")
    private BigDecimal amtThisPay;
    
    @ApiModelProperty(value = "供应链融资方式")
    private String finMode;
    
    @ApiModelProperty(value = "项目编码")
    private String prjName;
    
    @ApiModelProperty(value = "往来台账ID")
    private String associatId;
    
    @ApiModelProperty(value = "供应商数字钱包账户")
    private String collWalId;
    
    @ApiModelProperty(value = "供应商数字钱包开户行")
    private String collWalOpenBankId;

    @ApiModelProperty(value = "本次预留质保金金额", example = "0.00")
    private BigDecimal revDep;

    @ApiModelProperty(value = "本次核销预付金额", example = "0.00")
    private BigDecimal curwriOffAdvPmtTax;

    @ApiModelProperty(value = "费用类型")
    private String cosType;

    @ApiModelProperty(value = "明细费用类型")
    private String detCosType;

    @ApiModelProperty(value = "明细费用类型编码")
    private String detCosTypeCode;

    @ApiModelProperty(value = "部门分类")
    private String mainDeptType;
}

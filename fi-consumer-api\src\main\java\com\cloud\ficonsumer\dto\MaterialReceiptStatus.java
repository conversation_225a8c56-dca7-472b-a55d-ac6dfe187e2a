package com.cloud.ficonsumer.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/15.
 */
@Data
public class MaterialReceiptStatus {

    /**
     * MDM基础组织编码
     * 公共
     */
    private String prvMkCode;

    /**
     * 物料凭证年度
     */
    private String mtdocYear;

    /**
     * 物料凭证编号
     */
    private String mtDocNum;

    /**
     * 物料凭证中的项目
     */
    private String mtDocNumLine;

    /**
     * 采购订单号
     * 公共
     */
    private String purOrdNo;

    /**
     * 采购凭证的项目编号
     * 公共
     */
    private String ordProjId;

    /**
     * 发票状态
     * 公共
     * 0（发票校验中）、1（发票校验完成）、2（作废）
     */
    private String invoStatus;



    /**
     * 条目表编号
     */
    private String serIte;

    /**
     * 软件包编号
     */
    private String packageNumber;

    /**
     * 服务行号
     */
    private String serverLineNum;

}

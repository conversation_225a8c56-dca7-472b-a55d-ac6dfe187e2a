package com.cloud.ficonsumer.utils;

import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.common.oss.service.OssTemplate;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.FiConsumerApplication;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.UdsConfig;
import com.cloud.ficonsumer.entity.FiUdsFileRecord;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.service.FiUdsFileRecordService;
import com.sgcc.uds.cloud.sdk.action.result.CommonActionResult;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.util.ReflectionTestUtils;

import javax.sql.DataSource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.Statement;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class),
        @MockBean(ApiCompareCache.class),
        @MockBean(ApiCompareService.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UdsUtilTest {

    @MockBean
    private OssTemplate ossTemplate;

    @Autowired
    private FiUdsFileRecordService fileRecordService;
    
    @Autowired
    private DataSource dynamicDataSource;

    private static final String BUCKET_NAME = "test-bucket";
    private static final String OBJECT_NAME = "test/file.pdf";
    private static final String FILE_NAME = "test.pdf";

    @BeforeEach
    void setUp() {
        // 初始化 mysql 数据库
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("fileocrinfo/schema-ficonsumer.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
        
        // 启用UDS配置
        ReflectionTestUtils.setField(UdsConfig.class, "enable", true);
        
        // 重置所有mock
        reset(ossTemplate);
    }

    @AfterEach
    public void cleanData() {
        // 清理 mysql 库
        try (Connection conn = dynamicDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 MySQL 数据库失败", e);
        }
    }

    @Test
    void testPushFileToUdsFromOSS_Success() throws Exception {
        // 准备测试数据
        S3Object s3Object = mock(S3Object.class);
        S3ObjectInputStream inputStream = new S3ObjectInputStream(
                new ByteArrayInputStream("test content".getBytes()),
                null
        );
        when(s3Object.getObjectContent()).thenReturn(inputStream);
        when(ossTemplate.getObject(eq(BUCKET_NAME), eq(FILE_NAME))).thenReturn(s3Object);

        // Mock UDS响应
        CommonActionResult mockResult = new CommonActionResult();
        mockResult.setDocumentId("test-doc-id");
        mockResult.setFileName("test.pdf");

        try (MockedStatic<UdsUtil> mockedUdsUtil = mockStatic(UdsUtil.class, CALLS_REAL_METHODS)) {
            // 只mock pushFileToUds方法
            mockedUdsUtil.when(() -> UdsUtil.pushFileToUds(
                    any(InputStream.class),
                    any(String.class),
                    any(String.class),
                    any(String.class)
            )).thenReturn(mockResult);

            // 调用真实的pushFileToUdsFromOSS方法
            CommonActionResult result = UdsUtil.pushFileToUdsFromOSS(BUCKET_NAME, OBJECT_NAME, FILE_NAME);

            // 验证结果
            assertNotNull(result);
            assertEquals("test-doc-id", result.getDocumentId());
            assertEquals("test.pdf", result.getFileName());

            // 验证数据库记录
            FiUdsFileRecord record = fileRecordService.checkFileUploaded(BUCKET_NAME, FILE_NAME);
            assertNotNull(record);
            assertEquals("1", record.getPushStatus());
            assertEquals("test-doc-id", record.getDocumentId());
        }
        
        // 验证 mock 调用
        verify(ossTemplate).getObject(eq(BUCKET_NAME), eq(FILE_NAME));
    }

    @Test
    void testPushFileToUdsFromOSS_AlreadyUploaded() {
        // 准备已存在的记录
        FiUdsFileRecord record = new FiUdsFileRecord();
        record.setBucketName(BUCKET_NAME);
        record.setFileName(FILE_NAME);
        record.setOriginal(OBJECT_NAME);
        record.setPushStatus("1");
        record.setDocumentId("existing-doc-id");
        record.setUdsFileName("existing.pdf");
        fileRecordService.save(record);

        // 执行测试
        CommonActionResult result = UdsUtil.pushFileToUdsFromOSS(BUCKET_NAME, OBJECT_NAME, FILE_NAME);

        // 验证结果
        assertNotNull(result);
        assertEquals("existing-doc-id", result.getDocumentId());
        assertEquals("existing.pdf", result.getFileName());
        
        // 验证没有调用 OSS
        verify(ossTemplate, never()).getObject(any(), any());
    }

    @Test
    void testPushFileToUdsFromOSS_Failure() throws Exception {
        // 模拟OSS异常
        when(ossTemplate.getObject(BUCKET_NAME, FILE_NAME))
                .thenThrow(new RuntimeException("OSS error"));

        // 执行测试并验证异常
        CheckedException exception = assertThrows(CheckedException.class,
                () -> UdsUtil.pushFileToUdsFromOSS(BUCKET_NAME, OBJECT_NAME, FILE_NAME));

        // 验证异常消息
        assertTrue(exception.getMessage().contains("从OSS推送文件到UDS失败"));

        // 验证数据库记录
        FiUdsFileRecord record = fileRecordService.checkFileUploaded(BUCKET_NAME, FILE_NAME);
        assertNotNull(record);
        assertEquals("0", record.getPushStatus());
        assertTrue(record.getPushMsg().contains("OSS error"));
        
        // 验证 mock 调用
        verify(ossTemplate).getObject(BUCKET_NAME, FILE_NAME);
    }
}

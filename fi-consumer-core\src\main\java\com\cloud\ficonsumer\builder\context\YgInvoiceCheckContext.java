package com.cloud.ficonsumer.builder.context;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillQueryDTO;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.InvoiceBillCheckEnum;
import com.cloud.ficonsumer.enums.PayableType;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.vo.CosTypeVO;
import com.cloud.ficonsumer.vo.FiSourcePuPayableDetailVO;
import com.cloud.ficonsumer.vo.FiSourcePuPaybillDetailVO;
import com.cloud.ficonsumer.vo.YgInvoiceInfoVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.collections4.MapUtils.isEmpty;

@Data
@Slf4j
public class YgInvoiceCheckContext {
    private FiConvertPuPayable convert;
    private List<FiConvertPuPayableDetail> convertDetails;
    private FiSourcePuPayable source;
    private List<FiSourcePuPayableDetail> sourceDetails;

    /**
     * 应付单类型：采购应付或项目应付
     */
    private PayableType payableType;

    private Map<String, PoOrderB> poOrderBMap;
    private Map<String, IaI2billB> iaI2billBMap;

    private Map<String, PmContr> pmContrMap;

    private Map<String, PmFeebalanceB> pmFeebalanceBMap;
    private Map<String, FiSourceCostOrderDetail> fiSourceCostOrderDetailMap = new HashMap<>();

    private Map<String, List<YgInvoiceInfoVO>> invoiceMap;
    private List<BdUdsResult> imageResults;
    private String mdnOrgCode;
    private String projectCode;
    private String supplierCode;

    // 收款银行账户
    private Map<String, Account> recaccountMap = new HashMap<>();

    // 费用类型相关字段
    private String cosType2;
    private String cosType3;
    private String cosType4;
    private String cosTypeName4;

    private final ApiCompareService apiCompareService;
    private final YgInvoiceService ygInvoiceService;
    private final DmMapper dmMapper;
    private final FiSourceOrderService fiSourceOrderService;
    private final FiSourceStoreService fiSourceStoreService;
    private final FiSourcePuPayableService fiSourcePuPayableService;
    private final FiSourcePuPaybillService fiSourcePuPaybillService;
    private final FiSourceCostOrderService fiSourceCostOrderService;
    private final FiSourceVirtualOrderService fiSourceVirtualOrderService;
    private final FiSourcePuPayableDetailService fiSourcePuPayableDetailService;
    private final FiSourceCostOrderDetailService fiSourceCostOrderDetailService;

    /**
     * 私有构造函数，使用Builder模式创建实例
     */
    private YgInvoiceCheckContext() {
        this.apiCompareService = SpringContextHolder.getBean(ApiCompareService.class);
        this.ygInvoiceService = SpringContextHolder.getBean(YgInvoiceService.class);
        this.dmMapper = SpringContextHolder.getBean(DmMapper.class);
        this.fiSourceOrderService = SpringContextHolder.getBean(FiSourceOrderService.class);
        this.fiSourceStoreService = SpringContextHolder.getBean(FiSourceStoreService.class);
        this.fiSourcePuPayableService = SpringContextHolder.getBean(FiSourcePuPayableService.class);
        this.fiSourcePuPaybillService = SpringContextHolder.getBean(FiSourcePuPaybillService.class);
        this.fiSourceCostOrderService = SpringContextHolder.getBean(FiSourceCostOrderService.class);
        this.fiSourceVirtualOrderService = SpringContextHolder.getBean(FiSourceVirtualOrderService.class);
        this.fiSourcePuPayableDetailService = SpringContextHolder.getBean(FiSourcePuPayableDetailService.class);
        this.fiSourceCostOrderDetailService = SpringContextHolder.getBean(FiSourceCostOrderDetailService.class);
    }

    /**
     * 创建Builder实例
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * Builder类，用于逐步构建YgInvoiceCheckContext
     */
    public static class Builder {
        private final YgInvoiceCheckContext context;

        private Builder() {
            this.context = new YgInvoiceCheckContext();
        }

        /**
         * 设置转换数据
         */
        public Builder withConvertData(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
            context.convert = convert;
            context.convertDetails = convertDetails;
            // 根据交易类型设置应付单类型
            if (convert != null && StringUtils.isNotBlank(convert.getPkTradetype())) {
                context.payableType = PayableType.fromTradeType(convert.getPkTradetype());
            }
            return this;
        }

        /**
         * 设置源数据（可选）
         */
        public Builder withSourceData(FiSourcePuPayable source, List<FiSourcePuPayableDetail> sourceDetails) {
            context.source = source;
            context.sourceDetails = sourceDetails;
            return this;
        }

        /**
         * 构建Context实例
         */
        public YgInvoiceCheckContext build() {
            // 基本参数校验
            if (context.convert == null || CollectionUtils.isEmpty(context.convertDetails)) {
                throw new CheckedException("构建参数不能为空");
            }
            return context;
        }
    }

    /**
     * 准备构建所需的所有数据
     */
    public void prepareData() {
        // 前置参数校验
        validateData();

        // 准备数据
        prepareAccountInfo();
        prepareProjectCode();
        prepareSupplierCode();
        prepareOrgCode();
        prepareCosTypeInfo();
        prepareInvoiceInfo();
        prepareSrcPoInfo();
        prepareSrcPmInfo();
        prepareImageInfo();

        // 后置业务校验
        validateBusinessRules();
    }

    /**
     * 前置参数校验
     */
    private void validateData() {
        if (Objects.isNull(convert) || CollectionUtils.isEmpty(convertDetails)) {
            throw new CheckedException("构建参数不能为空");
        }
    }

    /**
     * 后置业务校验
     */
    private void validateBusinessRules() {
        // 采购应付
        if (PayableType.PURCHASE.equals(payableType)) {
            if (isEmpty(poOrderBMap)) {
                throw new CheckedException("采购订单不存在");
            }
            for (PoOrderB poOrderB : poOrderBMap.values()) {
                // 校验前置单据采购订单和入库单同步状态
                IaI2billB iaI2billB = iaI2billBMap.get(poOrderB.getPkOrderB());
                if (Objects.nonNull(iaI2billB)) {
                    // 存在入库单，校验入库单同步状态
                    fiSourceStoreService.checkSync(iaI2billB.getCbillid());
                } else {
                    // 不存在入库单，校验采购订单同步状态
                    fiSourceOrderService.checkSync(poOrderB.getPkOrder());
                }
            }

            // 校验相同采购订单的采购应付挂账时，不同步远光
            List<String> pkOrders = poOrderBMap.values().stream().map(PoOrderB::getPkOrder).distinct().collect(Collectors.toList());
            Map<String, FiSourcePuPayableDetailVO> noPostedPayables = fiSourcePuPayableService.getNoPostedBySrcBillIds(pkOrders);
            // 排除当前正在同步的应付单
            noPostedPayables.values().removeIf(detail -> detail.getPkPayablebill().equals(convert.getPkPayablebill()));
            if (!isEmpty(noPostedPayables)) {
                StringBuilder errorMsg = new StringBuilder();
                noPostedPayables.forEach((srcBillId, payableDetail) -> {
                    String orderInfo = MessageFormat.format("采购订单主键{0}，存在不为已挂账的应付单{1}\n", srcBillId, payableDetail.getBillno());
                    errorMsg.append(orderInfo);
                });
                throw new CheckedException(errorMsg.toString());
            }
        }
        // 项目分包应付
        else if (PayableType.PROJECT.equals(payableType)) {
            // 校验付款合同同步状态
            if (!isEmpty(pmContrMap)) {
                for (String pkContr : pmContrMap.keySet()) {
                    fiSourceVirtualOrderService.checkSync(pkContr);
                }
            }
            // 校验费用结算单同步状态
            if (!isEmpty(pmFeebalanceBMap)) {
                for (PmFeebalanceB pmFeebalanceB : pmFeebalanceBMap.values()) {
                    // 校验费用结算单同步状态
                    fiSourceCostOrderService.checkSync(pmFeebalanceB.getPkFeebalance());
                }
            }

            // 源头单据为付款合同时
            if ("4D42".equals(convertDetails.get(0).getSrcBilltype())) {
                // 下游所有的应付单，平台回写状态需为已挂账
                Map<String, FiSourcePuPayableDetailVO> noPostedPayable = fiSourcePuPayableService.getNoPostedBySrcBillIds(new ArrayList<>(pmContrMap.keySet()));
                // 排除当前正在同步的应付单
                noPostedPayable.values().removeIf(detail -> detail.getPkPayablebill().equals(convert.getPkPayablebill()));
                if (!isEmpty(noPostedPayable)) {
                    StringBuilder errorMsg = new StringBuilder();
                    noPostedPayable.forEach((srcBillId, payableDetail) -> {
                        String orderInfo = MessageFormat.format("付款合同主键{0}，存在不为已挂账的应付单{1}\n", srcBillId, payableDetail.getBillno());
                        errorMsg.append(orderInfo);
                    });
                    throw new CheckedException(errorMsg.toString());
                }

                // 下游所有为预付款的付款单，平台回写状态需为已记账
                FiSourcePuPaybillQueryDTO queryDTO = new FiSourcePuPaybillQueryDTO();
                queryDTO.setPrepay("1");
                queryDTO.setSrcBillIds(new ArrayList<>(pmContrMap.keySet()));
                Map<String, FiSourcePuPaybillDetailVO> noAccountedPay = fiSourcePuPaybillService.getNoAccountedBySrcBillIds(queryDTO);
                if (!isEmpty(noAccountedPay)) {
                    StringBuilder errorMsg = new StringBuilder();
                    noAccountedPay.forEach((srcBillId, payDetail) -> {
                        String billInfo = MessageFormat.format("付款合同主键{0}，存在预付款不为未记账的付款结算单{1}\n",
                                srcBillId, payDetail.getPkSettlement());
                        errorMsg.append(billInfo);
                    });
                    throw new CheckedException(errorMsg.toString());
                }
            }
        }
    }

    /**
     * 判断是否为采购应付
     */
    public boolean isPurchasePayable() {
        return PayableType.PURCHASE.equals(payableType);
    }

    /**
     * 判断是否为项目应付
     */
    public boolean isProjectPayable() {
        return PayableType.PROJECT.equals(payableType);
    }

    /**
     * 获取应付单类型名称
     */
    public String getPayableTypeName() {
        return payableType != null ? payableType.getName() : "未知";
    }

    /**
     * 准备项目编码映射
     */
    private void prepareProjectCode() {
        if (StringUtils.isNotEmpty(convertDetails.get(0).getProject())) {
            projectCode = apiCompareService.getTurnByType(
                    convertDetails.get(0).getProject(),
                    Constants.project);
        }
    }

    /**
     * 准备供应商编码映射
     */
    private void prepareSupplierCode() {
        String supplier = convertDetails.get(0).getSupplier();
        if (StringUtils.isBlank(supplier)) {
            throw new CheckedException(
                    MessageFormat.format("构建发票校验VO失败：供应商编码为空, 单据号: {0}", convert.getBillno()));
        }
        supplierCode = apiCompareService.getTurnByType(supplier, Constants.PkSupplier);
    }

    /**
     * 准备组织数据
     */
    private void prepareOrgCode() {
        String pkOrg = convert.getPkOrg();
        if (StringUtils.isBlank(pkOrg)) {
            throw new CheckedException(
                    MessageFormat.format("构建发票校验VO失败：组织编码为空, 单据号: {0}", convert.getBillno()));
        }
        mdnOrgCode = apiCompareService.getTurnByType(pkOrg, Constants.PkOrg);
    }

    private void prepareInvoiceInfo() {
        Set<String> bills = getBillsForInvoiceProcessing();
        invoiceMap = ygInvoiceService.batchProcessInvoiceInfo(bills);
    }

    /**
     * 获取需要处理的发票相关单据ID集合
     * 包含两部分：
     * 1. 上游单据ID：筛选出类型为"25"(采购发票)或"4D53"(收票登记)的上游单据ID
     * 2. 当前应付单ID：将当前处理的应付单主键也加入集合
     *
     * @return 需要处理的单据ID集合
     */
    private Set<String> getBillsForInvoiceProcessing() {
        // 获取上游单据ID
        List<String> topBillTypes = Arrays.asList(InvoiceBillCheckEnum.PO_INVOICE.getBusinessCode(),
                InvoiceBillCheckEnum.PM_RECEIPT_REG.getBusinessCode());
        Set<String> bills = convertDetails.stream()
                .filter(detail -> topBillTypes.contains(detail.getTopBilltype()))  // 筛选发票类型单据
                .map(FiConvertPuPayableDetail::getTopBillid)  // 获取上游单据ID
                .collect(Collectors.toSet());  // 收集到Set中去重

        // 将当前应付单主键加入集合
        bills.add(convert.getPkPayablebill());

        return bills;
    }

    private void prepareSrcPoInfo() {
        poOrderBMap = listPoOrderB();
        if (!poOrderBMap.isEmpty()) {
            iaI2billBMap = listIaI2billB(poOrderBMap.keySet());
        }
        // 回写单号、行号到fi_source_pu_payable_detail表
        updateSourcePuPayableDetail();
    }

    private void prepareSrcPmInfo() {
        // 付款合同
        pmContrMap = listPmContr();

        // 费用结算详情
        pmFeebalanceBMap = listPmFeebalanceB();
        if (!isEmpty(pmFeebalanceBMap)) {
            // 查询每个费用结算单对应的详情列表
            List<FiSourceCostOrderDetail> detailList = fiSourceCostOrderDetailService.list(
                    Wrappers.<FiSourceCostOrderDetail>lambdaQuery()
                            .in(FiSourceCostOrderDetail::getPkFeebalanceB, pmFeebalanceBMap.keySet()));
            fiSourceCostOrderDetailMap.putAll(detailList.stream()
                    .collect(Collectors.toMap(FiSourceCostOrderDetail::getPkFeebalanceB,
                            Function.identity(),
                            (v1, v2) -> v1))
            );
        }

        // 回写单号、行号到fi_source_pu_payable_detail表
        updateSourcePuPayableDetail();
    }

    /**
     * 回写单号、行号到fi_source_pu_payable_detail表
     */
    private void updateSourcePuPayableDetail() {
        for (FiConvertPuPayableDetail convertDetail : convertDetails) {

            if (!isEmpty(poOrderBMap)) {
                PoOrderB poOrderB = poOrderBMap.get(convertDetail.getSrcItemid());
                if (Objects.nonNull(poOrderB) && !isEmpty(iaI2billBMap)) {
                    IaI2billB iaI2billB = iaI2billBMap.get(poOrderB.getPkOrderB());
                    fiSourcePuPayableDetailService.update(
                            Wrappers.<FiSourcePuPayableDetail>lambdaUpdate()
                                    .set(FiSourcePuPayableDetail::getPurchaseorder, poOrderB.getVbillcode())
                                    .set(FiSourcePuPayableDetail::getCrowno, poOrderB.getCrowno())
                                    .set(FiSourcePuPayableDetail::getStoreBillno, iaI2billB.getVbillcode())
                                    .set(FiSourcePuPayableDetail::getStoreRowno, iaI2billB.getCrowno())
                                    .eq(FiSourcePuPayableDetail::getPkPayableitem, convertDetail.getPkPayableitem())
                    );
                }
            }

            if (!isEmpty(pmContrMap)) {
                PmContr pmContr = pmContrMap.get(convertDetail.getSrcBillid());
                if (Objects.nonNull(pmContr)) {
                    fiSourcePuPayableDetailService.update(
                            Wrappers.<FiSourcePuPayableDetail>lambdaUpdate()
                                    .set(FiSourcePuPayableDetail::getPurchaseorder, pmContr.getBillCode())
                                    .eq(FiSourcePuPayableDetail::getPkPayableitem, convertDetail.getPkPayableitem())
                    );
                }
            }

            if (!isEmpty(pmFeebalanceBMap)) {
                PmFeebalanceB pmFeebalanceB = pmFeebalanceBMap.get(convertDetail.getSrcBillid());
                if (Objects.nonNull(pmFeebalanceB)) {
                    fiSourcePuPayableDetailService.update(
                            Wrappers.<FiSourcePuPayableDetail>lambdaUpdate()
                                    .set(FiSourcePuPayableDetail::getPurchaseorder, pmFeebalanceB.getBillCode())
                                    .eq(FiSourcePuPayableDetail::getPkPayableitem, convertDetail.getPkPayableitem())
                    );
                }
            }
        }
    }

    private Map<String, PoOrderB> listPoOrderB() {
        // 源头单据为采购订单
        List<String> pkOrderBList = filterPkByBillType("21", FiConvertPuPayableDetail::getSrcItemid);
        if (CollectionUtils.isEmpty(pkOrderBList)) {
            return new HashMap<>();
        }

        return dmMapper.listPoOrderBByPkB(pkOrderBList).stream()
                .collect(Collectors.toMap(
                        PoOrderB::getPkOrderB,
                        Function.identity(),
                        (v1, v2) -> v1
                ));
    }

    private Map<String, PmContr> listPmContr() {
        // 源头单据为付款合同
        List<String> pkContrList = filterPkByBillType("4D42", FiConvertPuPayableDetail::getSrcBillid);
        if (CollectionUtils.isEmpty(pkContrList)) {
            return new HashMap<>();
        }

        return dmMapper.listPmContrByPk(pkContrList).stream()
                .collect(Collectors.toMap(
                        PmContr::getPkContr,
                        Function.identity(),
                        (v1, v2) -> v1
                ));
    }

    private Map<String, PmFeebalanceB> listPmFeebalanceB() {
        // 源头单据为费用结算单
        List<String> pkFeebalanceList = filterPkByBillType("4D83", FiConvertPuPayableDetail::getSrcBillid);
        if (CollectionUtils.isEmpty(pkFeebalanceList)) {
            return new HashMap<>();
        }

        return dmMapper.listPmFeebalanceByPks(pkFeebalanceList).stream()
                .collect(Collectors.toMap(
                        PmFeebalanceB::getPkFeebalanceB,
                        Function.identity(),
                        (v1, v2) -> v1
                ));
    }


    private Map<String, IaI2billB> listIaI2billB(Set<String> poOrderBKeys) {
        if (CollectionUtils.isEmpty(poOrderBKeys)) {
            return new HashMap<>();
        }
        return dmMapper.listIaI2billBByBids(new ArrayList<>(poOrderBKeys)).stream()
                .collect(Collectors.toMap(
                        IaI2billB::getCfirstbid,
                        Function.identity(),
                        (v1, v2) -> v1
                ));
    }

    private void prepareImageInfo() {
        imageResults = dmMapper.listUdsResult(
                convert.getPkPayablebill(),
                convert.getPkTradetype());
    }

    private void prepareAccountInfo() {
        List<String> recaaccountList = convertDetails.stream()
                .map(FiConvertPuPayableDetail::getRecaccount)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(recaaccountList)) {
            recaccountMap = dmMapper.listAccountsByPks(recaaccountList);
        }
    }

    /**
     * 准备费用类型信息
     */
    private void prepareCosTypeInfo() {
        // 初始化费用类型变量
        cosType2 = "";
        cosType3 = "";
        cosType4 = "";
        cosTypeName4 = "";

        // 获取费用类型信息
        if (StringUtils.isBlank(convert.getDef47())) {
            log.error("费用类型编码为空，单据号：{}", convert.getBillno());
            return;
        }

        CosTypeVO cosTypeVO = dmMapper.getCosTypeVOByCurr(convert.getDef47());
        if (cosTypeVO == null) {
            throw new CheckedException("平台业务事项不可为空");
        }

        // 根据innercode长度处理不同的费用类型
        if (cosTypeVO.getInnercode1().length() != 16 && cosTypeVO.getInnercode1().length() != 12) {
            throw new CheckedException("平台业务事项错误");
        } else {
            if (cosTypeVO.getInnercode1().length() == 16) {
                cosTypeName4 = cosTypeVO.getName1();
                cosType4 = cosTypeVO.getCode1();
                cosType3 = cosTypeVO.getCode2();
                cosType2 = cosTypeVO.getCode3();
            } else if (cosTypeVO.getInnercode1().length() == 12) {
                cosType3 = cosTypeVO.getCode1();
                cosType2 = cosTypeVO.getCode2();
            }
        }

        log.debug("费用类型信息准备完成: cosType2={}, cosType3={}, cosType4={}, cosTypeName4={}",
                cosType2, cosType3, cosType4, cosTypeName4);
    }

    /**
     * 从转换明细中筛选指定单据类型的字段值
     *
     * @param srcBillType 单据类型
     * @param fieldMapper 字段映射函数
     * @return 字段值列表
     */
    private List<String> filterPkByBillType(String srcBillType, Function<FiConvertPuPayableDetail, String> fieldMapper) {
        return convertDetails.stream()
                .filter(detail -> StringUtils.equals(srcBillType, detail.getSrcBilltype()))
                .map(fieldMapper)
                .distinct()
                .collect(Collectors.toList());
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloud.ficonsumer.mapper.DmMapper">

    <!-- 获取ERP组织及组织级参数 -->
    <select id="listAllPushEnableOrg" resultType="com.cloud.ficonsumer.common.FiOrgConvert">
        SELECT
        T2.PK_ORG as pkOrg,    <!-- ERP组织主键 -->
        T2.CODE as orgCode,     <!-- ERP组织编码 -->
        T2.NAME as orgName,     <!-- ERP组织名称 -->
        T2.DEF5 as mdmOrgCode,  <!-- MDM组织编码 -->
        T1.INITCODE as initCode, <!-- 组织级参数编码 -->
        T1.VALUE as paramValue  <!-- 组织级参数值 -->
        FROM PUB_SYSINIT T1
        LEFT JOIN ORG_ORGS T2 ON T1.PK_ORG = T2.PK_ORG
        WHERE T1.INITCODE = 'PT01'
        AND T1.DR = 0
        AND T1.VALUE = 'Y'
    </select>

    <!-- 查询部门信息 -->
    <select id="getDeptByPK" resultType="com.cloud.ficonsumer.common.UCN">
        SELECT pk_dept as uuid,
               code,
               name
        FROM org_dept
        WHERE pk_dept = #{pkDept}
    </select>

    <!-- 查询平台日志 -->
    <select id="selectPlatFormLog" resultType="com.cloud.ficonsumer.vo.PlatFormLogVO">
        SELECT
        PK_LOG,
        PK_BILL,
        PK_BILLTYPE,
        CODE,
        MESSAGE AS MSG
        FROM
        BD_PLATFORMLOG
        WHERE
        1=1
        <if test="pkBill != null and pkBill != ''">
            AND PK_BILL = #{pkBill}
        </if>
        <if test="pkBillType != null and pkBillType != ''">
            AND PK_BILLTYPE = #{pkBillType}
        </if>
    </select>

    <select id="listUdsResult" resultType="com.cloud.ficonsumer.entity.BdUdsResult">
        SELECT *
        FROM BD_UDSRESULT
        WHERE PK_BILL = #{pkBill}
          AND PK_BILLTYPE = #{pkBillType}
    </select>

    <select id="getDeptByUserCode" resultType="com.cloud.ficonsumer.common.UCN">
        SELECT T4.PK_DEPT AS ID,
               T4.CODE    AS CODE,
               T4.NAME    AS NAME
        FROM SM_USER T1
                 INNER JOIN BD_PSNDOC T2 ON T1.PK_PSNDOC = T2.PK_PSNDOC
                 INNER JOIN HI_PSNJOB T3 ON T2.PK_PSNDOC = T3.PK_PSNDOC
                 INNER JOIN ORG_DEPT T4 ON T3.PK_DEPT = T4.PK_DEPT
        WHERE T1.DR = 0
          AND T3.DR = 0
          AND T1.USER_CODE = #{userCode}
    </select>

    <!-- 【采购-发票校验申请服务】 -->
     <select id="listPuPayable" resultType="com.cloud.ficonsumer.vo.FiSourcePuPayableVO">
        SELECT T5.* FROM (
        SELECT
            T1.PK_ORG,           -- 应付财务组织
            T1.BILLMAKER,        -- 制单人
            T1.PK_PAYABLEBILL,   -- 应付单标识
            T1.DEF6,             -- 费用类型
            T1.PK_TRADETYPEID,   -- 应付类型
            T1.PK_TRADETYPE,     -- 应付类型编码
            T1.PK_CURRTYPE,      -- 币种
            T1.MONEY,            -- 原币金额
            T1.BILLSTATUS,       -- 单据状态
            T1.BILLNO,           -- 单据号
            T1.DEF47,            -- 付款性质
            T1.DEF52,            -- 部门分类
            T1.DEF24             -- 接收状态
        FROM AP_PAYABLEBILL T1
        WHERE T1.DR = 0  <!-- 删除状态 未逻辑删除 -->
        AND (T1.PK_TRADETYPE = 'F1-Cxx-02' AND T1.SRC_SYSCODE = 4)  <!-- F1-Cxx-02采购应付单，单据所属系统为采购系统 -->
        AND (T1.DEF24 IS NULL OR T1.DEF24 NOT IN ('1', '2'))  <!-- 同步状态 1:同步成功 2:存在项目为自建项目 -->
        AND T1.EFFECTSTATUS = 10 <!-- 生效状态 已生效 -->
        AND T1.PK_ORG in (SELECT T4.PK_ORG FROM PUB_SYSINIT T4 WHERE T4.INITCODE ='PT03' AND T4.VALUE = 'Y')  <!-- 启用同步远光组织 -->
        AND EXISTS (
            SELECT 1
            FROM AP_PAYABLEITEM T2
            JOIN PO_ORDER T3 ON T2.SRC_BILLID = T3.PK_ORDER AND T3.VDEF39 = '1'
            WHERE T1.PK_PAYABLEBILL = T2.PK_PAYABLEBILL
        ) <!-- 关联采购订单，采购订单已同步 -->
        <if test="query.grabTime != null and query.grabTime != ''">
            AND T1.creationtime >= #{query.grabTime}
        </if>
        <if test="query.billno != null and query.billno != ''">
            AND T1.BILLNO LIKE '%' || #{query.billno} || '%'
        </if>
        <if test="query.pkPayablebill != null and query.pkPayablebill != ''">
            AND T1.PK_PAYABLEBILL LIKE '%' || #{query.pkPayablebill} || '%'
        </if>
        ORDER BY T1.TS
        ) T5 WHERE ROWNUM &lt;= 1000
    </select>

    <!-- 【采购-发票校验申请服务】 -->
    <select id="listPrjPayable" resultType="com.cloud.ficonsumer.vo.FiSourcePuPayableVO">
        SELECT T7.* FROM (
        SELECT
        T1.PK_ORG,           -- 应付财务组织
        T1.BILLMAKER,        -- 制单人
        T1.PK_PAYABLEBILL,   -- 应付单标识
        T1.DEF6,             -- 费用类型
        T1.PK_TRADETYPEID,   -- 应付类型
        T1.PK_TRADETYPE,     -- 应付类型编码
        T1.PK_CURRTYPE,      -- 币种
        T1.MONEY,            -- 原币金额
        T1.BILLSTATUS,       -- 单据状态
        T1.BILLNO,           -- 单据号
        T1.DEF47,            -- 付款性质
        T1.DEF52,            -- 部门分类
        T1.DEF24             -- 接收状态
        FROM AP_PAYABLEBILL T1
        WHERE T1.DR = 0  <!-- 删除状态 未逻辑删除 -->
        AND T1.PK_TRADETYPE = 'F1-Cxx-01' <!-- F1-Cxx-01项目应付单 -->
        AND (T1.DEF24 IS NULL OR T1.DEF24 NOT IN ('1', '2'))  <!-- 同步状态 1:同步成功 2:存在项目为自建项目 -->
        AND T1.EFFECTSTATUS = 10 <!-- 生效状态 已生效 -->
        AND T1.PK_ORG in (SELECT T6.PK_ORG FROM PUB_SYSINIT T6 WHERE T6.INITCODE ='PT03' AND T6.VALUE = 'Y')  <!-- 启用同步远光组织 -->
        AND EXISTS (
            SELECT 1
            FROM AP_PAYABLEITEM T2
            WHERE T1.PK_PAYABLEBILL = T2.PK_PAYABLEBILL
            AND (
            EXISTS (SELECT 1 FROM PM_CONTR T4 WHERE T2.SRC_BILLID = T4.PK_CONTR AND T4.HDEF44 = '1')
            OR
            EXISTS (SELECT 1 FROM PM_FEEBALANCE T5 WHERE T2.SRC_BILLID = T5.PK_FEEBALANCE AND T5.DEF8 = '1')
            )
        ) <!-- 关联合同或费用结算单，合同或费用结算已同步 -->
        <if test="query.grabTime != null and query.grabTime != ''">
            AND T1.creationtime >= #{query.grabTime}
        </if>
        <if test="query.billno != null and query.billno != ''">
            AND T1.BILLNO LIKE '%' || #{query.billno} || '%'
        </if>
        <if test="query.pkPayablebill != null and query.pkPayablebill != ''">
            AND T1.PK_PAYABLEBILL LIKE '%' || #{query.pkPayablebill} || '%'
        </if>
        ORDER BY T1.TS
        ) T7 WHERE ROWNUM &lt;= 1000
    </select>

    <select id="getPuPayableDetails" resultType="com.cloud.ficonsumer.vo.FiSourcePuPayableDetailVO">
        SELECT T1.*
        FROM AP_PAYABLEITEM T1
        WHERE PK_PAYABLEBILL = #{pkPayablebill}
        ORDER BY TS
    </select>

    <update id="updatePuPayableReceive">
        UPDATE AP_PAYABLEBILL
        SET DEF24 = '1'
        WHERE PK_PAYABLEBILL = #{pkPayablebill}
    </update>

    <!-- 更新为自建项目状态 -->
    <update id="updatePuPayableSelfBuild">
        UPDATE AP_PAYABLEBILL
        SET DEF24 = '2'
        WHERE PK_PAYABLEBILL = #{pkPayablebill}
    </update>

    <!-- 【采购-预付付款申请单接收服务 (结算对照)】 -->
    <select id="listPuPaybill" resultType="com.cloud.ficonsumer.vo.FiSourcePuPaybillVO">
        SELECT T8.* FROM (
        SELECT
        T1.PK_SETTLEMENT,
        T1.PK_ORG,              <!-- 财务组织 -->
        T1.PK_BILLOPERATOR,     <!-- 业务单据录入人 -->
        T1.BILLCODE,            <!-- 业务单据编号 -->
        T1.PRIMAL,              <!-- 原币金额 -->
        T2.DEF6,                <!-- 费用类型 -->
        T2.PK_TRADETYPE,        <!-- 付款类型编码 -->
        T2.BILLNO
        FROM CMP_SETTLEMENT T1
        INNER JOIN AP_PAYBILL T2 ON T1.PK_BUSIBILL = T2.PK_PAYBILL
        WHERE (T2.PK_TRADETYPE = 'F3-Cxx-02' OR T2.PK_TRADETYPE = 'F3-Cxx-09')
        AND T1.BUSISTATUS IN (8)  <!-- 单据状态为 签字 -->
        AND T1.DR = 0  <!-- 删除状态 未逻辑删除 -->
        AND T2.EFFECTSTATUS = 10
        AND T2.src_syscode != 20
        AND (T2.DEF24 != '1' OR T2.DEF24 IS NULL)
        AND T1.PK_ORG IN (SELECT pk_org FROM pub_sysinit WHERE initcode='PT03' AND VALUE='Y')
        AND EXISTS (
            SELECT 1 FROM AP_PAYITEM T3
            WHERE T2.PK_PAYBILL = T3.PK_PAYBILL
            AND T3.TOP_BILLID IS NOT NULL
        )
        <if test="query.grabTime != null and query.grabTime != ''">
            AND T1.creationtime >= #{query.grabTime}
        </if>
        <if test="query.billcode != null and query.billcode != ''">
            AND T1.BILLCODE LIKE '%' || #{query.billcode} || '%'
        </if>
        <if test="query.pkSettlement != null and query.pkSettlement != ''">
            AND T1.PK_SETTLEMENT = #{query.pkSettlement}
        </if>
        ORDER BY T1.TS DESC
        ) T8 WHERE ROWNUM &lt;= 1000
    </select>
    <!-- 【项目-预付付款申请单】 -->
    <select id="listPuPaybillXM" resultType="com.cloud.ficonsumer.vo.FiSourcePuPaybillVO">
        SELECT T8.* FROM (
        SELECT
        T1.PK_SETTLEMENT,
        T1.PK_ORG,              <!-- 财务组织 -->
        T1.PK_BILLOPERATOR,     <!-- 业务单据录入人 -->
        T1.BILLCODE,            <!-- 业务单据编号 -->
        T2.PK_BILLTYPE,
        T1.PRIMAL,              <!-- 原币金额 -->
        T2.DEF6,                <!-- 费用类型 -->
        T2.PK_TRADETYPE,        <!-- 付款类型编码 -->
        T2.PK_PAYBILL,          <!-- 付款单主键 -->
        T2.DEF47,               <!-- 付款性质 -->
        T2.DEF52                <!-- 部门分类 -->
        FROM CMP_SETTLEMENT T1
        INNER JOIN AP_PAYBILL T2 ON T1.PK_BUSIBILL = T2.PK_PAYBILL
        WHERE T2.PK_TRADETYPE = 'F3-Cxx-01'
        AND T1.DR = 0  <!-- 删除状态 未逻辑删除 -->
        AND T2.EFFECTSTATUS = 10
        AND (T2.DEF24 NOT IN ('1', '2') OR T2.DEF24 IS NULL)
        AND T1.PK_ORG IN (SELECT pk_org FROM pub_sysinit WHERE initcode='PT03' AND VALUE='Y')
        AND EXISTS (
            SELECT 1 FROM AP_PAYITEM T3
            WHERE T2.PK_PAYBILL = T3.PK_PAYBILL
            AND T3.TOP_BILLID IS NOT NULL
        )
        <if test="query.grabTime != null and query.grabTime != ''">
            AND T1.creationtime >= #{query.grabTime}
        </if>
        <if test="query.billcode != null and query.billcode != ''">
            AND T1.BILLCODE LIKE '%' || #{query.billcode} || '%'
        </if>
        <if test="query.pkSettlement != null and query.pkSettlement != ''">
            AND T1.PK_SETTLEMENT = #{query.pkSettlement}
        </if>
        ORDER BY T1.TS DESC
        ) T8 WHERE ROWNUM &lt;= 1000
    </select>

    <select id="getPuPaybillDetails" resultType="com.cloud.ficonsumer.vo.FiSourcePuPaybillDetailVO">
        SELECT
        T1.PK_SETTLEMENT,    <!-- 结算信息主键 -->
        T2.PK_DEPTID_V,      <!-- 部门 -->
        T2.PK_DEPTID,      <!-- 部门 -->
        T2.PREPAY,           <!-- 付款性质 -->
        T1.MEMO,             <!-- 摘要 -->
        T2.PROJECT,          <!-- 项目 -->
        T2.CONTRACTNO,       <!-- 合同号 -->
        T2.PK_RECPAYTYPE,    <!-- 付款业务类型 -->
        T2.SUPPLIER,         <!-- 供应商 -->
        T1.OPPACCOUNT,       <!-- 对方账号 -->
        T1.OPPACCNAME,       <!-- 对方账户户名 -->
        T1.PK_OPPBANK,       <!-- 对方银行 -->
        T1.PAY,               <!-- 付款原币金额 -->
        T1.PK_CURRTYPE_LAST,    <!-- 实付币种 -->
        T2.src_billid,    <!-- 源头单据主键 -->
        T1.CHANGERATE,           <!-- 实付套汇汇率 -->
        T2.DEF38            <!-- 质保金金额 -->
        FROM CMP_DETAIL T1
        INNER JOIN AP_PAYITEM T2 ON T1.PK_BILLDETAIL = T2.PK_PAYITEM
        WHERE T1.PK_SETTLEMENT = #{pkSettlement} AND T1.DR = 0 AND T2.DR = 0
    </select>

    <update id="updatePuPaybillReceive">
        UPDATE AP_PAYBILL
        SET DEF24 = '1'
        WHERE DR = 0 AND BILLNO = #{billCode}
    </update>

    <update id="updatePuPaybillSelfBuild">
        UPDATE AP_PAYBILL
        SET DEF24 = '2'
        WHERE DR = 0 AND BILLNO = #{billCode}
    </update>

    <!-- 【项目分包-项目暂估应付单】 -->
    <select id="listProjEstiPayable" resultType="com.cloud.ficonsumer.vo.FiSourceProjEstiPayableVO">
        SELECT T2.* FROM (
        SELECT T1.*
        FROM AP_ESTIPAYABLEBILL T1
        WHERE PK_TRADETYPE = ''         <!-- 应付类型 -->
        AND BILLSTATUS IN (1)           <!-- 单据状态为 审批通过 -->
        AND DEF20 != '1'                <!-- 同步状态 未同步成功 -->
        AND DR = 0                      <!-- 删除状态 未逻辑删除 -->
        ORDER BY T1.TS DESC
        ) T2 WHERE ROWNUM &lt;= 1000
    </select>

    <select id="getProjEstiPayableDetails"
            resultType="com.cloud.ficonsumer.vo.FiSourceProjEstiPayableDetailVO">
        SELECT T1.*
        FROM AP_ESTIPAYABLEITEM T1
        WHERE PK_ESTIPAYABLEBILL = #{pkEstipayablebill}
        ORDER BY TS
    </select>

    <update id="updateProjEstiPayableReceive">
        UPDATE AP_ESTIPAYABLEBILL
        SET DEF20 = '1'
        WHERE PK_ESTIPAYABLEBILL = #{pkEstipayablebill}
    </update>

    <!-- 【项目分包-接收通用报账单服务】 -->
    <select id="listProjPaybill" resultType="com.cloud.ficonsumer.vo.FiSourceProjPaybillVO">
        SELECT T2.* FROM (
        SELECT T1.*
        FROM AP_PAYBILL T1
        WHERE PK_TRADETYPE = ''         <!-- 应付类型 -->
        AND BILLSTATUS IN (1)           <!-- 单据状态为 审批通过 -->
        AND DEF20 != '1'                <!-- 同步状态 未同步成功 -->
        AND DR = 0                      <!-- 删除状态 未逻辑删除 -->
        ORDER BY T1.TS DESC
        ) T2 WHERE ROWNUM &lt;= 1000
    </select>

    <select id="getProjPaybillDetails" resultType="com.cloud.ficonsumer.vo.FiSourceProjPaybillDetailVO">
        SELECT T1.*
        FROM AP_PAYITEM T1
        WHERE T1.PK_PAYBILL = #{pkPaybill}
    </select>

    <update id="updateProjPaybillReceive">
        UPDATE AP_PAYBILL
        SET DEF20 = '1'
        WHERE PK_PAYBILL = #{pkPaybill}
    </update>

    <!-- 【项目分包-付款申请】 -->
    <select id="listProjsubPayable" resultType="com.cloud.ficonsumer.vo.FiSourceProjsubPayableVO">
    </select>

    <select id="getProjsubPayableDetails" resultType="com.cloud.ficonsumer.vo.FiSourceProjsubPayableDetailVO">
    </select>

    <update id="updateProjsubPayableReceive">

    </update>

    <!-- 【销售订单接口-接收销售订单信息】 -->
    <select id="listSaleOrder" resultType="com.cloud.ficonsumer.vo.FiSourceSaleOrderVO">
        SELECT T2.*
        FROM (
        SELECT T1.*
        FROM SO_SALEORDER T1
        WHERE T1.FSTATUSFLAG = '2'  <!-- 审批通过 -->
        AND T1.VDEF20 != '1'         <!-- 同步状态 未同步成功 -->
        AND T1.DR = 0               <!-- 删除状态 未逻辑删除 -->
        ORDER BY T1.TS DESC         <!-- 假设TS是时间戳字段 -->
        ) T2
        WHERE ROWNUM &lt;= 1000
    </select>

    <select id="getSaleOrderDetails" resultType="com.cloud.ficonsumer.vo.FiSourceSaleOrderDetailVO">
        SELECT T1.*
        FROM SO_SALEORDER_B T1
        WHERE T1.CSALEORDERID = #{csaleorderid}
    </select>

    <update id="updateSaleOrderReceive">
        UPDATE SO_SALEORDER
        SET VDEF20 = '1'  <!-- VDEF20是同步状态字段 -->
        WHERE CSALEORDERID = #{csaleorderid}
    </update>

    <!-- 根据订单明细主键批量查询订单明细信息 -->
    <select id="listPoOrderBByPkB" resultType="com.cloud.ficonsumer.entity.PoOrderB">
        SELECT
        o.vbillcode,
        o.ctrantypeid,
        b.crowno,
        b.pk_material,
        b.pk_order_b,
        b.pk_order,
        o.vdef39
        FROM po_order_b b
        LEFT JOIN po_order o ON b.pk_order = o.pk_order
        WHERE b.dr = 0
        AND b.pk_order_b IN
        <foreach collection="pkOrderBList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 根据入库单表体主键批量查询入库单明细信息 -->
    <select id="listIaI2billBByBids" resultType="com.cloud.ficonsumer.entity.IaI2billB">
        SELECT B.CBILL_BID,
        B.CBILLID,
        O.VBILLCODE,
        B.CROWNO,
        B.NNUM,
        B.CFIRSTID,
        B.CFIRSTBID
        FROM IA_I2BILL_B B
        LEFT JOIN IA_I2BILL O ON B.CBILLID = O.CBILLID
        WHERE B.DR = 0
        <if test="cfirstbids != null and cfirstbids.size() > 0">
            AND B.CFIRSTBID IN
            <foreach collection="cfirstbids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 批量查询应付单状态 -->
    <select id="batchGetApPayablebillStatus" resultType="com.cloud.ficonsumer.dto.BillStatusDTO">
        SELECT PK_PAYABLEBILL AS ID, BILLSTATUS AS STATUS
        FROM AP_PAYABLEBILL
        WHERE DR = 0 AND PK_PAYABLEBILL IN
        <foreach collection="billIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND BILLSTATUS IN
        <foreach collection="validStatuses" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <!-- 批量查询采购发票状态 -->
    <select id="batchGetPoInvoiceStatus" resultType="com.cloud.ficonsumer.dto.BillStatusDTO">
        SELECT PK_INVOICE AS ID, FBILLSTATUS AS STATUS
        FROM PO_INVOICE
        WHERE DR = 0 AND PK_INVOICE IN
        <foreach collection="billIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND FBILLSTATUS IN
        <foreach collection="validStatuses" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <!-- 批量查询收票登记状态 -->
    <select id="batchGetPmReceiptregStatus" resultType="com.cloud.ficonsumer.dto.BillStatusDTO">
        SELECT PK_RECEIPTREG AS ID, BILL_STATUS AS STATUS
        FROM PM_RECEIPTREG
        WHERE DR = 0 AND PK_RECEIPTREG IN
        <foreach collection="billIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND BILL_STATUS IN
        <foreach collection="validStatuses" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <!-- 检查是否存在自建项目 -->
    <select id="checkSelfBuildProjects" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM BD_PROJECT T1
        LEFT JOIN PM_EPS T2 ON T1.PK_EPS = T2.PK_EPS
        WHERE T1.PK_PROJECT IN
        <foreach collection="projectList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND T2.EPS_CODE LIKE '03%'
    </select>

    <select id="listPmContrByPk" resultType="com.cloud.ficonsumer.entity.PmContr">
        SELECT pk_contr,
               bill_code,
               curr_mny,
               curr_taxmny,
               hdef44
        FROM pm_contr
        WHERE pk_contr IN
        <foreach collection="pkContrList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="listPmFeebalanceByPks" resultType="com.cloud.ficonsumer.entity.PmFeebalanceB">
        SELECT
        h.pk_feebalance,
        d.pk_feebalance_b,
        h.bill_code,
        h.def8
        FROM pm_feebalance h
        LEFT JOIN pm_feebalance_b d ON h.pk_feebalance = d.pk_feebalance
        WHERE h.dr = 0
        AND h.pk_feebalance IN
        <foreach collection="pkFeebalanceList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="batchGetOrgUcn" resultType="com.cloud.ficonsumer.common.UCN">
        SELECT
            pk_org as uuid,
            code,
            name
        FROM org_orgs
        WHERE pk_org IN
        <foreach collection="pkOrgs" item="pkOrg" open="(" separator="," close=")">
            #{pkOrg}
        </foreach>
    </select>

    <!-- 根据银行账户子户主键查询账户信息 -->
    <select id="listAccountsByPks" resultType="com.cloud.ficonsumer.entity.Account">
        SELECT
        T1.PK_BANKACCSUB as pkBankaccsub,
        T1.ACCNUM as accnum,
        T1.ACCNAME as accname,
        T2.COMBINENUM as combinenum
        FROM BD_BANKACCSUB T1
        LEFT JOIN BD_BANKACCBAS T2 ON T1.PK_BANKACCBAS = T2.PK_BANKACCBAS
        WHERE T1.PK_BANKACCSUB IN
        <foreach collection="pks" item="pk" open="(" separator="," close=")">
            #{pk}
        </foreach>
    </select>
</mapper>

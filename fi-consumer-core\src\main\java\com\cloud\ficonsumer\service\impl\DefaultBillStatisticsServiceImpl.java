package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.cloud.ficonsumer.entity.FiOrgCompare;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.mapper.FiConsumerMapper;
import com.cloud.ficonsumer.service.BillStatisticsService;
import com.cloud.ficonsumer.service.FiOrgCompareService;
import com.cloud.ficonsumer.vo.BillStatisticsVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
public class DefaultBillStatisticsServiceImpl implements BillStatisticsService {

    private final FiOrgCompareService fiOrgCompareService;
    private final FiConsumerMapper fiConsumerMapper;

    @Override
    public List<BillStatisticsVO> getStatistics(BillTypeEnum billType) {
        List<BillStatisticsVO> statisticsVOList = new ArrayList<>();
        List<FiOrgCompare> groupMap = fiOrgCompareService.getGroupMap();

        if (CollUtil.isEmpty(groupMap)) {
            return statisticsVOList;
        }

        BigDecimal sumNumSum = BigDecimal.ZERO;
        BigDecimal failNumSum = BigDecimal.ZERO;
        BigDecimal successNumSum = BigDecimal.ZERO;

        // 遍历每个集团获取统计数据
        for (FiOrgCompare fiOrgCompare : groupMap) {
            BillStatisticsVO billStatisticsVO = fiConsumerMapper.getStatisticsByGroup(billType.getCode(), fiOrgCompare.getPkGroup());

            if (billStatisticsVO == null) {
                billStatisticsVO = new BillStatisticsVO();
            }

            billStatisticsVO.setPkGroupName(fiOrgCompare.getGroupName());

            // 计算成功率
            if (StrUtil.isNotBlank(billStatisticsVO.getSumNum())) {
                BigDecimal sumNum = new BigDecimal(billStatisticsVO.getSumNum());
                BigDecimal failNum = new BigDecimal(billStatisticsVO.getFailNum());
                BigDecimal successNum = new BigDecimal(billStatisticsVO.getSuccessNum());

                // 累加总计数据
                sumNumSum = sumNumSum.add(sumNum);
                failNumSum = failNumSum.add(failNum);
                successNumSum = successNumSum.add(successNum);

                // 计算成功率
                billStatisticsVO.setSuccessRate(calculateSuccessRate(sumNum, successNum));
            }

            statisticsVOList.add(billStatisticsVO);
        }

        // 添加合计行
        statisticsVOList.add(createTotalRow(sumNumSum, failNumSum, successNumSum));

        return statisticsVOList;
    }

    @Override
    public BillTypeEnum getSupportedBillType() {
        // 默认实现
        return null;
    }

    /**
     * 计算成功率
     */
    private String calculateSuccessRate(BigDecimal sumNum, BigDecimal successNum) {
        if (sumNum.compareTo(BigDecimal.ZERO) == 0) {
            return "-";
        }

        BigDecimal ratio = successNum.divide(sumNum, 4, RoundingMode.HALF_UP);
        BigDecimal successRate = ratio.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);

        NumberFormat percentFormat = NumberFormat.getPercentInstance();
        percentFormat.setMaximumFractionDigits(2);

        return percentFormat.format(successRate.doubleValue() / 100);
    }

    /**
     * 创建合计行
     */
    private BillStatisticsVO createTotalRow(BigDecimal sumNumSum, BigDecimal failNumSum, BigDecimal successNumSum) {
        BillStatisticsVO totalRow = new BillStatisticsVO();
        totalRow.setPkGroupName("合计");
        totalRow.setSumNum(String.valueOf(sumNumSum));
        totalRow.setFailNum(String.valueOf(failNumSum));
        totalRow.setSuccessNum(String.valueOf(successNumSum));
        totalRow.setSuccessRate(calculateSuccessRate(sumNumSum, successNumSum));
        return totalRow;
    }
}

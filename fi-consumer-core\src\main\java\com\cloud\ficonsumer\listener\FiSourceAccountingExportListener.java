package com.cloud.ficonsumer.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiSourceAccountingDTO;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceAccountingService;
import com.cloud.ficonsumer.vo.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class FiSourceAccountingExportListener extends AbstractExportListener<FiSourceAccountingExportVO> {



    private final FiSourceAccountingService fiSourceAccountingService;
    private final FiSourceAccountingQueryVO queryDTO;
    private final Page<FiSourceAccountingVO> page;
    private static final int PAGE_SIZE = 1000;

    public FiSourceAccountingExportListener(UUID id,
                                            FiSourceAccountingService fiSourceAccountingService,
                                            Page<FiSourceAccountingVO> page,
                                            FiSourceAccountingQueryVO queryDTO) {
        super(id, "采购应付转换前数据");
        this.page = page;
        this.fiSourceAccountingService = fiSourceAccountingService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiSourceAccountingExportVO> doHandleData() {
        List<FiSourceAccountingDTO> vos = new ArrayList<>();

        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiSourceAccountingDTO> pageResult = fiSourceAccountingService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiSourceAccountingDTO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiSourceAccountingDTO> pageResult;
            do {
                pageResult = fiSourceAccountingService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }

        List<FiSourceAccountingExportVO> result = BeanUtil.copyToList(vos, FiSourceAccountingExportVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}
package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.admin.api.entity.SysDictItem;
import com.cloud.cloud.admin.api.feign.RemoteDictService;
import com.cloud.cloud.common.core.constant.SecurityConstants;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.common.oss.OssProperties;
import com.cloud.common.oss.service.OssTemplate;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementExcelVO;
import com.cloud.ficonsumer.entity.ApiCompare;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.factory.BillStatisticsFactory;
import com.cloud.ficonsumer.listener.AbstractExportListener;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.service.ExportService;
import com.cloud.ficonsumer.service.FiSourceReimbursementService;
import com.cloud.ficonsumer.utils.ExcelUtils;
import com.cloud.ficonsumer.utils.RedisUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: zhengjinjin
 * @Descrition: 导出excel文件 service
 * @Date: 2020/11/11
 */
@DS("apiexchange")
@Service
@Slf4j
@AllArgsConstructor
public class ExportServiceImpl implements ExportService {

    private final HttpServletResponse response;

    private final OssTemplate ossTemplate;

    private final OssProperties ossProperties;

    private final ApiCompareService apiCompareService;

    private final RemoteDictService remoteDictService;

    private final FiSourceReimbursementService fiSourceReimbursementService;

    private final BillStatisticsFactory billStatisticsFactory;

    @Async
    @Override
    public <T> void exportBeforeConvertData(UUID id, AbstractExportListener<T> listener) {
        try {
            listener.export();
        } catch (Exception e) {
            log.error("导出失败", e);
            ExcelExportStatusVO excelStatusVO = new ExcelExportStatusVO();
            excelStatusVO.setResult(false);
            excelStatusVO.setIsFinish(true);
            RedisUtil.set(ExcelUtils.EXCEL_EXPORT + id, excelStatusVO, Duration.ofDays(1));
        }
    }

    /**
     * 异步导出工程详细信息分析查询
     *
     * @param page
     * @param request
     * @param id
     */
    @Async
    @Override
    public void exportCompareAsync(Page page, ApiCompareVO request, String id) {
        try {
            Page<ApiCompare> resultIPage = apiCompareService.getApiComparePage(page, request);
            List<ApiCompare> records = resultIPage.getRecords();
            String primaryName = "对照信息";
            //List<SysDictItem> fileTypeDictItems = apiCompareService.getFileTypeDictItems();
            //获取档案类型字典项
            List<SysDictItem> fileTypeDictItems = remoteDictService.getDictByTypeInner("api_file_type", SecurityConstants.FROM_IN).getData();

            Map<String, String> collect = fileTypeDictItems.stream().collect(Collectors.toMap(SysDictItem::getValue, SysDictItem::getLabel, (key1, key2) -> key2));

            List<ApiCompareExportVO> list = records.stream().map(item -> {
                String value = collect.get(item.getFileType());
                ApiCompareExportVO excelVO = new ApiCompareExportVO();
                BeanUtils.copyProperties(item, excelVO);
                excelVO.setFileType(value);
                return excelVO;
            }).collect(Collectors.toList());
            download(list, ApiCompareExportVO.class, ExcelUtils.EXCEL_EXPORT + id, primaryName);
        } catch (Exception e) {
            log.error("导出失败", e);
            ExcelExportStatusVO excelStatusVO = new ExcelExportStatusVO();
            excelStatusVO.setResult(false);
            excelStatusVO.setIsFinish(true);
            RedisUtil.set(ExcelUtils.EXCEL_EXPORT + id, excelStatusVO, Duration.ofDays(1));
        }
    }

    /**
     * 下载导出文件
     *
     * @param exportData    导出数据列表
     * @param clzss         导出对象实体class
     * @param exportDataKey 导出信息缓存key
     * @param sheetName     表格名
     */
    private <E> void download(List<E> exportData, Class<E> clzss, String exportDataKey, String sheetName) {
        ExcelExportStatusVO excelStatusVO = new ExcelExportStatusVO();
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcel.write(out, clzss).autoCloseStream(Boolean.FALSE).sheet(sheetName).doWrite(exportData);
            String fileName = IdUtil.simpleUUID() + ExcelTypeEnum.XLSX.getValue();
            ossTemplate.putObject(ossProperties.getBucketName(), fileName, new ByteArrayInputStream(out.toByteArray()));
            excelStatusVO.setResult(true);
            excelStatusVO.setUrl(fileName);
        } catch (Exception e) {
            excelStatusVO.setResult(false);
            excelStatusVO.getErrorMsg().add(String.format("%s导出失败, 报错原因: %s", sheetName, e.getMessage()));
            log.error(String.format("%s导出失败", sheetName), e);
        } finally {
            excelStatusVO.setIsFinish(true);
            RedisUtil.set(exportDataKey, excelStatusVO, Duration.ofDays(1));
        }
    }

    /**
     * 导出单据统计信息
     *
     * @param billType 单据类型
     * @param response HTTP响应
     */
    @Override
    public void exportStatistics(BillTypeEnum billType, HttpServletResponse response) {
        try {
            // 1. 设置响应头
            setResponseHeader(response, billType.getName());
            
            // 2. 获取统计数据
            List<BillStatisticsVO> dataList = billStatisticsFactory.getService(billType).getStatistics(billType);
            if (CollUtil.isEmpty(dataList)) {
                throw new CheckedException("未查询到需要导出的数据");
            }

            // 3. 执行导出
            exportWithTemplate(response, dataList);
            
        } catch (CheckedException ce) {
            log.warn("导出{}统计失败: {}", billType.getName(), ce.getMessage());
            throw ce;
        } catch (Exception e) {
            log.error("导出{}统计异常", billType.getName(), e);
            throw new CheckedException(String.format("导出%s统计失败，请联系管理员", billType.getName()));
        }
    }

    /**
     * 使用模板导出数据
     */
    private void exportWithTemplate(HttpServletResponse response, List<BillStatisticsVO> dataList) throws IOException {
        String templatePath = "templates/CommonStatistics.xlsx";
        try (InputStream templateStream = loadTemplate(templatePath)) {
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .withTemplate(templateStream)
                    .build();
            
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            
            // 配置填充参数
            FillConfig fillConfig = FillConfig.builder()
                    .forceNewRow(Boolean.TRUE)
                    .build();

            // 填充数据
            excelWriter.fill(dataList, fillConfig, writeSheet);

            Map<String, Object> extraData = new HashMap<>(1);
            extraData.put("nowTime", DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
            excelWriter.fill(extraData, writeSheet);

            excelWriter.finish();
        }
    }

    /**
     * 加载Excel模板
     */
    private InputStream loadTemplate(String templatePath) {
        InputStream templateStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(templatePath);
        if (templateStream == null) {
            throw new CheckedException("未找到导出模板文件");
        }
        return templateStream;
    }

    //这里设置响应头的部分参数
    private void setResponseHeader(HttpServletResponse response, String fileName) throws Exception {
        try {
            // 修正文件扩展名为xlsx以匹配实际格式
            String fileNameStr = fileName + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx";
            String encodedFileName = URLEncoder.encode(fileNameStr, StandardCharsets.UTF_8.toString());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
        } catch (Exception e) {
            log.error("set response header error", e);
            throw new Exception("设置响应头失败: " + e.getMessage());
        }
    }
}

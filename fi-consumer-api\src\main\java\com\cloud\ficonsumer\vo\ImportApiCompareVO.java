package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2023/8/3.
 */
@Data
@ColumnWidth(7)
public class ImportApiCompareVO {


    @NotBlank(message = "档案类型不可为空")
    @ApiModelProperty(value="档案类型名称")
    @ExcelProperty(value="档案类型",index =0 )
    private String fileTypeName;
    /**
     * 来源系统主键
     */
    @NotBlank(message = "来源系统主键不可为空")
    @ApiModelProperty(value="来源系统主键")
    @ExcelProperty(value="来源系统主键",index =1 )
    private String sourceSystemId;

    /**
     * 来源系统id
     */
    @NotBlank(message = "来源系统id不可为空")
    @ApiModelProperty(value="来源系统id")
    @ExcelProperty(value="来源系统id",index =2 )
    private String sourceSystemCode;

    /**
     * 来源系统名称
     */
    @NotBlank(message = "来源系统名称不可为空")
    @ApiModelProperty(value="来源系统名称")
    @ExcelProperty(value="来源系统名称",index =3 )
    private String sourceSystemName;

    /**
     * 目标系统主键
     */
    @NotBlank(message = "目标系统主键不可为空")
    @ApiModelProperty(value="目标系统主键")
    @ExcelProperty(value="目标系统主键",index =4 )
    private String targetSystemId;

    /**
     * 目标系统id
     */
    @NotBlank(message = "目标系统id不可为空")
    @ApiModelProperty(value="目标系统id")
    @ExcelProperty(value="目标系统id",index =5 )
    private String targetSystemCode;

    /**
     * 目标系统名称
     */
    @NotBlank(message = "目标系统名称不可为空")
    @ApiModelProperty(value="目标系统名称")
    @ExcelProperty(value="目标系统名称",index =6 )
    private String targetSystemName;

    @ApiModelProperty(value="档案类型")
    @ExcelIgnore
    private String fileType;
}

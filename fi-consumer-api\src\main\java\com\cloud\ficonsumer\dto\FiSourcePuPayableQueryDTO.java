package com.cloud.ficonsumer.dto;

import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class FiSourcePuPayableQueryDTO extends FiSourcePuPayable {

    @ApiModelProperty(value = "组织编码")
    private String orgCode;

    @ApiModelProperty(value = "ERP组织主键")
    private String pkOrg;

    /**
     * 集团标识
     * 当查询为某一个集团下所有单位数据时候传集团编码
     */
    @ApiModelProperty(value="集团标识")
    private String groupCodeFlag;

    /**
     * 集团主键
     */
    @ApiModelProperty(value="集团主键")
    private String group;

    /**
     * 组织集合
     */
    @ApiModelProperty(value="组织集合")
    private List<String> pkOrgList;

    @ApiModelProperty(value = "同步状态列表")
    private List<Integer> pushStatusList;

    /**
     * 单据爬取时间
     */
    @ApiModelProperty(value="单据爬取时间")
    private String grabTime;

    /**
     * 拉取起始时间
     */
    @ApiModelProperty(value="拉取起始时间")
    private String createTimeStart;


    /**
     * 拉取结束时间
     */
    @ApiModelProperty(value="拉取结束时间")
    private String createTimeEnd;
}

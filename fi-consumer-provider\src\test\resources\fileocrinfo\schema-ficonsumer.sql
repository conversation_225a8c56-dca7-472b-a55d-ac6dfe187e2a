-- 创建进项发票源数据表
CREATE TABLE `fi_source_file_ocr_info`
(
    `id`                  bigint        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `file_manage_id`      text          NOT NULL COMMENT 'FileManage系统表主键',
    `einvoiceid`          varchar(100) DEFAULT NULL COMMENT '发票ID',
    `uniquecodeofinvoice` varchar(100) DEFAULT NULL COMMENT '发票唯一标识',
    `createtime`          datetime     DEFAULT NULL COMMENT '数据插入时间',
    `bukrs`               varchar(50)  DEFAULT NULL COMMENT '公司代码',
    `einvoicenumber`      varchar(100) DEFAULT NULL COMMENT '发票号码',
    `einvoicecode`        varchar(100) DEFAULT NULL COMMENT '发票代码',
    `invoicedate`         varchar(20)  DEFAULT NULL COMMENT '开票日期',
    `invoicetype`         varchar(50)  DEFAULT NULL COMMENT '发票类型',
    `checkcode`           varchar(100) DEFAULT NULL COMMENT '校验码',
    `salername`           varchar(200) DEFAULT NULL COMMENT '销方名称',
    `salertaxno`          varchar(50)  DEFAULT NULL COMMENT '销方税号',
    `saleraddressphone`   varchar(200) DEFAULT NULL COMMENT '销方地址、电话',
    `saleraccount`        varchar(200) DEFAULT NULL COMMENT '销方开户行及账号',
    `buyername`           varchar(200) DEFAULT NULL COMMENT '购方名称',
    `buyertaxno`          varchar(50)  DEFAULT NULL COMMENT '购方税号',
    `buyeraddressphone`   varchar(200) DEFAULT NULL COMMENT '购方地址、电话',
    `buyeraccount`        varchar(200) DEFAULT NULL COMMENT '购方开户行及账号',
    `invoiceamount`       varchar(20)  DEFAULT NULL COMMENT '发票金额(不含税)',
    `taxamount`           varchar(20)  DEFAULT NULL COMMENT '发票税额',
    `totalamount`         varchar(20)  DEFAULT NULL COMMENT '价税合计',
    `dt`                  varchar(50)  DEFAULT NULL COMMENT 'dt',
    `file_id`             varchar(50)  DEFAULT NULL COMMENT '文件id',
    `sync_status`         varchar(2)   DEFAULT '0' COMMENT '同步状态',
    `invoice_type_code`   varchar(10)   null comment '发票类型编码',
    `elec_invoice_no`     varchar(20)   null comment '全电发票号码',
    `invoice_status`      varchar(10)   null comment '发票状态',
    `dparty`              varchar(50)   null comment '开票人',
    `tax_cal_code`        varchar(50)   null comment '税收分类编码',
    `special_policy_sign` int(1)        null comment '特殊政策标识 0：否 1：是',
    `tax_rate_flag`       int(1)        null comment '零税率标识',
    `body`                varchar(1024) null comment '发票表体',
    `unit_code`           varchar(100)  null comment '发票使用单位',
    `pk_dept`             varchar(100)  null comment '部门主键',
    `integration_status`  varchar(2)   DEFAULT '0' COMMENT '集成状态',
    `push_status`         int          DEFAULT NULL COMMENT '推送状态(4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败)',
    `push_msg`            text         DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`          text         DEFAULT NULL COMMENT '返回同步消息',
    `return_status`       varchar(2)   DEFAULT NULL COMMENT '返回同步状态',
    `try_number`          bigint       DEFAULT '0' COMMENT '重试次数',
    `readiness`           bigint       DEFAULT '0' COMMENT '0 未就绪 1 就绪',
    `create_time`         datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_time`         datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `create_by`           varchar(50)  DEFAULT NULL COMMENT '创建人',
    `update_by`           varchar(50)  DEFAULT NULL COMMENT '更新人',
    `del_flag`            bigint       DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='进项发票源数据详情';

-- 创建文件信息源表
CREATE TABLE `fi_source_file_info`
(
    `id`                 int    NOT NULL AUTO_INCREMENT COMMENT 'id',
    `file_manage_id`     bigint NOT NULL COMMENT 'FileManage系统表主键',
    `org_id`             int    null comment '单位id',
    `relation_id`        int          DEFAULT NULL COMMENT '关联id',
    `extends_id`         int          DEFAULT NULL COMMENT '继承id',
    `image_code`         varchar(50)  DEFAULT NULL COMMENT '影像类型编码',
    `image_name`         varchar(50)  DEFAULT NULL COMMENT '影像类型名称',
    `is_upload`          varchar(2)   DEFAULT '0' COMMENT '是否刚性控制 1-是 0-否',
    `file_name`          varchar(255) DEFAULT NULL COMMENT '文件名',
    `bucket_name`        varchar(255) DEFAULT NULL COMMENT '保存路径',
    `original`           varchar(255) DEFAULT NULL COMMENT '原文件名',
    `file_type`          varchar(255) DEFAULT NULL COMMENT '文件类型',
    `file_size`          varchar(50)  DEFAULT NULL COMMENT '文件大小',
    `upload_type`        varchar(16)  DEFAULT NULL COMMENT '上传方式（0-手动上传 1-扫描录入）',
    `module_type`        varchar(255) DEFAULT NULL COMMENT '模块对应的表',
    `module_id`          int          DEFAULT NULL COMMENT '模块业务id',
    `type`               varchar(10)  DEFAULT '0' COMMENT '模块子类型（0-未知，1-录音，2-图片）',
    `show_name`          varchar(255) DEFAULT NULL COMMENT '显示文件名',
    `business_type`      varchar(255) DEFAULT NULL COMMENT '业务类型',
    `erp_file_check`     varchar(2)   DEFAULT NULL COMMENT '文件一致性校验结果 1-通过 0-不通过',
    `create_by_name`     varchar(50)  DEFAULT NULL COMMENT '创建人姓名',
    `update_by_name`     varchar(50)  DEFAULT NULL COMMENT '最后修改人姓名',
    `source_id`          varchar(40)  DEFAULT NULL COMMENT '来源主键id',
    `source_relation_id` varchar(40)  DEFAULT NULL COMMENT '来源关联id',
    `ocr_type`           varchar(20)  DEFAULT NULL COMMENT 'ocr类型',
    `create_time`        datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_time`        datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `create_by`          varchar(50)  DEFAULT NULL COMMENT '创建人',
    `update_by`          varchar(50)  DEFAULT NULL COMMENT '更新人',
    `del_flag`           bigint       DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='文件信息源';

-- 创建进项发票转换数据表
CREATE TABLE `fi_convert_file_ocr_info`
(
    `id`                  bigint        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `file_manage_id`      text          NOT NULL COMMENT 'FileManage系统表主键',
    `einvoiceid`          varchar(100) DEFAULT NULL COMMENT '发票ID',
    `uniquecodeofinvoice` varchar(100) DEFAULT NULL COMMENT '发票唯一标识',
    `createtime`          datetime     DEFAULT NULL COMMENT '数据插入时间',
    `bukrs`               varchar(50)  DEFAULT NULL COMMENT '公司代码',
    `einvoicenumber`      varchar(100) DEFAULT NULL COMMENT '发票号码',
    `einvoicecode`        varchar(100) DEFAULT NULL COMMENT '发票代码',
    `invoicedate`         varchar(20)  DEFAULT NULL COMMENT '开票日期',
    `invoicetype`         varchar(50)  DEFAULT NULL COMMENT '发票类型',
    `checkcode`           varchar(100) DEFAULT NULL COMMENT '校验码',
    `salername`           varchar(200) DEFAULT NULL COMMENT '销方名称',
    `salertaxno`          varchar(50)  DEFAULT NULL COMMENT '销方税号',
    `saleraddressphone`   varchar(200) DEFAULT NULL COMMENT '销方地址、电话',
    `saleraccount`        varchar(200) DEFAULT NULL COMMENT '销方开户行及账号',
    `buyername`           varchar(200) DEFAULT NULL COMMENT '购方名称',
    `buyertaxno`          varchar(50)  DEFAULT NULL COMMENT '购方税号',
    `buyeraddressphone`   varchar(200) DEFAULT NULL COMMENT '购方地址、电话',
    `buyeraccount`        varchar(200) DEFAULT NULL COMMENT '购方开户行及账号',
    `invoiceamount`       varchar(20)  DEFAULT NULL COMMENT '发票金额(不含税)',
    `taxamount`           varchar(20)  DEFAULT NULL COMMENT '发票税额',
    `totalamount`         varchar(20)  DEFAULT NULL COMMENT '价税合计',
    `dt`                  varchar(50)  DEFAULT NULL COMMENT 'dt',
    `file_id`             varchar(50)  DEFAULT NULL COMMENT '文件id',
    `sync_status`         varchar(2)   DEFAULT '0' COMMENT '同步状态',
    `invoice_type_code`   varchar(10)   null comment '发票类型编码',
    `elec_invoice_no`     varchar(20)   null comment '全电发票号码',
    `invoice_status`      varchar(10)   null comment '发票状态',
    `dparty`              varchar(50)   null comment '开票人',
    `tax_cal_code`        varchar(50)   null comment '税收分类编码',
    `special_policy_sign` int(1)        null comment '特殊政策标识 0：否 1：是',
    `tax_rate_flag`       int(1)        null comment '零税率标识',
    `body`                varchar(1024) null comment '发票表体',
    `unit_code`           varchar(100)  null comment '发票使用单位',
    `pk_dept`             varchar(100)  null comment '部门主键',
    `integration_status`  varchar(2)   DEFAULT '0' COMMENT '集成状态',
    `push_status`         int          DEFAULT NULL COMMENT '推送状态(4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败)',
    `push_msg`            text         DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`          text         DEFAULT NULL COMMENT '返回同步消息',
    `return_status`       varchar(2)   DEFAULT NULL COMMENT '返回同步状态',
    `try_number`          bigint       DEFAULT '0' COMMENT '重试次数',
    `readiness`           bigint       DEFAULT '0' COMMENT '0 未就绪 1 就绪',
    `create_time`         datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_time`         datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `create_by`           varchar(50)  DEFAULT NULL COMMENT '创建人',
    `update_by`           varchar(50)  DEFAULT NULL COMMENT '更新人',
    `del_flag`            bigint       DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='进项发票转换数据';

-- 创建文件信息转换表
CREATE TABLE `fi_convert_file_info`
(
    `id`                 int    NOT NULL AUTO_INCREMENT COMMENT 'id',
    `file_manage_id`     bigint NOT NULL COMMENT 'FileManage系统表主键',
    `org_id`             int    null comment '单位id',
    `relation_id`        int          DEFAULT NULL COMMENT '关联id',
    `extends_id`         int          DEFAULT NULL COMMENT '继承id',
    `image_code`         varchar(50)  DEFAULT NULL COMMENT '影像类型编码',
    `image_name`         varchar(50)  DEFAULT NULL COMMENT '影像类型名称',
    `is_upload`          varchar(2)   DEFAULT '0' COMMENT '是否刚性控制 1-是 0-否',
    `file_name`          varchar(255) DEFAULT NULL COMMENT '文件名',
    `bucket_name`        varchar(255) DEFAULT NULL COMMENT '保存路径',
    `original`           varchar(255) DEFAULT NULL COMMENT '原文件名',
    `file_type`          varchar(255) DEFAULT NULL COMMENT '文件类型',
    `file_size`          varchar(50)  DEFAULT NULL COMMENT '文件大小',
    `upload_type`        varchar(16)  DEFAULT NULL COMMENT '上传方式（0-手动上传 1-扫描录入）',
    `module_type`        varchar(255) DEFAULT NULL COMMENT '模块对应的表',
    `module_id`          int          DEFAULT NULL COMMENT '模块业务id',
    `type`               varchar(10)  DEFAULT '0' COMMENT '模块子类型（0-未知，1-录音，2-图片）',
    `show_name`          varchar(255) DEFAULT NULL COMMENT '显示文件名',
    `business_type`      varchar(255) DEFAULT NULL COMMENT '业务类型',
    `erp_file_check`     varchar(2)   DEFAULT NULL COMMENT '文件一致性校验结果 1-通过 0-不通过',
    `create_by_name`     varchar(50)  DEFAULT NULL COMMENT '创建人姓名',
    `update_by_name`     varchar(50)  DEFAULT NULL COMMENT '最后修改人姓名',
    `source_id`          varchar(40)  DEFAULT NULL COMMENT '来源主键id',
    `source_relation_id` varchar(40)  DEFAULT NULL COMMENT '来源关联id',
    `ocr_type`           varchar(20)  DEFAULT NULL COMMENT 'ocr类型',
    `create_time`        datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_time`        datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `create_by`          varchar(50)  DEFAULT NULL COMMENT '创建人',
    `update_by`          varchar(50)  DEFAULT NULL COMMENT '更新人',
    `del_flag`           bigint       DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='文件信息转换';

create table fi_org_convert
(
    id           bigint auto_increment comment '主键'
        primary key,
    org_id       bigint       null comment '业+组织ID',
    pk_org       varchar(64)  null comment '组织主键',
    org_code     varchar(64)  null comment '组织编码',
    org_name     varchar(255) null comment '组织名称',
    mdm_org_code varchar(64)  null comment 'MDM组织编码',
    init_code    varchar(64)  null comment '组织级参数编码',
    param_value  varchar(255) null comment '组织级参数值'
)
    comment '智慧平台组织转换信息' charset = utf8mb4;

ALTER TABLE fi_org_convert
    ADD CONSTRAINT uk_pk_org_init_code UNIQUE (pk_org, init_code);

-- 创建UDS文件记录表
CREATE TABLE `fi_uds_file_record`
(
    `id`             bigint       NOT NULL AUTO_INCREMENT COMMENT '主键',
    -- OSS相关信息
    `bucket_name`    varchar(255) NOT NULL COMMENT 'OSS存储桶名称',
    `file_name`      varchar(255) NOT NULL COMMENT 'OSS文件名称',
    `original`       varchar(255) NOT NULL COMMENT 'OSS原始文件名',
    `file_type`      varchar(50)  DEFAULT NULL COMMENT '文件类型',
    `file_size`      varchar(50)  DEFAULT NULL COMMENT '文件大小',
    -- UDS相关信息
    `document_id`    varchar(100) DEFAULT NULL COMMENT 'UDS文档ID',
    `version_id`     varchar(100) DEFAULT NULL COMMENT 'UDS版本ID',
    `uds_file_name`  varchar(255) DEFAULT NULL COMMENT 'UDS返回的文件名',
    `meta_type_name` varchar(50)  DEFAULT NULL COMMENT 'UDS元数据类型名称',
    `push_status`    varchar(2)   DEFAULT '0' COMMENT '推送状态(0-未推送 1-推送成功)',
    `push_msg`       text         DEFAULT NULL COMMENT '推送失败信息',
    `push_time`      datetime     DEFAULT NULL COMMENT '推送时间',
    -- 基础字段
    `create_time`    datetime     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`    datetime     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`      varchar(50)  DEFAULT NULL COMMENT '创建人',
    `update_by`      varchar(50)  DEFAULT NULL COMMENT '更新人',
    `del_flag`       char(1)      DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_bucket_file` (`bucket_name`, `file_name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='UDS文件记录';


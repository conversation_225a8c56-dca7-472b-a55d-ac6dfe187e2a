package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.cloud.admin.api.entity.SysDepart;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.ficonsumer.common.FiOrgConvert;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiOrgConvertMapper;
import com.cloud.ficonsumer.service.FiOrgConvertService;
import com.cloud.ficonsumer.utils.AdminUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 智慧平台组织转换信息服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiOrgConvertServiceImpl extends ServiceImpl<FiOrgConvertMapper, FiOrgConvert> implements FiOrgConvertService {

    private final DmMapper dmMapper;

    @Override
    public void syncPushEnableOrg() {
        log.info("开始同步智慧平台组织转换信息");

        // 1. 查询ERP中需要同步智慧平台的组织信息
        List<FiOrgConvert> orgList = dmMapper.listAllPushEnableOrg();
        if (CollectionUtil.isEmpty(orgList)) {
            log.info("未找到需要同步的组织信息");
            return;
        }

        // 2. 提取组织编码集合
        Set<String> orgCodes = orgList.stream()
                .map(FiOrgConvert::getOrgCode)
                .collect(Collectors.toSet());

        // 3. 根据组织编码，获取业+组织
        Map<String, SysDepart> orgCodeMappings = AdminUtils.getDepartByErpOrgCodes(orgCodes);

        // 4. 设置业+组织ID
        for (FiOrgConvert org : orgList) {
            SysDepart sysDepart = orgCodeMappings.get(org.getOrgCode());
            if (Objects.isNull(sysDepart)) {
                log.error("ERP组织编码{}获取对应的业+组织ID失败", org.getOrgCode());
            } else {
                org.setOrgId(sysDepart.getId());
            }
        }

        // 5. 调用事务方法保存数据
        SpringContextHolder.getBean(FiOrgConvertService.class)
                .saveOrgConvertBatch(orgList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveOrgConvertBatch(List<FiOrgConvert> orgList) {
        if (CollectionUtil.isEmpty(orgList)) {
            return;
        }

        // 1. 获取所有pkOrg
        Set<String> pkOrgs = orgList.stream()
                .map(FiOrgConvert::getPkOrg)
                .collect(Collectors.toSet());

        // 2. 查询所有历史记录
        List<FiOrgConvert> existingOrgs = this.list();

        // 3. 将不在本次同步列表中的记录paramValue设置为N
        List<FiOrgConvert> toUpdateOrgs = existingOrgs.stream()
                .filter(org -> !pkOrgs.contains(org.getPkOrg()))
                .peek(org -> org.setParamValue("N"))
                .collect(Collectors.toList());

        // 4. 构建pkOrg到id的映射，用于设置本次同步记录的ID
        Map<String, Long> pkOrgToIdMap = existingOrgs.stream()
                .collect(Collectors.toMap(FiOrgConvert::getPkOrg, FiOrgConvert::getId, (v1, v2) -> v1));

        // 5. 设置已存在记录的ID
        orgList.forEach(org -> {
            Long existingId = pkOrgToIdMap.get(org.getPkOrg());
            if (existingId != null) {
                org.setId(existingId);
            }
        });

        // 6. 先更新被禁用的记录
        if (!toUpdateOrgs.isEmpty()) {
            this.updateBatchById(toUpdateOrgs);
        }

        // 7. 更新或插入本次同步的记录
        this.saveOrUpdateBatch(orgList);
        log.info("成功同步{}条组织转换信息，禁用{}条历史记录", orgList.size(), toUpdateOrgs.size());
    }

    @Override
    public Set<Long> listEnablePushOrgId(Set<Long> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return new HashSet<>();
        }

        // 调用Mapper查询数据库获取启用了同步的组织信息
        List<FiOrgConvert> fiOrgConverts = this.list(Wrappers.<FiOrgConvert>lambdaQuery().in(FiOrgConvert::getOrgId, orgIds));

        // 过滤出paramValue为"Y"的组织ID
        return fiOrgConverts.stream()
                .filter(org -> "Y".equals(org.getParamValue()))
                .map(FiOrgConvert::getOrgId)
                .collect(Collectors.toSet());
    }

    @Override
    public Boolean orgEnablePush(Long orgId) {
        List<FiOrgConvert> fiOrgConverts = this.list(Wrappers.<FiOrgConvert>lambdaQuery()
                .eq(FiOrgConvert::getOrgId, orgId));
        if (CollectionUtil.isEmpty(fiOrgConverts)) {
            return Boolean.FALSE;
        }

        return "Y".equals(fiOrgConverts.get(0).getParamValue());
    }

    @Override
    public String getMdmOrgCodeByOrgId(Long orgId) {
        List<FiOrgConvert> fiOrgConverts = this.list(Wrappers.<FiOrgConvert>lambdaQuery()
                .eq(FiOrgConvert::getOrgId, orgId));
        if (CollectionUtil.isEmpty(fiOrgConverts)) {
            return null;
        }

        return fiOrgConverts.get(0).getMdmOrgCode();
    }
}
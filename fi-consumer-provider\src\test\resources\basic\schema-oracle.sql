create table BD_SUPPLIER
(
    BUSLICENSENUM VARCHAR2 (200),
    CODE VARCHAR2 (40) not null,
    CORCUSTOMER VARCHAR2 (20) default '~',
    CORPADDRESS VARCHAR2 (20) default '~',
    CREATIONTIME       CHAR(19),
    CREATOR VARCHAR2 (20) default '~',
    DATAORIGINFLAG NUMBER (38) default 0,
    DEF1 VARCHAR2 (101),
    DEF10 VARCHAR2 (101),
    DEF11 VARCHAR2 (101),
    DEF12 VARCHAR2 (101),
    DEF13 VARCHAR2 (101),
    DEF14 VARCHAR2 (101),
    DEF15 VARCHAR2 (101),
    DEF16 VARCHAR2 (101),
    DEF17 VARCHAR2 (101),
    DEF18 VARCHAR2 (101),
    DEF19 VARCHAR2 (101),
    DEF2 VARCHAR2 (101),
    DEF20 VARCHAR2 (101),
    DEF21 VARCHAR2 (101),
    DEF22 VARCHAR2 (101),
    DEF23 VARCHAR2 (101),
    DEF24 VARCHAR2 (101),
    DEF25 VARCHAR2 (101),
    DEF26 VARCHAR2 (101),
    DEF27 VARCHAR2 (101),
    DEF28 VARCHAR2 (101),
    DEF29 VARCHAR2 (101),
    DEF3 VARCHAR2 (101),
    DEF30 VARCHAR2 (101),
    DEF4 VARCHAR2 (101),
    DEF5 VARCHAR2 (101),
    DEF6 VARCHAR2 (101),
    DEF7 VARCHAR2 (101),
    DEF8 VARCHAR2 (101),
    DEF9 VARCHAR2 (101),
    DELETESTATE NUMBER (38),
    DELPERSON VARCHAR2 (20) default '~',
    DELTIME            CHAR(19),
    DR NUMBER (10) default 0,
    ECOTYPESINCEVFIVE VARCHAR2 (20) default '~',
    EMAIL VARCHAR2 (50),
    ENABLESTATE NUMBER (38) default 2,
    ENAME VARCHAR2 (200),
    ESTABLISHDATE      CHAR(19),
    FAX1 VARCHAR2 (50),
    FAX2 VARCHAR2 (50),
    ISCARRIER          CHAR     default 'N',
    ISCUSTOMER         CHAR     default 'N',
    ISFREECUST         CHAR     default 'N',
    ISMOBILECOOPERTIVE CHAR     default 'N',
    ISOUTCHECK         CHAR     default 'N',
    ISVAT              CHAR     default 'N',
    LEGALBODY VARCHAR2 (200),
    MEMO VARCHAR2 (1536),
    MNECODE VARCHAR2 (50),
    MODIFIEDTIME       CHAR(19),
    MODIFIER VARCHAR2 (20) default '~',
    NAME VARCHAR2 (300) not null,
    NAME2 VARCHAR2 (300),
    NAME3 VARCHAR2 (300),
    NAME4 VARCHAR2 (300),
    NAME5 VARCHAR2 (300),
    NAME6 VARCHAR2 (300),
    PK_AREACL VARCHAR2 (20) default '~',
    PK_BILLTYPECODE VARCHAR2 (20) default '~',
    PK_COUNTRY VARCHAR2 (20) default '~',
    PK_CURRTYPE VARCHAR2 (20) default '~',
    PK_FINANCEORG VARCHAR2 (20) default '~',
    PK_FORMAT VARCHAR2 (20) default '~',
    PK_GROUP           CHAR(20)             not null,
    PK_OLDSUPPLIER     CHAR(20) default '~' not null,
    PK_ORG             CHAR(20)             not null,
    PK_SUPPLIER        CHAR(20)             not null constraint PK_BD_SUPPLIER
            primary key,
    PK_SUPPLIER_MAIN VARCHAR2 (20) default '~',
    PK_SUPPLIER_PF     CHAR(20),
    PK_SUPPLIERCLASS   CHAR(20)             not null,
    PK_SUPTAXES VARCHAR2 (20),
    PK_TIMEZONE VARCHAR2 (20) default '~',
    REGISTERFUND NUMBER (14, 2),
    SHORTNAME VARCHAR2 (300),
    SUPPROP NUMBER (38) default 0 not null,
    SUPSTATE NUMBER (38) default 1,
    TAXPAYERID VARCHAR2 (20),
    TEL1 VARCHAR2 (50),
    TEL2 VARCHAR2 (50),
    TEL3 VARCHAR2 (50),
    TRADE VARCHAR2 (20) default '~',
    TS                 CHAR(19) default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    URL VARCHAR2 (60),
    VATCODE VARCHAR2 (50),
    ZIPCODE            CHAR(6)
);

create table BD_PROJECT
(
    ACTU_FINISH_DATE  CHAR(19),
    ACTU_START_DATE   CHAR(19),
    ACTUDURATION NUMBER (38),
    BEGIN_FLAG        CHAR,
    BILL_TYPE VARCHAR2 (4),
    CHECK_DATE        CHAR(19),
    CREATIONSTATE VARCHAR2 (20) default '~',
    CREATIONTIME      CHAR(19),
    CREATOR VARCHAR2 (20) default '~',
    DEF1 VARCHAR2 (101),
    DEF10 VARCHAR2 (101),
    DEF11 VARCHAR2 (101),
    DEF12 VARCHAR2 (101),
    DEF13 VARCHAR2 (101),
    DEF14 VARCHAR2 (101),
    DEF15 VARCHAR2 (101),
    DEF16 VARCHAR2 (101),
    DEF17 VARCHAR2 (101),
    DEF18 VARCHAR2 (101),
    DEF19 VARCHAR2 (101),
    DEF2 VARCHAR2 (101),
    DEF20 VARCHAR2 (101),
    DEF21 VARCHAR2 (101),
    DEF22 VARCHAR2 (101),
    DEF23 VARCHAR2 (101),
    DEF24 VARCHAR2 (101),
    DEF25 VARCHAR2 (101),
    DEF26 VARCHAR2 (101),
    DEF27 VARCHAR2 (101),
    DEF28 VARCHAR2 (101),
    DEF29 VARCHAR2 (101),
    DEF3 VARCHAR2 (101),
    DEF30 VARCHAR2 (101),
    DEF31 VARCHAR2 (101),
    DEF32 VARCHAR2 (101),
    DEF33 VARCHAR2 (101),
    DEF34 VARCHAR2 (101),
    DEF35 VARCHAR2 (101),
    DEF36 VARCHAR2 (101),
    DEF37 VARCHAR2 (101),
    DEF38 VARCHAR2 (101),
    DEF39 VARCHAR2 (101),
    DEF4 VARCHAR2 (101),
    DEF40 VARCHAR2 (101),
    DEF41 VARCHAR2 (101),
    DEF42 VARCHAR2 (101),
    DEF43 VARCHAR2 (101),
    DEF44 VARCHAR2 (101),
    DEF45 VARCHAR2 (101),
    DEF46 VARCHAR2 (101),
    DEF47 VARCHAR2 (101),
    DEF48 VARCHAR2 (101),
    DEF49 VARCHAR2 (101),
    DEF5 VARCHAR2 (101),
    DEF50 VARCHAR2 (101),
    DEF6 VARCHAR2 (101),
    DEF7 VARCHAR2 (101),
    DEF8 VARCHAR2 (101),
    DEF9 VARCHAR2 (101),
    DELETESTATE NUMBER (38),
    DELPERSON VARCHAR2 (20) default '~',
    DELTIME           CHAR(19),
    DR NUMBER (10) default 0,
    ENABLESTATE NUMBER (38),
    ESTIMATE_GLOBAL NUMBER (28, 8),
    ESTIMATE_GROUP NUMBER (28, 8),
    ESTIMATE_MNY NUMBER (28, 8),
    FINISH_WORK_DATE  CHAR(19),
    GENERAL_GLOBAL NUMBER (28, 8),
    GENERAL_GROUP NUMBER (28, 8),
    GENERAL_MNY NUMBER (28, 8),
    HDEF100 VARCHAR2 (101),
    HDEF51 VARCHAR2 (101),
    HDEF52 VARCHAR2 (101),
    HDEF53 VARCHAR2 (101),
    HDEF54 VARCHAR2 (101),
    HDEF55 VARCHAR2 (101),
    HDEF56 VARCHAR2 (101),
    HDEF57 VARCHAR2 (101),
    HDEF58 VARCHAR2 (101),
    HDEF59 VARCHAR2 (101),
    HDEF60 VARCHAR2 (101),
    HDEF61 VARCHAR2 (101),
    HDEF62 VARCHAR2 (101),
    HDEF63 VARCHAR2 (101),
    HDEF64 VARCHAR2 (101),
    HDEF65 VARCHAR2 (101),
    HDEF66 VARCHAR2 (101),
    HDEF67 VARCHAR2 (101),
    HDEF68 VARCHAR2 (101),
    HDEF69 VARCHAR2 (101),
    HDEF70 VARCHAR2 (101),
    HDEF71 VARCHAR2 (101),
    HDEF72 VARCHAR2 (101),
    HDEF73 VARCHAR2 (101),
    HDEF74 VARCHAR2 (101),
    HDEF75 VARCHAR2 (101),
    HDEF76 VARCHAR2 (101),
    HDEF77 VARCHAR2 (101),
    HDEF78 VARCHAR2 (101),
    HDEF79 VARCHAR2 (101),
    HDEF80 VARCHAR2 (101),
    HDEF81 VARCHAR2 (101),
    HDEF82 VARCHAR2 (101),
    HDEF83 VARCHAR2 (101),
    HDEF84 VARCHAR2 (101),
    HDEF85 VARCHAR2 (101),
    HDEF86 VARCHAR2 (101),
    HDEF87 VARCHAR2 (101),
    HDEF88 VARCHAR2 (101),
    HDEF89 VARCHAR2 (101),
    HDEF90 VARCHAR2 (101),
    HDEF91 VARCHAR2 (101),
    HDEF92 VARCHAR2 (101),
    HDEF93 VARCHAR2 (101),
    HDEF94 VARCHAR2 (101),
    HDEF95 VARCHAR2 (101),
    HDEF96 VARCHAR2 (101),
    HDEF97 VARCHAR2 (101),
    HDEF98 VARCHAR2 (101),
    HDEF99 VARCHAR2 (101),
    MEMO VARCHAR2 (3072),
    MODIFIEDTIME      CHAR(19),
    MODIFIER VARCHAR2 (20) default '~',
    ORDER_FINISH_DATE CHAR(19),
    ORDER_START_DATE  CHAR(19),
    ORDERDURATION NUMBER (38),
    ORDERMETHOD NUMBER (38),
    PK_BUSITYPE VARCHAR2 (20) default '~',
    PK_CURRTYPE VARCHAR2 (20) default '~',
    PK_DUTIER VARCHAR2 (20) default '~',
    PK_DUTY_DEPT VARCHAR2 (20) default '~',
    PK_DUTY_DEPT_V VARCHAR2 (20) default '~',
    PK_DUTY_ORG VARCHAR2 (20) default '~',
    PK_DUTY_ORG_V VARCHAR2 (20) default '~',
    PK_EPS VARCHAR2 (20) default '~',
    PK_GROUP VARCHAR2 (20) default '~',
    PK_ORG VARCHAR2 (20) default '~',
    PK_ORG_V VARCHAR2 (20) default '~',
    PK_PARENTPRO VARCHAR2 (20) default '~',
    PK_PARENTTASK VARCHAR2 (20) default '~',
    PK_PLANMAKER VARCHAR2 (20) default '~',
    PK_PROJECT        CHAR(20) not null constraint PK_BD_PROJECT
            primary key,
    PK_PROJECTCLASS VARCHAR2 (20) default '~',
    PK_PROJECTSTATE VARCHAR2 (20) default '~',
    PK_TRANSITYPE VARCHAR2 (20) default '~',
    PK_WBSTEMPLATE VARCHAR2 (20) default '~',
    PK_WORKCALENDAR VARCHAR2 (20) default '~',
    PK_YEARPLAN_B VARCHAR2 (20),
    PLAN_AUTH NUMBER (38),
    PLAN_FINISH_DATE  CHAR(19),
    PLAN_START_DATE   CHAR(19),
    PLANDURATION NUMBER (38),
    PLANMODEL NUMBER (38),
    PLANPRIORITY NUMBER (38),
    PROJECT_CODE VARCHAR2 (255),
    PROJECT_NAME VARCHAR2 (500),
    PROJECT_NAME2 VARCHAR2 (200),
    PROJECT_NAME3 VARCHAR2 (200),
    PROJECT_NAME4 VARCHAR2 (200),
    PROJECT_NAME5 VARCHAR2 (200),
    PROJECT_NAME6 VARCHAR2 (200),
    PROJECT_OT_NAME VARCHAR2 (500),
    PROJECT_SH_NAME VARCHAR2 (80),
    REQ_FINISH_DATE   CHAR(19),
    REQ_START_DATE    CHAR(19),
    REQDURATION NUMBER (38),
    SRC_BILL_CODE VARCHAR2 (40),
    SRC_BILL_TYPE VARCHAR2 (4),
    SRC_PK_BILL VARCHAR2 (20),
    SRC_PK_TRANSITYPE VARCHAR2 (20) default '~',
    SRC_TRANSI_TYPE VARCHAR2 (30),
    START_WORK_DATE   CHAR(19),
    STATUS_DATE       CHAR(19),
    TAX_FLAG          CHAR,
    TRANSI_TYPE VARCHAR2 (30),
    TS                CHAR(19) default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    UPLOAD_FLAG       CHAR     default 'N',
    LATITUDE NUMBER (28, 8),
    LONGITUDE NUMBER (28, 8),
    POSITION VARCHAR2 (200),
    PMC_FLAG          CHAR,
    POINT_FLAG        CHAR,
    PK_PROJECTLIB VARCHAR2 (101) default '~',
    PRE_REPEAT_DATE   CHAR(19),
    REPEAT_WORK_DATE  CHAR(19),
    STOP_WORK_DATE    CHAR(19),
    STOP_WORK_DAYS VARCHAR2 (50),
    PK_PARENTORG_NEW VARCHAR2 (101),
    PROJECT_VISIBILITY NUMBER (1),
    PK_PARENTPRO_NEW VARCHAR2 (101),
    T_CREATIONSTATE VARCHAR2 (101)
);

create table BD_UDSRESULT
(
    PK_UDSRESULT VARCHAR2(40)  not null,
    PK_BILL      VARCHAR2(101) not null,
    PK_BILLTYPE  VARCHAR2(101),
    DOCUMENT_ID  VARCHAR2(101),
    VERSION_ID   VARCHAR2(101),
    FILE_NAME    VARCHAR2(101),
    DR           NUMBER(10),
    DEF1         VARCHAR2(101),
    DEF2         VARCHAR2(101),
    DEF3         VARCHAR2(101),
    DEF4         VARCHAR2(101),
    DEF5         VARCHAR2(101),
    DEF6         VARCHAR2(101),
    DEF7         VARCHAR2(101),
    DEF8         VARCHAR2(101),
    DEF9         VARCHAR2(101),
    DEF10        VARCHAR2(101),
    TS           CHAR(19) default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    PK_DOC       VARCHAR2(100)
);

create table PUB_SYSINIT
(
    CONTROLFLAG    CHAR         not null,
    DATAORIGINFLAG NUMBER(38),
    DR             NUMBER(10) default 0,
    EDITFLAG       CHAR         not null,
    INITCODE       VARCHAR2(50),
    INITNAME       VARCHAR2(768),
    MODIFIEDTIME   CHAR(19),
    MODIFIER       VARCHAR2(20),
    PK_ORG         VARCHAR2(20),
    PK_SYSINIT     CHAR(20)     not null
        constraint PK_PUB_SYSINIT
            primary key,
    SYSINIT        VARCHAR2(20) not null,
    TS             CHAR(19)   default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    `VALUE`          VARCHAR2(512)
);

create table PM_EPS
(
    CREATIONTIME CHAR(19),
    CREATOR      VARCHAR2(20) default '~',
    DR           NUMBER(10)   default 0,
    ENABLESTATE  NUMBER(38),
    EPS_CODE     VARCHAR2(40),
    EPS_LEVEL    NUMBER(38),
    EPS_NAME     VARCHAR2(120),
    EPS_NAME2    VARCHAR2(120),
    EPS_NAME3    VARCHAR2(120),
    EPS_NAME4    VARCHAR2(120),
    EPS_NAME5    VARCHAR2(120),
    EPS_NAME6    VARCHAR2(120),
    INNERCODE    VARCHAR2(40),
    MODIFIEDTIME CHAR(19),
    MODIFIER     VARCHAR2(20) default '~',
    PK_EPS       CHAR(20) not null
        constraint PK_PM_EPS
            primary key,
    PK_GROUP     VARCHAR2(20) default '~',
    PK_ORG       VARCHAR2(50) default '~',
    PK_PARENT    VARCHAR2(20) default '~',
    TS           CHAR(19)     default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    UPLOAD_FLAG  CHAR
);
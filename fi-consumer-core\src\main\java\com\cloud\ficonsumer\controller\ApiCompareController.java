

package com.cloud.ficonsumer.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.util.R;
import com.cloud.cloud.common.data.dict.annotation.DictTrans;
import com.cloud.ficonsumer.entity.ApiCompare;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.vo.ApiCompareVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 对照表
 *
 * <AUTHOR> code generator
 * @date 2024-07-12 10:14:10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/apicompare" )
@Api(value = "apicompare", tags = "对照表管理")
public class ApiCompareController {

    private final ApiCompareService apiCompareService;

    /**
     * 分页查询
     * @param page 分页对象
     * @param apiCompare 对照表
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/getApiComparePage" )
    @DictTrans
    //@PreAuthorize("@pms.hasPermission('generator_apicompare_view')" )
    public R<Page<ApiCompare>> getApiComparePage(Page page, ApiCompareVO apiCompare) {
        return R.ok(apiCompareService.getApiComparePage(page, apiCompare));
    }



    /**
     * 新增对照表
     * @param apiCompare 对照表
     * @return R
     */
    @ApiOperation(value = "新增对照表", notes = "新增对照表")
    @PostMapping("/saveApiCompare")
    //@PreAuthorize("@pms.hasPermission('generator_apicompare_add')" )
    public R<Boolean> save(@RequestBody ApiCompareVO apiCompare) {
        return R.ok(apiCompareService.saveApiCompare(apiCompare));
    }

    /**
     * 修改对照表
     * @param apiCompare 对照表
     * @return R
     */
    @ApiOperation(value = "修改对照表", notes = "修改对照表")
    @PostMapping("/editApiCompare")
    //@PreAuthorize("@pms.hasPermission('generator_apicompare_edit')" )
    public R<Boolean> updateById(@RequestBody ApiCompareVO apiCompare) {
        return R.ok(apiCompareService.editApiCompare(apiCompare));
    }

    /**
     * 通过id删除对照表
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除对照表", notes = "通过id删除对照表")
    @GetMapping("/delById/{id}" )
    //@PreAuthorize("@pms.hasPermission('generator_apicompare_del')" )
    public R<Boolean> delById(@PathVariable Long id) {
        return R.ok(apiCompareService.delById(id));
    }

    /**
     * 通过id批量删除对照表
     * @return R
     */
    @ApiOperation(value = "通过id批量删除对照表", notes = "通过id批量删除对照表")
    @GetMapping("/delBatchById" )
    public R<Boolean> delBatchById(@RequestParam(value = "ids") List<Long> ids) {
        return R.ok(apiCompareService.delBatchById(ids));
    }

}

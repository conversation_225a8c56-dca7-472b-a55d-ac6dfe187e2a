package com.cloud.ficonsumer;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.cloud.cloud.common.feign.annotation.EnableCloudFeignClients;
import com.cloud.cloud.common.security.annotation.EnableCloudResourceServer;
import com.cloud.cloud.common.swagger.annotation.EnableCloudSwagger2;
import com.cloud.common.eventbus.annotation.EnableEventBus;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableCloudSwagger2
@EnableDiscoveryClient
@SpringBootApplication(exclude = DruidDataSourceAutoConfigure.class)
@EnableCloudFeignClients(basePackages = "com.cloud")
@EnableCloudResourceServer
//事件监听
@EnableEventBus
public class FiConsumerApplication {

    public static void main(String[] args) {
        SpringApplication.run(FiConsumerApplication.class, args);
    }
}

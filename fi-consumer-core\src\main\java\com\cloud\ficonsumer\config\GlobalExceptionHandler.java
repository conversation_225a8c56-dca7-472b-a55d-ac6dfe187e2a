package com.cloud.ficonsumer.config;

import cn.hutool.core.util.StrUtil;
import com.cloud.cloud.common.core.util.R;
import com.cloud.common.idempotent.exception.IdempotentException;
import com.cloud.ficonsumer.exception.BusinessException;
import com.mysql.cj.jdbc.exceptions.MySQLTransactionRollbackException;
import io.seata.core.context.RootContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    /**
     * 全局异常.
     * @param e the e
     * @return R
     */
    @ExceptionHandler(Exception.class)
    public R handleGlobalException(HttpServletRequest request,
                                   HttpServletResponse response, Exception e) {
        log.error("全局异常信息 ex={}", e.getMessage(), e);
        if(StrUtil.isNotBlank(RootContext.getXID())){
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
        if(e instanceof IdempotentException){
            return R.failed("重复请求");
        } else if (e instanceof DuplicateKeyException) {
            String errorMsg = e.getMessage();
            errorMsg = String.format("存在重复数据【%s】，请确认!", errorMsg.substring(errorMsg.lastIndexOf("Duplicate"), errorMsg.lastIndexOf("for")));
            return R.failed(errorMsg);
        } else if (e instanceof MySQLTransactionRollbackException) {
            String errorMsg = "请稍后再试";
            return R.failed(errorMsg);
        }
        return R.failed(e.getLocalizedMessage());
    }

    @ExceptionHandler(BusinessException.class)
    public R handleBusinessException(HttpServletResponse response, BusinessException e) {
        log.error("业务异常信息 ex={}", e.getMessage(), e);
        if(StrUtil.isNotBlank(RootContext.getXID())){
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        }
        if(e.getCode()!=0){
            return R.failed(e.getMessage()).setCode(e.getCode());
        }
        return R.failed(e.getMessage());
    }

}

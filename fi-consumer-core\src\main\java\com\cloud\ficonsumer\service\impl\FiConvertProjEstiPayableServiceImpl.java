package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertProjEstiPayable;
import com.cloud.ficonsumer.mapper.FiConvertProjEstiPayableMapper;
import com.cloud.ficonsumer.service.FiConvertProjEstiPayableService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiConvertProjEstiPayableServiceImpl extends ServiceImpl<FiConvertProjEstiPayableMapper, FiConvertProjEstiPayable> implements FiConvertProjEstiPayableService {

}

create table AP_ESTIPAYABLEBILL
(
    ACCESSORYNUM       NUMBER(38)    default 0,
    APPROVEDATE        CHAR(19),
    APPROVER           VARCHAR2(20)  default '~',
    APPROVESTATUS      NUMBER(38)    default -1 not null,
    BILLCLASS          VARCHAR2(2)              not null,
    BILLDATE           CHAR(19)                 not null,
    B<PERSON>LMAKER          CHAR(20)                 not null,
    B<PERSON><PERSON>N<PERSON>             VARCHAR2(40),
    BILLPERIOD         VARCHAR2(2),
    B<PERSON><PERSON>TATUS         NUMBER(38)               not null,
    BILLYEAR           VARCHAR2(4),
    CONFIRMUSER        VARCHAR2(20)  default '~',
    CREATIONTIME       CHAR(19)                 not null,
    CREATOR            CHAR(20)                 not null,
    CUS<PERSON><PERSON><PERSON>GA<PERSON>       CHAR(20),
    DEF1               VARCHAR2(101),
    DEF10              VARCHAR2(101),
    DEF11              VARCHAR2(101),
    DEF12              VARCHAR2(101),
    DEF13              VARCHAR2(101),
    DEF14              VARCHAR2(101),
    DEF15              VARCHAR2(101),
    <PERSON><PERSON><PERSON>              VARCHAR2(101),
    DEF17              VARCHAR2(101),
    <PERSON>F18              VARCHAR2(101),
    DEF19              VARCHAR2(101),
    DEF2               VARCHAR2(101),
    DEF20              VARCHAR2(101),
    DEF21              VARCHAR2(101),
    DEF22              VARCHAR2(101),
    DEF23              VARCHAR2(101),
    DEF24              VARCHAR2(101),
    DEF25              VARCHAR2(101),
    DEF26              VARCHAR2(101),
    DEF27              VARCHAR2(101),
    DEF28              VARCHAR2(101),
    DEF29              VARCHAR2(101),
    DEF3               VARCHAR2(101),
    DEF30              VARCHAR2(101),
    DEF4               VARCHAR2(101),
    DEF5               VARCHAR2(101),
    DEF6               VARCHAR2(101),
    DEF7               VARCHAR2(101),
    DEF8               VARCHAR2(101),
    DEF9               VARCHAR2(101),
    DR                 NUMBER(10)    default 0,
    EFFECTDATE         CHAR(19),
    EFFECTSTATUS       NUMBER(38),
    EFFECTUSER         VARCHAR2(20)  default '~',
    ESTFLAG            NUMBER(38),
    GLOBALLOCAL        NUMBER(28, 8) default 1,
    GROUPLOCAL         NUMBER(28, 8) default 1,
    ISFLOWBILL         CHAR,
    ISINIT             CHAR,
    ISMANDATEPAY       CHAR,
    ISREDED            CHAR,
    LASTADJUSTUSER     VARCHAR2(20)  default '~',
    LASTAPPROVEID      VARCHAR2(20)  default '~',
    LOCAL_MONEY        NUMBER(28, 8),
    MODIFIEDTIME       CHAR(19),
    MODIFIER           VARCHAR2(20)  default '~',
    MONEY              NUMBER(28, 8),
    OFFICIALPRINTDATE  CHAR(19),
    OFFICIALPRINTUSER  VARCHAR2(20)  default '~',
    OUTBUSITYPE        CHAR(20),
    PK_BILLTYPE        VARCHAR2(20)             not null,
    PK_BUSITYPE        VARCHAR2(20)  default '~',
    PK_CORP            CHAR(20),
    PK_CURRTYPE        VARCHAR2(20)  default '~',
    PK_ESTIPAYABLEBILL CHAR(20)                 not null
        constraint PK_ESTIPAYABLEBILL
            primary key,
    PK_FIORG           VARCHAR2(20)  default '~',
    PK_FIORG_V         VARCHAR2(20)  default '~',
    PK_GROUP           CHAR(20)                 not null,
    PK_ORG             CHAR(20)                 not null,
    PK_ORG_V           VARCHAR2(20)  default '~',
    PK_PCORG           VARCHAR2(20),
    PK_PCORG_V         VARCHAR2(20)  default '~',
    PK_TRADETYPE       VARCHAR2(20)             not null,
    PK_TRADETYPEID     VARCHAR2(20)  default '~',
    RECECOUNTRYID      VARCHAR2(20),
    SCOMMENT           VARCHAR2(250),
    SETT_ORG           VARCHAR2(20)  default '~',
    SETT_ORG_V         VARCHAR2(20)  default '~',
    SIGNDATE           CHAR(19),
    SIGNPERIOD         VARCHAR2(2),
    SIGNUSER           VARCHAR2(20)  default '~',
    SIGNYEAR           VARCHAR2(4),
    SRC_SYSCODE        NUMBER(38),
    SUBJCODE           VARCHAR2(20)  default '~',
    SYSCODE            NUMBER(38)               not null,
    TAXCOUNTRYID       VARCHAR2(20),
    TS                 CHAR(19)      default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    SAGA_BTXID         VARCHAR2(64),
    SAGA_FROZEN        NUMBER(38)    default 0  not null,
    SAGA_GTXID         VARCHAR2(64),
    SAGA_STATUS        NUMBER(38)    default 0  not null
);

create table AP_ESTIPAYABLEITEM
(
    ASSETPACTNO        VARCHAR2(50),
    BANKROLLPROJET     VARCHAR2(20)  default '~',
    BILLCLASS          VARCHAR2(2)  not null,
    BILLDATE           CHAR(19)     not null,
    BILLNO             VARCHAR2(40),
    BUSIDATE           CHAR(19),
    BUYSELLFLAG        NUMBER(38),
    CALTAXMNY          NUMBER(28, 8),
    CASHITEM           VARCHAR2(20)  default '~',
    CHECKDIRECTION     VARCHAR2(10),
    CHECKELEMENT       VARCHAR2(20)  default '~',
    CHECKNO            VARCHAR2(50),
    CHECKTYPE          VARCHAR2(20)  default '~',
    CKDID              CHAR(20),
    CONTRACTNO         VARCHAR2(40),
    COORDFLAG          NUMBER(38),
    COSTCENTER         VARCHAR2(20),
    DDHID              CHAR(20),
    DEALNO             VARCHAR2(20),
    DEF1               VARCHAR2(101),
    DEF10              VARCHAR2(101),
    DEF11              VARCHAR2(101),
    DEF12              VARCHAR2(101),
    DEF13              VARCHAR2(101),
    DEF14              VARCHAR2(101),
    DEF15              VARCHAR2(101),
    DEF16              VARCHAR2(101),
    DEF17              VARCHAR2(101),
    DEF18              VARCHAR2(101),
    DEF19              VARCHAR2(101),
    DEF2               VARCHAR2(101),
    DEF20              VARCHAR2(101),
    DEF21              VARCHAR2(101),
    DEF22              VARCHAR2(101),
    DEF23              VARCHAR2(101),
    DEF24              VARCHAR2(101),
    DEF25              VARCHAR2(101),
    DEF26              VARCHAR2(101),
    DEF27              VARCHAR2(101),
    DEF28              VARCHAR2(101),
    DEF29              VARCHAR2(101),
    DEF3               VARCHAR2(101),
    DEF30              VARCHAR2(101),
    DEF4               VARCHAR2(101),
    DEF5               VARCHAR2(101),
    DEF6               VARCHAR2(101),
    DEF7               VARCHAR2(101),
    DEF8               VARCHAR2(101),
    DEF9               VARCHAR2(101),
    DIRECTION          NUMBER(38)   not null,
    DR                 NUMBER(10)    default 0,
    EQUIPMENTCODE      VARCHAR2(50),
    FACARD             VARCHAR2(50),
    FPHID              CHAR(20),
    FREECUST           VARCHAR2(20)  default '~',
    GLOBALBALANCE      NUMBER(28, 8) default 0,
    GLOBALCREBIT       NUMBER(28, 8) default 0,
    GLOBALNOTAX_CRE    NUMBER(28, 8) default 0,
    GLOBALRATE         NUMBER(15, 8) default 1,
    GLOBALTAX_CRE      NUMBER(28, 8) default 0,
    GROUPBALANCE       NUMBER(28, 8) default 0,
    GROUPCREBIT        NUMBER(28, 8) default 0,
    GROUPNOTAX_CRE     NUMBER(28, 8) default 0,
    GROUPRATE          NUMBER(15, 8) default 1,
    GROUPTAX_CRE       NUMBER(28, 8) default 0,
    INNERORDERNO       VARCHAR2(40),
    INVOICENO          VARCHAR2(140),
    LOCAL_MONEY_BAL    NUMBER(28, 8) default 0,
    LOCAL_MONEY_CR     NUMBER(28, 8) default 0,
    LOCAL_NOTAX_CR     NUMBER(28, 8) default 0,
    LOCAL_TAX_CR       NUMBER(28, 8) default 0,
    MATERIAL           VARCHAR2(20)  default '~',
    MATERIAL_SRC       VARCHAR2(20),
    MONEY_BAL          NUMBER(28, 8) default 0,
    MONEY_CR           NUMBER(28, 8) default 0,
    NOSUBTAX           NUMBER(28, 8),
    NOSUBTAXRATE       NUMBER(28, 8),
    NOTAX_CR           NUMBER(28, 8) default 0,
    OBJTYPE            NUMBER(38),
    OPPTAXFLAG         CHAR,
    ORDERCUBASDOC      VARCHAR2(20)  default '~',
    OUTSTORENO         VARCHAR2(40),
    PAUSETRANSACT      CHAR,
    PAYACCOUNT         VARCHAR2(20)  default '~',
    PK_BALATYPE        VARCHAR2(20)  default '~',
    PK_BILLTYPE        VARCHAR2(20) not null,
    PK_CURRTYPE        VARCHAR2(20)  default '~',
    PK_DEPTID          VARCHAR2(20)  default '~',
    PK_DEPTID_V        VARCHAR2(20)  default '~',
    PK_ESTIPAYABLEBILL CHAR(20),
    PK_ESTIPAYABLEITEM CHAR(20)     not null
        constraint PK_ESTIPAYABLEITEM
            primary key,
    PK_FIORG           VARCHAR2(20)  default '~',
    PK_FIORG_V         VARCHAR2(20)  default '~',
    PK_GROUP           CHAR(20)     not null,
    PK_ORG             CHAR(20)     not null,
    PK_ORG_V           VARCHAR2(20)  default '~',
    PK_PAYTERM         VARCHAR2(20)  default '~',
    PK_PCORG           VARCHAR2(20),
    PK_PCORG_V         VARCHAR2(20)  default '~',
    PK_PSNDOC          VARCHAR2(20)  default '~',
    PK_SSITEM          CHAR(20),
    PK_SUBJCODE        VARCHAR2(20)  default '~',
    PK_TRADETYPE       VARCHAR2(20),
    PK_TRADETYPEID     VARCHAR2(20) not null,
    POSTPRICE          NUMBER(28, 8),
    POSTPRICENOTAX     NUMBER(28, 8),
    POSTQUANTITY       NUMBER(20, 8),
    POSTUNIT           VARCHAR2(40),
    PRICE              NUMBER(28, 8) default 0,
    PRODUCTLINE        CHAR(20),
    PROJECT            CHAR(20),
    PROJECT_TASK       VARCHAR2(20),
    PU_DEPTID          VARCHAR2(20)  default '~',
    PU_DEPTID_V        VARCHAR2(20)  default '~',
    PU_ORG             VARCHAR2(20)  default '~',
    PU_ORG_V           CHAR(20),
    PU_PSNDOC          VARCHAR2(20)  default '~',
    PURCHASEORDER      VARCHAR2(40),
    QUANTITY_BAL       NUMBER(20, 8) default 0,
    QUANTITY_CR        NUMBER(20, 8) default 0,
    RATE               NUMBER(15, 8) default 1,
    RECACCOUNT         VARCHAR2(20)  default '~',
    ROWNO              NUMBER(38),
    ROWTYPE            NUMBER(38),
    SCOMMENT           VARCHAR2(250),
    SENDCOUNTRYID      VARCHAR2(20),
    SETT_ORG           VARCHAR2(20)  default '~',
    SETT_ORG_V         VARCHAR2(20)  default '~',
    SRC_BILLID         CHAR(20),
    SRC_BILLTYPE       VARCHAR2(20),
    SRC_ITEMID         CHAR(20),
    SRC_TRADETYPE      VARCHAR2(20),
    SUBJCODE           VARCHAR2(20)  default '~',
    SUPPLIER           VARCHAR2(20)  default '~',
    TAXCODEID          VARCHAR2(20),
    TAXNUM             VARCHAR2(30)  default '0',
    TAXPRICE           NUMBER(28, 8) default 0,
    TAXRATE            NUMBER(9, 6)  default 0,
    TAXTYPE            NUMBER(38),
    TOP_BILLID         CHAR(20),
    TOP_BILLTYPE       VARCHAR2(20),
    TOP_ITEMID         CHAR(20),
    TOP_TRADETYPE      VARCHAR2(20),
    TS                 CHAR(19)      default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VATCODE            VARCHAR2(50),
    VENDORVATCODE      VARCHAR2(50)
);
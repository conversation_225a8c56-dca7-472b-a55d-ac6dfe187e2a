package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillDetailQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillQueryDTO;
import com.cloud.ficonsumer.entity.FiSourcePuPaybill;
import com.cloud.ficonsumer.vo.FiConvertPuPaybillDetailVO;
import com.cloud.ficonsumer.vo.FiConvertPuPaybillVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourcePuPaybillDetailVO;
import com.cloud.ficonsumer.vo.FiSourcePuPaybillVO;

import java.util.List;
import java.util.Map;

public interface FiSourcePuPaybillService extends IService<FiSourcePuPaybill> {

    boolean push(String pkPaybill);

    Page<FiSourcePuPaybillVO> beforeConvertDataPage(Page<FiSourcePuPaybillVO> page, FiSourcePuPaybillQueryDTO queryDTO);

    Page<FiSourcePuPaybillDetailVO> getBeforeConvertDataPage(Page<FiSourcePuPaybillDetailVO> page, FiSourcePuPaybillDetailQueryDTO queryDTO);

    List<FiConvertPuPaybillVO> getConvertList(String pk);

    Page<FiConvertPuPaybillDetailVO> getConvertData(Page<FiConvertPuPaybillDetailVO> page, String pk);

    /**
     * 根据来源单据ID列表查询未记账的付款单
     * key为采购订单主键(src_billid)，value为对应的付款单
     *
     * @param queryDTO 查询参数
     * @return Map<String, FiSourcePuPaybillVO> 采购订单主键 -> 付款单的映射
     */
    Map<String, FiSourcePuPaybillDetailVO> getNoAccountedBySrcBillIds(FiSourcePuPaybillQueryDTO queryDTO);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}

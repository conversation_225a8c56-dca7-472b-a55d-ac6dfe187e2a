package com.cloud.ficonsumer.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourcePuPaybillDetail;
import com.cloud.ficonsumer.mapper.FiSourcePuPaybillDetailMapper;
import com.cloud.ficonsumer.service.FiSourcePuPaybillDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourcePuPaybillDetailServiceImpl extends ServiceImpl<FiSourcePuPaybillDetailMapper, FiSourcePuPaybillDetail> implements FiSourcePuPaybillDetailService {

}

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "销售订单源数据详情")
@TableName("fi_source_sale_order")
public class FiSourceSaleOrder extends BaseEntity<FiSourceSaleOrder> {

    @TableId
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "销售订单主键")
    private String csaleorderid;

    @ApiModelProperty(value = "组织版本主键")
    private String pk_org_v;

    @ApiModelProperty(value = "交易类型ID")
    private String ctrantypeid;

    @ApiModelProperty(value = "单据日期")
    private Date dbilldate;

    @ApiModelProperty(value = "单据号")
    private String vbillcode;

    @ApiModelProperty(value = "客户ID")
    private String ccustomerid;

    @ApiModelProperty(value = "业务员ID")
    private String cemployeeid;

    @ApiModelProperty(value = "部门版本ID")
    private String cdeptvid;

    @ApiModelProperty(value = "合同管理ID")
    private String cctmanageid;

    @ApiModelProperty(value = "备注")
    private String vnote;

    @ApiModelProperty(value = "单据状态")
    private String fstatusflag;

    @ApiModelProperty(value = "自定义项9, 备用字段")
    private String vbdef9;

    @ApiModelProperty(value = "同步状态")
    private String vdef20;

    @ApiModelProperty(value = "删除状态,0未删除,1已删除")
    private Integer dr;

    @ApiModelProperty(value = "时间戳")
    private Date ts;

    @ApiModelProperty(value = "集成状态")
    private String integrationStatus;

    @ApiModelProperty(value = "4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败")
    private Integer pushStatus;

    @ApiModelProperty(value = "推送成功失败信息")
    private String pushMsg;

    @ApiModelProperty(value = "返回同步消息")
    private String returnMsg;

    @ApiModelProperty(value = "返回同步状态")
    private String returnStatus;

    @ApiModelProperty(value = "重试次数")
    private Long tryNumber;

    @ApiModelProperty(value = "0 未就绪 1 就绪")
    private Long readiness;
}
package com.cloud.ficonsumer.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillQueryDTO;
import com.cloud.ficonsumer.dto.FiSourceStoreDTO;
import com.cloud.ficonsumer.entity.FiSourcePuPaybill;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourcePuPaybillService;
import com.cloud.ficonsumer.service.FiSourceStoreService;
import com.cloud.ficonsumer.vo.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class FiSourcePuPaybillExportListener extends AbstractExportListener<FiSourcePuPaybillExportVO> {



    private final FiSourcePuPaybillService fiSourcePuPaybillService;
    private final FiSourcePuPaybillQueryDTO queryDTO;
    private final Page<FiSourcePuPaybillVO> page;
    private static final int PAGE_SIZE = 1000;

    public FiSourcePuPaybillExportListener(UUID id,
                                           FiSourcePuPaybillService fiSourcePuPaybillService,
                                           Page<FiSourcePuPaybillVO> page,
                                           FiSourcePuPaybillQueryDTO queryDTO) {
        super(id, "采购应付转换前数据");
        this.page = page;
        this.fiSourcePuPaybillService = fiSourcePuPaybillService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiSourcePuPaybillExportVO> doHandleData() {
        List<FiSourcePuPaybillVO> vos = new ArrayList<>();

        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiSourcePuPaybillVO> pageResult = fiSourcePuPaybillService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiSourcePuPaybillVO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiSourcePuPaybillVO> pageResult;
            do {
                pageResult = fiSourcePuPaybillService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }

        List<FiSourcePuPaybillExportVO> result = BeanUtil.copyToList(vos, FiSourcePuPaybillExportVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}
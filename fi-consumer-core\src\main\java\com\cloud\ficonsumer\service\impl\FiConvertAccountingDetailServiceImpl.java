
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertAccountingDetail;
import com.cloud.ficonsumer.mapper.FiConvertAccountingDetailMapper;
import com.cloud.ficonsumer.service.FiConvertAccountingDetailService;
import org.springframework.stereotype.Service;

/**
 * 采购入库详情数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:46
 */
@Service
public class FiConvertAccountingDetailServiceImpl extends ServiceImpl<FiConvertAccountingDetailMapper, FiConvertAccountingDetail> implements FiConvertAccountingDetailService {

}

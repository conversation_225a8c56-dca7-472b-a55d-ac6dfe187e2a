package com.cloud.ficonsumer.utils;

import cn.com.hyit.finmp.sdk.FinMPSDK;
import cn.com.hyit.finmp.sdk.InvokeResp;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.apiexchange.entity.ApiInfoHis;
import com.cloud.cloud.common.core.constant.SecurityConstants;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.cloud.common.data.tenant.TenantContextHolder;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.integration.dto.Param;
import com.cloud.ficonsumer.integration.feign.RemoteApiExchange;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.vo.YgResultMain;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2025/1/17.
 */
@Slf4j
@UtilityClass
public class YgUtil {

    /**
     * 调用远光接口
     *
     */
    public String sendToYg(ApiExchange apiExchange, String request) {
        ApiInfoHis apiMsg = new ApiInfoHis();
        apiMsg.setRequestId(IdUtil.simpleUUID());
        apiMsg.setCode(apiExchange.getCode());
        Param<JSONObject> param = Param.addData(apiExchange.getCode(), apiExchange.getAccessKey(), JSONObject.parseObject(request));
        apiMsg.setRequest(JSON.toJSONString(param));
        apiMsg.setIsCallback(0);
        apiMsg.setServiceName(apiExchange.getServiceName());
        apiMsg.setStatus(0);
        apiMsg.setUrl(apiExchange.getUrl());
        apiMsg.setTenantId(TenantContextHolder.getTenantId());
        apiMsg.setTransactionId(UUID.fastUUID().toString());
        String result = "";
        try {
            log.info("yg入参为-->>>>>  " + request);
            YgConfig ygConfig = SpringContextHolder.getBean(YgConfig.class);
            String gatewayHost = ygConfig.getGatewayHost();
            String signUri = apiExchange.getUrl();
            String apiName = "";
            String apiNameNoParams = "";
            int lastIndex = signUri.lastIndexOf('/');
            int lastParamIndex = signUri.lastIndexOf('?');
            if (lastIndex != -1) {
                apiName = signUri.substring(lastIndex + 1);
                int lastSuffixIndex = apiName.lastIndexOf('?');
                if(lastSuffixIndex != -1) {
                    apiNameNoParams = apiName.substring(0,lastSuffixIndex);
                }
            }
            if (lastParamIndex != -1) {
                signUri = signUri.substring(0,lastParamIndex);
            }
            String apiVersion = "V1";
            String url = apiExchange.getServiceName() + "/V1/"+apiName;
            log.info("yg参数为-->>>>>url:" + url + " gatewayHost:" + gatewayHost +" signUri:" + signUri + " ak:" + ygConfig.getAk()+ " sk:" + ygConfig.getSk());
            log.info("yg参数为-->>>>>request:" + request);
            FinMPSDK sdk = FinMPSDK.builder(ygConfig.getAk(), ygConfig.getSk(), gatewayHost).setSignUri(signUri).build();
            JSONObject jsonObject = JSONObject.parseObject(request);
            if(jsonObject.get("list")!=null) {
                request = jsonObject.get("list").toString();
            }
            InvokeResp invokeResp = sdk.invoke("POST", url, apiNameNoParams, apiVersion, request, new HashMap<String, String>());
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.writeValueAsString(invokeResp);
            log.info("yg出参为-->>>>>  " + result);
            apiMsg.setResponse(result);
        }catch (Exception e){
            LogUtil.info(log, "调用远光失败:", e);
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            apiMsg.setResponse(pushMsg);
            apiMsg.setStatus(3);
            YgResultMain ygResultMain = new YgResultMain();
            ygResultMain.setHttpStatusCode(500);
            ygResultMain.setRawBody(pushMsg);
            result = JSONObject.toJSONString(ygResultMain);
        }
        ApiCompareService apiCompareService = SpringContextHolder.getApplicationContext().getBean(ApiCompareService.class);
        apiCompareService.saveApiInfoHis(apiMsg);

        YgResultMain mainResult = JSONObject.parseObject(result,YgResultMain.class);
        if(mainResult.getHttpStatusCode().equals(200)) {
            result = mainResult.getRawBody();
        }else {
            throw new CheckedException(result);
        }
        return result;
    }

    /**
     * 调用远光接口
     *
     */
    public String sendTurnExchangeYg(ApiExchange apiExchange, Param param) throws Exception {
        RemoteApiExchange remoteApiExchange = SpringContextHolder.getApplicationContext().getBean(RemoteApiExchange.class);
        String result = remoteApiExchange.exchange(param, SecurityConstants.FROM_IN);
        log.info("yg出参为-->>>>>  " + result);
        return result;
    }

    public String toStringTrim(String s){
        if (StringUtils.isNotBlank(s)){
            return s.trim();
        }else {
            return null;
        }
    }

}

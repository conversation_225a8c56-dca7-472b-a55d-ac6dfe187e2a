create table IA_I2BILL
(
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>   CHAR,
    BESTI<PERSON><PERSON><PERSON><PERSON>  CHAR,
    BILLMAKER      VARCHAR2(20) default '~',
    BSYSTEMFLAG    CHAR,
    CACCOUNTPERIOD CHAR(7),
    CB<PERSON><PERSON><PERSON>        CHAR(20) not null
        constraint PK_IA_I2BILL
            primary key,
    CBIZTYPEID     VARCHAR2(20),
    CDEPTID        VARCHAR2(20) default '~',
    CDEPTVID       VARCHAR2(20) default '~',
    CPSNID         VARCHAR2(20) default '~',
    CREATIONTIME   CHAR(19),
    CREATOR        VARCHAR2(20) default '~',
    CSRCMODULECODE VARCHAR2(10),
    CSTOCKORGID    VARCHAR2(20) default '~',
    CSTOCKORGVID   VARCHAR2(20) default '~',
    CSTORDOCID     VARCHAR2(20) default '~',
    CSTORDOCMANID  VARCHAR2(20) default '~',
    CTRANTYPEID    VARCHAR2(20),
    C<PERSON>NDORID      VARCHAR2(20) default '~',
    DBILLDATE      CHAR(19),
    DMAKEDATE      CHAR(19),
    DR             NUMBER(10)   default 0,
    IPRINTCOUNT    NUMBER(38),
    MODIFIEDTIME   CHAR(19),
    MODIFIER       VARCHAR2(20) default '~',
    PK_BOOK        VARCHAR2(20) default '~',
    PK_GROUP       VARCHAR2(20) default '~',
    PK_ORG         VARCHAR2(20) default '~',
    TS             CHAR(19)     default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VBILLCODE      VARCHAR2(40),
    VDEF1          VARCHAR2(101),
    VDEF10         VARCHAR2(101),
    VDEF11         VARCHAR2(101),
    VDEF12         VARCHAR2(101),
    VDEF13         VARCHAR2(101),
    VDEF14         VARCHAR2(101),
    VDEF15         VARCHAR2(101),
    VDEF16         VARCHAR2(101),
    VDEF17         VARCHAR2(101),
    VDEF18         VARCHAR2(101),
    VDEF19         VARCHAR2(101),
    VDEF2          VARCHAR2(101),
    VDEF20         VARCHAR2(101),
    VDEF3          VARCHAR2(101),
    VDEF4          VARCHAR2(101),
    VDEF5          VARCHAR2(101),
    VDEF6          VARCHAR2(101),
    VDEF7          VARCHAR2(101),
    VDEF8          VARCHAR2(101),
    VDEF9          VARCHAR2(101),
    VNOTE          VARCHAR2(181)
);

create table IA_I2BILL_B
(
    APPROVER           VARCHAR2(20) default '~',
    BLARGESSFLAG       CHAR,
    CACCOUNTPERIOD     CHAR(7),
    CADJUSTBILLID      VARCHAR2(20) default '~',
    CADJUSTBILLITEMID  VARCHAR2(20) default '~',
    CASSCUSTID         VARCHAR2(20),
    CASTUNITID         VARCHAR2(20) default '~',
    CBILL_BID          CHAR(20) not null
        constraint PK_IA_I2BILL_B
            primary key,
    CBILLID            CHAR(20),
    CCALCID            VARCHAR2(20) default '~',
    CCALCTHREADID      VARCHAR2(20) default '~',
    CCURRENCYID        VARCHAR2(20) default '~',
    CFFILEID           VARCHAR2(20),
    CFIRSTBID          VARCHAR2(20) default '~',
    CFIRSTID           VARCHAR2(20) default '~',
    CICITEMID          VARCHAR2(20) default '~',
    CINVENTORYID       VARCHAR2(20) default '~',
    CINVENTORYVID      VARCHAR2(20) default '~',
    CPRJTASKID         VARCHAR2(20),
    CPRODUCTORID       VARCHAR2(20) default '~',
    CPROFITCENTERID    VARCHAR2(20) default '~',
    CPROFITCENTERVID   VARCHAR2(20) default '~',
    CPROJECTID         VARCHAR2(20) default '~',
    CROWNO             VARCHAR2(20),
    CSRCBID            VARCHAR2(20) default '~',
    CSRCID             VARCHAR2(20) default '~',
    CUNITID            VARCHAR2(20) default '~',
    DACCOUNTDATE       CHAR(19),
    DBILLDATE          CHAR(19),
    DBIZDATE           CHAR(19),
    DR                 NUMBER(10)   default 0,
    FCALCBIZFLAG       NUMBER(38),
    FCALCTHREADBIZFLAG NUMBER(38),
    NADJUSTNUM         NUMBER(28, 8),
    NASTNUM            NUMBER(28, 8),
    NFACTOR1           NUMBER(28, 8),
    NFACTOR2           NUMBER(28, 8),
    NFACTOR3           NUMBER(28, 8),
    NFACTOR4           NUMBER(28, 8),
    NFACTOR5           NUMBER(28, 8),
    NFACTOR6           NUMBER(28, 8),
    NFACTOR7           NUMBER(28, 8),
    NFACTOR8           NUMBER(28, 8),
    NGLOBALEXCHGRATE   NUMBER(28, 8),
    NGLOBALMNY         NUMBER(28, 8),
    NGROUPEXCHGRATE    NUMBER(28, 8),
    NGROUPMNY          NUMBER(28, 8),
    NMNY               NUMBER(28, 8),
    NNUM               NUMBER(28, 8),
    NPLANEDMNY         NUMBER(28, 8),
    NPLANEDPRICE       NUMBER(28, 8),
    NPRICE             NUMBER(28, 8),
    NREASONALWASTMNY   NUMBER(28, 8),
    NREASONALWASTNUM   NUMBER(28, 8),
    NREASONALWASTPRICE NUMBER(28, 8),
    NREDBACKNUM        NUMBER(28, 8),
    NVARYMNY           NUMBER(28, 8),
    PK_BOOK            VARCHAR2(20) default '~',
    PK_GROUP           VARCHAR2(20) default '~',
    PK_ORG             VARCHAR2(20) default '~',
    TAUDITTIME         VARCHAR2(19),
    TS                 CHAR(19)     default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VBATCHCODE         VARCHAR2(40),
    VBDEF1             VARCHAR2(101),
    VBDEF10            VARCHAR2(101),
    VBDEF11            VARCHAR2(101),
    VBDEF12            VARCHAR2(101),
    VBDEF13            VARCHAR2(101),
    VBDEF14            VARCHAR2(101),
    VBDEF15            VARCHAR2(101),
    VBDEF16            VARCHAR2(101),
    VBDEF17            VARCHAR2(101),
    VBDEF18            VARCHAR2(101),
    VBDEF19            VARCHAR2(101),
    VBDEF2             VARCHAR2(101),
    VBDEF20            VARCHAR2(101),
    VBDEF3             VARCHAR2(101),
    VBDEF4             VARCHAR2(101),
    VBDEF5             VARCHAR2(101),
    VBDEF6             VARCHAR2(101),
    VBDEF7             VARCHAR2(101),
    VBDEF8             VARCHAR2(800),
    VBDEF9             VARCHAR2(101),
    VCHANGERATE        VARCHAR2(60),
    VFIRSTCODE         VARCHAR2(40),
    VFIRSTROWNO        VARCHAR2(20),
    VFIRSTTRANTYPE     VARCHAR2(20) default '~',
    VFIRSTTYPE         VARCHAR2(20) default '~',
    VFREE1             VARCHAR2(101),
    VFREE10            VARCHAR2(101),
    VFREE2             VARCHAR2(101),
    VFREE3             VARCHAR2(101),
    VFREE4             VARCHAR2(101),
    VFREE5             VARCHAR2(101),
    VFREE6             VARCHAR2(101),
    VFREE7             VARCHAR2(101),
    VFREE8             VARCHAR2(101),
    VFREE9             VARCHAR2(101),
    VSRCCODE           VARCHAR2(40),
    VSRCROWNO          VARCHAR2(20),
    VSRCTRANTYPE       VARCHAR2(20) default '~',
    VSRCTYPE           VARCHAR2(20) default '~'
)


package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiInvoicePostingDTO;
import com.cloud.ficonsumer.dto.FiInvoicePostingItemDTO;
import com.cloud.ficonsumer.entity.FiInvoicePosting;
import com.cloud.ficonsumer.vo.FiInvoicePostingQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 采购应付源数据详情
 *
 * <AUTHOR>
 * @date 2025-04-22 10:04:33
 */
@Mapper
public interface FiInvoicePostingMapper extends CloudBaseMapper<FiInvoicePosting> {

    @Select("<script>SELECT t.* " +
            " FROM fi_invoice_posting t " +
            " WHERE t.del_flag = 0" +
            "<if test=\"param.pkOrgList != null and param.pkOrgList.size() > 0\"> " +
            "   AND pk_org in " +
            "   <foreach collection=\"param.pkOrgList\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "      #{item} " +
            "   </foreach>" +
            " </if> " +
            "<if test=\"param.groupCodeFlag !=null and param.groupCodeFlag !=''\"> " +
            " and pk_org in (select pk_org " +
            "       from fi_org_compare " +
            "       where group_code = #{param.groupCodeFlag} and del_flag =0) " +
            "</if> " +
            "<if test=\"param.busibillno !=null and param.busibillno !=''\"> " +
            " and busibillno like CONCAT('%',#{param.busibillno},'%') " +
            "</if> " +
            "<if test=\"param.pushStatus !=null and param.pushStatus !=''\"> " +
            " and push_status = #{param.pushStatus} " +
            "</if> " +
            "<if test=\"param.pushStatusList != null and param.pushStatusList.size() > 0\">"+
            " and push_status in " +
            "<foreach collection=\"param.pushStatusList\" item=\"item\" open=\"(\" separator=\",\" close=\")\">"+
            "#{item}" +
            "</foreach>" +
            "</if> " +
            "<if test=\"param.createTimeStart !=null and param.createTimeStart !=''\"> " +
            " and create_time >= #{param.createTimeStart} " +
            "</if> " +
            "<if test=\"param.createTimeEnd !=null and param.createTimeEnd !=''\"> " +
            " and #{param.createTimeEnd} >= create_time " +
            "</if> " +
            " order by create_time desc  " +
            "</script>")
    Page<FiInvoicePostingDTO> beforeConvertDataPage(Page page, @Param("param") FiInvoicePostingQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_invoice_posting t " +
            "    LEFT JOIN fi_invoice_posting_item td ON t.busibillno = td.busibillno " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.busibillno = #{param.busibillno}" +
            "</script>")
    Page<FiInvoicePostingItemDTO> getBeforeConvertDataPage(Page page, @Param("param") FiInvoicePostingQueryVO queryVO);
}

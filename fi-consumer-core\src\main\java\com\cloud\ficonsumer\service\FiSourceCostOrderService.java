

package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiConvertCostOrderDTO;
import com.cloud.ficonsumer.dto.FiConvertCostOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceCostOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceCostOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceCostOrder;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceCostOrderQueryVO;

import java.util.List;

/**
 * 虚拟采购订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:00
 */
public interface FiSourceCostOrderService extends IService<FiSourceCostOrder> {

    boolean pushAgain(String s);

    Page<FiSourceCostOrderDTO> beforeConvertDataPage(Page page, FiSourceCostOrderQueryVO queryVO);

    Page<FiSourceCostOrderDetailDTO> getBeforeConvertDataPage(Page page, FiSourceCostOrderQueryVO queryVO);

    List<FiConvertCostOrderDTO> getConvertList(String pk);

    Page<FiConvertCostOrderDetailDTO> getConvertData(Page page, String pk);

    /**
     * 校验费用结算单同步状态
     *
     * @param pkFeebalance 费用结算单主键
     */
    void checkSync(String pkFeebalance);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}



package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.*;
import com.cloud.ficonsumer.entity.FiSourceAccounting;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceAccountingService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceAccountingQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 凭证记账数据源数据主表管理
 *
 * <AUTHOR>
 * @date 2025-04-11 15:14:21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/accoungting" )
@Api(value = "accoungting", tags = "凭证记账数据源数据主表管理")
public class FiSourceAccountingController {

    private final FiSourceAccountingService fiSourceAccountingService;

    /**
     * 凭证记账数据重新推送
     *
     * @return
     */
    @ApiOperation(value = "会计凭证重新推送", notes = "会计凭证重新推送")
    @GetMapping("/pushAgain/{pkVoucher}")
    public R<Boolean> pushAgain(@PathVariable("pkVoucher") String pkVoucher) {
        return R.ok(fiSourceAccountingService.pushAgain(pkVoucher));
    }


    /**
     * 订单转换前分页查询
     *
     * @return
     */
    @ApiOperation(value = "订单转换前分页查询", notes = "订单转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiSourceAccountingDTO>> beforeConvertNewDataPage(Page page, FiSourceAccountingQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        return R.ok(fiSourceAccountingService.beforeConvertDataPage(page,queryVO));
    }

    /**
     * 主体表转换后单条字段
     * @return
     */
    @ApiOperation(value = "主体表转换后单条字段", notes = "主体表转换后单条字段")
    @GetMapping("/getConvertList" )
    public R<List<FiConvertAccountingDTO>> getConvertNewList(String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceAccountingService.getConvertList(pk));
    }

    /**
     * 转换前数据
     * @return
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage" )
    public R<Page<FiSourceAccountingDetailDTO>> getBeforeConvertNewDataPage(Page page, FiSourceAccountingQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getPkVoucher(), "主键不能为空");
        return R.ok(fiSourceAccountingService.getBeforeConvertDataPage(page,queryVO));
    }
    /**
     * 获取转换后详情分页数据
     * @return
     */
    @ApiOperation(value = "获取转换后详情分页数据", notes = "获取转换后详情分页数据")
    @GetMapping("/getConvertData" )
    public R<Page<FiConvertAccountingDetailDTO>> getNewConvertData(Page page, String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceAccountingService.getConvertData(page,pk));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result = fiSourceAccountingService.update(Wrappers.<FiSourceAccounting>lambdaUpdate()
                .set(FiSourceAccounting::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiSourceAccounting::getPushMsg, "成功")
                .in(FiSourceAccounting::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiSourceAccountingService.pushAgainBatch(queryVO));
    }
}

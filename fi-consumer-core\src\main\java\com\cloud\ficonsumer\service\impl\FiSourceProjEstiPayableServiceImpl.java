package com.cloud.ficonsumer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiSourceProjEstiPayableDetailQueryDTO;
import com.cloud.ficonsumer.dto.FiSourceProjEstiPayableQueryDTO;
import com.cloud.ficonsumer.entity.FiConvertProjEstiPayable;
import com.cloud.ficonsumer.entity.FiConvertProjEstiPayableDetail;
import com.cloud.ficonsumer.entity.FiSourceAccounting;
import com.cloud.ficonsumer.entity.FiSourceProjEstiPayable;
import com.cloud.ficonsumer.entity.FiSourceProjEstiPayableDetail;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceProjEstiPayableMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourceProjEstiPayableServiceImpl extends ServiceImpl<FiSourceProjEstiPayableMapper, FiSourceProjEstiPayable>
        implements FiSourceProjEstiPayableService {

    private final FiSourceProjEstiPayableDetailService detailService;
    private final FiConvertProjEstiPayableService convertService;
    private final FiConvertProjEstiPayableDetailService convertDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;
    private final YgConfig ygConfig;

    @Override
    public boolean push(String pkPayablebill) {
        FiSourceProjEstiPayable source = this.getOne(Wrappers.<FiSourceProjEstiPayable>lambdaQuery()
                .eq(FiSourceProjEstiPayable::getPkEstipayablebill, pkPayablebill));
        List<FiSourceProjEstiPayableDetail> sourceDetails = detailService.list(Wrappers.<FiSourceProjEstiPayableDetail>lambdaQuery()
                .eq(FiSourceProjEstiPayableDetail::getPkEstipayablebill, pkPayablebill));

        // 1. 转换数据
        try {
            convertData(source, sourceDetails);
        } catch (Exception e) {
            handleConversionException(source, e);
        }

        // 2. 推送数据到智慧平台
        try {
            pushToYgPlatform(source);
            updatePushSuccess(source);
        } catch (Exception e) {
            handlePushException(source, e);
        }

        return true;
    }

    @Override
    public Page<FiSourceProjEstiPayableVO> beforeConvertDataPage(Page<FiSourceProjEstiPayableVO> page, FiSourceProjEstiPayableQueryDTO queryDTO) {
        return baseMapper.beforeConvertDataPage(page, queryDTO);
    }

    @Override
    public Page<FiSourceProjEstiPayableDetailVO> getBeforeConvertDataPage(Page<FiSourceProjEstiPayableDetailVO> page, FiSourceProjEstiPayableDetailQueryDTO queryDTO) {
        return baseMapper.getBeforeConvertDataPage(page, queryDTO);
    }

    @Override
    public List<FiConvertProjEstiPayableVO> getConvertList(String pkEstipayablebill) {
        return baseMapper.getConvertList(pkEstipayablebill);
    }

    @Override
    public Page<FiConvertProjEstiPayableDetailVO> getConvertData(Page<FiConvertProjEstiPayableDetailVO> page, String pkEstipayablebill) {
        return baseMapper.getConvertData(page, pkEstipayablebill);
    }

    /**
     * 更新主单推送成功的状态，并保存成功日志
     */
    private void updatePushSuccess(FiSourceProjEstiPayable source) {
        source.setIntegrationStatus("0");
        source.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
        source.setPushMsg("");
        source.setReadiness(1L);
        this.updateById(source);

        // 保存成功日志
        insertPlatformLog(source, Constants.SUCCESS, "【项目暂估应付接收服务】集成财务中台成功");
    }

    /**
     * 记录转换异常，更新状态，并抛出 CheckedException
     */
    private void handleConversionException(FiSourceProjEstiPayable source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        // 保存转换失败日志
        insertPlatformLog(source, Constants.FAIL, pushMsg);

        LogUtil.info(log, "项目暂估应付集成信息转换失败:", e);
        throw new CheckedException("项目暂估应付集成信息转换失败:" + pushMsg);
    }

    /**
     * 记录推送异常，更新状态，并抛出 CheckedException
     */
    private void handlePushException(FiSourceProjEstiPayable source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        // 保存推送失败日志
        insertPlatformLog(source, Constants.FAIL, pushMsg);

        LogUtil.info(log, "项目暂估应付集成信息推送失败:", e);
        throw new CheckedException("项目暂估应付集成信息推送失败:" + pushMsg);
    }

    /**
     * 插入平台日志
     */
    private void insertPlatformLog(FiSourceProjEstiPayable source, String code, String msg) {
        PlatFormLogVO platFormLogVO = new PlatFormLogVO();
        platFormLogVO.setPkLog(UUID.randomUUID().toString());
        platFormLogVO.setPkBill(source.getPkEstipayablebill());
        platFormLogVO.setPkBillType(BillTypeEnum.PROJ_ESTI_PAYABLE.getCode());
        platFormLogVO.setCode(code);
        platFormLogVO.setMsg(msg);
        dmMapper.insertPlatFormLog(platFormLogVO);
    }

    /**
     * 推送数据至智慧平台
     */
    private void pushToYgPlatform(FiSourceProjEstiPayable source) throws Exception {
        if (!ygConfig.getEnable()) {
            return;
        }

        FiConvertProjEstiPayable convert = convertService.getOne(
                Wrappers.<FiConvertProjEstiPayable>lambdaQuery()
                        .eq(FiConvertProjEstiPayable::getPkEstipayablebill, source.getPkEstipayablebill()));
        List<FiConvertProjEstiPayableDetail> details = convertDetailService.list(
                Wrappers.<FiConvertProjEstiPayableDetail>lambdaQuery()
                        .eq(FiConvertProjEstiPayableDetail::getPkEstipayablebill, source.getPkEstipayablebill()));

        YgReimbursementVO ygPayableVO = createYgReimbursementVO(convert, details);
        ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000012.getServCode());

        String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(ygPayableVO));
        if (StringUtils.isEmpty(result)) {
            throw new CheckedException("响应失败，响应为空。");
        }

        YgResult ygResult = JSONObject.parseObject(result, YgResult.class);
        if (!Constants.SUCCESS.equals(ygResult.getCode())) {
            throw new CheckedException("推送失败，响应结果: " + JSON.toJSONString(ygResult));
        }
    }

    /**
     * 转换数据
     */
    private void convertData(FiSourceProjEstiPayable sourceMainData, List<FiSourceProjEstiPayableDetail> details) throws Exception {
        String pkPayablebill = sourceMainData.getPkEstipayablebill();
        String billType = BillTypeEnum.PROJ_ESTI_PAYABLE.getCode();

        // 转换主信息
        FiConvertProjEstiPayable fiConvert = new FiConvertProjEstiPayable();
        apiCompareCache.convertData(sourceMainData, fiConvert, billType);

        // 如若已存在转换数据，则使用已有主键并清空老明细数据
        FiConvertProjEstiPayable existing = convertService.getOne(
                Wrappers.<FiConvertProjEstiPayable>lambdaQuery()
                        .eq(FiConvertProjEstiPayable::getPkEstipayablebill, pkPayablebill));
        if (existing != null) {
            fiConvert.setId(existing.getId());
            convertDetailService.remove(
                    Wrappers.<FiConvertProjEstiPayableDetail>lambdaQuery()
                            .eq(FiConvertProjEstiPayableDetail::getPkEstipayablebill, pkPayablebill));
        }

        // 转换明细数据
        List<FiConvertProjEstiPayableDetail> convertDetailList = new ArrayList<>();
        for (FiSourceProjEstiPayableDetail sourceDetail : details) {
            FiConvertProjEstiPayableDetail convertDetail = new FiConvertProjEstiPayableDetail();
            apiCompareCache.convertData(sourceDetail, convertDetail, billType);
            convertDetailList.add(convertDetail);
        }

        convertService.saveOrUpdate(fiConvert);
        convertDetailService.saveOrUpdateBatch(convertDetailList);
    }

    /**
     * 创建智慧平台请求VO
     */
    private YgReimbursementVO createYgReimbursementVO(FiConvertProjEstiPayable convert, List<FiConvertProjEstiPayableDetail> details) {
        YgReimbursementVO reimbursementVO = new YgReimbursementVO();
        reimbursementVO.setDwdh(convert.getPkOrgV());
        reimbursementVO.setHdlgNm(convert.getBillmaker());
        reimbursementVO.setClaimant(details.get(0).getPuPsndoc());
        reimbursementVO.setSettleObj(details.get(0).getSupplier());
        reimbursementVO.setTProject(details.get(0).getProject());
        reimbursementVO.setAmount(String.valueOf(convert.getLocalMoney()));
        reimbursementVO.setBeDept(details.get(0).getPkDeptid());
        reimbursementVO.setPrjClassifId(details.get(0).getProject());

        List<InvoiceVO> generbillistatInvoice = new ArrayList<>();
        List<PayDetVO> generbillistatPayDet = new ArrayList<>();
        List<YgReimbursementDetailVO> generbillistatCostDet = new ArrayList<>();
        for (FiConvertProjEstiPayableDetail detail : details) {
            // 发票信息
            InvoiceVO invoiceVO = new InvoiceVO();
            invoiceVO.setInvoiceNo(detail.getInvoiceno());
            generbillistatInvoice.add(invoiceVO);

            // 付款信息
            PayDetVO payDetVO = new PayDetVO();
            payDetVO.setPayAmo(detail.getLocalMoneyCr());

            // 费用明细信息
            YgReimbursementDetailVO detailVO = new YgReimbursementDetailVO();
            detailVO.setAmount(detail.getLocalMoneyCr());
            detailVO.setAmtExTaxCy(detail.getLocalNotaxCr());
            detailVO.setTProject(detail.getProject());
            generbillistatCostDet.add(detailVO);
        }

        reimbursementVO.setGenerbillistatInvoice(generbillistatInvoice);
        reimbursementVO.setGenerbillistatPayDet(generbillistatPayDet);
        reimbursementVO.setGenerbillistatCostDet(generbillistatCostDet);

        return reimbursementVO;
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourceProjEstiPayable entity = this.getById(id);
            try {
                this.push(entity.getPkEstipayablebill());
            } catch (Exception e) {
                resultList.add(entity.getPkEstipayablebill()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}
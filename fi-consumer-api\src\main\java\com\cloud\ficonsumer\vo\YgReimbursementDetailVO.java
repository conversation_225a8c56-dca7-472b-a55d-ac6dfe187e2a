package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/24.
 */
@Data
public class YgReimbursementDetailVO {

    /**
     * 金额
     */
    @ApiModelProperty(value="金额")
    private BigDecimal amount;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value="不含税金额")
    private BigDecimal amtExTax;

    /**
     * 本位币不含税金额
     */
    @ApiModelProperty(value="本位币不含税金额")
    private BigDecimal amtExTaxCy;

    /**
     * 本次应付金额
     */
    @ApiModelProperty(value="本次应付金额")
    private BigDecimal amtThisNeedPay;

    /**
     * 本次应付金额本位币
     */
    @ApiModelProperty(value="本次应付金额本位币")
    private BigDecimal amtThisNeedPayCy;

    /**
     * 本次付款金额
     */
    @ApiModelProperty(value="本次付款金额")
    private BigDecimal amtThisPay;

    /**
     * 本次付款金额本位币
     */
    @ApiModelProperty(value="本次付款金额本位币")
    private BigDecimal amtThisPayCy;

    /**
     * 业务活动
     */
    @ApiModelProperty(value="业务活动")
    private BigDecimal biAct;

    /**
     * 凭证核算费用类型
     */
    @ApiModelProperty(value="凭证核算费用类型")
    private String billCosType;

    /**
     * 现金流预算科目
     */
    @ApiModelProperty(value="现金流预算科目")
    private String cfBudSubId;

    /**
     * 核销金额
     */
    @ApiModelProperty(value="核销金额")
    private BigDecimal chrgOffAmt;

    /**
     * 本位币核销金额
     */
    @ApiModelProperty(value="本位币核销金额")
    private BigDecimal chrgOffAmtCy;

    /**
     * 报销人
     */
    @ApiModelProperty(value="报销人")
    private String claimant;

    /**
     * 成本调整标识
     */
    @ApiModelProperty(value="成本调整标识")
    private String costAdjustMark;

    /**
     * 成本中心
     */
    @ApiModelProperty(value="成本中心")
    private String costCenter;

    /**
     * 成本中心所属单位
     */
    @ApiModelProperty(value="成本中心所属单位")
    private String costUnit;

    /**
     * 费用类型
     */
    @ApiModelProperty(value="费用类型")
    private String cosType;

    /**
     * 费用类型编码
     */
    @ApiModelProperty(value="费用类型编码")
    private String cosTypeCode;

    /**
     * 费用大类
     */
    @ApiModelProperty(value="费用大类")
    private String cstCat;

    /**
     * 费用大类编码
     */
    @ApiModelProperty(value="费用大类编码")
    private String cstCatCode;

    /**
     * 所属部门
     */
    @ApiModelProperty(value="所属部门")
    private String dept;

    /**
     * 部门分类
     */
    @ApiModelProperty(value="部门分类")
    private String deptType;

    /**
     * 部门分类编码
     */
    @ApiModelProperty(value="部门分类编码")
    private String deptTypeCode;

    /**
     * 明细费用类型
     */
    @ApiModelProperty(value="明细费用类型")
    private String detCosType;

    /**
     * 明细费用类型编码
     */
    @ApiModelProperty(value="明细费用类型编码")
    private String detCosTypeCode;

    /**
     * 明细税项
     */
    @ApiModelProperty(value="明细税项")
    private BigDecimal detTaxItem;

    /**
     * 职工姓名
     */
    @ApiModelProperty(value="职工姓名")
    private String empNam;

    /**
     * 汇率
     */
    @ApiModelProperty(value="汇率")
    private BigDecimal exchRat;

    /**
     * 内部订单编码
     */
    @ApiModelProperty(value="内部订单编码")
    private String inteOrdCode;

    /**
     * 内部订单编码
     */
    @ApiModelProperty(value="内部订单编码")
    private String inteOrdNum;

    /**
     * 发票明细金额
     */
    @ApiModelProperty(value="发票明细金额")
    private BigDecimal invDetAmt;

    /**
     * 发票明细主键
     */
    @ApiModelProperty(value="发票明细主键")
    private String invDetId;

    /**
     * 发票类型
     */
    @ApiModelProperty(value="发票类型")
    private String invoType;

    /**
     * 发票税额
     */
    @ApiModelProperty(value="发票税额")
    private BigDecimal invTax;

    /**
     * 发票类型编码
     */
    @ApiModelProperty(value="发票类型编码")
    private String invTypeCode;

    /**
     * 滞纳金
     */
    @ApiModelProperty(value="滞纳金")
    private BigDecimal lateFee;

    /**
     * 保证金核销金额
     */
    @ApiModelProperty(value="保证金核销金额")
    private BigDecimal marginWriteOffAmt;

    /**
     * 本位币保证金核销金额
     */
    @ApiModelProperty(value="本位币保证金核销金额")
    private BigDecimal marginWriteOffAmtCy;

    /**
     * 资金属性
     */
    @ApiModelProperty(value="资金属性")
    private String moneyAttr;

    /**
     * 币种
     */
    @ApiModelProperty(value="币种")
    private String moneyDm;

    /**
     * 使用性质
     */
    @ApiModelProperty(value="使用性质")
    private String natureUse;

    /**
     * 挂账金额
     */
    @ApiModelProperty(value="挂账金额")
    private BigDecimal onAccountAmount;

    /**
     * 挂账金额本
     */
    @ApiModelProperty(value="挂账金额本")
    private BigDecimal onAccountAmountCy;

    /**
     * 收款人
     */
    @ApiModelProperty(value="收款人")
    private String payee;

    /**
     * 利润中心
     */
    @ApiModelProperty(value="利润中心")
    private String profCen;

    /**
     * 报销金额
     */
    @ApiModelProperty(value="报销金额")
    private BigDecimal reimbAmt;

    /**
     * 本位币报销金额
     */
    @ApiModelProperty(value="本位币报销金额")
    private BigDecimal reimbAmtCy;

    /**
     * 科目名称
     */
    @ApiModelProperty(value="科目名称")
    private Integer sbjctName;

    /**
     * 本位币代码
     */
    @ApiModelProperty(value="本位币代码")
    private String staCyCode;

    /**
     * 税项
     */
    @ApiModelProperty(value="税项")
    private BigDecimal taxItem;

    /**
     * 税率
     */
    @ApiModelProperty(value="税率")
    private BigDecimal taxrt;

    /**
     * 项目
     */
    @ApiModelProperty(value="项目")
    private String tProject;

    /**
     * 增值税税额
     */
    @ApiModelProperty(value="增值税税额")
    private BigDecimal vatAmt;

    /**
     * 车船税
     */
    @ApiModelProperty(value="车船税")
    private BigDecimal vehVelTax;

    /**
     * 项目
     */
    @ApiModelProperty(value="WBS描述")
    private String wbsDesc;

    /**
     * 项目
     */
    @ApiModelProperty(value="WBS元素")
    private String wbsElement;

    /**
     * 项目
     */
    @ApiModelProperty(value="所得税代扣代缴")
    private BigDecimal withholdingPayInTax;

    /**
     * 冲销借款
     */
    @ApiModelProperty(value="冲销借款")
    private BigDecimal writeOffLoan;

    /**
     * 本次冲销金额
     */
    @ApiModelProperty(value="本次冲销金额")
    private BigDecimal wtOffAmt;

    /**
     * 本次冲销金额本位币
     */
    @ApiModelProperty(value="本次冲销金额本位币")
    private BigDecimal wtOffAmtCy;

    /**
     * 起租编号
     */
    @ApiModelProperty(value="起租编号")
    private String hiredCode;

    /**
     * 客户名称
     */
    @ApiModelProperty(value="客户名称")
    private String cusName;

    /**
     * 客户类别
     */
    @ApiModelProperty(value="客户类别")
    private String customerCate;

    /**
     * 产品服务
     */
    @ApiModelProperty(value="产品服务")
    private String prodAndSer;

}

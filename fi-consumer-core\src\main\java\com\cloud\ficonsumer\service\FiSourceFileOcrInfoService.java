package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiSourceFileInfoQueryDTO;
import com.cloud.ficonsumer.dto.FiSourceFileOcrInfoQueryDTO;
import com.cloud.ficonsumer.entity.FiSourceFileOcrInfo;
import com.cloud.ficonsumer.enums.InvoiceBillCheckEnum;
import com.cloud.ficonsumer.vo.FiConvertFileInfoVO;
import com.cloud.ficonsumer.vo.FiConvertFileOcrInfoVO;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceFileInfoVO;
import com.cloud.ficonsumer.vo.FiSourceFileOcrInfoVO;

import java.util.List;
import java.util.Map;

/**
 * 进项发票源数据服务接口
 *
 * <AUTHOR>
 */
public interface FiSourceFileOcrInfoService extends IService<FiSourceFileOcrInfo> {

    boolean push(String uniquecodeofinvoice);

    Page<FiSourceFileOcrInfoVO> beforeConvertDataPage(Page<FiSourceFileOcrInfoVO> page, FiSourceFileOcrInfoQueryDTO queryDTO);

    Page<FiSourceFileInfoVO> getBeforeConvertDataPage(Page<FiSourceFileInfoVO> page, FiSourceFileInfoQueryDTO queryDTO);

    List<FiConvertFileOcrInfoVO> getConvertList(String uniquecodeofinvoice);

    Page<FiConvertFileInfoVO> getConvertData(Page<FiConvertFileInfoVO> page, String uniquecodeofinvoice);

    Map<String, Integer> listOcrPushStatus(List<String> fileManageIds);

    /**
     * 根据业务代码过滤有效的单据状态
     *
     * @param businessCodeGroup 按业务代码分组的单据列表，key为业务代码枚举，value为对应的单据列表
     * @return 过滤后的有效单据列表，如果没有有效单据则返回空列表
     */
    List<FiSourceFileOcrInfoVO> filterValidBillStatus(Map<InvoiceBillCheckEnum, List<FiSourceFileOcrInfoVO>> businessCodeGroup);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}
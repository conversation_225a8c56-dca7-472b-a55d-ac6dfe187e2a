

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 采购入库源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:35
 */
@Data
@TableName("fi_source_store")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购入库源数据主表")
public class FiSourceStore extends BaseEntity<FiSourceStore> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 入库单主键
     */
    @ApiModelProperty(value="入库单主键")
    private String cbillid;

    /**
     * 订单编号
     */
    @ApiModelProperty(value="订单编号")
    private String vbillcode;

    /**
     * 财务组织
     */
    @ApiModelProperty(value="财务组织")
    private String pkOrg;

    /**
     * 财务组织名称
     */
    @ApiModelProperty(value="财务组织名称")
    private String pkOrgName;

    /**
     * 供应商
     */
    @ApiModelProperty(value="供应商")
    private String cvendorid;

    /**
     * 部门
     */
    @ApiModelProperty(value="部门")
    private String cdeptvid;

    /**
     * 库存组织
     */
    @ApiModelProperty(value="库存组织")
    private String cstockorgvid;

    /**
     * 单据日期
     */
    @ApiModelProperty(value="单据日期")
    private String dbilldate;

    /**
     * 合同编号
     */
    @ApiModelProperty(value="合同编号")
    private String vcontractcode;

    /**
     * 仓库
     */
    @ApiModelProperty(value="仓库")
    private String cstordocid;

    /**
     * 库管员
     */
    @ApiModelProperty(value="库管员")
    private String cstordocmanid;

    @ApiModelProperty(value = "制单人")
    private String billmaker;

    /**
     * 业务员
     */
    @ApiModelProperty(value="业务员")
    private String cpsnid;

    /**
     * 主数量
     */
    @ApiModelProperty(value="主数量")
    private String nnum;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String vnote;

    /**
     * 制单日期
     */
    @ApiModelProperty(value="制单日期")
    private String dmakedate;

    /**
     * 暂估标志
     */
    @ApiModelProperty(value="暂估标志")
    private String bestimateflag;

    /**
     * 采购退库
     */
    @ApiModelProperty(value="采购退库")
    private String freplenishflag;

    /**
     * 推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中
     */
    @ApiModelProperty(value="推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中")
    private Integer pushStatus;

    /**
     * 推送成功失败信息
     */
    @ApiModelProperty(value="推送成功失败信息")
    private String pushMsg;

    /**
     * 推送时间
     */
    @ApiModelProperty(value="推送时间")
    private String pushTime;

    /**
     * 重试次数
     */
    @ApiModelProperty(value="重试次数")
    private Long tryNumber;

    /**
     * 0 未就绪 1 就绪
     */
    @ApiModelProperty(value="0 未就绪 1 就绪")
    private Long readiness;

}

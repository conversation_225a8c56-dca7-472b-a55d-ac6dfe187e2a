CREATE TABLE `fi_source_proj_esti_payable`
(
    `id`                 BIGINT      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_estipayablebill` VARCHAR(64) NOT NULL COMMENT '暂估应付单主键',
    `pk_org`             VARCHAR(64)    DEFAULT NULL COMMENT '财务组织',
    `billmaker`          VARCHAR(64)    DEFAULT NULL COMMENT '制单人',
    `local_money`        DECIMAL(18, 2) DEFAULT NULL COMMENT '组织本币金额',
    `pk_tradetype`       VARCHAR(64)    DEFAULT NULL COMMENT '应付类型编码',
    `integration_status` VARCHAR(32)    DEFAULT NULL COMMENT '集成状态',
    `push_status`        INT            DEFAULT NULL COMMENT '推送状态 4:推送中、1:转换失败、2:推送失败、3:推送成功、5:推送财务失败',
    `push_msg`           VARCHAR(255)   DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`         VARCHAR(255)   DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      VARCHAR(32)    DEFAULT NULL COMMENT '返回同步状态',
    `try_number`         BIGINT         DEFAULT NULL COMMENT '重试次数',
    `readiness`          BIGINT         DEFAULT NULL COMMENT '0 未就绪 1 就绪',
    `create_by`          varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目暂估应付源数据详情';

CREATE TABLE `fi_source_proj_esti_payable_detail`
(
    `id`                 BIGINT      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pkEstipayableitem`  VARCHAR(64) NOT NULL COMMENT '暂估应付单行标识',
    `pk_estipayablebill` VARCHAR(64) NOT NULL COMMENT '暂估应付单主键',
    `pu_psndoc`          VARCHAR(64)    DEFAULT NULL COMMENT '业务人员',
    `supplier`           VARCHAR(128)   DEFAULT NULL COMMENT '供应商',
    `project`            VARCHAR(64)    DEFAULT NULL COMMENT '项目',
    `pk_deptid`          VARCHAR(64)    DEFAULT NULL COMMENT '部门',
    `pk_currtype`        VARCHAR(64)    DEFAULT NULL COMMENT '币种',
    `invoiceno`          VARCHAR(64)    DEFAULT NULL COMMENT '发票号',
    `local_money_cr`     DECIMAL(28, 8) DEFAULT NULL COMMENT '组织本币金额',
    `local_notax_cr`     DECIMAL(28, 8) DEFAULT NULL COMMENT '组织本币无税金额',
    `create_by`          varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目暂估应付明细源数据详情';

CREATE TABLE `fi_convert_proj_esti_payable`
(
    `id`                 BIGINT      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_estipayablebill` VARCHAR(64) NOT NULL COMMENT '暂估应付单主键',
    `pk_org`             VARCHAR(64)    DEFAULT NULL COMMENT '财务组织',
    `billmaker`          VARCHAR(64)    DEFAULT NULL COMMENT '制单人',
    `local_money`        DECIMAL(18, 2) DEFAULT NULL COMMENT '组织本币金额',
    `pk_tradetype`       VARCHAR(64)    DEFAULT NULL COMMENT '应付类型编码',
    `integration_status` VARCHAR(32)    DEFAULT NULL COMMENT '集成状态',
    `push_status`        INT            DEFAULT NULL COMMENT '推送状态 4:推送中、1:转换失败、2:推送失败、3:推送成功、5:推送财务失败',
    `push_msg`           VARCHAR(255)   DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`         VARCHAR(255)   DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      VARCHAR(32)    DEFAULT NULL COMMENT '返回同步状态',
    `try_number`         BIGINT         DEFAULT NULL COMMENT '重试次数',
    `readiness`          BIGINT         DEFAULT NULL COMMENT '0 未就绪 1 就绪',
    `create_by`          varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目暂估应付转换数据详情';

CREATE TABLE `fi_convert_proj_esti_payable_detail`
(
    `id`                 BIGINT      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pkEstipayableitem`  VARCHAR(64) NOT NULL COMMENT '暂估应付单行标识',
    `pk_estipayablebill` VARCHAR(64) NOT NULL COMMENT '暂估应付单主键',
    `pu_psndoc`          VARCHAR(64)    DEFAULT NULL COMMENT '业务人员',
    `supplier`           VARCHAR(128)   DEFAULT NULL COMMENT '供应商',
    `project`            VARCHAR(64)    DEFAULT NULL COMMENT '项目',
    `pk_deptid`          VARCHAR(64)    DEFAULT NULL COMMENT '部门',
    `pk_currtype`        VARCHAR(64)    DEFAULT NULL COMMENT '币种',
    `invoiceno`          VARCHAR(64)    DEFAULT NULL COMMENT '发票号',
    `local_money_cr`     DECIMAL(28, 8) DEFAULT NULL COMMENT '组织本币金额',
    `local_notax_cr`     DECIMAL(28, 8) DEFAULT NULL COMMENT '组织本币无税金额',
    `create_by`          varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='项目暂估应付明细转换数据详情';
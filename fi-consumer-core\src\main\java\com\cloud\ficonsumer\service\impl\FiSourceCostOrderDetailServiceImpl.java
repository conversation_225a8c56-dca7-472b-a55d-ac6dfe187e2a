
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceCostOrderDetail;
import com.cloud.ficonsumer.mapper.FiSourceCostOrderDetailMapper;
import com.cloud.ficonsumer.service.FiSourceCostOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * 虚拟采购订单源数据详情表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:45:53
 */
@Service
public class FiSourceCostOrderDetailServiceImpl extends ServiceImpl<FiSourceCostOrderDetailMapper, FiSourceCostOrderDetail> implements FiSourceCostOrderDetailService {

}

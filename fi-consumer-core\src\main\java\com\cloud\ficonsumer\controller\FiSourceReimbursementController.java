

package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.FiConvertReimbursementDTO;
import com.cloud.ficonsumer.dto.FiConvertReimbursementDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceReimbursement;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.FileManageMapper;
import com.cloud.ficonsumer.service.FiSourceReimbursementService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceReimbursementQueryVO;
import com.cloud.ficonsumer.vo.InvoiceFileVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 通用报销单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:38
 */
@RestController
@AllArgsConstructor
@RequestMapping("/reimbursement" )
@Api(value = "reimbursement", tags = "通用报销单源数据主表管理")
public class FiSourceReimbursementController {

    private final  FiSourceReimbursementService fiSourceReimbursementService;
    private final FileManageMapper fileManageMapper;

    /**
     * 报错重推
     *
     * @return
     */
    @ApiOperation(value = "报错重推", notes = "报错重推")
    @GetMapping("/pushAgain/{pkPayablebill}")
    public R<Boolean> pushAgain(@PathVariable("pkPayablebill") String pkPayablebill) {
        return R.ok(fiSourceReimbursementService.pushAgain(pkPayablebill));
    }

    /**
     * 转换前分页查询
     *
     * @return
     */
    @ApiOperation(value = "转换前分页查询", notes = "转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiSourceReimbursementDTO>> beforeConvertDataPage(Page page, FiSourceReimbursementQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        return R.ok(fiSourceReimbursementService.beforeConvertDataPage(page,queryVO));
    }

    /**
     * 转换前数据
     * @return
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage" )
    public R<Page<FiSourceReimbursementDetailDTO>> getBeforeConvertDataPage(Page page, FiSourceReimbursementQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getPkPayablebill(), "主键不能为空");
        return R.ok(fiSourceReimbursementService.getBeforeConvertDataPage(page,queryVO));
    }

    /**
     * 主体表转换后单条字段
     * @return
     */
    @ApiOperation(value = "主体表转换后单条字段", notes = "主体表转换后单条字段")
    @GetMapping("/getConvertList" )
    public R<List<FiConvertReimbursementDTO>> getConvertList(String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceReimbursementService.getConvertList(pk));
    }

    /**
     * 获取转换后详情分页数据
     * @return
     */
    @ApiOperation(value = "获取转换后详情分页数据", notes = "获取转换后详情分页数据")
    @GetMapping("/getConvertData" )
    public R<Page<FiConvertReimbursementDetailDTO>> getConvertData(Page page, String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceReimbursementService.getConvertData(page,pk));
    }

    /**
     * 根据主键查询发票列表
     * @return
     */
    @ApiOperation(value = "根据主键查询发票列表", notes = "根据主键查询发票列表")
    @GetMapping("/getInvoiceListByPk" )
    public R<List<InvoiceFileVO>> getInvoiceListByPk(String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        List<InvoiceFileVO> invoiceVOList = fileManageMapper.getInvoiceByPk(pk);
        return R.ok(invoiceVOList);
    }

    /**
     * 根据主键更新实体
     * @return
     */
    @ApiOperation(value = "根据主键更新实体", notes = "根据主键更新实体")
    @PostMapping("/updateEntityByPk" )
    public R<Boolean> updateEntityByPk(@RequestBody FiSourceReimbursementQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getPkPayablebill(), "主键不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        FiSourceReimbursement source = fiSourceReimbursementService.getOne(Wrappers.<FiSourceReimbursement>lambdaQuery().eq(FiSourceReimbursement::getPkPayablebill, queryVO.getPkPayablebill()));
        if(source==null) {
            throw new CheckedException("单据不存在");
        }
        source.setPushStatus(queryVO.getPushStatus());
        source.setPushMsg(queryVO.getPushMsg());
        return R.ok(fiSourceReimbursementService.updateById(source));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result = fiSourceReimbursementService.update(Wrappers.<FiSourceReimbursement>lambdaUpdate()
                .set(FiSourceReimbursement::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiSourceReimbursement::getPushMsg, "成功")
                .in(FiSourceReimbursement::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiSourceReimbursementService.pushAgainBatch(queryVO));
    }
}

package com.cloud.ficonsumer.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.cloud.cloud.common.core.constant.CommonConstants;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import lombok.experimental.UtilityClass;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Author: zhengjinjin
 * @Descrition: 校验参数工具类
 * @Date: 2020/11/12
 */
@UtilityClass
public class AssertUtils {

    /**
     * @param obj
     * @param msg
     */
    public void isTrue(boolean obj, String msg) {
        if (!obj) {
            throw new CheckedException(msg);
        }
    }

    /**
     * 对象不为null
     *
     * @param obj
     * @param msg
     */
    public void notNull(Object obj, String msg) {
        isTrue(obj != null, msg);
    }

    /**
     * 字符串不为null
     *
     * @param str
     * @param msg
     */
    public void notBlank(String str, String msg) {
        isTrue(StrUtil.isNotBlank(str), msg);
    }

    /**
     * 集合不为空
     *
     * @param collection
     * @param msg
     */
    public void notEmpty(Collection collection, String msg) {
        isTrue(CollectionUtil.isNotEmpty(collection), msg);
    }
    /**
     * 集合不为空
     *
     * @param collection
     * @param msg
     */
    public void notEmpty(Map collection, String msg) {
        isTrue(CollectionUtil.isNotEmpty(collection), msg);
    }

    /**
     * 字符串相同
     * @param str1
     * @param str2
     * @param msg
     */
    public void equals(String str1, String str2, String msg) {
        isTrue(StrUtil.equals(str1, str2), msg);
    }


    /**
     * 包含
     * @param collection
     * @param value
     * @param msg
     */
    public void contain(Collection collection, String value, String msg) {
        isTrue(collection.contains(value), msg);
    }

    /**
     * 调用feign成功
     *
     * @param r
     * @param msg
     */
    public void success(R r, String msg) {
        isTrue(r.getCode() == CommonConstants.SUCCESS.intValue(), msg);
    }

    /**
     * 将arrays转为clazz集合
     *
     * @param arrays
     * @param clazz
     * @param <T>
     * @return
     */
    public <T> List<T> parseArray(Collection<?> arrays, Class<T> clazz) {
        return JSONArray.parseArray(JSONArray.toJSONString(arrays), clazz);
    }



}



package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.entity.FiOrgCompare;
import com.cloud.ficonsumer.vo.FiOrgListVO;

import java.util.List;

/**
 * 组织对照关系
 *
 * <AUTHOR>
 * @date 2025-04-27 10:55:07
 */
public interface FiOrgCompareService extends IService<FiOrgCompare> {

    List<FiOrgCompare> getGroupMap();

    /**
     * 根据当前登录组织的departCode字段获取组织树
     *
     * @param departCode
     * @return
     */
    FiOrgListVO getOrgList(String departCode);

    /**
     * 根据当前登录集团的departCode字段获取组织树
     *
     * @param departCode
     * @return
     */
    FiOrgListVO getGroupList(String departCode);
}

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/3/12.
 */
@Data
public class StoreMainVO {

    /**
     * 单据编号
     */
    @ApiModelProperty(value="单据编号")
    private String extDocumentNo;

    /**
     * 单位编码
     */
    @ApiModelProperty(value="单位编码")
    private String UnitCode;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value="供应商编码")
    private String supplierCode;

    /**
     * 利润中心
     */
    @ApiModelProperty(value="利润中心")
    private String profCen;

    /**
     * 成本中心
     */
    @ApiModelProperty(value="成本中心")
    private String costCenter;

    /**
     * 库存组织
     */
    @ApiModelProperty(value="库存组织")
    private String invtryOrg;

    /**
     * 业务日期
     */
    @ApiModelProperty(value="业务日期")
    private String busDate;

    /**
     * 合同编号
     */
    @ApiModelProperty(value="合同编号")
    private String contractNo;

    /**
     * 出库仓库
     */
    @ApiModelProperty(value="出库仓库")
    private String inWarehouse;

    /**
     * 仓管员
     */
    @ApiModelProperty(value="仓管员")
    private String useCustdiaObject;

    /**
     * 经办人
     */
    @ApiModelProperty(value="经办人")
    private String maintMgrObject;

    /**
     * 单据状态
     */
    @ApiModelProperty(value="单据状态")
    private String docuStatus;

    /**
     * 单据方向
     */
    @ApiModelProperty(value="单据方向")
    private String direct;

    /**
     * 业务类型
     */
    @ApiModelProperty(value="业务类型")
    private String biType;

    /**
     * 00000001-红冲 ------冲销的单
     * 00000002-报销入库 ------红冲之后新入库的单
     * 00000003-原单 ------原来的单
     */
    @ApiModelProperty(value="暂估处理类型")
    private String estimationProcessingType;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String note;

    /**
     * 被冲销单号
     */
    @ApiModelProperty(value="被冲销单号")
    private String offSetDocNo;

    /**
     * 经办部门
     */
    @ApiModelProperty(value="经办部门")
    private String deptName;

    /**
     * 移动类型
     */
    @ApiModelProperty(value="移动类型")
    private String mbType;

    private List<StoreDetailVO> item;

}

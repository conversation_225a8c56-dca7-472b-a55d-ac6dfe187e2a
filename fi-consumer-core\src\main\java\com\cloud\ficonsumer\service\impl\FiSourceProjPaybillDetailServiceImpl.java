package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceProjPaybillDetail;
import com.cloud.ficonsumer.mapper.FiSourceProjPaybillDetailMapper;
import com.cloud.ficonsumer.service.FiSourceProjPaybillDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourceProjPaybillDetailServiceImpl extends ServiceImpl<FiSourceProjPaybillDetailMapper, FiSourceProjPaybillDetail> implements FiSourceProjPaybillDetailService {

}

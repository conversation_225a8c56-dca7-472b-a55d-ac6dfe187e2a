package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@TableName("fi_source_projsub_payable")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目分包应付源数据详情")
public class FiSourceProjsubPayable extends BaseEntity<FiSourceProjsubPayable> {
    private static final long serialVersionUID = -1L;
    
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value = "应付单标识")
    private String pkPayablebill;

    @ApiModelProperty(value="集成状态")
    private String integrationStatus;

    @ApiModelProperty(value="4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败")
    private Integer pushStatus;

    @ApiModelProperty(value="推送成功失败信息")
    private String pushMsg;

    @ApiModelProperty(value="返回同步消息")
    private String returnMsg;

    @ApiModelProperty(value="返回同步状态")
    private String returnStatus;

    @ApiModelProperty(value="重试次数")
    private Long tryNumber;

    @ApiModelProperty(value="0 未就绪 1 就绪")
    private Long readiness;
}

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "YgInvoiceDataResult", description = "远光【根据比对一致和人票关联数据创建进项发票入池信息服务】响应参数")
public class YgInvoiceDataResult implements Serializable {

    @ApiModelProperty(value = "入池成功的发票信息")
    List<YgInvoiceProcResult> procSucc;

    @ApiModelProperty(value = "入池失败的发票信息")
    List<YgInvoiceProcResult> procFail;
}
package com.cloud.ficonsumer.service;

import com.cloud.ficonsumer.dto.BillStatusDTO;
import com.cloud.ficonsumer.enums.InvoiceBillCheckEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.impl.FiSourceFileOcrInfoServiceImpl;
import com.cloud.ficonsumer.vo.FiSourceFileOcrInfoVO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("FiSourceFileOcrInfoService 单据状态过滤测试")
class FiSourceFileOcrInfoServiceTest {

    @Mock
    private DmMapper dmMapper;

    @InjectMocks
    private FiSourceFileOcrInfoServiceImpl service;

    @Nested
    @DisplayName("应付单(F1)测试")
    class ApPayablebillTest {
        
        @Test
        @DisplayName("应付单 - 状态1和8有效")
        void testApPayablebillValidStatus() {
            // 准备测试数据
            List<FiSourceFileOcrInfoVO> bills = Arrays.asList(
                createBill("AP001", "F1"),  // 状态1
                createBill("AP002", "F1"),  // 状态8
                createBill("AP003", "F1")   // 状态2（无效）
            );
            
            Map<InvoiceBillCheckEnum, List<FiSourceFileOcrInfoVO>> input = new HashMap<>();
            input.put(InvoiceBillCheckEnum.AP_PAYABLE_BILL, bills);

            // Mock状态查询结果
            Map<String, BillStatusDTO> statusMap = new HashMap<>();
            statusMap.put("AP001", new BillStatusDTO("AP001", 1));
            statusMap.put("AP002", new BillStatusDTO("AP002", 8));
            when(dmMapper.batchGetApPayablebillStatus(anyList(), eq(InvoiceBillCheckEnum.AP_PAYABLE_BILL.getValidStatuses())))
                .thenReturn(statusMap);

            // 执行测试
            List<FiSourceFileOcrInfoVO> result = service.filterValidBillStatus(input);

            // 验证结果
            assertEquals(2, result.size(), "应该只有2个有效单据");
            assertTrue(result.stream().anyMatch(bill -> "AP001".equals(bill.getBillId())), "状态为1的单据应该包含在结果中");
            assertTrue(result.stream().anyMatch(bill -> "AP002".equals(bill.getBillId())), "状态为8的单据应该包含在结果中");
            assertFalse(result.stream().anyMatch(bill -> "AP003".equals(bill.getBillId())), "状态为2的单据不应该包含在结果中");
            
            verify(dmMapper, times(1)).batchGetApPayablebillStatus(anyList(), eq(InvoiceBillCheckEnum.AP_PAYABLE_BILL.getValidStatuses()));
        }
    }

    @Nested
    @DisplayName("采购发票(25)测试")
    class PoInvoiceTest {
        
        @Test
        @DisplayName("采购发票 - 状态3有效")
        void testPoInvoiceValidStatus() {
            // 准备测试数据
            List<FiSourceFileOcrInfoVO> bills = Arrays.asList(
                createBill("PO001", "25"),  // 状态1（无效）
                createBill("PO002", "25"),  // 状态3（有效）
                createBill("PO003", "25")   // 状态2（无效）
            );
            
            Map<InvoiceBillCheckEnum, List<FiSourceFileOcrInfoVO>> input = new HashMap<>();
            input.put(InvoiceBillCheckEnum.PO_INVOICE, bills);

            // Mock状态查询结果 - 只返回有效状态的单据
            Map<String, BillStatusDTO> statusMap = new HashMap<>();
            statusMap.put("PO002", new BillStatusDTO("PO002", 3)); // 只返回状态为3的单据
            
            when(dmMapper.batchGetPoInvoiceStatus(anyList(), eq(InvoiceBillCheckEnum.PO_INVOICE.getValidStatuses())))
                .thenReturn(statusMap);

            List<FiSourceFileOcrInfoVO> result = service.filterValidBillStatus(input);

            assertEquals(1, result.size(), "应该只有1个有效单据");
            assertTrue(result.stream().anyMatch(bill -> "PO002".equals(bill.getBillId())), "状态为3的单据应该包含在结果中");
            
            verify(dmMapper, times(1)).batchGetPoInvoiceStatus(anyList(), eq(InvoiceBillCheckEnum.PO_INVOICE.getValidStatuses()));
        }
    }

    @Nested
    @DisplayName("收票登记(4D53)测试")
    class PmReceiptregTest {
        
        @Test
        @DisplayName("收票登记 - 状态1和3有效")
        void testPmReceiptregValidStatus() {
            List<FiSourceFileOcrInfoVO> bills = Arrays.asList(
                createBill("PM001", "4D53"),  // 状态1
                createBill("PM002", "4D53"),  // 状态3
                createBill("PM003", "4D53")   // 状态2（无效）
            );
            
            Map<InvoiceBillCheckEnum, List<FiSourceFileOcrInfoVO>> input = new HashMap<>();
            input.put(InvoiceBillCheckEnum.PM_RECEIPT_REG, bills);

            Map<String, BillStatusDTO> statusMap = new HashMap<>();
            statusMap.put("PM001", new BillStatusDTO("PM001", 1));
            statusMap.put("PM002", new BillStatusDTO("PM002", 3));
            when(dmMapper.batchGetPmReceiptregStatus(anyList(), eq(InvoiceBillCheckEnum.PM_RECEIPT_REG.getValidStatuses())))
                .thenReturn(statusMap);

            List<FiSourceFileOcrInfoVO> result = service.filterValidBillStatus(input);

            assertEquals(2, result.size(), "应该只有2个有效单据");
            assertTrue(result.stream().anyMatch(bill -> "PM001".equals(bill.getBillId())), "状态为1的单据应该包含在结果中");
            assertTrue(result.stream().anyMatch(bill -> "PM002".equals(bill.getBillId())), "状态为3的单据应该包含在结果中");
            assertFalse(result.stream().anyMatch(bill -> "PM003".equals(bill.getBillId())), "状态为2的单据不应该包含在结果中");
            
            verify(dmMapper, times(1)).batchGetPmReceiptregStatus(anyList(), eq(InvoiceBillCheckEnum.PM_RECEIPT_REG.getValidStatuses()));
        }
    }

    @Nested
    @DisplayName("边界情况测试")
    class EdgeCasesTest {
        
        @Test
        @DisplayName("空输入Map测试")
        void testEmptyInput() {
            List<FiSourceFileOcrInfoVO> result = service.filterValidBillStatus(new HashMap<>());
            assertTrue(result.isEmpty(), "空输入应返回空列表");
            
            verifyNoInteractions(dmMapper);
        }

        @Test
        @DisplayName("空单据列表测试")
        void testEmptyBillList() {
            Map<InvoiceBillCheckEnum, List<FiSourceFileOcrInfoVO>> input = new HashMap<>();
            input.put(InvoiceBillCheckEnum.AP_PAYABLE_BILL, Collections.emptyList());
            
            List<FiSourceFileOcrInfoVO> result = service.filterValidBillStatus(input);
            assertTrue(result.isEmpty(), "空单据列表应返回空列表");
            
            verifyNoInteractions(dmMapper);
        }

        @Test
        @DisplayName("混合业务代码测试")
        void testMixedBusinessCodes() {
            // 准备测试数据
            Map<InvoiceBillCheckEnum, List<FiSourceFileOcrInfoVO>> input = new HashMap<>();
            // 应付单 - 有效状态
            input.put(InvoiceBillCheckEnum.AP_PAYABLE_BILL, Collections.singletonList(createBill("AP001", "F1")));
            // 采购发票 - 有效状态
            input.put(InvoiceBillCheckEnum.PO_INVOICE, Collections.singletonList(createBill("PO001", "25")));
            // OTHER类型 - 不需要验证状态
            input.put(InvoiceBillCheckEnum.OTHER, Arrays.asList(
                createBill("OTHER001", "XXX"),
                createBill("OTHER002", "YYY")
            ));
            // 收票登记 - 包含无效状态
            input.put(InvoiceBillCheckEnum.PM_RECEIPT_REG, Arrays.asList(
                createBill("PM001", "4D53"),  // 状态1 - 有效
                createBill("PM002", "4D53")   // 状态2 - 无效
            ));
            
            // Mock状态查询结果
            Map<String, BillStatusDTO> apStatusMap = Collections.singletonMap("AP001", new BillStatusDTO("AP001", 1));
            Map<String, BillStatusDTO> poStatusMap = Collections.singletonMap("PO001", new BillStatusDTO("PO001", 3));
            Map<String, BillStatusDTO> pmStatusMap = Collections.singletonMap("PM001", new BillStatusDTO("PM001", 1));
            
            when(dmMapper.batchGetApPayablebillStatus(anyList(), eq(InvoiceBillCheckEnum.AP_PAYABLE_BILL.getValidStatuses())))
                .thenReturn(apStatusMap);
            when(dmMapper.batchGetPoInvoiceStatus(anyList(), eq(InvoiceBillCheckEnum.PO_INVOICE.getValidStatuses())))
                .thenReturn(poStatusMap);
            when(dmMapper.batchGetPmReceiptregStatus(anyList(), eq(InvoiceBillCheckEnum.PM_RECEIPT_REG.getValidStatuses())))
                .thenReturn(pmStatusMap);

            // 执行测试
            List<FiSourceFileOcrInfoVO> result = service.filterValidBillStatus(input);
            
            // 验证结果
            assertEquals(5, result.size(), "应该返回5个有效单据（1个应付单 + 1个采购发票 + 2个OTHER + 1个收票登记）");
            assertTrue(result.stream().anyMatch(bill -> "AP001".equals(bill.getBillId())), "应包含应付单");
            assertTrue(result.stream().anyMatch(bill -> "PO001".equals(bill.getBillId())), "应包含采购发票");
            assertTrue(result.stream().anyMatch(bill -> "OTHER001".equals(bill.getBillId())), "应包含OTHER类型单据1");
            assertTrue(result.stream().anyMatch(bill -> "OTHER002".equals(bill.getBillId())), "应包含OTHER类型单据2");
            assertTrue(result.stream().anyMatch(bill -> "PM001".equals(bill.getBillId())), "应包含有效状态的收票登记");
            assertFalse(result.stream().anyMatch(bill -> "PM002".equals(bill.getBillId())), "不应包含无效状态的收票登记");
            
            // 验证调用
            verify(dmMapper, times(1)).batchGetApPayablebillStatus(anyList(), eq(InvoiceBillCheckEnum.AP_PAYABLE_BILL.getValidStatuses()));
            verify(dmMapper, times(1)).batchGetPoInvoiceStatus(anyList(), eq(InvoiceBillCheckEnum.PO_INVOICE.getValidStatuses()));
            verify(dmMapper, times(1)).batchGetPmReceiptregStatus(anyList(), eq(InvoiceBillCheckEnum.PM_RECEIPT_REG.getValidStatuses()));
        }

        @Test
        @DisplayName("状态查询返回空结果测试")
        void testEmptyStatusResponse() {
            List<FiSourceFileOcrInfoVO> bills = Collections.singletonList(createBill("AP001", "F1"));
            Map<InvoiceBillCheckEnum, List<FiSourceFileOcrInfoVO>> input = new HashMap<>();
            input.put(InvoiceBillCheckEnum.AP_PAYABLE_BILL, bills);
            
            when(dmMapper.batchGetApPayablebillStatus(anyList(), eq(InvoiceBillCheckEnum.AP_PAYABLE_BILL.getValidStatuses())))
                .thenReturn(Collections.emptyMap());

            List<FiSourceFileOcrInfoVO> result = service.filterValidBillStatus(input);
            
            assertTrue(result.isEmpty(), "状态查询返回空时应返回空列表");
            verify(dmMapper, times(1)).batchGetApPayablebillStatus(anyList(), eq(InvoiceBillCheckEnum.AP_PAYABLE_BILL.getValidStatuses()));
        }
    }

    private FiSourceFileOcrInfoVO createBill(String billId, String businessCode) {
        FiSourceFileOcrInfoVO vo = new FiSourceFileOcrInfoVO();
        vo.setBillId(billId);
        vo.setBusinessCode(businessCode);
        return vo;
    }
}
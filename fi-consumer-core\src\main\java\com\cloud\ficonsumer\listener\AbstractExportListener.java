package com.cloud.ficonsumer.listener;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.common.oss.OssProperties;
import com.cloud.common.oss.service.OssTemplate;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.utils.ExcelUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.vo.ExcelExportStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.lang.NonNull;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.Duration;
import java.util.List;

/**
 * Excel导出抽象监听器
 * 处理导出进度条，子类只需实现数据查询逻辑
 */
@Slf4j
public abstract class AbstractExportListener<T> {

    // 缓存时间：1天
    private static final Duration CACHE_DURATION = Duration.ofDays(1);

    // Redis，用于存储导出状态
    protected final RedisTemplate<String, ExcelExportStatusVO> redisTemplate;
    // 导出状态Redis键
    private final String exportDataKey;

    // OSS对象存储
    protected final OssTemplate ossTemplate;
    protected final OssProperties ossProperties;

    // 导出数据类型
    private final Class<T> clazz;

    // 导出状态对象
    protected final ExcelExportStatusVO excelStatusVO;

    // Excel工作表名称
    private final String sheetName;

    /**
     * 构造函数
     * @param id 导出任务ID
     * @param sheetName Excel工作表名称
     */
    protected AbstractExportListener(@NonNull UUID id, @NonNull String sheetName) {
        this.redisTemplate = SpringContextHolder.getApplicationContext().getBean(RedisTemplate.class);
        this.ossTemplate = SpringContextHolder.getApplicationContext().getBean(OssTemplate.class);
        this.ossProperties = SpringContextHolder.getApplicationContext().getBean(OssProperties.class);
        this.clazz = getClazz();
        this.exportDataKey = ExcelUtils.EXCEL_EXPORT + id;
        this.sheetName = sheetName;
        this.excelStatusVO = new ExcelExportStatusVO();
        setExcelStatus(false, false);
    }

    /**
     * 子类需要实现的数据查询方法
     *
     * @return 导出的数据列表
     */
    protected abstract List<T> doHandleData();

    /**
     * 执行导出操作
     */
    public void export() {
        try {
            List<T> result = doHandleData();
            AssertUtils.notNull(result, "导出的数据不能为空");
            download(result);
            setExcelStatus(true, true);
        } catch (Exception e) {
            handleExportError(e);
        }
    }

    /**
     * 处理导出过程中的错误
     *
     * @param e 异常信息
     */
    private void handleExportError(Exception e) {
        excelStatusVO.getErrorMsg().add("导出数据失败");
        for (StackTraceElement stack : e.getStackTrace()) {
            excelStatusVO.getErrorMsg().add(stack.toString());
        }
        setExcelStatus(Boolean.FALSE, Boolean.TRUE);
        LogUtil.error(log, "导出数据失败", e);
    }

    /**
     * 下载并保存导出文件到OSS
     *
     * @param exportData 要导出的数据列表
     */
    private void download(List<T> exportData) throws Exception {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcel.write(out, clazz)
                    .autoCloseStream(false)
                    .sheet(sheetName)
                    .doWrite(exportData);

            String fileName = generateFileName();
            ossTemplate.putObject(ossProperties.getBucketName(), fileName,
                    new ByteArrayInputStream(out.toByteArray()));
            excelStatusVO.setUrl(ossTemplate.getObjectURL(ossProperties.getBucketName(), fileName));
        }
    }

    /**
     * 生成唯一的文件名
     */
    private String generateFileName() {
        return IdUtil.simpleUUID() + ExcelTypeEnum.XLSX.getValue();
    }

    /**
     * 设置导出状态
     * @param success 是否成功
     * @param finish 是否完成
     */
    protected void setExcelStatus(boolean success, boolean finish) {
        excelStatusVO.setResult(success);
        excelStatusVO.setIsFinish(finish);
        redisTemplate.opsForValue().set(exportDataKey, excelStatusVO, CACHE_DURATION);
    }

    /**
     * 获取泛型类型
     */
    @SuppressWarnings("unchecked")
    private Class<T> getClazz() {
        Type genericSuperclass = this.getClass().getGenericSuperclass();
        if (genericSuperclass instanceof ParameterizedType) {
            return (Class<T>) ((ParameterizedType) genericSuperclass)
                    .getActualTypeArguments()[0];
        }
        throw new IllegalStateException("未能解析泛型类型");
    }
}
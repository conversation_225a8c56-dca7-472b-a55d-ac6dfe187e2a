package com.cloud.ficonsumer.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceFileInfo;
import com.cloud.ficonsumer.mapper.FiSourceFileInfoMapper;
import com.cloud.ficonsumer.service.FiSourceFileInfoService;
import org.springframework.stereotype.Service;

@Service
public class FiSourceFileInfoServiceImpl extends ServiceImpl<FiSourceFileInfoMapper, FiSourceFileInfo>
        implements FiSourceFileInfoService {

}

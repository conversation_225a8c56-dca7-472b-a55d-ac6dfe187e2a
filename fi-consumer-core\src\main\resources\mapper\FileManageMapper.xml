<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloud.ficonsumer.mapper.FileManageMapper">

    <!-- 增加接收日志到日志记录表 -->
    <insert id="insertPlatFormLog">
        insert into bd_platformlog (pk_log, pk_bill, pk_billtype, code, message)
        values (#{query.pkLog}, #{query.pkBill}, #{query.pkBillType}, #{query.code}, #{query.msg})
    </insert>

    <!-- 更新接收日志到日志记录表 -->
    <update id="updatePlatFormLog">
        update bd_platformlog
        set code    = #{query.code},
            message = #{query.msg}
        where pk_log = #{query.pkLog}
    </update>

    <!-- 查询平台日志 -->
    <select id="selectPlatFormLog" resultType="com.cloud.ficonsumer.vo.PlatFormLogVO">
        select
        pk_log,
        pk_bill,
        pk_billtype,
        code,
        message as msg
        from
        bd_platformlog
        where
        1=1
        <if test="pkBill != null and pkBill != ''">
            and pk_bill = #{pkBill}
        </if>
        <if test="pkBillType != null and pkBillType != ''">
            and pk_billtype = #{pkBillType}
        </if>
    </select>

    <!-- 【进项发票采集-根据比对一致和人票关联数据创建进项发票入池信息服务】 -->
    <select id="listFileOrcInfo" resultType="com.cloud.ficonsumer.vo.FiSourceFileOcrInfoVO">
        select t1.*,
        t1.id as fileManageId,
        t3.bill_id as billId, -- 单据主键
        t3.business_code as businessCode -- 单据类型编码
        from file_ocr_info_data_push t1
        left join file_info t2 on t1.file_id = t2.id
        left join file_image_upload_info t3 on t2.relation_id = t3.id
        where (t1.sync_status != '1' or t1.sync_status is null)
        <!-- invoice_status 0:正常 2:作废 3:红冲 6:部分红冲 -->
        and (t1.invoice_status = 'Y' or t1.invoice_status in (0, 2, 3, 6))
        <if test="enableOrgIds != null and enableOrgIds.size() > 0">
            and t3.org_id in
            <foreach collection="enableOrgIds" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        order by t1.createtime desc
        limit 1000
    </select>

    <select id="getFileInfos" resultType="com.cloud.ficonsumer.vo.FiSourceFileInfoVO">
        select t1.*, t1.id as fileManageId, t2.org_id as orgId
        from file_info t1
                 left join file_image_upload_info t2 on t1.relation_id = t2.id
        where t1.del_flag = 0
          and t1.id = #{fileId}
    </select>

    <update id="updateFileOcrInfoReceive">
        update file_ocr_info_data_push
        set sync_status = '1'
        where id = #{fileManageId}
    </update>

    <select id="getInvoiceByPk" resultType="com.cloud.ficonsumer.vo.InvoiceFileVO">
        select T1.einvoicenumber    as invoiceNo,
               T1.einvoicecode      as invoCode,
               T1.invoicedate       as invDate,
               T1.invoice_type_code as invoType,
               T1.id                as sourceId
        from file_ocr_info_data_push T1
                 left join file_info T2 on T1.file_id = T2.id
                 left join file_image_upload_info T3 on T3.id = T2.relation_id
        where T3.bill_id = #{pk}
          and T2.del_flag = 0
          and T3.del_flag = 0
    </select>

    <select id="getSourceInvoiceList" resultType="java.lang.Long">
        select t1.id
        from file_image_upload_info t1
        left join file_info t2 on t1.id = t2.relation_id
        left join file_ocr_filed_info t3 on t2.id = t3.file_id
        where t1.del_flag = 0
        and t2.del_flag = 0
        and t3.del_flag =0
        and t1.bill_id = #{pk}
        <if test="dictTypeList != null and dictTypeList.size() > 0">
            and t2.ocr_type in
            <foreach collection="dictTypeList" item="dictType" open="(" separator="," close=")">
                #{dictType}
            </foreach>
        </if>
    </select>

    <update id="updateFileOcrInfoStatus">
        update file_ocr_info_data_push
        set push_status = #{pushStatus},
            push_msg = #{pushMsg}
        where id = #{fileManageId}
    </update>
</mapper>
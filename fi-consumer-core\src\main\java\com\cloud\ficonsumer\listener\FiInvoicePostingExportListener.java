package com.cloud.ficonsumer.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiInvoicePostingDTO;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiInvoicePostingService;
import com.cloud.ficonsumer.vo.FiInvoicePostingExportVO;
import com.cloud.ficonsumer.vo.FiInvoicePostingQueryVO;
import com.cloud.ficonsumer.vo.FiSourceAccountingVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class FiInvoicePostingExportListener extends AbstractExportListener<FiInvoicePostingExportVO> {



    private final FiInvoicePostingService fiInvoicePostingService;
    private final FiInvoicePostingQueryVO queryDTO;
    private final Page<FiSourceAccountingVO> page;
    private static final int PAGE_SIZE = 1000;

    public FiInvoicePostingExportListener(UUID id,
                                          FiInvoicePostingService fiInvoicePostingService,
                                          Page<FiSourceAccountingVO> page,
                                          FiInvoicePostingQueryVO queryDTO) {
        super(id, "采购应付转换前数据");
        this.page = page;
        this.fiInvoicePostingService = fiInvoicePostingService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiInvoicePostingExportVO> doHandleData() {
        List<FiInvoicePostingDTO> vos = new ArrayList<>();

        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiInvoicePostingDTO> pageResult = fiInvoicePostingService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiInvoicePostingDTO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiInvoicePostingDTO> pageResult;
            do {
                pageResult = fiInvoicePostingService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }

        List<FiInvoicePostingExportVO> result = BeanUtil.copyToList(vos, FiInvoicePostingExportVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}

package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiConvertStoreDTO;
import com.cloud.ficonsumer.dto.FiConvertStoreDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDTO;
import com.cloud.ficonsumer.dto.FiSourceStoreDTO;
import com.cloud.ficonsumer.dto.FiSourceStoreDetailDTO;
import com.cloud.ficonsumer.entity.ApiCompare;
import com.cloud.ficonsumer.entity.FiConvertStore;
import com.cloud.ficonsumer.entity.FiConvertStoreDetail;
import com.cloud.ficonsumer.entity.FiSourceStore;
import com.cloud.ficonsumer.entity.FiSourceStoreDetail;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceStoreMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;


/**
 * 采购入库源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:35
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiSourceStoreServiceImpl extends ServiceImpl<FiSourceStoreMapper, FiSourceStore> implements FiSourceStoreService {

    private final FiConvertStoreService fiConvertStoreService;
    private final FiConvertStoreDetailService fiConvertStoreDetailService;
    private final FiSourceStoreDetailService fiSourceStoreDetailService;
    private final FiSourceOrderService fiSourceOrderService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;
    private final YgConfig ygConfig;

    /**
     * 重新推送
     *
     * @param pk
     * @return
     */
    @Override
    public boolean pushAgain(String pk) {
        FiSourceStore sourceMainData = this.getOne(Wrappers.<FiSourceStore>lambdaQuery().eq(FiSourceStore::getCbillid, pk));
        List<FiSourceStoreDetail> details = fiSourceStoreDetailService.list(Wrappers.<FiSourceStoreDetail>lambdaQuery().eq(FiSourceStoreDetail::getCbillid, pk));
        //重新转换
        try {
            this.turnData(sourceMainData,details);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            this.updateById(sourceMainData);
            LogUtil.info(log, "采购入库单集成信息转换失败:", e);
            throw new CheckedException("采购入库单集成信息转换失败:"+pushMsg);
        }
        //重新推送智慧平台
        sourceMainData.setPushTime(DateUtil.formatDateTime(new Date()));
        try {
            YgResult ygResult = this.pushData(pk);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
            sourceMainData.setPushMsg("");
            sourceMainData.setReadiness(1l);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,Constants.TradeType.Store);
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000000");
                updatePlatFormLog.setMsg("");
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TradeType.Store);
                platFormLogVO.setCode("000000");
                platFormLogVO.setMsg("");
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk, Constants.TradeType.Store);
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000001");
                updatePlatFormLog.setMsg(pushMsg);
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TradeType.Store);
                platFormLogVO.setCode("000001");
                platFormLogVO.setMsg(pushMsg);
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
            LogUtil.info(log, "采购入库单集成信息推送失败:", e);
            throw new CheckedException("采购入库单集成信息推送失败:"+pushMsg);
        }
        return true;
    }

    /**
     * 数据推送
     *
     * @param pk
     * @return
     * @throws Exception
     */
    private YgResult pushData(String pk) throws Exception {
        YgResult ygResult = new YgResult();
        if(ygConfig.getEnable()) {
            FiConvertStore convertMainData = fiConvertStoreService.getOne(Wrappers.<FiConvertStore>lambdaQuery().eq(FiConvertStore::getCbillid, pk));
            List<FiConvertStoreDetail> details = fiConvertStoreDetailService.list(Wrappers.<FiConvertStoreDetail>lambdaQuery().eq(FiConvertStoreDetail::getCbillid,pk));
            StoreMainVO request = this.turnYgData(convertMainData,details);
            ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000009.getServCode());
            String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(request));
            ygResult = JSONObject.parseObject(result,YgResult.class);
            //成功6100000 失败6990399
            if(!ygResult.getCode().equals("6100000")) {
                throw new CheckedException(ygResult.getmessage());
            }else {
                YgStoreResult ygStoreResult = JSONObject.parseObject(ygResult.getData().toString(),YgStoreResult.class);
                YgCommonResult ygCommonResult = ygStoreResult.getResult();
                if(null == ygCommonResult) {
                    throw new CheckedException(result);
                }
                if(ygCommonResult.getResultState().equals("1")) {
                    throw new CheckedException(ygCommonResult.getResultMsg());
                }
            }
        }
        return ygResult;
    }

    /**
     * 将转换后的数据用远光提供的格式推送
     *
     * @param convertMainData
     * @param details
     * @return
     */
    private StoreMainVO turnYgData(FiConvertStore convertMainData, List<FiConvertStoreDetail> details) {
        StoreMainVO storeMainVO = new StoreMainVO();
        storeMainVO.setExtDocumentNo(convertMainData.getVbillcode());
        String pkOrg = apiCompareService.getTurnByType(convertMainData.getPkOrg(),Constants.PkOrg);
        if(StrUtil.isBlank(pkOrg)) {
            throw new CheckedException("单位代号不可为空");
        }
        storeMainVO.setUnitCode(pkOrg);
        String supplierCode = apiCompareService.getTurnByType(convertMainData.getCvendorid(),Constants.PkSupplier);
        storeMainVO.setSupplierCode(supplierCode);
//        String cdeptvid = apiCompareService.getTurnByType(convertMainData.getCdeptvid(),Constants.PkOrg);
        storeMainVO.setDeptName(pkOrg);
        //改成传输日期
        //storeMainVO.setBusDate(convertMainData.getDbilldate());
        storeMainVO.setBusDate(DateFormatUtils.format(new Date(),"yyyy-MM-dd"));
        String contractNo = convertMainData.getVcontractcode();
        storeMainVO.setContractNo(StrUtil.isNotBlank(contractNo) && contractNo.startsWith("SG")
                ? contractNo : null);
//        String cstordocid = apiCompareService.getTurnByType(convertMainData.getCstordocid(),Constants.PkStock);
        storeMainVO.setInWarehouse(pkOrg + "001");
        storeMainVO.setMaintMgrObject(convertMainData.getBillmaker());
        storeMainVO.setDocuStatus("00000002");
        storeMainVO.setDirect(String.valueOf(0));
//        storeMainVO.setBiType(convertMainData.getFreplenishflag());
        List<StoreDetailVO> item = new ArrayList<>();
        String estimationProcessingType = "";
        String vsrctype = details.get(0).getVsrctype();
        if(convertMainData.getBestimateflag().equals("Y") && vsrctype.equals("45")) {
            estimationProcessingType = "00000003";
        }else if(convertMainData.getBestimateflag().equals("Y") && vsrctype.equals("27")) {
            estimationProcessingType = "00000002";
        }else if(convertMainData.getBestimateflag().equals("N") && vsrctype.equals("27")) {
            estimationProcessingType = "00000001";
        }
        storeMainVO.setEstimationProcessingType(estimationProcessingType);
        String cbillBid = details.get(0).getCbillBid();
        String freplenishflag = "";
        if(vsrctype.equals("45")) {
            freplenishflag = dmMapper.getFreplenishflagTypeOne(cbillBid);
        }else if(vsrctype.equals("27")) {
            freplenishflag = dmMapper.getFreplenishflagTypeTwo(cbillBid);
        }
        storeMainVO.setBiType(freplenishflag.equals("Y")?"20":"10");
//        ApiCompare apiCompare0 = apiCompareCache.getDictionaryValue(BillTypeEnum.STORE.getCode(), "code", convertMainData.getVbillcode());
//        String purpose = Objects.isNull(apiCompare0)?null:apiCompare0.getTargetSystemId();
//        if(StrUtil.isBlank(purpose)) {
//            throw new CheckedException("用途不能为空");
//        }
        ApiCompare apiCompare1 = apiCompareCache.getDictionaryValue(BillTypeEnum.STORE.getCode(), "keeper", pkOrg);
        String keeper = Objects.isNull(apiCompare1)?null:apiCompare1.getTargetSystemId();
        if(StrUtil.isBlank(keeper)) {
            throw new CheckedException("库管员不能为空,仓库为:"+pkOrg);
        }
        storeMainVO.setUseCustdiaObject(keeper);
        for(FiConvertStoreDetail detail : details) {
            boolean readinessFlag = fiSourceOrderService.getOrderReadiness(detail.getVbillcode());
            if(!readinessFlag) {
                throw new CheckedException("请先推送订单:" + detail.getVbillcode());
            }
            StoreDetailVO storeDetailVO = new StoreDetailVO();
            storeDetailVO.setPurOrd(detail.getVbillcode());
            storeDetailVO.setOrderLineNo(detail.getCrownoOrder());
            if(StrUtil.isNotBlank(detail.getNnum())) {
                BigDecimal inWhsQty = new BigDecimal(detail.getNnum());
                if(storeMainVO.getBiType().equals("20")) {
                    inWhsQty = inWhsQty.abs();
                }
                storeDetailVO.setInWhsQty(inWhsQty);
            }
            if(StrUtil.isNotBlank(detail.getNprice())) {
                BigDecimal inWhsUnitPriceIncTax = new BigDecimal(detail.getNprice());
                storeDetailVO.setInWhsUnitPriceIncTax(inWhsUnitPriceIncTax.abs());
                storeDetailVO.setInWhsUnitPriceExcTax(inWhsUnitPriceIncTax.abs());
            }
            if(StrUtil.isNotBlank(detail.getNmny())) {
                BigDecimal nmny = new BigDecimal(detail.getNmny());
                if(storeMainVO.getBiType().equals("20")) {
                    nmny = nmny.abs();
                }
                storeDetailVO.setTaxAmt(nmny);
                storeDetailVO.setAmtExTax(nmny);
            }
            storeDetailVO.setTaxrt(BigDecimal.ZERO);
            storeDetailVO.setTaxAmount(BigDecimal.ZERO);
//            storeDetailVO.setPrjCode(detail.getCprojectid());
//            storeDetailVO.setPrjCode(apiCompareService.getProjectCode(detail.getCprojectid()));
            if(StrUtil.isBlank(detail.getCinventoryvid())) {
                throw new CheckedException("物料关联为空");
            }
            MaterialVO materialVO = dmMapper.getMaterialByPk(detail.getCinventoryvid());
            if(!YgConfig.environment.equals("dev")) {
                if (null == materialVO || StrUtil.isBlank(materialVO.getCode())) {
                    throw new CheckedException("物料查询为空");
                }
                if (!materialVO.getCode().startsWith("CY")) {
                    throw new CheckedException("物料MDM编码未配置");
                }
            }
            storeDetailVO.setMtCode(materialVO.getCode().substring(2));
//            storeDetailVO.setMtName(materialVO.getName());
            storeDetailVO.setNote("");
            storeDetailVO.setLineNo(detail.getCrowno());
            storeDetailVO.setWbsCod("");
            //todo “用途”对方是必填字段
            storeDetailVO.setPurpose(detail.getVbdef7());
//            storeDetailVO.setInveTyp(StringUtils.isNotEmpty(detail.getCprojectid())?"00000021":"00000001");
            storeDetailVO.setInveTyp("00000001");
            item.add(storeDetailVO);
        }
        storeMainVO.setItem(item);
        return storeMainVO;
    }

    /**
     * 数据转换
     *
     * @param sourceMainData
     * @param details
     */
    private void turnData(FiSourceStore sourceMainData, List<FiSourceStoreDetail> details) throws Exception {
        String pk = sourceMainData.getCbillid();
        FiConvertStore fiConvertStore = new FiConvertStore();
        apiCompareCache.convertData(sourceMainData,fiConvertStore, BillTypeEnum.STORE.getCode());
        FiConvertStore convertMainData = fiConvertStoreService.getOne(Wrappers.<FiConvertStore>lambdaQuery().eq(FiConvertStore::getCbillid, pk));
        fiConvertStore.setId(null);
        if(convertMainData != null) {
            fiConvertStore.setId(convertMainData.getId());
            fiConvertStoreDetailService.remove(Wrappers.<FiConvertStoreDetail>lambdaQuery().eq(FiConvertStoreDetail::getCbillid,pk));
        }
        List<FiConvertStoreDetail> convertDetailList = new ArrayList<>();
        for(FiSourceStoreDetail sourceDetailData : details) {
            FiConvertStoreDetail fiConvertStoreDetail = new FiConvertStoreDetail();
            apiCompareCache.convertData(sourceDetailData,fiConvertStoreDetail,BillTypeEnum.STORE_DETAIL.getCode());
            fiConvertStoreDetail.setId(null);
            convertDetailList.add(fiConvertStoreDetail);
        }
        fiConvertStoreService.saveOrUpdate(fiConvertStore);
        fiConvertStoreDetailService.saveOrUpdateBatch(convertDetailList);
    }

    @Override
    public Page<FiSourceStoreDTO> beforeConvertDataPage(Page page, FiSourceStoreQueryVO queryVO) {
        Page<FiSourceStoreDTO> result = this.baseMapper.beforeConvertDataPage(page,queryVO);
        if (CollectionUtil.isNotEmpty(result.getRecords())){
            for (FiSourceStoreDTO data : result.getRecords()){
                if(StrUtil.isNotBlank(data.getCvendorid())) {
                    data.setCvendorvidName(dmMapper.getSupplierName(data.getCvendorid()));
                }
                if(StrUtil.isNotBlank(data.getBestimateflag())) {
                    if(data.getBestimateflag().equals("Y")) {
                        data.setBestimateflagName("是");
                    }else if(data.getBestimateflag().equals("N")) {
                        data.setBestimateflagName("否");
                    }
                }
                FiSourceStoreDetail detail = fiSourceStoreDetailService.getOne(Wrappers.<FiSourceStoreDetail>lambdaQuery().eq(FiSourceStoreDetail::getCbillid, data.getCbillid()).last("limit 1"));
                if(null != detail) {
                    ApiCompare apiCompare = apiCompareCache.getDictionaryValue(BillTypeEnum.STORE_DETAIL.getCode(), "vbdef7", YgUtil.toStringTrim(detail.getVbdef7()));
                    data.setVbdef7Name(Objects.isNull(apiCompare)?null:apiCompare.getTargetSystemName());
                    data.setOrderNum(detail.getVbillcode());
                }
            }
        }
        return result;
    }

    @Override
    public Page<FiSourceStoreDetailDTO> getBeforeConvertDataPage(Page page, FiSourceStoreQueryVO queryVO) {
        Page<FiSourceStoreDetailDTO> result = this.baseMapper.getBeforeConvertDataPage(page,queryVO);
        if (CollectionUtil.isNotEmpty(result.getRecords())){
            for (FiSourceStoreDetailDTO data : result.getRecords()){
                ApiCompare apiCompare = apiCompareCache.getDictionaryValue(BillTypeEnum.STORE_DETAIL.getCode(), "vbdef7", YgUtil.toStringTrim(data.getVbdef7()));
                data.setVbdef7Name(Objects.isNull(apiCompare)?null:apiCompare.getTargetSystemName());
            }
        }
        return result;
    }

    @Override
    public List<FiConvertStoreDTO> getConvertList(String pk) {
        List<FiConvertStoreDTO> convertDataDTOList = new ArrayList<>();
        FiConvertStoreDTO convertDataDTO = new FiConvertStoreDTO();
        FiConvertStore convertDataOld = fiConvertStoreService.getOne(Wrappers.<FiConvertStore>lambdaQuery().eq(FiConvertStore::getCbillid, pk));
        if(null != convertDataOld) {
            BeanUtil.copyProperties(convertDataOld,convertDataDTO);
            convertDataDTOList.add(convertDataDTO);
        }
        return convertDataDTOList;
    }

    @Override
    public Page<FiConvertStoreDetailDTO> getConvertData(Page page, String pk) {
        Page<FiConvertStoreDetailDTO> result = this.baseMapper.getConvertData(page,pk);
        return result;
    }

    @Override
    public void checkSync(String cbillid) {
        FiSourceStore fiSourceStore = this.getOne(Wrappers.<FiSourceStore>lambdaQuery()
                .eq(FiSourceStore::getCbillid, cbillid));

        if (Objects.isNull(fiSourceStore)) {
            throw new CheckedException(
                    MessageFormat.format("采购入库单未同步成功, 采购入库单不存在，传入cbillid: {0}", cbillid));
        }

        if (Objects.isNull(fiSourceStore.getPushStatus()) ||
                ObjectUtil.notEqual(PushStatusEnum.PUSH_SUCCESS.getCode(), fiSourceStore.getPushStatus())) {
            throw new CheckedException(
                    MessageFormat.format("采购入库单未同步成功, pushStatus: {0}，入库单编码: {1}",
                            fiSourceStore.getPushStatus(), fiSourceStore.getVbillcode()));
        }
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourceStore fiSourceStore = this.getById(id);
            try {
                this.pushAgain(fiSourceStore.getCbillid());
            } catch (Exception e) {
                resultList.add(fiSourceStore.getVbillcode()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}

package com.cloud.ficonsumer.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiSourceFileOcrInfoQueryDTO;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceFileOcrInfoService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.vo.FiSourceFileOcrInfoExportVO;
import com.cloud.ficonsumer.vo.FiSourceFileOcrInfoVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class FiSourceFileOcrInfoExportListener extends AbstractExportListener<FiSourceFileOcrInfoExportVO> {

    private final FiSourceFileOcrInfoService fiSourceFileOcrInfoService;
    private final FiSourceFileOcrInfoQueryDTO queryDTO;
    private final Page<FiSourceFileOcrInfoVO> page;
    private static final int PAGE_SIZE = 1000;

    public FiSourceFileOcrInfoExportListener(UUID id,
                                             FiSourceFileOcrInfoService fiSourceFileOcrInfoService,
                                             Page<FiSourceFileOcrInfoVO> page,
                                             FiSourceFileOcrInfoQueryDTO queryDTO) {
        super(id, "进项发票转换前数据");
        this.page = page;
        this.fiSourceFileOcrInfoService = fiSourceFileOcrInfoService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiSourceFileOcrInfoExportVO> doHandleData() {
        Long orgId = AdminUtils.processToOrgId(queryDTO.getOrgCode());
        queryDTO.setOrgId(orgId);

        List<FiSourceFileOcrInfoVO> vos = new ArrayList<>();

        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiSourceFileOcrInfoVO> pageResult = fiSourceFileOcrInfoService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiSourceFileOcrInfoVO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiSourceFileOcrInfoVO> pageResult;
            do {
                pageResult = fiSourceFileOcrInfoService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }

        List<FiSourceFileOcrInfoExportVO> result = BeanUtil.copyToList(vos, FiSourceFileOcrInfoExportVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}
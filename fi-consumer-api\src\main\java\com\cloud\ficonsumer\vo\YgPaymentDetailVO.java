package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/24.
 */
@Data
public class YgPaymentDetailVO {

    /**
     * 台账GID
     */
    @ApiModelProperty(value="台账GID")
    private String associatId;

    /**
     * 来源单据类型
     */
    @ApiModelProperty(value="来源单据类型")
    private String sourDocType;

    /**
     * 来源业务主键
     */
    @ApiModelProperty(value="来源业务主键")
    private String sourBusId;

    /**
     * 来源单据编号
     */
    @ApiModelProperty(value="来源单据编号")
    private String sourDocNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value="供应商名称")
    private String supp;

    /**
     * 付款性质
     */
    @ApiModelProperty(value="付款性质")
    private String payType;

    /**
     * 合同名称
     */
    @ApiModelProperty(value="合同名称")
    private String conNam;

    /**
     * 项目名称
     */
    @ApiModelProperty(value="项目名称")
    private String prjName;

    /**
     * 业务事项
     */
    @ApiModelProperty(value="业务事项")
    private String cosType;

    /**
     * 明细业务事项名称
     */
    @ApiModelProperty(value="明细业务事项名称")
    private String detCosType;

    /**
     * 明细业务事项编码
     */
    @ApiModelProperty(value="明细业务事项编码")
    private String detCosTypeCode;

    /**
     * 起租编号
     */
    @ApiModelProperty(value="起租编号")
    private String hiredCode;

    /**
     * 承租人
     */
    @ApiModelProperty(value="承租人")
    private String lesseePers;

    /**
     * 实际承租人
     */
    @ApiModelProperty(value="实际承租人")
    private String actualLessee;

    /**
     * 客户类别
     */
    @ApiModelProperty(value="客户类别")
    private String customerCate;

    /**
     * 渠道
     */
    @ApiModelProperty(value="渠道")
    private String irrigDitch;

    /**
     * 产品服务
     */
    @ApiModelProperty(value="产品服务")
    private String prodAndSer;

    /**
     * 产品类型
     */
    @ApiModelProperty(value="产品类型")
    private String prdType;

    /**
     * 本次付款金额
     */
    @ApiModelProperty(value="本次付款金额")
    private String payAmo;

    /**
     * 民工工资账户标识
     */
    @ApiModelProperty(value="民工工资账户标识")
    private String migrantAccountSign;

    /**
     * 收款方账户
     */
    @ApiModelProperty(value="收款方账户")
    private String rcvrAcc;

    /**
     * 票据收款方账户
     */
    @ApiModelProperty(value="票据收款方账户")
    private String billRcvrAcc;

    /**
     * 收款钱包账户
     */
    @ApiModelProperty(value="收款钱包账户")
    private String collWalId;

    /**
     * 对账编号
     */
    @ApiModelProperty(value="对账编号")
    private String chkId;

    /**
     * 本次扣留质保金
     */
    @ApiModelProperty(value="本次扣留质保金")
    private String dedTimeAmo;

    /**
     * 原支付计划号
     */
    @ApiModelProperty(value="原支付计划号")
    private String oriPayPlanNo;

}

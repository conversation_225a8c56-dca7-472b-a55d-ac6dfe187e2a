package com.cloud.ficonsumer;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillQueryDTO;
import com.cloud.ficonsumer.entity.FiSourcePuPaybill;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.service.FiSourcePuPaybillService;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.FiSourcePuPaybillVO;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.Connection;
import java.sql.Statement;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class PuPaybillPushTest {

    @SpyBean
    private DmMapper dmMapper;

    @SpyBean
    private ApiProjectService apiProjectService;

    @SpyBean
    private FiSourcePuPaybillService fiSourcePuPaybillService;

    @SpyBean
    private ApiCompareCache apiCompareCache;

    private String ygSuccessResp;
    private String ygFailResp;

    @BeforeAll
    public void mockResp() throws Exception {
        ygSuccessResp = new String(Files.readAllBytes(new ClassPathResource("YgSuccessResp.json").getFile().toPath()), StandardCharsets.UTF_8);
        ygFailResp = new String(Files.readAllBytes(new ClassPathResource("YgFailResp.json").getFile().toPath()), StandardCharsets.UTF_8);
    }

    @BeforeEach
    public void initData() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);

        // 初始化 erp 库数据：采购付款 oracle 数据库
        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator erpPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("pupaybill/schema-oracle.sql"),
                    new ClassPathResource("pupaybill/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(erpPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 初始化 mysql 库数据：采购付款 mysql 数据库
        DynamicDataSourceContextHolder.push("mysql");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("pupaybill/schema-mysql.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 同步 ERP 采购付款到 MySQL
        List<FiSourcePuPaybillVO> fiSourcePuPaybillList = dmMapper.listPuPaybill(new FiSourcePuPaybillQueryDTO());
        for (FiSourcePuPaybillVO fiSourcePuPaybill : fiSourcePuPaybillList) {
            apiProjectService.processPuPaybill(fiSourcePuPaybill);
        }
    }

    @AfterEach
    public void cleanData() {
        DynamicRoutingDataSource dynamicDataSource = (DynamicRoutingDataSource) SpringContextHolder.getBean(DataSource.class);

        // 清理 erp 库
        DataSource erpDataSource = dynamicDataSource.getDataSource("erp");
        try (Connection conn = erpDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 ERP 数据库失败", e);
        }

        // 清理 mysql 库
        DataSource mysqlDataSource = dynamicDataSource.getDataSource("mysql");
        try (Connection conn = mysqlDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 MySQL 数据库失败", e);
        }
    }

    @Test
    public void testPushSuccess() {
        String pkSettlement = "1001A110000000GZCBGF";

        try (MockedStatic<YgUtil> ygUtil = mockStatic(YgUtil.class)) {
            ygUtil.when(() -> YgUtil.sendToYg(any(), any())).thenReturn(ygSuccessResp);

            // 执行推送
            boolean result = fiSourcePuPaybillService.push(pkSettlement);
            assertTrue(result, "推送成功");

            // 验证数据库状态
            FiSourcePuPaybill updatedSource = fiSourcePuPaybillService.getOne(
                    Wrappers.<FiSourcePuPaybill>lambdaQuery()
                            .eq(FiSourcePuPaybill::getPkSettlement, pkSettlement)
            );
            assertEquals("0", updatedSource.getIntegrationStatus(), "集成状态应为成功");
            assertEquals(PushStatusEnum.PUSH_SUCCESS.getCode(), updatedSource.getPushStatus(), "推送状态应为成功");
            assertEquals(1L, updatedSource.getReadiness(), "readiness 值不正确");

            // 验证平台日志
            List<PlatFormLogVO> logs = dmMapper.selectPlatFormLog(pkSettlement, null);
            assertFalse(logs.isEmpty(), "平台日志为空");
            assertEquals(Constants.SUCCESS, logs.get(0).getCode(), "日志状态应为成功");
            assertEquals(updatedSource.getPkTradetype(), logs.get(0).getPkBillType(), "日志单据类型不正确");
        }
    }

    @Test
    public void testConvertFail() {
        String pkSettlement = "1001A110000000GZCBGF";

        // 模拟数据转换异常
        doThrow(new CheckedException("采购付款集成信息转换失败"))
                .when(apiCompareCache).convertData(any(), any(), any());

        // 执行推送并验证异常
        CheckedException exception = assertThrows(CheckedException.class,
                () -> fiSourcePuPaybillService.push(pkSettlement));
        assertTrue(exception.getMessage().contains("采购付款集成信息转换失败"), "异常消息不匹配");

        // 验证数据库状态
        FiSourcePuPaybill source = fiSourcePuPaybillService.getOne(
                Wrappers.<FiSourcePuPaybill>lambdaQuery()
                        .eq(FiSourcePuPaybill::getPkSettlement, pkSettlement));
        assertEquals("1", source.getIntegrationStatus(), "集成状态不正确");
        assertEquals(PushStatusEnum.CONVERT_FAIL.getCode(), source.getPushStatus(), "推送状态应为转换失败");
        assertNotNull(source.getPushMsg(), "推送错误消息应不为空");
    }

    @Test
    public void testPushFail() {
        String pkPaybill = "1001A110000000GZCBGF";

        try (MockedStatic<YgUtil> ygUtil = mockStatic(YgUtil.class)) {
            ygUtil.when(() -> YgUtil.sendToYg(any(), any())).thenReturn(ygFailResp);

            // 执行推送并验证异常
            CheckedException exception = assertThrows(CheckedException.class,
                    () -> fiSourcePuPaybillService.push(pkPaybill));
            assertTrue(exception.getMessage().contains("响应失败，响应结果: "), "异常消息不匹配");

            // 验证数据库状态
            FiSourcePuPaybill updatedSource = fiSourcePuPaybillService.getOne(
                    Wrappers.<FiSourcePuPaybill>lambdaQuery()
                            .eq(FiSourcePuPaybill::getPkSettlement, pkPaybill)
            );
            assertEquals("1", updatedSource.getIntegrationStatus(), "集成状态不正确");
            assertEquals(PushStatusEnum.PUSH_FAIL.getCode(), updatedSource.getPushStatus(), "推送状态应为推送失败");

            // 验证平台日志
            List<PlatFormLogVO> logs = dmMapper.selectPlatFormLog(pkPaybill, null);
            assertFalse(logs.isEmpty(), "平台日志为空");
            assertEquals(Constants.FAIL, logs.get(0).getCode(), "日志CODE不正确");
            assertEquals(updatedSource.getPkTradetype(), logs.get(0).getPkBillType(), "日志类型不正确");
            assertTrue(logs.get(0).getMsg().contains("响应失败，响应结果: "), "日志消息不包含失败信息");
        }
    }
}
package com.cloud.ficonsumer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/12.
 */
@Component
@ConfigurationProperties(prefix = "yg")
public class YgConfig {

    public static boolean enable;
    public static boolean orgEnable;
    public static String allOrgCode;
    public static String environment;
    public static String ak;
    public static String sk;
    public static String gatewayHost;
    public static String loginOrgCode;
    public static String imageType;
    public static String fromFileSystemId;
    public static String invoType;
    public static String innercode;
    public static String storeGrabTime;
    public static String receivableGrabTime;
    public static String puPayableGrabTime;
    public static String puPaybillGrabTime;
    public static String orderGrabTime;

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        YgConfig.environment = environment;
    }

    public boolean getEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public boolean isOrgEnable() {
        return orgEnable;
    }

    public void setOrgEnable(boolean orgEnable) {
        this.orgEnable = orgEnable;
    }

    public String getAllOrgCode() {
        return allOrgCode;
    }

    public void setAllOrgCode(String allOrgCode) {
        YgConfig.allOrgCode = allOrgCode;
    }

    public String getAk() {
        return ak;
    }

    public void setAk(String ak) {
        this.ak = ak;
    }

    public String getSk() {
        return sk;
    }

    public void setSk(String sk) {
        this.sk = sk;
    }

    public String getGatewayHost() {
        return gatewayHost;
    }

    public void setGatewayHost(String gatewayHost) {
        this.gatewayHost = gatewayHost;
    }

    public String getLoginOrgCode() {
        return loginOrgCode;
    }

    public void setLoginOrgCode(String loginOrgCode) {
        this.loginOrgCode = loginOrgCode;
    }

    public String getImageType() {
        return imageType;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    public String getFromFileSystemId() {
        return fromFileSystemId;
    }

    public void setFromFileSystemId(String fromFileSystemId) {
        this.fromFileSystemId = fromFileSystemId;
    }

    public String getInvoType() {
        return invoType;
    }

    public void setInvoType(String invoType) {
        this.invoType = invoType;
    }

    public String getInnercode() {
        return innercode;
    }

    public void setInnercode(String innercode) {
        this.innercode = innercode;
    }

    public String getStoreGrabTime() {
        return storeGrabTime;
    }

    public void setStoreGrabTime(String storeGrabTime) {
        YgConfig.storeGrabTime = storeGrabTime;
    }

    public String getReceivableGrabTime() {
        return receivableGrabTime;
    }

    public void setReceivableGrabTime(String receivableGrabTime) {
        YgConfig.receivableGrabTime = receivableGrabTime;
    }

    public String getPuPayableGrabTime() {
        return puPayableGrabTime;
    }

    public void setPuPayableGrabTime(String puPayableGrabTime) {
        YgConfig.puPayableGrabTime = puPayableGrabTime;
    }

    public String getPuPaybillGrabTime() {
        return puPaybillGrabTime;
    }

    public void setPuPaybillGrabTime(String puPaybillGrabTime) {
        YgConfig.puPaybillGrabTime = puPaybillGrabTime;
    }

    public String getOrderGrabTime() {
        return orderGrabTime;
    }

    public void setOrderGrabTime(String orderGrabTime) {
        YgConfig.orderGrabTime = orderGrabTime;
    }
}

package com.cloud.ficonsumer.controller;

import cn.hutool.core.io.FileUtil;
import com.amazonaws.services.s3.model.S3Object;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.cloud.apiexchange.client.message.CloudMqMessage;
import com.cloud.apiexchange.client.producer.CloudMqProducer;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.common.oss.service.OssTemplate;
import com.cloud.ficonsumer.dto.FiSourceCostOrderDTO;
import com.cloud.ficonsumer.dto.*;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDTO;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.mapper.FileManageMapper;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.utils.UdsUtil;
import com.cloud.ficonsumer.vo.FiSourceFileInfoVO;
import com.sgcc.uds.cloud.sdk.action.result.CommonActionResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


/**
 * 公共测试接口
 *
 * <AUTHOR>
 * @date 2025-01-16 16:53:16
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/common")
@Api(value = "common", tags = "公共测试接口")
public class CommonController {

    private final ApiProjectService apiProjectService;
    private final FileManageMapper fileManageMapper;
    private final OssTemplate ossTemplate;

    /**
     * 推送文件测试
     *
     * @return
     */
    @ApiOperation(value = "推送文件测试", notes = "推送文件测试")
    @PostMapping("/pushFile")
    public R<CommonActionResult> pushFile(MultipartFile file, String metaTypeName) {
        CommonActionResult commonActionResult = null;
        try {
            String extName = FileUtil.extName(file.getOriginalFilename());
            commonActionResult = UdsUtil.pushFileToUds(file.getInputStream(), "推送文件测试", extName, metaTypeName);
        } catch (Exception e) {
            throw new CheckedException("文件推送异常:" + e.getMessage());
        }
        return R.ok(commonActionResult);
    }

    /**
     * 测试进项发票入池文件推送OSS
     *
     * @param file       文件
     * @param bucketName S3存储桶名称
     * @return 推送结果
     */
    @ApiOperation(value = "测试进项发票入池文件推送OSS", notes = "测试进项发票入池文件推送OSS")
    @PostMapping("/testFilePushToOSS")
    public R<Void> testInvoiceFilePush(@RequestParam("file") MultipartFile file,
                                       @RequestParam("bucketName") String bucketName) {
        if (file == null || file.isEmpty()) {
            throw new CheckedException("上传文件不能为空");
        }
        if (StringUtils.isBlank(bucketName)) {
            throw new CheckedException("存储桶名称不能为空");
        }

        try {
            // 上传文件到S3
            String fileName = file.getOriginalFilename();
            ossTemplate.putObject(bucketName, fileName, file.getInputStream());

            // 获取S3对象
            S3Object object = ossTemplate.getObject(bucketName, fileName);
            if (object == null) {
                throw new CheckedException("文件上传失败，未找到对应的S3文件对象");
            }

            log.info("文件上传OSS成功，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("文件推送异常", e);
            throw new CheckedException("文件推送异常: " + e.getMessage());
        }

        return R.ok();
    }

    /**
     * 测试进项发票入池文件推送UDS
     *
     * @param fileId 文件ID
     * @return 推送结果
     */
    @ApiOperation(value = "测试进项发票入池文件推送UDS", notes = "测试进项发票入池文件推送UDS")
    @GetMapping("/testFilePushToUDS/{fileId}")
    public R<CommonActionResult> testInvoiceFilePush(@PathVariable Long fileId) {
        if (fileId == null) {
            throw new CheckedException("文件ID不能为空");
        }

        // 获取文件信息
        List<FiSourceFileInfoVO> fileInfos = fileManageMapper.getFileInfos(fileId);
        if (CollectionUtils.isEmpty(fileInfos)) {
            throw new CheckedException("未找到对应的文件信息，fileId: " + fileId);
        }

        CommonActionResult result;
        try {
            // 获取S3对象
            FiSourceFileInfoVO fileInfo = fileInfos.get(0);
            S3Object object = ossTemplate.getObject(fileInfo.getBucketName(), fileInfo.getFileName());
            if (object == null) {
                throw new CheckedException("未找到对应的S3文件对象");
            }

            // 推送文件到UDS
            String fileName = fileInfo.getFileName();
            String extName = FileUtil.extName(fileName);
            result = UdsUtil.pushFileToUds(
                    object.getObjectContent(),
                    fileName,
                    extName,
                    "YJCWXT_FPWJ"
            );

            log.info("文件推送成功，文件名: {}, 结果: {}", fileName, result);
        } catch (Exception e) {
            log.error("文件推送异常", e);
            throw new CheckedException("文件推送异常: " + e.getMessage());
        }

        return R.ok(result);
    }

    /**
     * 手动触发订单数据的定时任务
     *
     * @return
     */
    @ApiOperation(value = "手动触发订单数据的定时任务", notes = "手动触发订单数据的定时任务")
    @GetMapping("/getOrderList")
    public R<Boolean> getOrderList(FiSourceOrderDTO fiSourceOrderDTO) {
        apiProjectService.getOrderList(fiSourceOrderDTO);
        return R.ok(true);
    }

    @GetMapping("/getCostOrderList")
    public R<Boolean> getCostOrderList(FiSourceCostOrderDTO fiSourceCostOrderDTO) {
        apiProjectService.getCostOrderList(fiSourceCostOrderDTO);
        return R.ok(true);
    }

    @ApiOperation(value = "手动触发订单数据的定时任务", notes = "手动触发订单数据的定时任务")
    @GetMapping("/getVirtualOrderList")
    public R<Boolean> getVirtualOrderList(FiSourceVirtualOrderDTO fiSourceVirtualOrderDTO) {
        apiProjectService.getVirtualOrderList(fiSourceVirtualOrderDTO);
        return R.ok(true);
    }

    /**
     * 通用报销单接口数据（通用报账）
     *
     * @return
     */
    @ApiOperation(value = "通用报销单接口数据", notes = "通用报销单接口数据")
    @GetMapping("/getReimbursementList")
    public R<Boolean> getReimbursementList(FiSourceReimbursementDTO fiSourceReimbursementDTO) {
        apiProjectService.getReimbursementList(fiSourceReimbursementDTO);
        return R.ok(true);
    }

    /**
     * 项目应收数据（承包业务）
     *
     * @return
     */
    @ApiOperation(value = "项目应收数据", notes = "项目应收数据")
    @GetMapping("/getReceivableList")
    public R<Boolean> getReceivableList(FiSourceReceivableDTO fiSourceReceivableDTO) {
        apiProjectService.getReceivableList(fiSourceReceivableDTO);
        return R.ok(true);
    }

    /**
     * 手动触发进项发票采集增量推送
     *
     * @return 处理结果
     */
    @ApiOperation(value = "手动触发进项发票采集增量推送", notes = "手动触发进项发票采集-根据比对一致和人票关联数据创建进项发票入池信息服务")
    @GetMapping("/pushFileOcrInfo")
    public R<Boolean> pushFileOcrInfo() {
        try {
            log.info("开始手动触发进项发票采集增量推送");
            apiProjectService.incrementalPushFileOcrInfo();
            log.info("手动触发进项发票采集增量推送完成");
            return R.ok(true);
        } catch (Exception e) {
            log.error("进项发票采集增量推送异常", e);
            throw new CheckedException("进项发票采集增量推送异常: " + e.getMessage());
        }
    }

    /**
     * 测试采购发票校验申请服务
     *
     * @param queryDTO 查询参数
     * @return 处理结果
     */
    @ApiOperation(value = "测试采购发票校验申请服务", notes = "测试采购发票校验申请服务-增量推送")
    @PostMapping("/testPuPayablePush")
    public R<Boolean> testPuPayablePush(@RequestBody FiSourcePuPayableQueryDTO queryDTO) {
        log.info("开始测试采购发票校验申请服务推送, 参数: {}", queryDTO);
        apiProjectService.incrementalPushPuPayable(queryDTO);
        log.info("测试采购发票校验申请服务推送完成");
        return R.ok(true);
    }

    /**
     * 测试MQ
     *
     * @return
     */
    @ApiOperation(value = "测试MQ", notes = "测试MQ")
    @GetMapping("/testMQ")
    public R<Boolean> testMQ() {
        CloudMqMessage cloudMqMessage = new CloudMqMessage();
        cloudMqMessage.setTopic(Constants.TOPIC_ORDER_TRANS);
        cloudMqMessage.setMsgGroup(Constants.Group.group);
        cloudMqMessage.setMsgData("11111111111");
        CloudMqProducer.produce(cloudMqMessage);
        return R.ok(true);
    }

    /**
     * 定时拉取增量的付款单数据
     *
     * @return
     */
    @ApiOperation(value = "定时拉取增量的付款单数据", notes = "定时拉取增量的付款单数据")
    @GetMapping("/getPuPaybillList")
    public R<Boolean> getPuPaybillList(FiSourcePuPaybillQueryDTO queryDTO) {
        apiProjectService.incrementalPushPuPaybill(queryDTO);
        return R.ok(true);
    }

    /**
     * 定时拉取增量的采购入库数据
     *
     * @return
     */
    @ApiOperation(value = "定时拉取增量的采购入库数据", notes = "定时拉取增量的采购入库数据")
    @GetMapping("/getStoreLists")
    public R<Boolean> getStoreList(FiSourceStoreDTO fiSourceStoreDTO) {
        apiProjectService.getStoreList(fiSourceStoreDTO);
        return R.ok(true);
    }


    /**
     * 定时拉取会计凭证
     *
     * @return
     */
    @ApiOperation(value = "定时拉取会计凭证", notes = "定时拉取会计凭证")
    @GetMapping("/getAccountingList")
    public R<Boolean> getAccountingList(FiSourceAccountingDTO fiAccountingDTO) {
        apiProjectService.getAccountingList(fiAccountingDTO);
        return R.ok(true);
    }
}

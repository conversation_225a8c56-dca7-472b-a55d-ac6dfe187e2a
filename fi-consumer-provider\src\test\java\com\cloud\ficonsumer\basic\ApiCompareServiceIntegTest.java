package com.cloud.ficonsumer.basic;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.FiConsumerApplication;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.service.ApiCompareService;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ApiCompareServiceIntegTest {

    @Autowired
    private ApiCompareService apiCompareService;

    @BeforeAll
    void setUp() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);
        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator populator = new ResourceDatabasePopulator(
                    // 基础资料
                    new ClassPathResource("basic/schema-oracle.sql"),
                    new ClassPathResource("basic/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(populator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    @Test
    @DisplayName("测试根据项目ID获取项目编码")
    void testGetTurnByTypeForProject() {
        // 准备测试数据
        String pkProject = "1001A710000000LD3VKV";
        
        // 执行测试
        String result = apiCompareService.getTurnByType(pkProject, Constants.project);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("870000Z202504002", result);
    }

    @Test
    @DisplayName("测试根据供应商ID获取供应商编码")
    void testGetTurnByTypeForSupplier() {
        // 准备测试数据
        String pkSupplier = "1001A1100000000I8JIF";
        
        // 执行测试
        String result = apiCompareService.getTurnByType(pkSupplier, Constants.PkSupplier);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("1000066412", result);
    }

    @Test
    @DisplayName("测试获取不存在的项目编码时抛出异常")
    void testGetTurnByTypeForNonExistentProject() {
        // 准备测试数据
        String nonExistentProjectId = "NONEXISTENT";
        
        // 验证异常抛出
        CheckedException exception = assertThrows(CheckedException.class, () ->
                apiCompareService.getTurnByType(nonExistentProjectId, Constants.project)
        );
        assertEquals("项目编码查询为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试获取不存在的供应商编码时抛出异常")
    void testGetTurnByTypeForNonExistentSupplier() {
        // 准备测试数据
        String nonExistentSupplierId = "NONEXISTENT";
        
        // 验证异常抛出
        CheckedException exception = assertThrows(CheckedException.class, () ->
            apiCompareService.getTurnByType(nonExistentSupplierId, Constants.PkSupplier)
        );
        assertEquals("供应商MDM编码查询为空", exception.getMessage());
    }
}
package com.cloud.ficonsumer.integration.dto;

import cn.hutool.core.date.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 参数格式
 */
@Data
public class Param <T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private ParamHeader head;
    private ParamBody<T> body;

    /**
     * 返回成功
     * @param result  请求参数
     * @return
     */
    public static <T> Param<T> ok(Param result) {
        Param<T> apiResult = new Param<T>();
        ParamHeader head = new ParamHeader();
        head.setSeqNo(result.getHead().getSeqNo());
        head.setResponseCode("10000");
        apiResult.setHead(head);

        ParamBody<T> body = new ParamBody<T>();
        apiResult.setBody(body);
        return apiResult;
    }

    /**
     * 返回成功
     * @param result 请求参数
     * @param msg 响应信息
     * @return
     */
    public static <T> Param<T> ok(Param result,String msg) {
        Param<T> apiResult = new Param<T>();
        ParamHeader head = new ParamHeader();
        head.setSeqNo(result.getHead().getSeqNo());
        head.setResponseCode("10000");
        head.setResponseMsg(msg);
        apiResult.setHead(head);

        ParamBody<T> body = new ParamBody<T>();
        apiResult.setBody(body);
        return apiResult;
    }

    /**
     * 返回成功
     * @param result 请求参数
     * @param data 响应参数
     * @param msg 响应信息
     * @return
     */
    public static <T> Param<T> ok(Param result,T data, String msg) {
        Param<T> apiResult = new Param<T>();
        ParamHeader head = new ParamHeader();
        head.setSeqNo(result.getHead().getSeqNo());
        head.setResponseCode("10000");
        head.setResponseMsg(msg);
        apiResult.setHead(head);

        ParamBody<T> body = new ParamBody<T>();
        body.setData(data);
        apiResult.setBody(body);
        return apiResult;
    }


    /**
     * 返回失败
     * @param result 请求参数
     * @param msg 响应信息
     * @return
     */
    public static <T> Param<T> failed(Param result,String msg) {
        Param<T> apiResult = new Param<T>();
        ParamHeader head = new ParamHeader();
        head.setSeqNo(result.getHead().getSeqNo());
        head.setResponseCode("20000");
        head.setResponseMsg(msg);
        apiResult.setHead(head);

        ParamBody<T> body = new ParamBody<T>();
        apiResult.setBody(body);
        return apiResult;
    }

    /**
     * 返回失败
     * @param result 请求参数
     * @param data  响应参数
     * @param msg 响应信息
     * @return
     */
    public static <T> Param<T> failed(Param result,T data,String msg) {
        Param<T> apiResult = new Param<T>();
        ParamHeader head = new ParamHeader();
        head.setSeqNo(result.getHead().getSeqNo());
        head.setResponseCode("20000");
        head.setResponseMsg(msg);
        apiResult.setHead(head);

        ParamBody<T> body = new ParamBody<T>();
        body.setData(data);
        apiResult.setBody(body);
        return apiResult;
    }

    /**
     * 构造请求参数
     * @param servCode  服务端编码
     * @param accessKey 密钥
     * @param data  入参
     * @return
     */
    public static  <T> Param<T> addData(String servCode,String accessKey,T data) {
        Param<T> apiResult = new Param<T>();
        ParamHeader head = new ParamHeader();
        Date date = new Date();
        head.setServCode(servCode);
        head.setAccessKey(accessKey);
        head.setRequestDate(DateUtil.format(date, "yyyy-MM-dd"));
        head.setRequestTime(DateUtil.format(date, "HH:mm:ss.SSS"));
        head.setResponseDate(null);
        head.setResponseTime(null);
        apiResult.setHead(head);

        ParamBody<T> body = new ParamBody<T>();
        body.setData(data);
        apiResult.setBody(body);
        return apiResult;
    }
}

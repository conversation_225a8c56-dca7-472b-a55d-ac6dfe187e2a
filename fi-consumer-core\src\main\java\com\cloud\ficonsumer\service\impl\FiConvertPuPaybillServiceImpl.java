package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertPuPaybill;
import com.cloud.ficonsumer.mapper.FiConvertPuPaybillMapper;
import com.cloud.ficonsumer.service.FiConvertPuPaybillService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiConvertPuPaybillServiceImpl extends ServiceImpl<FiConvertPuPaybillMapper, FiConvertPuPaybill> implements FiConvertPuPaybillService {

}

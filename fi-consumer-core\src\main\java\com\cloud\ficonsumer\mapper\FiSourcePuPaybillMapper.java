package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillDetailQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPaybillQueryDTO;
import com.cloud.ficonsumer.entity.FiSourcePuPaybill;
import com.cloud.ficonsumer.vo.FiConvertPuPaybillDetailVO;
import com.cloud.ficonsumer.vo.FiConvertPuPaybillVO;
import com.cloud.ficonsumer.vo.FiSourcePuPaybillDetailVO;
import com.cloud.ficonsumer.vo.FiSourcePuPaybillVO;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 采购付款源数据主表
 */
@Mapper
public interface FiSourcePuPaybillMapper extends CloudBaseMapper<FiSourcePuPaybill> {

    Page<FiSourcePuPaybillVO> beforeConvertDataPage(Page<FiSourcePuPaybillVO> page, @Param("param") FiSourcePuPaybillQueryDTO queryDTO);

    Page<FiSourcePuPaybillDetailVO> getBeforeConvertDataPage(Page<FiSourcePuPaybillDetailVO> page, @Param("param") FiSourcePuPaybillDetailQueryDTO queryDTO);

    List<FiConvertPuPaybillVO> getConvertList(@Param("pkSettlement") String pkPaybill);

    Page<FiConvertPuPaybillDetailVO> getConvertData(Page<FiConvertPuPaybillDetailVO> page, @Param("pkSettlement") String pkPaybill);

    /**
     * 查询未记账的预付款付款单明细
     * <p>
     * 查询条件：
     * 1. 明细表预付款标识为1
     * 2. 主表未记账(return_status不为4或为空)
     * 3. 源单据ID在指定列表中
     *
     * @param queryDTO 查询参数
     * @return 返回Map结构，key为源单据ID(src_billid)，value为付款单明细信息
     */
    @MapKey("srcBillid")
    Map<String, FiSourcePuPaybillDetailVO> getNoAccountedPrepayBySrcBillIds(@Param("query") FiSourcePuPaybillQueryDTO queryDTO);
}

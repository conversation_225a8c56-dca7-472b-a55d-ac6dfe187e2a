package com.cloud.ficonsumer.controller;

import cn.hutool.core.lang.UUID;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.cloud.cloud.admin.api.entity.SysDictItem;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.enums.ImportTemplateEnum;
import com.cloud.ficonsumer.mapper.ApiUpmsMapper;
import com.cloud.ficonsumer.service.ImportService;
import com.cloud.ficonsumer.utils.ExcelUtils;
import com.cloud.ficonsumer.vo.ExcelImportStatusVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: cyz
 * @Descrition: 导入excel文件
 * @Date: 2023/07/03
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/importNew")
@Api(value = "导入excel文件", tags = "导入excel文件")
public class ImportNewController {
    private final ImportService importService;

    private final RedisTemplate redisTemplate;
    private final ApiUpmsMapper apiUpmsMapper;



    /**
     * 下载导入模板
     *
     * @param fileName
     */
    @ApiOperation(value = "下载导入模板，supplierframecontract_import_template：分包框架合同导入模板，"
            , notes = "下载导入模板")
    @GetMapping("/download/{fileName}")
    public void file(@PathVariable String fileName, HttpServletResponse response) {
        try {
            String name = "templates/" + fileName + ExcelTypeEnum.XLSX.getValue();
            InputStream inputStream = Thread.currentThread().getContextClassLoader()
                    .getResourceAsStream(name);
            ExcelUtils.downloadExcel(inputStream, ImportTemplateEnum.getName(fileName), response);
        } catch (Exception e) {
            throw new CheckedException("模板下载出错！");
        }
    }

    /**
     * 获取导入文件进度
     *
     * @param id
     */
    @ApiOperation(value = "获取导入文件进度", notes = "获取导入文件进度")
    @GetMapping("/progress/{id}")
    public R<ExcelImportStatusVO> getImportExcelStatus(@PathVariable String id) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        ExcelImportStatusVO vo = (ExcelImportStatusVO) valueOperations.get(ExcelUtils.EXCEL_IMPORT + id);
        return R.ok(vo);
    }


    /**
     * 对照数据导入
     *
     * @param file
     */
    @ApiOperation(value = "对照数据导入", notes = "对照数据导入")
    @PostMapping("/apiCompareImport")
    public R<String> apiCompareImport(@RequestBody MultipartFile file) {
        UUID id = UUID.randomUUID();
        ExcelUtils.checkFile(file);
        InputStream is;
        try {
            is = file.getInputStream();
        } catch (IOException e) {
            throw new CheckedException("上传文件错误");
        }
        String redisKey = ExcelUtils.EXCEL_IMPORT + id;
        ExcelImportStatusVO excelStatusVO = new ExcelImportStatusVO();
        ValueOperations valueOperations = redisTemplate.opsForValue();
        valueOperations.set(redisKey, excelStatusVO, Duration.ofDays(1));
        List<SysDictItem> fileTypeDictItems = apiUpmsMapper.getFileTypeDictItems();
        Map<String, String> collect = fileTypeDictItems.stream().collect(Collectors.toMap(SysDictItem::getLabel, SysDictItem::getValue, (key1, key2) -> key2));
        importService.apiCompareImport(is, id,collect);

        return R.ok(id.toString());
    }

}

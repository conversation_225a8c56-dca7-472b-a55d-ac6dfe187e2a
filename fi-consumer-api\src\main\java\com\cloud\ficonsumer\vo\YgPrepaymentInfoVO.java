package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "预付款冲销信息")
public class YgPrepaymentInfoVO {

    @ApiModelProperty(value = "预付单据ID")
    private String sourceBusId;
    
    @ApiModelProperty(value = "供应商名称")
    private String supp;
    
    @ApiModelProperty(value = "已冲销金额")
    private BigDecimal deducteAmo;
    
    @ApiModelProperty(value = "预付款余额")
    private BigDecimal prePmtBal;
    
    @ApiModelProperty(value = "本次冲销金额")
    private BigDecimal dedTimeAmo;
    
    @ApiModelProperty(value = "预付款日期")
    private String advPayDate;
}
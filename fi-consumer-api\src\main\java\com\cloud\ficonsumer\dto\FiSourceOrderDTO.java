package com.cloud.ficonsumer.dto;

import com.cloud.ficonsumer.entity.FiSourceOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/7.
 */
@Data
public class FiSourceOrderDTO extends FiSourceOrder {

    /**
     * 单据爬取时间
     */
    @ApiModelProperty(value="单据爬取时间")
    private String grabTime;

    /**
     * 币种中文
     */
    @ApiModelProperty(value="币种中文")
    private String corigcurrencyidName;

    @ApiModelProperty(value="单据类型名称")
    private String vtrantypecodeName;

    @ApiModelProperty(value="供应商名称")
    private String pkSupplierName;
}


package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.entity.FiOrgCompare;
import com.cloud.ficonsumer.mapper.FiOrgCompareMapper;
import com.cloud.ficonsumer.service.FiOrgCompareService;
import com.cloud.ficonsumer.vo.FiOrgCompareVO;
import com.cloud.ficonsumer.vo.FiOrgListVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 组织对照关系
 *
 * <AUTHOR>
 * @date 2025-04-27 10:55:07
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiOrgCompareServiceImpl extends ServiceImpl<FiOrgCompareMapper, FiOrgCompare> implements FiOrgCompareService {

    @Override
    public List<FiOrgCompare> getGroupMap() {
        return this.baseMapper.getGroupMap();
    }

    /**
     * 根据当前登录组织的departCode字段获取组织树
     * @param departCode
     * @return
     */
    @Override
    public FiOrgListVO getOrgList(String departCode) {
        FiOrgListVO fiOrgListVO = new FiOrgListVO();
        List<FiOrgCompareVO> groupList = new ArrayList<>();
        List<FiOrgCompare> orgList = new ArrayList<>();
        FiOrgCompare fiOrgCompare = this.getOne(Wrappers.<FiOrgCompare>lambdaQuery().eq(FiOrgCompare::getOrgCode, departCode));
        if(null == fiOrgCompare) {
            throw new CheckedException("当前组织不在配置的组织范围内");
        }
        if(fiOrgCompare.getLevel().equals("0")) {
            FiOrgCompareVO fiOrgCompareVO = new FiOrgCompareVO();
            FiOrgCompare groupVO = this.getOne(Wrappers.<FiOrgCompare>lambdaQuery().eq(FiOrgCompare::getGroupCode, fiOrgCompare.getGroupCode())
                    .eq(FiOrgCompare::getLevel, "1"));
            BeanUtil.copyProperties(groupVO,fiOrgCompareVO);
            orgList.add(fiOrgCompare);
            fiOrgCompareVO.setOrgList(orgList);
            groupList.add(fiOrgCompareVO);
        } else if(fiOrgCompare.getLevel().equals("1")) {
            FiOrgCompareVO fiOrgCompareVO = new FiOrgCompareVO();
            BeanUtil.copyProperties(fiOrgCompare,fiOrgCompareVO);
            orgList = this.list(Wrappers.<FiOrgCompare>lambdaQuery().eq(FiOrgCompare::getGroupCode, fiOrgCompare.getGroupCode()));
            fiOrgCompareVO.setOrgList(orgList);
            groupList.add(fiOrgCompareVO);
        } else if(fiOrgCompare.getLevel().equals("2")) {
            FiOrgCompareVO fiOrgCompareVO = new FiOrgCompareVO();
            BeanUtil.copyProperties(fiOrgCompare,fiOrgCompareVO);
            orgList = this.list();
            fiOrgCompareVO.setOrgList(orgList);
            groupList.add(fiOrgCompareVO);
            List<FiOrgCompare> groupListVO = this.list(Wrappers.<FiOrgCompare>lambdaQuery().eq(FiOrgCompare::getLevel, "1"));
            for(FiOrgCompare item : groupListVO) {
                FiOrgCompareVO groupVO = new FiOrgCompareVO();
                BeanUtil.copyProperties(item,groupVO);
                List<FiOrgCompare> orgNewList = this.list(Wrappers.<FiOrgCompare>lambdaQuery().eq(FiOrgCompare::getGroupCode, item.getGroupCode()));
                groupVO.setOrgList(orgNewList);
                groupList.add(groupVO);
            }
        }
        fiOrgListVO.setGroupList(groupList);
        return fiOrgListVO;
    }

    /**
     * 根据当前登录集团的departCode字段获取组织树
     * @param departCode
     * @return
     */
    @Override
    public FiOrgListVO getGroupList(String departCode) {
        FiOrgListVO fiOrgListVO = new FiOrgListVO();
        List<FiOrgCompareVO> groupList = new ArrayList<>();
        List<FiOrgCompare> orgList = new ArrayList<>();
        FiOrgCompare fiOrgCompare = this.getOne(Wrappers.<FiOrgCompare>lambdaQuery().eq(FiOrgCompare::getOrgCode, departCode));
        if(null == fiOrgCompare) {
            throw new CheckedException("组织配置异常");
        }
        if(fiOrgCompare.getLevel().equals("0")) {
            throw new CheckedException("当前组织不在配置的组织范围内");
        } else if(fiOrgCompare.getLevel().equals("1")) {
            FiOrgCompareVO fiOrgCompareVO = new FiOrgCompareVO();
            BeanUtil.copyProperties(fiOrgCompare,fiOrgCompareVO);
            orgList = this.list(Wrappers.<FiOrgCompare>lambdaQuery().eq(FiOrgCompare::getGroupCode, fiOrgCompare.getGroupCode()));
            fiOrgCompareVO.setOrgList(orgList);
            groupList.add(fiOrgCompareVO);
        } else if(fiOrgCompare.getLevel().equals("2")) {
            FiOrgCompareVO fiOrgCompareVO = new FiOrgCompareVO();
            BeanUtil.copyProperties(fiOrgCompare,fiOrgCompareVO);
            orgList = this.list();
            fiOrgCompareVO.setOrgList(orgList);
            groupList.add(fiOrgCompareVO);
            List<FiOrgCompare> groupListVO = this.list(Wrappers.<FiOrgCompare>lambdaQuery().eq(FiOrgCompare::getLevel, "1"));
            for(FiOrgCompare item : groupListVO) {
                FiOrgCompareVO groupVO = new FiOrgCompareVO();
                BeanUtil.copyProperties(item,groupVO);
                List<FiOrgCompare> orgNewList = this.list(Wrappers.<FiOrgCompare>lambdaQuery().eq(FiOrgCompare::getGroupCode, item.getGroupCode()));
                groupVO.setOrgList(orgNewList);
                groupList.add(groupVO);
            }
        }
        fiOrgListVO.setGroupList(groupList);
        return fiOrgListVO;
    }
}

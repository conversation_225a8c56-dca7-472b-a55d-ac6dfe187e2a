
package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.admin.api.entity.SysDictItem;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiConvertReimbursementDTO;
import com.cloud.ficonsumer.dto.FiConvertReimbursementDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDetailDTO;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.enums.ReturnStatusEnum;
import com.cloud.ficonsumer.mapper.ApiUpmsMapper;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceReimbursementMapper;
import com.cloud.ficonsumer.mapper.FileManageMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.*;

import static java.util.stream.Collectors.toList;

/**
 * 通用报销单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:38
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiSourceReimbursementServiceImpl extends ServiceImpl<FiSourceReimbursementMapper, FiSourceReimbursement> implements FiSourceReimbursementService {

    private final FiSourceReimbursementDetailService fiSourceReimbursementDetailService;
    private final FiConvertReimbursementService fiConvertReimbursementService;
    private final FiConvertReimbursementDetailService fiConvertReimbursementDetailService;
    private final FiSourceFileOcrInfoService fiSourceFileOcrInfoService;
    private final ApiUpmsMapper apiUpmsMapper;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final FiOrgCompareService fiOrgCompareService;
    private final DmMapper dmMapper;
    private final FileManageMapper fileManageMapper;
    private final YgConfig ygConfig;

    /**
     * 通用报销单重新推送
     * @param
     */
    @Override
    public boolean pushAgain(String pk) {
        FiSourceReimbursement sourceMainData = this.getOne(Wrappers.<FiSourceReimbursement>lambdaQuery().eq(FiSourceReimbursement::getPkPayablebill, pk));
        List<FiSourceReimbursementDetail> details = fiSourceReimbursementDetailService.list(Wrappers.<FiSourceReimbursementDetail>lambdaQuery().eq(FiSourceReimbursementDetail::getPkPayablebill, pk));
        //重新转换
        try {
            this.turnData(sourceMainData,details);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            this.updateById(sourceMainData);
            LogUtil.info(log, "通用报销单集成信息转换失败:", e);
            throw new CheckedException("通用报销单集成信息转换失败:"+pushMsg);
        }
        //重新推送智慧平台
        sourceMainData.setPushTime(DateUtil.formatDateTime(new Date()));
        try {
            YgResult ygResult = this.pushData(pk);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
            sourceMainData.setPushMsg("");
            sourceMainData.setReadiness(1l);
            sourceMainData.setReturnStatus("");
            sourceMainData.setReturnMsg("");
            sourceMainData.setReturnTime("");
            sourceMainData.setReturnBill("");
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,Constants.TradeType.reimbursement);
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000000");
                updatePlatFormLog.setMsg("");
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TradeType.reimbursement);
                platFormLogVO.setCode("000000");
                platFormLogVO.setMsg("");
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,Constants.TradeType.reimbursement);
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000001");
                updatePlatFormLog.setMsg(pushMsg);
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TradeType.reimbursement);
                platFormLogVO.setCode("000001");
                platFormLogVO.setMsg(pushMsg);
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
            LogUtil.info(log, "通用报销单集成信息推送失败:", e);
            throw new CheckedException("通用报销单集成信息推送失败:"+pushMsg);
        }
        return true;
    }

    /**
     * 数据推送
     *
     * @param pk
     * @return
     * @throws Exception
     */
    private YgResult pushData(String pk) throws Exception {
        YgResult ygResult = new YgResult();
        if(ygConfig.getEnable()) {
            FiConvertReimbursement convertMainData = fiConvertReimbursementService.getOne(Wrappers.<FiConvertReimbursement>lambdaQuery().eq(FiConvertReimbursement::getPkPayablebill, pk));
            List<FiConvertReimbursementDetail> details = fiConvertReimbursementDetailService.list(Wrappers.<FiConvertReimbursementDetail>lambdaQuery().eq(FiConvertReimbursementDetail::getPkPayablebill,pk));
            YgReimbursementVO request = this.turnYgData(convertMainData,details);
            ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000008.getServCode());
            String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(request));
            ygResult = JSONObject.parseObject(result,YgResult.class);
            //成功 6100000 或 6710300 失败6990399
            if(!(ygResult.getCode().equals("6100000") || ygResult.getCode().equals("6710300"))) {
                throw new CheckedException(ygResult.getmessage());
            }
        }
        return ygResult;
    }

    /**
     * 将转换后的数据用远光提供的格式推送
     *
     * @param convertMainData
     * @param details
     * @return
     */
    private YgReimbursementVO turnYgData(FiConvertReimbursement convertMainData, List<FiConvertReimbursementDetail> details) {
        YgReimbursementVO request = new YgReimbursementVO();
        request.setTransId(convertMainData.getBillno());
        request.setSourSystem("ZJYJ");
        String cosType2 = "";
        String cosType3 = "";
        String cosType4 = "";
        String cosTypeName4 = "";
        CosTypeVO cosTypeVO = dmMapper.getCosTypeVOByCurr(convertMainData.getDef47());
        if(cosTypeVO == null) {
            throw new CheckedException("平台业务事项不可为空");
        }
        if(cosTypeVO.getInnercode1().length()!=16 && cosTypeVO.getInnercode1().length()!=12) {
            throw new CheckedException("平台业务事项错误");
        }else {
            if(cosTypeVO.getInnercode1() !=null && cosTypeVO.getInnercode1().length()==16) {
                cosTypeName4 = cosTypeVO.getName1();
                cosType4 = cosTypeVO.getCode1();
                cosType3 = cosTypeVO.getCode2();
                cosType2 = cosTypeVO.getCode3();
            }else if(cosTypeVO.getInnercode1() !=null && cosTypeVO.getInnercode1().length()==12) {
                cosType3 = cosTypeVO.getCode1();
                cosType2 = cosTypeVO.getCode2();
            }
        }
        request.setCosType(cosType3);
        request.setDetCosType(cosTypeName4);
        request.setDetCosTypeCode(cosType4);
        String pkOrg = apiCompareService.getTurnByType(convertMainData.getPkOrg(),Constants.PkOrg);
        if(StrUtil.isBlank(pkOrg)) {
            throw new CheckedException("单位代号不可为空");
        }
        request.setDwdh(pkOrg);
        String billmaker = convertMainData.getBillmaker();
        request.setHdlgNm(billmaker);
        String isAcr = apiCompareService.getTurnByType(convertMainData.getPkOrg(),Constants.isAcr);
        request.setIsAcr(isAcr);
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_YEAR, 30);
        Date newDate = calendar.getTime();
        request.setApplicantTime(DateUtil.formatDate(newDate));
        String pkSupplier = apiCompareService.getTurnByType(convertMainData.getSupplier(),Constants.PkSupplier);
        request.setTProject(apiCompareService.getProjectCode(convertMainData.getProject()));
        request.setBackTag(convertMainData.getBackTag());
        request.setAmount(convertMainData.getLocalMoney());
        String scomment = convertMainData.getScomment();
        if(StrUtil.isBlank(scomment)) {
            scomment = "无";
        }
        request.setCause(scomment);
        String objtype = convertMainData.getObjtype();
        String isSupp = "";
        String isAdvanPym = "";
        if(StrUtil.isNotBlank(objtype)) {
            if(objtype.equals("0") || objtype.equals("1")) {
                isSupp = "00000001";
                isAdvanPym = "00000000";
            }if(objtype.equals("3")) {
                isSupp = "00000000";
                isAdvanPym = "00000001";
            }
        }
        request.setIsAdvanPym(isAdvanPym);
        request.setIsSupp(isSupp);
        request.setMoneyDm(convertMainData.getPkCurrtype());
        String deptType = apiCompareService.getTurnByType(convertMainData.getDef52(),Constants.deptType);
        request.setDeptType("1222." + deptType);
        List<YgReimbursementDetailVO> generbillistatCostDet = new ArrayList<>();
        List<PayDetVO> generbillistatPayDet = new ArrayList<>();
        List<ConDetVO> generbillistatConDet = new ArrayList<>();
        List<AdvPayVO> generbillistatAdvPay = new ArrayList<>();
        List<ShareInfos> generbillistatCostShare = new ArrayList<>();
        List<StatApplyVO> generbillistatApply = new ArrayList<>();
        List<StatLoanRepayVO> generbillistatLoanRepay = new ArrayList<>();
        List<StatBatchPayVO> generbillistatBatchPay = new ArrayList<>();
        List<ApprvDetails> apprv = new ArrayList<>();
        String payAmo = apiCompareService.getTurnByType(convertMainData.getDef2(),Constants.payAmo);
        for(FiConvertReimbursementDetail detail : details) {
            PayDetVO payDetVO = new PayDetVO();
            BigDecimal localMoneyCr = null;
            if(StrUtil.isNotBlank(detail.getLocalMoneyCr())) {
                localMoneyCr = new BigDecimal(detail.getLocalMoneyCr());
            }
            String pkPsndoc = detail.getPkPsndoc();
            String payeeCod = "";
            if(StrUtil.isNotBlank(objtype)) {
                if(objtype.equals("1")) {
                    payeeCod = pkSupplier;
                }if(objtype.equals("3")) {
                    payeeCod = pkPsndoc;
                }
            }
            payDetVO.setPayeeCod(payeeCod);
            BigDecimal payAmount = BigDecimal.ZERO;
            if(StrUtil.isNotBlank(payAmo) && payAmo.equals("直接付款")) {
                payAmount = localMoneyCr;
            }
            payDetVO.setPayAmo(payAmount);
            if(StrUtil.isNotBlank(detail.getRecaccount())) {
                String accnum = apiCompareService.getTurnByType(detail.getRecaccount(),Constants.accNum);
                payDetVO.setRcvrAcc(accnum);
                if(StrUtil.isBlank(payeeCod)) {
                    throw new CheckedException("收款人不可为空");
                }
            }
            if(StrUtil.isNotBlank(detail.getPayaccount())) {
                String accnum = apiCompareService.getTurnByType(detail.getPayaccount(),Constants.accNum);
                payDetVO.setPyrBkAcct(accnum);
            }
            payDetVO.setPyMethod(detail.getPkBalatype());
            generbillistatPayDet.add(payDetVO);
        }
        List<BdUdsResult> bdUdsResults = dmMapper.listUdsResult(convertMainData.getPkPayablebill(), convertMainData.getPkTradetype());
        List<ImageDetail> fileInfo = bdUdsResults.stream().map(
                bdUdsResult -> {
                    ImageDetail image = new ImageDetail();
                    image.setImageType(YgConfig.imageType);
                    image.setFileResId(bdUdsResult.getDocumentId());
                    image.setFileName(bdUdsResult.getFileName());
                    image.setFileSource("UDS");
                    image.setFromFileSystemId(YgConfig.fromFileSystemId);
                    return image;
                }).collect(toList());
        //1. 单据不存在上传的发票，传收据
        //2.单据存在上传的发票，且存在一张同步成功的发票，同步成功的发票
        //3.单据所有上传的发票没有任意一条同步成功，不允许同步，后续定时任务继续同步
        List<InvoiceVO> generbillistatInvoice = new ArrayList<>();
        Map<String, Integer> filePushStatus = new HashMap<>();
        List<InvoiceFileVO> invoiceVOList = fileManageMapper.getInvoiceByPk(convertMainData.getPkPayablebill());
        if(CollectionUtil.isNotEmpty(invoiceVOList)) {
            List<String> sourceIdList = new ArrayList<>();
            for(InvoiceFileVO invoiceFileVO : invoiceVOList) {
                sourceIdList.add(invoiceFileVO.getSourceId());
            }
            filePushStatus = fiSourceFileOcrInfoService.listOcrPushStatus(sourceIdList);
            for(InvoiceFileVO item : invoiceVOList) {
                Integer pushStatus = filePushStatus.get(item.getSourceId());
                if(pushStatus!=null && pushStatus==3) {
                    InvoiceVO invoiceVO = new InvoiceVO();
                    invoiceVO.setInvoiceNo(item.getInvoiceNo());
                    invoiceVO.setInvDate(item.getInvDate());
                    invoiceVO.setInvoCode(item.getInvoCode());
                    invoiceVO.setInvoType(item.getInvoType());
                    generbillistatInvoice.add(invoiceVO);
                }
            }
        }
        if(CollectionUtil.isEmpty(generbillistatInvoice)) {
            boolean checkInvoiceFlag = this.getCheckInvoice(convertMainData.getPkPayablebill());
            if(checkInvoiceFlag) {
                throw new CheckedException("请先完成发票入池");
            }
            generbillistatInvoice = new ArrayList<>();
            InvoiceVO invoiceVO = new InvoiceVO();
            invoiceVO.setInvoType(YgConfig.invoType);
            generbillistatInvoice.add(invoiceVO);
        }
        request.setGenerbillistatCostDet(generbillistatCostDet);
        request.setGenerbillistatInvoice(generbillistatInvoice);
        request.setGenerbillistatPayDet(generbillistatPayDet);
        request.setGenerbillistatConDet(generbillistatConDet);
        request.setGenerbillistatAdvPay(generbillistatAdvPay);
        request.setFileInfo(fileInfo);
        request.setGenerbillistatCostShare(generbillistatCostShare);
        request.setGenerbillistatApply(generbillistatApply);
        request.setGenerbillistatLoanRepay(generbillistatLoanRepay);
        request.setGenerbillistatBatchPay(generbillistatBatchPay);
        request.setApprv(apprv);
        return request;
    }

    /**
     * 查看是否需要需要发票必填
     *
     * @param pkPayablebill
     * @return
     */
    private boolean getCheckInvoice(String pkPayablebill) {
        List<String> dictTypeList = new ArrayList<>();
        dictTypeList.add("999");
        List<SysDictItem> ocrTypeList = apiUpmsMapper.getItemListByType("filemanage_ocr_type");
        for(SysDictItem sysDictItem : ocrTypeList) {
            if(StrUtil.isNotBlank(sysDictItem.getRemarks()) && sysDictItem.getRemarks().equals("是")) {
                dictTypeList.add(sysDictItem.getValue());
            }
        }
        List<Long> mainIdList = fileManageMapper.getSourceInvoiceList(pkPayablebill,dictTypeList);
        if(CollectionUtil.isNotEmpty(mainIdList)) {
            return true;
        }
        return false;
    }

    /**
     * 数据转换
     *
     * @param sourceMainData
     * @param details
     */
    private void turnData(FiSourceReimbursement sourceMainData, List<FiSourceReimbursementDetail> details) throws Exception {
        String pk = sourceMainData.getPkPayablebill();
        FiConvertReimbursement fiConvertReimbursement = new FiConvertReimbursement();
        apiCompareCache.convertData(sourceMainData,fiConvertReimbursement,BillTypeEnum.REIMBURSEMENT.getCode());
        FiConvertReimbursement convertMainData = fiConvertReimbursementService.getOne(Wrappers.<FiConvertReimbursement>lambdaQuery().eq(FiConvertReimbursement::getPkPayablebill, pk));
        fiConvertReimbursement.setId(null);
        if(convertMainData != null) {
            fiConvertReimbursement.setId(convertMainData.getId());
            fiConvertReimbursementDetailService.remove(Wrappers.<FiConvertReimbursementDetail>lambdaQuery().eq(FiConvertReimbursementDetail::getPkPayablebill,pk));
        }
        List<FiConvertReimbursementDetail> convertDetailList = new ArrayList<>();
        for(FiSourceReimbursementDetail sourceDetailData : details) {
            FiConvertReimbursementDetail fiConvertReimbursementDetail = new FiConvertReimbursementDetail();
            apiCompareCache.convertData(sourceDetailData,fiConvertReimbursementDetail,BillTypeEnum.REIMBURSEMENT_DETAIL.getCode());
            fiConvertReimbursementDetail.setId(null);
            convertDetailList.add(fiConvertReimbursementDetail);
        }
        fiConvertReimbursementService.saveOrUpdate(fiConvertReimbursement);
        fiConvertReimbursementDetailService.saveOrUpdateBatch(convertDetailList);
    }

    @Override
    public Page<FiSourceReimbursementDTO> beforeConvertDataPage(Page page, FiSourceReimbursementQueryVO queryVO) {
        Page<FiSourceReimbursementDTO> result = this.baseMapper.beforeConvertDataPage(page,queryVO);
        if (CollectionUtil.isNotEmpty(result.getRecords())){
            for (FiSourceReimbursementDTO data : result.getRecords()){
                data.setReturnStatusName(ReturnStatusEnum.getByCode(data.getReturnStatus()).getName());
                if(StrUtil.isNotBlank(data.getSupplier())) {
                    data.setSupplierName(dmMapper.getSupplierName(data.getSupplier()));
                }
            }
        }
        return result;
    }

    @Override
    public Page<FiSourceReimbursementDetailDTO> getBeforeConvertDataPage(Page page, FiSourceReimbursementQueryVO queryVO) {
        Page<FiSourceReimbursementDetailDTO> result = this.baseMapper.getBeforeConvertDataPage(page,queryVO);
        return result;
    }

    @Override
    public List<FiConvertReimbursementDTO> getConvertList(String pk) {
        List<FiConvertReimbursementDTO> convertDataDTOList = new ArrayList<>();
        FiConvertReimbursementDTO convertDataDTO = new FiConvertReimbursementDTO();
        FiConvertReimbursement convertDataOld = fiConvertReimbursementService.getOne(Wrappers.<FiConvertReimbursement>lambdaQuery().eq(FiConvertReimbursement::getPkPayablebill, pk));
        if(null != convertDataOld) {
            BeanUtil.copyProperties(convertDataOld,convertDataDTO);
            convertDataDTOList.add(convertDataDTO);
        }
        return convertDataDTOList;
    }

    @Override
    public Page<FiConvertReimbursementDetailDTO> getConvertData(Page page, String pk) {
        Page<FiConvertReimbursementDetailDTO> result = this.baseMapper.getConvertData(page,pk);
        return result;
    }

    /**
     * 导出通用报销单的统计
     *
     * @return
     */
    @Override
    public List<BillStatisticsVO> getReimbursementStatistics() {
        List<BillStatisticsVO> statisticsVOList = new ArrayList<>();
        List<FiOrgCompare> groupMap = fiOrgCompareService.getGroupMap();
        if(CollectionUtil.isNotEmpty(groupMap)) {
            BigDecimal sumNumSum = BigDecimal.ZERO;
            BigDecimal failNumSum = BigDecimal.ZERO;
            BigDecimal successNumSum = BigDecimal.ZERO;
            for(FiOrgCompare fiOrgCompare : groupMap) {
                BillStatisticsVO billStatisticsVO = fiSourceReimbursementDetailService.getStatisticsByGroup(fiOrgCompare.getPkGroup());
                if(null == billStatisticsVO) {
                    billStatisticsVO = new BillStatisticsVO();
                }
                billStatisticsVO.setPkGroupName(fiOrgCompare.getGroupName());
                if(StrUtil.isNotBlank(billStatisticsVO.getSumNum())) {
                    BigDecimal sumNum = new BigDecimal(billStatisticsVO.getSumNum());
                    BigDecimal failNum = new BigDecimal(billStatisticsVO.getFailNum());
                    BigDecimal successNum = new BigDecimal(billStatisticsVO.getSuccessNum());
                    sumNumSum = sumNumSum.add(sumNum);
                    failNumSum = failNumSum.add(failNum);
                    successNumSum = successNumSum.add(successNum);
                    if (sumNum.compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal ratio = successNum.divide(sumNum, 4, RoundingMode.HALF_UP);
                        BigDecimal successRate = ratio.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                        // 格式化为百分比字符串
                        NumberFormat percentFormat = NumberFormat.getPercentInstance();
                        percentFormat.setMaximumFractionDigits(2);
                        // 175.00% → 1.75 → 175%
                        String formattedResult = percentFormat.format(successRate.doubleValue() / 100);
                        billStatisticsVO.setSuccessRate(formattedResult);
                    } else {
                        billStatisticsVO.setSuccessRate("-");
                    }
                }
                statisticsVOList.add(billStatisticsVO);
            }
            BillStatisticsVO billStatisticsVO = new BillStatisticsVO();
            billStatisticsVO.setPkGroupName("合计");
            billStatisticsVO.setSumNum(String.valueOf(sumNumSum));
            billStatisticsVO.setFailNum(String.valueOf(failNumSum));
            billStatisticsVO.setSuccessNum(String.valueOf(successNumSum));
            if (sumNumSum.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal ratio = successNumSum.divide(sumNumSum, 4, RoundingMode.HALF_UP);
                BigDecimal successRate = ratio.multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
                // 格式化为百分比字符串
                NumberFormat percentFormat = NumberFormat.getPercentInstance();
                percentFormat.setMaximumFractionDigits(2);
                // 175.00% → 1.75 → 175%
                String formattedResult = percentFormat.format(successRate.doubleValue() / 100);
                billStatisticsVO.setSuccessRate(formattedResult);
            } else {
                billStatisticsVO.setSuccessRate("-");
            }
            statisticsVOList.add(billStatisticsVO);
        }
        return statisticsVOList;
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourceReimbursement entity = this.getById(id);
            try {
                this.pushAgain(entity.getPkPayablebill());
            } catch (Exception e) {
                resultList.add(entity.getBillno()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}

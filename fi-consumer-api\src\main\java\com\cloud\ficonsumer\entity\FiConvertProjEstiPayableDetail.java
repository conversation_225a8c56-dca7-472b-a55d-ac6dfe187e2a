package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@TableName("fi_convert_projesti_payable_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目暂估应付明细转换表")
public class FiConvertProjEstiPayableDetail extends BaseEntity<FiConvertProjEstiPayableDetail> {

    private static final long serialVersionUID = -1L;

    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value = "暂估应付单行标识")
    private String pkEstipayableitem;

    @ApiModelProperty(value = "暂估应付单标识")
    private String pkEstipayablebill;

    @ApiModelProperty(value = "业务人员")
    private String puPsndoc;

    @ApiModelProperty(value = "供应商")
    private String supplier;

    @ApiModelProperty(value = "项目")
    private String project;

    @ApiModelProperty(value = "部门ID")
    private String pkDeptid;

    @ApiModelProperty(value = "币种")
    private String pkCurrtype;

    @ApiModelProperty(value = "发票号")
    private String invoiceno;

    @ApiModelProperty(value = "组织本币金额")
    private BigDecimal localMoneyCr;

    @ApiModelProperty(value = "组织本币无税金额")
    private BigDecimal localNotaxCr;
}



package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 采购应付源数据详情
 *
 * <AUTHOR>
 * @date 2025-04-22 10:04:33
 */
@Data
@TableName("fi_invoice_posting")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购应付源数据详情")
public class FiInvoicePosting extends BaseEntity<FiInvoicePosting> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 业务单据ID
     */
    @ApiModelProperty(value="业务单据ID")
    private String busibillno;

    /**
     * 应用ID
     */
    @ApiModelProperty(value="应用ID")
    private String appid;

    /**
     * 业务类型编码
     */
    @ApiModelProperty(value="业务类型编码")
    private String typeid;

    /**
     * 标识：记账发票
     */
    @ApiModelProperty(value="标识：记账发票")
    private String xrech;

    /**
     * 标识：后续借/贷
     */
    @ApiModelProperty(value="标识：后续借/贷")
    private String tbtkz;

    /**
     * 公司代码MDM
     */
    @ApiModelProperty(value="公司代码MDM")
    private String comcod;

    /**
     * 财务组织
     */
    @ApiModelProperty(value="财务组织")
    private String pkOrg;

    /**
     * 财务组织名称
     */
    @ApiModelProperty(value="财务组织名称")
    private String pkOrgName;

    /**
     * 利润中心MDM
     */
    @ApiModelProperty(value="利润中心MDM")
    private String prctr;

    /**
     * 凭证日期
     */
    @ApiModelProperty(value="凭证日期")
    private String apldate;

    /**
     * 过账日期
     */
    @ApiModelProperty(value="过账日期")
    private String voudat;

    /**
     * 出票方
     */
    @ApiModelProperty(value="出票方")
    private String supcracc;

    /**
     * 附件张数
     */
    @ApiModelProperty(value="附件张数")
    private String refdocnum;

    /**
     * 金额
     */
    @ApiModelProperty(value="金额")
    private String invveramt;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String taxamt;

    /**
     * 货币
     */
    @ApiModelProperty(value="货币")
    private String trancurren;

    /**
     * 付款基准日期
     */
    @ApiModelProperty(value="付款基准日期")
    private String basedate;

    /**
     * 付款条件
     */
    @ApiModelProperty(value="付款条件")
    private String terpay;

    /**
     * 付款方式
     */
    @ApiModelProperty(value="付款方式")
    private String paymmet;

    /**
     * 凭证类型
     */
    @ApiModelProperty(value="凭证类型")
    private String doctype;

    /**
     * 凭证抬头文本
     */
    @ApiModelProperty(value="凭证抬头文本")
    private String instruction;

    /**
     * 税率代码
     */
    @ApiModelProperty(value="税率代码")
    private String taxrtcd;

    /**
     * 税项
     */
    @ApiModelProperty(value="税项")
    private String taxitem;

    /**
     * 经办人编号
     */
    @ApiModelProperty(value="经办人编号")
    private String managerid;

    /**
     * 制证人
     */
    @ApiModelProperty(value="制证人")
    private String app;

    /**
     * 审核人
     */
    @ApiModelProperty(value="审核人")
    private String checker;

    /**
     * 财务主管
     */
    @ApiModelProperty(value="财务主管")
    private String financer;

    /**
     * 农维费使用标志
     */
    @ApiModelProperty(value="农维费使用标志")
    private String agmaintfeeusemark;

    /**
     * 部门分类
     */
    @ApiModelProperty(value="部门分类")
    private String maindepttype;

    /**
     * 业务事项
     */
    @ApiModelProperty(value="业务事项")
    private String costype;

    /**
     * 明细业务事项
     */
    @ApiModelProperty(value="明细业务事项")
    private String detcostype;

    /**
     * 4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败
     */
    @ApiModelProperty(value="4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败")
    private Integer pushStatus;

    /**
     * 推送成功失败信息
     */
    @ApiModelProperty(value="推送成功失败信息")
    private String pushMsg;

    /**
     * 重试次数
     */
    @ApiModelProperty(value="重试次数")
    private Long tryNumber;

    /**
     * 0 未就绪 1 就绪
     */
    @ApiModelProperty(value="0 未就绪 1 就绪")
    private Long readiness;

}

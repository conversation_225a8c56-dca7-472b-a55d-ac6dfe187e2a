package com.cloud.ficonsumer.enums;

import com.cloud.cloud.common.core.exception.CheckedException;

/**
 * 应付单类型枚举
 */
public enum PayableType {
    /**
     * 采购应付
     */
    PURCHASE("F1-Cxx-02", "采购应付"),
    
    /**
     * 项目应付
     */
    PROJECT("F1-Cxx-01", "项目应付");
    
    private final String code;
    private final String name;
    
    PayableType(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    /**
     * 根据交易类型编码获取应付单类型
     */
    public static PayableType fromTradeType(String tradeType) {
        for (PayableType type : values()) {
            if (type.getCode().equals(tradeType)) {
                return type;
            }
        }
        throw new CheckedException("不支持的应付单类型: " + tradeType);
    }
}

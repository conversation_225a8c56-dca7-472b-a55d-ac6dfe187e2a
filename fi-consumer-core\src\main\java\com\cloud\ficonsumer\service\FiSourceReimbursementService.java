

package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiConvertReimbursementDTO;
import com.cloud.ficonsumer.dto.FiConvertReimbursementDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceReimbursement;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceReimbursementQueryVO;
import com.cloud.ficonsumer.vo.BillStatisticsVO;

import java.util.List;

/**
 * 通用报销单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:38
 */
public interface FiSourceReimbursementService extends IService<FiSourceReimbursement> {

    /**
     * 通用报销单重新推送
     * @param s
     */
    boolean pushAgain(String s);

    Page<FiSourceReimbursementDTO> beforeConvertDataPage(Page page, FiSourceReimbursementQueryVO queryVO);

    Page<FiSourceReimbursementDetailDTO> getBeforeConvertDataPage(Page page, FiSourceReimbursementQueryVO queryVO);

    List<FiConvertReimbursementDTO> getConvertList(String pk);

    Page<FiConvertReimbursementDetailDTO> getConvertData(Page page, String pk);

    /**
     * 导出通用报销单的统计
     *
     * @return
     */
    List<BillStatisticsVO> getReimbursementStatistics();

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}

INSERT INTO AP_PAYAB<PERSON><PERSON>LL (ACCESSORY<PERSON><PERSON>, APPR<PERSON><PERSON><PERSON><PERSON>, APPROVER, <PERSON><PERSON><PERSON><PERSON><PERSON>AT<PERSON>, B<PERSON><PERSON>LASS, BILLDATE, B<PERSON>LMAKER, BILLNO, B<PERSON>LPER<PERSON>D, <PERSON><PERSON><PERSON>TATUS, BILLYEAR, <PERSON><PERSON><PERSON><PERSON><PERSON>R, <PERSON>OR<PERSON><PERSON>G, CREA<PERSON><PERSON><PERSON><PERSON>, CREATO<PERSON>, DEF1, <PERSON>F10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, <PERSON>F47, <PERSON>F48, DEF49, <PERSON>F5, <PERSON>F50, <PERSON>F51, <PERSON>F52, <PERSON>F<PERSON>, <PERSON>F54, <PERSON>F<PERSON>, <PERSON>F56, <PERSON>F57, <PERSON>F58, <PERSON>F59, <PERSON>F6, <PERSON>F60, DEF61, DEF62, <PERSON>F63, <PERSON>F64, DEF65, DEF66, DEF67, <PERSON>F68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DR, EFFECTDATE, EFFECTSTATUS, EFFECTUSER, GLOBALLOCAL, GROUPLOCAL, INVOICENO, ISFLOWBILL, ISINIT, ISPUADJUST, ISREDED, ISREFUND, ISURGENT, LASTADJUSTUSER, LASTAPPROVEID, LOCAL_MONEY, MODIFIEDTIME, MODIFIER, MONEY, OFFICIALPRINTDATE, OFFICIALPRINTUSER, OUTBUSITYPE, PK_BALATYPE, PK_BILLTYPE, PK_BUSITYPE, PK_CURRTYPE, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYABLEBILL, PK_PCORG, PK_TRADETYPE, PK_TRADETYPEID, RECECOUNTRYID, SCOMMENT, SETT_ORG, SETT_ORG_V, SRC_SYSCODE, START_PERIOD, SUBJCODE, SYSCODE, TAXCOUNTRYID, TOTAL_PERIOD, TS, VID_PCORG, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS) VALUES (null, '2024-06-19 18:46:07', '1005A11********EP9BG', 1, 'yf', '2024-06-19 12:22:11', '1005A11********EP9BG', 'D12024061900001214', '06', 1, '2024', '~                   ', null, '2024-06-19 12:22:32', '1005A11********EP9BG', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 'D32024061900000853', '0', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', null, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 0, '2024-06-19 18:46:07', 10, '1005A11********EP9BG', 0.********, 90.********, null, 'N', 'N', 'N', 'N', 'N', 'N', '~', '1005A11********EP9BG', 90.********, null, '~', 90.********, null, '~', '~                   ', null, 'F1', '1005O91********3ELME', '1002Z01********001K1', '0001A11********03ZM0', '0001A11********03ZLZ', '0001A11********00XCI', '0001A11********03ZM0', '0001A11********03ZLZ', '1005A11********EV72M', '~', 'F1-Cxx-02', '1005A11********0UQ2I', '0001Z01********79UJJ', null, '0001A11********03ZM0', '0001A11********03ZLZ', 1, '~', '~', 1, '0001Z01********79UJJ', null, '2024-06-19 18:45:23', '~', '81873ce3-ba2a-4deb-b4de-ef103ee56bb7', 0, '20240619765e4302-9792-4409-bdd7-d68bf5e46593', 0);
INSERT INTO AP_PAYABLEBILL (ACCESSORYNUM, APPROVEDATE, APPROVER, APPROVESTATUS, BILLCLASS, BILLDATE, BILLMAKER, BILLNO, BILLPERIOD, BILLSTATUS, BILLYEAR, CONFIRMUSER, COORDFLAG, CREATIONTIME, CREATOR, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DR, EFFECTDATE, EFFECTSTATUS, EFFECTUSER, GLOBALLOCAL, GROUPLOCAL, INVOICENO, ISFLOWBILL, ISINIT, ISPUADJUST, ISREDED, ISREFUND, ISURGENT, LASTADJUSTUSER, LASTAPPROVEID, LOCAL_MONEY, MODIFIEDTIME, MODIFIER, MONEY, OFFICIALPRINTDATE, OFFICIALPRINTUSER, OUTBUSITYPE, PK_BALATYPE, PK_BILLTYPE, PK_BUSITYPE, PK_CURRTYPE, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYABLEBILL, PK_PCORG, PK_TRADETYPE, PK_TRADETYPEID, RECECOUNTRYID, SCOMMENT, SETT_ORG, SETT_ORG_V, SRC_SYSCODE, START_PERIOD, SUBJCODE, SYSCODE, TAXCOUNTRYID, TOTAL_PERIOD, TS, VID_PCORG, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS) VALUES (null, '2024-06-19 12:12:02', '1005A11********EP9BG', 1, 'yf', '2024-06-18 11:08:47', '1005A11********EP9BG', 'D12024061800001212', '06', 1, '2024', '~                   ', null, '2024-06-18 11:09:02', '1005A11********EP9BG', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 'D32024061800000849', '0', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', null, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 'aa①采购付款单：1）须准确填写报销单位、报销部门、经办人、', '~', 0, '2024-06-19 12:12:02', 10, '1005A11********EP9BG', 0.********, 1.********, null, 'N', 'N', 'N', 'N', 'N', 'N', '~', '1005A11********EP9BG', 1.********, null, '~', 1.********, null, '~', '~                   ', null, 'F1', '1005O91********3ELME', '1002Z01********001K1', '0001A11********03ZM0', '0001A11********03ZLZ', '0001A11********00XCI', '0001A11********03ZM0', '0001A11********03ZLZ', '1005A11********EV6ZY', '~', 'F1-Cxx-02', '1005A11********0UQ2I', '0001Z01********79UJJ', null, '0001A11********03ZM0', '0001A11********03ZLZ', 1, '~', '~', 1, '0001Z01********79UJJ', null, '2024-06-19 12:11:22', '~', 'ec7c49c0-b953-4438-b05b-7f80bb4b9b8a', 0, '202406198670bb18-e56c-43b7-96b4-cf89fef23c06', 0);
INSERT INTO AP_PAYABLEBILL (ACCESSORYNUM, APPROVEDATE, APPROVER, APPROVESTATUS, BILLCLASS, BILLDATE, BILLMAKER, BILLNO, BILLPERIOD, BILLSTATUS, BILLYEAR, CONFIRMUSER, COORDFLAG, CREATIONTIME, CREATOR, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DR, EFFECTDATE, EFFECTSTATUS, EFFECTUSER, GLOBALLOCAL, GROUPLOCAL, INVOICENO, ISFLOWBILL, ISINIT, ISPUADJUST, ISREDED, ISREFUND, ISURGENT, LASTADJUSTUSER, LASTAPPROVEID, LOCAL_MONEY, MODIFIEDTIME, MODIFIER, MONEY, OFFICIALPRINTDATE, OFFICIALPRINTUSER, OUTBUSITYPE, PK_BALATYPE, PK_BILLTYPE, PK_BUSITYPE, PK_CURRTYPE, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYABLEBILL, PK_PCORG, PK_TRADETYPE, PK_TRADETYPEID, RECECOUNTRYID, SCOMMENT, SETT_ORG, SETT_ORG_V, SRC_SYSCODE, START_PERIOD, SUBJCODE, SYSCODE, TAXCOUNTRYID, TOTAL_PERIOD, TS, VID_PCORG, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS) VALUES (null, '2021-09-14 10:26:24', '1005O91********2QRLS', 1, 'yf', '2021-09-14 10:23:12', '1005O91********2QRLS', 'D12021091400000608', '09', 1, '2021', '~                   ', null, '2021-09-14 10:23:27', '1005O91********2QRLS', '1005A11********0UZBX', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 0, '2021-09-14 10:26:24', 10, '1005O91********2QRLS', 0.********, 12.********, null, 'N', 'N', 'N', 'N', 'N', 'N', '~', '1005O91********2QRLS', 12.********, null, '~', 12.********, null, '~', '~                   ', null, 'F1', '1005A11********0UZC8', '1002Z01********001K1', '0001A11********03ZM0', '0001A11********03ZLZ', '0001A11********00XCI', '0001A11********03ZM0', '0001A11********03ZLZ', '1005O41********3MSC2', '~', 'F1-Cxx-02', '1005A11********0UQ2I', '0001Z01********79UJJ', '~', '0001A11********03ZM0', '0001A11********03ZLZ', 1, '~', '~', 1, '0001Z01********79UJJ', null, '2024-05-27 10:05:51', '~', '2e8bdae9-cb20-4d25-b71b-4fc59111e894', 0, '20240527e0afccb8-be3a-49dc-9e24-7d73342a2a2e', 0);

INSERT INTO AP_PAYABLEITEM (ASSETPACTNO, BANKROLLPROJET, BILLCLASS, BILLDATE, BILLNO, BUSIDATE, BUYSELLFLAG, CALTAXMNY, CASHITEM, CBS, CHECKELEMENT, CHECKTYPE, CONFERNUM, CONTRACTNO, COORDFLAG, COSTCENTER, CUSTOMER, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DIRECTION, DR, EQUIPMENTCODE, FACARD, FREECUST, GLOBALBALANCE, GLOBALCREBIT, GLOBALNOTAX_CRE, GLOBALRATE, GLOBALTAX_CRE, GROUPBALANCE, GROUPCREBIT, GROUPNOTAX_CRE, GROUPRATE, GROUPTAX_CRE, INNERORDERNO, INVOICENO, ISURGENT, LOCAL_MONEY_BAL, LOCAL_MONEY_CR, LOCAL_NOTAX_CR, LOCAL_PRICE, LOCAL_TAX_CR, LOCAL_TAXPRICE, MATERIAL, MATERIAL_SRC, MONEY_BAL, MONEY_CR, NOSUBTAX, NOSUBTAXRATE, NOTAX_CR, OBJTYPE, OCCUPATIONMNY, OPPTAXFLAG, ORDERCUBASDOC, OUTSTORENO, PAUSETRANSACT, PAYACCOUNT, PK_BALATYPE, PK_BILLTYPE, PK_CURRTYPE, PK_DEPTID, PK_DEPTID_V, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYABLEBILL, PK_PAYABLEITEM, PK_PAYTERM, PK_PCORG, PK_PCORG_V, PK_PSNDOC, PK_SSITEM, PK_SUBJCODE, PK_TRADETYPE, PK_TRADETYPEID, POSTPRICE, POSTPRICENOTAX, POSTQUANTITY, POSTUNIT, PRICE, PRODUCTLINE, PROJECT, PROJECT_TASK, PU_DEPTID, PU_DEPTID_V, PU_ORG, PU_ORG_V, PU_PSNDOC, PURCHASEORDER, QUANTITY_BAL, QUANTITY_CR, RATE, RECACCOUNT, ROWNO, ROWTYPE, SCOMMENT, SENDCOUNTRYID, SETT_ORG, SETT_ORG_V, SETTLECURR, SETTLEMONEY, SETTLENO, SRC_BILLID, SRC_BILLTYPE, SRC_ITEMID, SRC_TRADETYPE, SUBJCODE, SUPPLIER, TAXCODEID, TAXNUM, TAXPRICE, TAXRATE, TAXTYPE, TOP_BILLID, TOP_BILLTYPE, TOP_ITEMID, TOP_TRADETYPE, TS, VATCODE, VENDORVATCODE) VALUES (null, '~', 'yf', '2024-06-18 11:08:47', 'D12024061800001212', '2024-06-18 11:08:47', 2, 0.********, '~', '~', '~', '~', null, null, null, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', null, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', null, '~', '~', -1, 0, null, '~', '~', 0.********, 0.********, 0.********, 0.********, 0.********, 0.********, 1.********, 0.********, 1.********, 0.********, null, null, 'N', 0.********, 1.********, 0.********, 0.********, 0.10000000, 0.********, '~', '~', 0.********, 1.********, 0.********, 0.********, 0.********, 1, 0.********, 'N', '~', null, 'N', '~', '~', 'F1', '1002Z01********001K1', '~', '~', '0001A11********03ZM0', '0001A11********03ZLZ', '0001A11********00XCI', '0001A11********03ZM0', '0001A11********03ZLZ', '1005A11********EV6ZY', '1005A11********EV6ZZ', '~', '~', '~', '~', null, '1005A11********01AF1', 'F1-Cxx-02', '1005A11********0UQ2I', null, null, null, '~', null, '~', '~', '~', '~', '~', '~', '~                   ', '~                   ', null, 0.********, null, 1.********, '~', 0, 0, null, '0001Z01********79UJJ', '~', '0001A11********03ZLZ', '1002Z01********001K1', 1.********, '~', '1005A11********EV6ZO', 'F3', '1005A11********EV6ZP', 'F3-Cxx-02', '~', '1005A11********00C0S', '1005O91********4Q9SM', null, 0.********, 11.000000, 1, '1005A11********EV6ZO', 'F3', '1005A11********EV6ZP', 'F3-Cxx-02', '2024-06-19 12:11:22', null, null);
INSERT INTO AP_PAYABLEITEM (ASSETPACTNO, BANKROLLPROJET, BILLCLASS, BILLDATE, BILLNO, BUSIDATE, BUYSELLFLAG, CALTAXMNY, CASHITEM, CBS, CHECKELEMENT, CHECKTYPE, CONFERNUM, CONTRACTNO, COORDFLAG, COSTCENTER, CUSTOMER, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DIRECTION, DR, EQUIPMENTCODE, FACARD, FREECUST, GLOBALBALANCE, GLOBALCREBIT, GLOBALNOTAX_CRE, GLOBALRATE, GLOBALTAX_CRE, GROUPBALANCE, GROUPCREBIT, GROUPNOTAX_CRE, GROUPRATE, GROUPTAX_CRE, INNERORDERNO, INVOICENO, ISURGENT, LOCAL_MONEY_BAL, LOCAL_MONEY_CR, LOCAL_NOTAX_CR, LOCAL_PRICE, LOCAL_TAX_CR, LOCAL_TAXPRICE, MATERIAL, MATERIAL_SRC, MONEY_BAL, MONEY_CR, NOSUBTAX, NOSUBTAXRATE, NOTAX_CR, OBJTYPE, OCCUPATIONMNY, OPPTAXFLAG, ORDERCUBASDOC, OUTSTORENO, PAUSETRANSACT, PAYACCOUNT, PK_BALATYPE, PK_BILLTYPE, PK_CURRTYPE, PK_DEPTID, PK_DEPTID_V, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYABLEBILL, PK_PAYABLEITEM, PK_PAYTERM, PK_PCORG, PK_PCORG_V, PK_PSNDOC, PK_SSITEM, PK_SUBJCODE, PK_TRADETYPE, PK_TRADETYPEID, POSTPRICE, POSTPRICENOTAX, POSTQUANTITY, POSTUNIT, PRICE, PRODUCTLINE, PROJECT, PROJECT_TASK, PU_DEPTID, PU_DEPTID_V, PU_ORG, PU_ORG_V, PU_PSNDOC, PURCHASEORDER, QUANTITY_BAL, QUANTITY_CR, RATE, RECACCOUNT, ROWNO, ROWTYPE, SCOMMENT, SENDCOUNTRYID, SETT_ORG, SETT_ORG_V, SETTLECURR, SETTLEMONEY, SETTLENO, SRC_BILLID, SRC_BILLTYPE, SRC_ITEMID, SRC_TRADETYPE, SUBJCODE, SUPPLIER, TAXCODEID, TAXNUM, TAXPRICE, TAXRATE, TAXTYPE, TOP_BILLID, TOP_BILLTYPE, TOP_ITEMID, TOP_TRADETYPE, TS, VATCODE, VENDORVATCODE) VALUES (null, '~', 'yf', '2024-06-19 12:22:11', 'D12024061900001214', '2024-06-19 12:22:11', 2, 81.********, '~', '~', '~', '~', null, null, null, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', null, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', null, '~', '~', -1, 0, null, '~', '~', 0.********, 0.********, 0.********, 0.********, 0.********, 0.********, 90.********, 81.********, 1.********, 0.********, null, null, 'N', 0.********, 90.********, 81.********, 0.********, 8.92000000, 0.********, '~', '~', 0.********, 90.********, 0.********, 0.********, 81.********, 1, 0.********, 'N', '~', null, 'N', '~', '~', 'F1', '1002Z01********001K1', '~', '~', '0001A11********03ZM0', '0001A11********03ZLZ', '0001A11********00XCI', '0001A11********03ZM0', '0001A11********03ZLZ', '1005A11********EV72M', '1005A11********EV72N', '~', '~', '~', '~', null, '~', 'F1-Cxx-02', '1005A11********0UQ2I', null, null, null, '~', null, '~', '~', '~', '~', '~', '~', '~                   ', '~                   ', null, 0.********, null, 1.********, '~', 0, 0, null, '0001Z01********79UJJ', '~', '0001A11********03ZLZ', '1002Z01********001K1', 90.********, '~', '1005A11********EV72C', 'F3', '1005A11********EV72D', 'F3-Cxx-02', '~', '1005A11********008ZQ', '1005O91********4Q9SM', null, 0.********, 11.000000, 1, '1005A11********EV72C', 'F3', '1005A11********EV72D', 'F3-Cxx-02', '2024-06-19 18:45:23', null, null);
INSERT INTO AP_PAYABLEITEM (ASSETPACTNO, BANKROLLPROJET, BILLCLASS, BILLDATE, BILLNO, BUSIDATE, BUYSELLFLAG, CALTAXMNY, CASHITEM, CBS, CHECKELEMENT, CHECKTYPE, CONFERNUM, CONTRACTNO, COORDFLAG, COSTCENTER, CUSTOMER, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DIRECTION, DR, EQUIPMENTCODE, FACARD, FREECUST, GLOBALBALANCE, GLOBALCREBIT, GLOBALNOTAX_CRE, GLOBALRATE, GLOBALTAX_CRE, GROUPBALANCE, GROUPCREBIT, GROUPNOTAX_CRE, GROUPRATE, GROUPTAX_CRE, INNERORDERNO, INVOICENO, ISURGENT, LOCAL_MONEY_BAL, LOCAL_MONEY_CR, LOCAL_NOTAX_CR, LOCAL_PRICE, LOCAL_TAX_CR, LOCAL_TAXPRICE, MATERIAL, MATERIAL_SRC, MONEY_BAL, MONEY_CR, NOSUBTAX, NOSUBTAXRATE, NOTAX_CR, OBJTYPE, OCCUPATIONMNY, OPPTAXFLAG, ORDERCUBASDOC, OUTSTORENO, PAUSETRANSACT, PAYACCOUNT, PK_BALATYPE, PK_BILLTYPE, PK_CURRTYPE, PK_DEPTID, PK_DEPTID_V, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYABLEBILL, PK_PAYABLEITEM, PK_PAYTERM, PK_PCORG, PK_PCORG_V, PK_PSNDOC, PK_SSITEM, PK_SUBJCODE, PK_TRADETYPE, PK_TRADETYPEID, POSTPRICE, POSTPRICENOTAX, POSTQUANTITY, POSTUNIT, PRICE, PRODUCTLINE, PROJECT, PROJECT_TASK, PU_DEPTID, PU_DEPTID_V, PU_ORG, PU_ORG_V, PU_PSNDOC, PURCHASEORDER, QUANTITY_BAL, QUANTITY_CR, RATE, RECACCOUNT, ROWNO, ROWTYPE, SCOMMENT, SENDCOUNTRYID, SETT_ORG, SETT_ORG_V, SETTLECURR, SETTLEMONEY, SETTLENO, SRC_BILLID, SRC_BILLTYPE, SRC_ITEMID, SRC_TRADETYPE, SUBJCODE, SUPPLIER, TAXCODEID, TAXNUM, TAXPRICE, TAXRATE, TAXTYPE, TOP_BILLID, TOP_BILLTYPE, TOP_ITEMID, TOP_TRADETYPE, TS, VATCODE, VENDORVATCODE) VALUES (null, '~', 'yf', '2021-09-14 10:23:12', 'D12021091400000608', '2021-09-14 10:23:12', 2, 12.********, '~', '~', '~', '~', null, null, null, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', null, '~', '~', -1, 0, null, '~', '~', 0.********, 0.********, 0.********, 0.********, 0.********, 12.********, 12.********, 12.********, 1.********, 0.********, null, null, 'N', 12.********, 12.********, 12.********, 0.********, 0.********, 0.********, '~', '~', 12.********, 12.********, 0.********, 0.********, 12.********, 1, 0.********, 'N', '~', null, 'N', '~', '~', 'F1', '1002Z01********001K1', '1005A11********06UUU', '0001A11********133JF', '0001A11********03ZM0', '0001A11********03ZLZ', '0001A11********00XCI', '0001A11********03ZM0', '0001A11********03ZLZ', '1005O41********3MSC2', '1005O41********3MSC3', '~', '~', '~', '~', null, '~', 'F1-Cxx-02', '1005A11********0UQ2I', null, null, null, '~', null, '~', '~', '~', '~', '~', '~', '~                   ', '~                   ', null, 0.********, null, 1.********, '~', 0, null, '~', '0001Z01********79UJJ', '0001A11********03ZM0', '0001A11********03ZLZ', '~', 0.********, '~', null, '~', null, '~', '~', '1005A11********008ZQ', '1001Z01********3W0X1', null, 0.********, 0.000000, 1, null, '~', null, '~', '2024-05-27 10:05:51', null, null);

INSERT INTO PO_INVOICE (APPROVER, BAPFLAG, BFEE, BFROZEN, BILLMAKER, BINITIAL, BOPPTAXFLAG, BTRIATRADEFLAG, BVIRTUAL, CCURRENCYID, CORIGCURRENCYID, CREATIONTIME, CREATOR, CRECECOUNTRYID, CSENDCOUNTRYID, CTAXCOUNTRYID, CTRADEWORDID, CTRANTYPEID, DARRIVEDATE, DBILLDATE, DMAKEDATE, DR, FBILLSTATUS, FBUYSELLFLAG, FINVOICECLASS, FINVOICETYPE, FTAXTYPEFLAGH, IPRINTCOUNT, MODIFIEDTIME, MODIFIER, NEXCHANGERATE, NGLOBALEXCHGRATE, NGROUPEXCHGRATE, NTAXRATEH, NTOTALASTNUM, NTOTALORIGMNY, PK_BALATYPE, PK_BANKACCBAS, PK_BIZPSN, PK_BUSITYPE, PK_DEPT, PK_DEPT_V, PK_FREECUST, PK_FROZENUSER, PK_GROUP, PK_INVOICE, PK_ORG, PK_ORG_V, PK_PARENTINVOICE, PK_PAYTERM, PK_PAYTOSUPPLIER, PK_PURCHASEORG, PK_PURCHASEORG_V, PK_STOCKORG, PK_STOCKORG_V, PK_SUPPLIER, TAUDITTIME, TFROZENTIME, TS, VADJUSTREASON, VBILLCODE, VDEF1, VDEF10, VDEF11, VDEF12, VDEF13, VDEF14, VDEF15, VDEF16, VDEF17, VDEF18, VDEF19, VDEF2, VDEF20, VDEF3, VDEF4, VDEF5, VDEF6, VDEF7, VDEF8, VDEF9, VFROZENREASON, VMEMO, VTRANTYPECODE, VVATCODE, VVENDORVATCODE, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS) VALUES ('~', 'N', 'N', 'N', '1005A51********B3TH2', 'N', 'N', 'N', 'N', '1002Z01********001K1', '1002Z01********001K1', '2024-12-23 14:05:53', '1005A51********B3TH2', '0001Z01********79UJJ', '0001Z01********79UJJ', '0001Z01********79UJJ', '~', '0001A11********03GQ3', '2024-12-23 14:05:09', '2024-12-23 14:05:09', '2024-12-23 14:05:53', 0, 1, 2, 0, 0, 1, 0, null, '~', 1.********, null, 1.********, 9.********, 90000.********, 99900.********, '~', '~', '~', '1005A51********049AL', '1005A11********0FSV3', '0001A11********1DNF1', '~', '~', '0001A11********00XCI', '1005A11********FA0XD', '0001A11********16ZWF', '0001A11********16ZWE', '~', '~', '1005A11********07QVA', '0001A11********16ZWF', '0001A11********16ZWE', '0001A11********16ZWF', '0001A11********16ZWE', '1005A11********07QVA', null, null, '2024-12-23 14:05:54', null, 'CF2024122300000090', '~', 'N', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', null, null, '25-01', null, null, '03603dc4-f2b7-4838-97f3-60467497fb0f', 0, '2024122330e4c18e-89db-49e4-a95e-267629de05e3', 0);

-- 更新 AP_PAYABLEBILL 表
UPDATE AP_PAYABLEBILL SET INVOICENO = 'CF2024122300000090' WHERE BILLNO IN ('D12024061800001212');
UPDATE AP_PAYABLEBILL SET INVOICENO = 'CF2024122300000090' WHERE BILLNO IN ('D12024061900001214');
UPDATE AP_PAYABLEBILL SET INVOICENO = 'CF2024122300000090' WHERE BILLNO IN ('D12021091400000608');

UPDATE AP_PAYABLEBILL SET PK_TRADETYPE = 'F1-Cxx-02', SRC_SYSCODE = 4, EFFECTSTATUS = 10 WHERE DR = 0;
UPDATE AP_PAYABLEBILL SET BILLMAKER = '1001A710000000K5J08D' WHERE DR = 0;

-- 更新 AP_PAYABLEITEM 表
UPDATE AP_PAYABLEITEM SET INVOICENO = 'CF2024122300000090' WHERE BILLNO IN ('D12024061800001212');
UPDATE AP_PAYABLEITEM SET INVOICENO = 'CF2024122300000090' WHERE BILLNO IN ('D12024061900001214');
UPDATE AP_PAYABLEITEM SET INVOICENO = 'CF2024122300000090' WHERE BILLNO IN ('D12021091400000608');

UPDATE AP_PAYABLEITEM SET PURCHASEORDER = 'PO202502280001', OUTSTORENO = 'OUT202502280001' WHERE BILLNO IN ('D12024061800001212');
UPDATE AP_PAYABLEITEM SET PURCHASEORDER = 'PO202502280001', OUTSTORENO = 'OUT202502280001' WHERE BILLNO IN ('D12024061900001214');
UPDATE AP_PAYABLEITEM SET PURCHASEORDER = 'PO202502280001', OUTSTORENO = 'OUT202502280001' WHERE BILLNO IN ('D12021091400000608');

-- 更新 PO_INVOICE 表
UPDATE PO_INVOICE SET FBILLSTATUS = 3 WHERE PK_INVOICE = '1005A11********FA0XD';
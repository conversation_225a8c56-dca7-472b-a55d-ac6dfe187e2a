package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 采购/项目应付导出VO
 */
@Data
@ColumnWidth(25)
public class FiSourceAccountingExportVO {

    @ExcelProperty(value = "财务组织", index = 0)
    private String pkOrg;

    @ExcelProperty(value = "凭证主键", index = 1)
    private String pkVoucher;

    @ExcelProperty(value = "凭证编码", index = 2)
    private String num;

    @ExcelProperty(value = "业务单元", index = 3)
    private String free5;

    @ExcelProperty(value = "摘要", index = 4)
    private String explanation;

    @ExcelProperty(value = "制单日期", index = 5)
    private String prepareddate;

    @ExcelProperty(value = "记账日期", index = 6)
    private String tallydate;

    @ExcelProperty(value = "会计期间", index = 7)
    private Integer period;

    @ExcelProperty(value = "制单人", index = 8)
    private String pkPrepared;

    @ExcelProperty(value = "审核人", index = 9)
    private String pkChecked;

    @ExcelProperty(value = "币种", index = 10)
    private String pkCurrtype;

    @ExcelProperty(value = "附单据数", index = 11)
    private String attachment;

    @ExcelProperty(value = "会计年度", index = 12)
    private String year;

    @ExcelProperty(value = "调整期间", index = 13)
    private String adjustperiod;

    @ExcelProperty(value = "冲销凭证", index = 14)
    private String offervoucher;

    @ExcelProperty(value = "推送状态", index = 15)
    private Integer pushStatus;

    @ExcelProperty(value = "推送状态名称", index = 16)
    private String pushStatusName;

    @ExcelProperty(value = "推送成功失败信息", index = 17)
    private String pushMsg;

    /**
     * 创建时间
     */
    @ExcelProperty(value="拉取时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/1/24.
 */
@Data
public class AdvPayVO {

    /**
     * 供应商编码
     */
    @ApiModelProperty(value="供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value="供应商名称")
    private String supp;

    /**
     * 预付款金额
     */
    @ApiModelProperty(value="预付款金额")
    private BigDecimal advPmlAmt;

    /**
     * 应收交易台账业务主键
     */
    @ApiModelProperty(value="应收交易台账业务主键")
    private String transLegId;

    /**
     * 预付款日期
     */
    @ApiModelProperty(value="预付款日期")
    private String advPayDate;

    /**
     * 预付款余额
     */
    @ApiModelProperty(value="预付款余额")
    private BigDecimal prePmtBal;

    /**
     * 已冲销金额
     */
    @ApiModelProperty(value="已冲销金额")
    private BigDecimal amtElim;

    /**
     * 本次冲销金额
     */
    @ApiModelProperty(value="本次冲销金额")
    private BigDecimal wtOffAmt;

    /**
     * 预付单号
     */
    @ApiModelProperty(value="预付单号")
    private BigDecimal ppdBillNo;

    /**
     * 保证金类型
     */
    @ApiModelProperty(value="保证金类型")
    private String marGinType;

    /**
     * 在途冲销金额_原币
     */
    @ApiModelProperty(value="在途冲销金额_原币")
    private BigDecimal amtElimInTransitCy;

    /**
     * 币种
     */
    @ApiModelProperty(value="币种")
    private String moneyDm;

    /**
     * 汇率
     */
    @ApiModelProperty(value="汇率")
    private BigDecimal exchRat;

    /**
     * 对账编号
     */
    @ApiModelProperty(value="对账编号")
    private String chkId;

    /**
     * 本位币已冲销金额
     */
    @ApiModelProperty(value="本位币已冲销金额")
    private BigDecimal amtElimCy;

    /**
     * 本位币预付款金额
     */
    @ApiModelProperty(value="本位币预付款金额")
    private BigDecimal advPmlAmtCy;

    /**
     * 本位币代码
     */
    @ApiModelProperty(value="本位币代码")
    private String staCyCode;

    /**
     * 本次冲销金额本位币
     */
    @ApiModelProperty(value="本次冲销金额本位币")
    private BigDecimal wtOffAmtCy;

}

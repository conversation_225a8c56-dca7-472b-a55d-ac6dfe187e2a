

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单转换数据主表
 *
 * <AUTHOR>
 * @date 2025-01-16 16:52:58
 */
@Data
@TableName("fi_convert_order")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "订单转换数据主表")
public class FiConvertOrder extends BaseEntity<FiConvertOrder> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 订单主键
     */
    @ApiModelProperty(value="订单主键")
    private String pkOrder;

    /**
     * 项目主键
     */
    @ApiModelProperty(value="项目主键")
    private String pkProject;

    /**
     * 订单编号
     */
    @ApiModelProperty(value="订单编号")
    private String vbillcode;

    /**
     * 采购组织
     */
    @ApiModelProperty(value="采购组织")
    private String pkOrg;

    /**
     * 财务组织名称
     */
    @ApiModelProperty(value="财务组织名称")
    private String pkOrgName;

    /**
     * 采购组织
     */
    @ApiModelProperty(value="采购组织")
    private String pkOrgV;

    /**
     * 制单日期
     */
    @ApiModelProperty(value="制单日期")
    private String dmakedate;

    /**
     * 制单人
     */
    @ApiModelProperty(value="制单人")
    private String billmaker;

    /**
     * 供应商
     */
    @ApiModelProperty(value="供应商")
    private String pkSupplier;

    /**
     * 币种
     */
    @ApiModelProperty(value="币种")
    private String corigcurrencyid;

    /**
     * 合同号
     */
    @ApiModelProperty(value="合同号")
    private String vcontractcode;

    /**
     * 无税金额
     */
    @ApiModelProperty(value="无税金额")
    private String norigmny;

    /**
     * 调整方式
     */
    @ApiModelProperty(value="调整方式")
    private String nexchangerate;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String ntax;

    /**
     * 退货
     */
    @ApiModelProperty(value="退货")
    private String breturn;

    /**
     * 订单类型
     */
    @ApiModelProperty(value="订单类型")
    private String ctrantypeid;

    /**
     * 单据类型编码
     */
    @ApiModelProperty(value="单据类型编码")
    private String vtrantypecode;

}

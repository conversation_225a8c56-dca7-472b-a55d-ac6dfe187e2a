package com.cloud.ficonsumer.cache;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cloud.cloud.admin.api.entity.SysDictItem;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.entity.ApiCompare;
import com.cloud.ficonsumer.mapper.ApiCompareMapper;
import com.cloud.ficonsumer.mapper.ApiExchangeMapper;
import com.cloud.ficonsumer.mapper.ApiUpmsMapper;
import com.cloud.ficonsumer.service.ApiCompareService;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@AllArgsConstructor
@Slf4j
public class ApiCompareCache {
    private Map<String, ApiCompare> comPareCache = new HashMap<>();

    private Map<String, ApiCompare> reverseCompareCache = new HashMap<>();

    private Map<String, Map<String, String>> apiAuxiliaryItemCache = new HashMap<>();

    private final ApiCompareService apiCompareService;
    private final ApiUpmsMapper apiUpmsMapper;


    @PostConstruct
    public void loadAllData() {
        // 加载所有对照表数据到缓存
        comPareCache.putAll(queryCompareAllData());
        //加载字段映射字典项
        apiAuxiliaryItemCache.putAll(queryApiAuxiliaryItem());
        //加载逆向对照表数据到缓存
        reverseCompareCache.putAll(queryReverseCompareAllData());
    }

    // 从数据库获取所有正向对照表数据,以源数据主键为key
    private Map<String, ApiCompare> queryCompareAllData() {
        //获取档案类型字典项
        List<SysDictItem> fileTypeDictItems = apiUpmsMapper.getFileTypeDictItems();
        Map<String, String> collect = fileTypeDictItems.stream().collect(Collectors.toMap(SysDictItem::getValue, SysDictItem::getRemarks, (key1, key2) -> key2));
        //暂时取出对照表所有数据
        List<ApiCompare> apiCompares = apiCompareService.list();
        Map<String,ApiCompare> apiComparesMap=new HashMap<>();
        //缓存对照数据  key:fileType+code
        apiCompares.forEach(apiCompare -> {
            String fileTypeCode=collect.get(apiCompare.getFileType());
            if (StringUtils.isNotBlank(fileTypeCode)){
                apiComparesMap.put(fileTypeCode+"_"+apiCompare.getSourceSystemId(),apiCompare);
            }
        });
        return apiComparesMap;
    }

    // 从数据库获取所有逆向对照表数据，以目标数据主键为key
    private Map<String, ApiCompare> queryReverseCompareAllData() {
        //获取档案类型字典项
        List<SysDictItem> fileTypeDictItems = apiUpmsMapper.getFileTypeDictItems();
        Map<String, String> collect = fileTypeDictItems.stream().collect(Collectors.toMap(SysDictItem::getValue, SysDictItem::getRemarks, (key1, key2) -> key2));
        //暂时取出对照表所有数据
        List<ApiCompare> apiCompares = apiCompareService.list();
        Map<String,ApiCompare> apiComparesMap=new HashMap<>();
        //缓存对照数据  key:fileType+code
        apiCompares.forEach(apiCompare -> {
            String fileTypeCode=collect.get(apiCompare.getFileType());
            if (StringUtils.isNotBlank(fileTypeCode)){
                apiComparesMap.put(fileTypeCode+"_"+apiCompare.getTargetSystemId(),apiCompare);
            }
        });
        return apiComparesMap;
    }

    //获取字段映射字典项
    private Map<String, Map<String, String>> queryApiAuxiliaryItem(){
        //获取字段映射字典项
        List<SysDictItem> auxiliaryDictItems = apiUpmsMapper.getAuxiliaryDictItems();
        //根据不同的表分组
        Map<String, Map<String, String>> auxiliaryDictMap =
                auxiliaryDictItems.stream().collect(Collectors.groupingBy(SysDictItem::getRemarks, Collectors.toMap(SysDictItem::getValue, SysDictItem::getLabel)));
        return auxiliaryDictMap;
    }

    public void reloadCompareCache() {
        // 重新加载整个缓存
        comPareCache.clear();
        apiAuxiliaryItemCache.clear();
        reverseCompareCache.clear();
        comPareCache.putAll(queryCompareAllData());
        apiAuxiliaryItemCache.putAll(queryApiAuxiliaryItem());
        reverseCompareCache.putAll(queryReverseCompareAllData());
    }

    public ApiCompare getCompareValue(String key) {
        return comPareCache.get(key);
    }

    //tableName 表名
    //key 字段名
    //data 字段值
    public ApiCompare getDictionaryValue(String tableName,String key,String data) {
        Map<String, String> map = apiAuxiliaryItemCache.get(tableName);
        if (CollectionUtil.isNotEmpty(map)){
            String value = map.get(key);
            if (StringUtils.isNotBlank(value)){
                return comPareCache.get(value+"_"+data);
            }
        }
        return null;
    }

    //tableName 表名
    //key 字段名
    //data 字段值
    public ApiCompare getReverseDictionaryValue(String tableName,String key,String data) {
        Map<String, String> map = apiAuxiliaryItemCache.get(tableName);
        if (CollectionUtil.isNotEmpty(map)){
            String value = map.get(key);
            if (StringUtils.isNotBlank(value)){
                return reverseCompareCache.get(value+"_"+data);
            }
        }
        return null;
    }

    //获取字段对应的档案类型编码，例如 f1对应F00001
    public String getFieldCode(String tableName,String key) {
        Map<String, String> map = apiAuxiliaryItemCache.get(tableName);
        if (CollectionUtil.isNotEmpty(map)){
            String value = map.get(key);
            return value;
        }
        return null;
    }

    public Map<String, ApiCompare> getCompareCache() {
        return comPareCache;
    }

    @SneakyThrows
    public void convertData(Object source, Object target,String tableName) {
        Class<?> classA = source.getClass();
        Class<?> classB = target.getClass();
        //获取该表中需要转换的字段  key是字段名，value是档案标识
        Map<String, String> convertFields = apiAuxiliaryItemCache.get(tableName);
        if (CollectionUtil.isEmpty(convertFields)){
            convertFields = new HashMap<>();
        }
        // 遍历A对象的所有字段
        for (Field fieldA : classA.getDeclaredFields()) {
            fieldA.setAccessible(true);
            String fieldName = fieldA.getName();
            Object fieldValue = fieldA.get(source);
            Object valueToSet=null;
            if(fieldName.equals("id") || fieldName.equals("serialVersionUID")){
                continue;
            }
            if(Objects.isNull(fieldValue) || StringUtils.isBlank(fieldValue.toString()) ||
                    fieldValue.toString().trim().equals("N/A") || fieldValue.toString().trim().equals("NN/A") ||
                    fieldValue.toString().trim().equals("null") || fieldValue.toString().trim().equals("NULL") ||
                    fieldValue.toString().trim().equals("～")||fieldValue.toString().trim().equals("~")){
                continue;
            }
            if (Objects.nonNull(fieldValue)) {
                String underscore = camelToUnderscore(fieldName);
                // 判断Map中是否有该字段
                if (convertFields.containsKey(underscore)) {
                    //去除比较字段的前后空格
                    String fieldValueTrim = (String) fieldValue;
                    ApiCompare apiCompare = getDictionaryValue(tableName, underscore, fieldValueTrim.trim());
                    // 获取转换后的值
                    if (Objects.nonNull(apiCompare)) {
                        valueToSet = apiCompare.getTargetSystemId();
                    }else {
                        throw new CheckedException("表"+tableName+"里的字段"+underscore+"字段转换为空");
                    }
                } else {
                    // 直接获取A对象的字段值
                    valueToSet = fieldValue;
                }

                // 查找B对象中同名字段
                try {
                    Field sameField = classB.getDeclaredField(fieldName);
                    sameField.setAccessible(true);
                    // 设置值到B对象的同名字段中
                    sameField.set(target, valueToSet);
                } catch (NoSuchFieldException e) {
                    // target对象中没有同名字段，什么都不管
                }
            }

        }
    }

    public void reverseConvertData(Object source, Object target,String tableName) throws Exception {
        Class<?> classA = source.getClass();
        Class<?> classB = target.getClass();
        //获取该表中需要转换的字段  key是字段名，value是档案标识
        Map<String, String> convertFields = apiAuxiliaryItemCache.get(tableName);
        if (CollectionUtil.isEmpty(convertFields)){
            convertFields = new HashMap<>();
        }
        // 遍历A对象的所有字段
        for (Field fieldA : classA.getDeclaredFields()) {
            fieldA.setAccessible(true);
            String fieldName = fieldA.getName();
            Object fieldValue = fieldA.get(source);
            Object valueToSet=null;
            if(fieldName.equals("id") || fieldName.equals("serialVersionUID")){
                continue;
            }
            if(Objects.isNull(fieldValue) || StringUtils.isBlank(fieldValue.toString()) ||
                    fieldValue.toString().trim().equals("N/A") || fieldValue.toString().trim().equals("NN/A") ||
                    fieldValue.toString().trim().equals("null") || fieldValue.toString().trim().equals("NULL") ||
                    fieldValue.toString().trim().equals("～")||fieldValue.toString().trim().equals("~")){
                continue;
            }
            if (Objects.nonNull(fieldValue)) {
                String underscore = camelToUnderscore(fieldName);
                // 判断Map中是否有该字段
                if (convertFields.containsKey(underscore)) {
                    //去除比较字段的前后空格
                    String fieldValueTrim = (String) fieldValue;
                    ApiCompare apiCompare = getReverseDictionaryValue(tableName, underscore, fieldValueTrim.trim());
                    // 获取转换后的值
                    if (Objects.nonNull(apiCompare)) {
                        valueToSet = apiCompare.getSourceSystemId();
                    }else {
                        throw new CheckedException("表"+tableName+"里的字段"+underscore+"字段转换为空");
                    }
                } else {
                    // 直接获取A对象的字段值
                    valueToSet = fieldValue;
                }

                // 查找B对象中同名字段
                try {
                    Field sameField = classB.getDeclaredField(fieldName);
                    sameField.setAccessible(true);
                    // 设置值到B对象的同名字段中
                    sameField.set(target, valueToSet);
                } catch (NoSuchFieldException e) {
                    // target对象中没有同名字段，什么都不管
                }
            }

        }
    }

    //驼峰转下划线
    public static String camelToUnderscore(String camelCase) {
        if (Character.isUpperCase(camelCase.charAt(0))) {
            // 特殊字段，保持原样
            return camelCase;
        }
        StringBuilder result = new StringBuilder();
        for (char c : camelCase.toCharArray()) {
            if (Character.isUpperCase(c)) {
                result.append('_').append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }
}

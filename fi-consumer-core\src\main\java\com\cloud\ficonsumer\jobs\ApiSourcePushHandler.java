package com.cloud.ficonsumer.jobs;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cloud.cloud.common.data.tenant.TenantBroker;
import com.cloud.ficonsumer.dto.FiSourceCostOrderDTO;
import com.cloud.ficonsumer.dto.*;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDTO;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.utils.LogUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.xxl.job.core.biz.model.ReturnT.FAIL;
import static com.xxl.job.core.biz.model.ReturnT.SUCCESS;

/**
 * 转换数据定时推送财务管控
 *
 * <AUTHOR>
 * @date 2024/07/10
 */
@Slf4j
@Component
@AllArgsConstructor
public class ApiSourcePushHandler {

    private final static String TENANT_ID = "tenantId";

    private final ApiProjectService apiProjectService;

    /**
     * 定时拉取订单增量数据
     */
    @XxlJob("getOrderList")
    public ReturnT<String> getOrderList() {
        LogUtil.info(log, "getOrderList开始:");
        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }
        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.getOrderList(new FiSourceOrderDTO()));
        LogUtil.info(log, "getOrderList结束:");
        return SUCCESS;
    }

    /**
     * 定时拉取增量数据
     */
    @XxlJob("getCostOrderList")
    public ReturnT<String> getCostOrderList() {
        LogUtil.info(log, "getCostOrderList开始:");
        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }
        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.getCostOrderList(new FiSourceCostOrderDTO()));
        LogUtil.info(log, "getCostOrderList结束:");
        return SUCCESS;
    }

    /**
     * 定时拉取增量数据
     */
    @XxlJob("getVirtualOrderList")
    public ReturnT<String> getVirtualOrderList() {
        LogUtil.info(log, "getVirtualOrderList开始:");
        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }
        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.getVirtualOrderList(new FiSourceVirtualOrderDTO()));
        LogUtil.info(log, "getVirtualOrderList结束:");
        return SUCCESS;
    }

    /**
     * 定时拉取通用报销单接口数据（通用报账）
     */
    @XxlJob("getReimbursementList")
    public ReturnT<String> getReimbursementList() {
        LogUtil.info(log, "getReimbursementList开始:");
        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }
        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.getReimbursementList(new FiSourceReimbursementDTO()));
        LogUtil.info(log, "getReimbursementList结束:");
        return SUCCESS;
    }

    /**
     * 定时拉取增量的项目应收数据（承包业务）
     */
    @XxlJob("getReceivableList")
    public ReturnT<String> getReceivableList() {
        LogUtil.info(log, "getReceivableList开始:");
        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }
        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.getReceivableList(new FiSourceReceivableDTO()));
        LogUtil.info(log, "getReceivableList结束:");
        return SUCCESS;
    }

    /**
     * 定时拉取增量的采购入库数据
     */
    @XxlJob("getStoreList")
    public ReturnT<String> getStoreList() {
        LogUtil.info(log, "getStoreList 开始:");
        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }
        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.getStoreList(new FiSourceStoreDTO()));
        LogUtil.info(log, "getStoreList 结束:");
        return SUCCESS;
    }

    /**
     * 定时拉取项目应付单数据
     */
    @XxlJob("getAccountingList")
    public ReturnT<String> getAccountingList() {
        LogUtil.info(log, "getAccountingList 开始:");
        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }
        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.getAccountingList(new FiSourceAccountingDTO()));
        LogUtil.info(log, "getAccountingList 结束:");
        return SUCCESS;
    }


    @XxlJob("incrementalPushPuPayable")
    public ReturnT<String> incrementalPushPuPayable() {
        LogUtil.info(log, "增量推送【采购-发票校验申请服务】开始：");

        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }

        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.incrementalPushPuPayable(new FiSourcePuPayableQueryDTO()));

        LogUtil.info(log, "增量推送【采购-发票校验申请服务】结束：");

        return SUCCESS;
    }

    @XxlJob("incrementalPushPuPaybill")
    public ReturnT<String> incrementalPushPuPaybill() {
        LogUtil.info(log, "增量推送【采购-预付付款申请单接收服务 (结算对照)】开始：");

        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }

        // 执行租户上下文中的增量推送
        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.incrementalPushPuPaybill(new FiSourcePuPaybillQueryDTO()));

        log.info("增量推送【采购-预付付款申请单接收服务 (结算对照)】结束：");

        return SUCCESS;
    }

    @XxlJob("incrementalPushProjEstiPayable")
    public ReturnT<String> incrementalPushProjEstiPayable() {
        log.info("增量推送【项目分包-项目暂估应付单】开始：");

        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }

        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.incrementalPushProjEstiPayable());

        log.info("增量推送【项目分包-项目暂估应付单】结束：");

        return SUCCESS;
    }

    @XxlJob("incrementalPushProjPaybill")
    public ReturnT<String> incrementalPushProjPaybill() {
        log.info("增量推送【项目分包-接收通用报账单服务】开始：");

        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }

        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.incrementalPushProjPaybill());

        log.info("增量推送【项目分包-接收通用报账单服务】结束：");

        return SUCCESS;
    }

    @XxlJob("incrementalPushProjsubPayable")
    public ReturnT<String> incrementalPushProjsubPayable() {
        log.info("增量推送【项目分包-付款申请】开始：");

        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }

        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.incrementalPushProjsubPayable());

        log.info("增量推送【项目分包-付款申请】结束：");

        return SUCCESS;
    }

    @XxlJob("incrementalPushSalesOrder")
    public ReturnT<String> incrementalPushSalesOrder() {
        log.info("增量推送【销售订单接口-接收销售订单信息】开始：");

        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }

        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.incrementalPushSaleOrder());

        log.info("增量推送【销售订单接口-接收销售订单信息】结束：");

        return SUCCESS;
    }

    @XxlJob("incrementalPushPurchaseInvoice")
    public ReturnT<String> incrementalPushPurchaseInvoice() {
        LogUtil.info(log, "增量推送【进项发票采集-根据比对一致和人票关联数据创建进项发票入池信息服务】开始：");

        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }

        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.incrementalPushFileOcrInfo());

        LogUtil.info(log, "增量推送【进项发票采集-根据比对一致和人票关联数据创建进项发票入池信息服务】结束：");

        return SUCCESS;
    }

    /**
     * 重新推送失败的数据
     */
    @XxlJob("pushAgainTryJob")
    public ReturnT<String> pushAgainTryJob() {
        LogUtil.info(log, "pushAgainTryJob 开始:");
        String para = XxlJobHelper.getJobParam();
        JSONObject jsonObject = JSONUtil.parseObj(para);
        Integer tid = jsonObject.getInt(TENANT_ID);
        if (tid == null) {
            XxlJobHelper.handleFail("租户id不能为空");
            return FAIL;
        }
        TenantBroker.runAs(tid, tenantId ->
                apiProjectService.pushAgainTryJob());
        LogUtil.info(log, "pushAgainTryJob 结束:");
        return SUCCESS;
    }
}


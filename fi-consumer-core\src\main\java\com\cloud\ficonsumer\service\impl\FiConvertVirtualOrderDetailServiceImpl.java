
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertVirtualOrderDetail;
import com.cloud.ficonsumer.mapper.FiConvertVirtualOrderDetailMapper;
import com.cloud.ficonsumer.service.FiConvertVirtualOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * 虚拟采购订单转换数据详情表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:08
 */
@Service
public class FiConvertVirtualOrderDetailServiceImpl extends ServiceImpl<FiConvertVirtualOrderDetailMapper, FiConvertVirtualOrderDetail> implements FiConvertVirtualOrderDetailService {

}

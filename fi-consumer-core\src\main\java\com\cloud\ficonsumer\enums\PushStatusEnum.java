package com.cloud.ficonsumer.enums;

/**
 * 审批状态枚举  取字典pm_process_approve_status
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
public enum PushStatusEnum {

    //状态 4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败
    CONVERT_FAIL(1, "转换失败"),
    PUSH_FAIL(2, "推送失败"),
    PUSH_SUCCESS(3, "推送成功"),
    PUSH_DING(4, "推送中"),
    ;
    private Integer code;
    private String name;

    PushStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code获取枚举类
     *
     * @param code
     * @return
     */
    public static PushStatusEnum getByCode(Integer code) {
        for (PushStatusEnum en : values()) {
            if (code.equals(en.getCode())) {
                return en;
            }
        }
        return null;
    }

}

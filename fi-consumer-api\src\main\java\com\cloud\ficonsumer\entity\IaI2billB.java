package com.cloud.ficonsumer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购入库单明细实体类
 */
@Data
@ApiModel(value = "采购入库单明细")
public class IaI2billB {

    @ApiModelProperty(value = "采购入库表头主键")
    private String cbillid;

    @ApiModelProperty(value = "采购入库表体主键")
    private String cbillBid;

    @ApiModelProperty(value = "采购入库单据号")
    private String vbillcode;

    @ApiModelProperty(value = "行号")
    private String crowno;

    @ApiModelProperty(value = "数量")
    private BigDecimal nnum;

    @ApiModelProperty(value = "源头单据")
    private String cfirstid;

    @ApiModelProperty(value = "源头单据分录")
    private String cfirstbid;
}
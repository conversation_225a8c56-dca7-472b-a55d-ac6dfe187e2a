package com.cloud.ficonsumer.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.cloud.ficonsumer.vo.FiSourceFileInfoVO;
import com.cloud.ficonsumer.vo.FiSourceFileOcrInfoVO;
import com.cloud.ficonsumer.vo.InvoiceFileVO;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DS("filemanage")
@Mapper
public interface FileManageMapper {

    /**
     * 增加接收日志到日志记录表
     *
     * @param platFormLogVO 平台日志VO
     * @return 影响行数
     */
    int insertPlatFormLog(@Param("query") PlatFormLogVO platFormLogVO);

    /**
     * 更新接收日志到日志记录表
     *
     * @param platFormLogVO 平台日志VO
     * @return 影响行数
     */
    int updatePlatFormLog(@Param("query") PlatFormLogVO platFormLogVO);

    /**
     * 查询ERP集成日志
     *
     * @param pkBill     单据主键
     * @param pkBillType 单据类型
     * @return 集成日志
     */
    List<PlatFormLogVO> selectPlatFormLog(@Param("pkBill") String pkBill,
                                          @Param("pkBillType") String pkBillType);

    /**
     * 【进项发票采集-根据比对一致和人票关联数据创建进项发票入池信息服务】
     */
    List<FiSourceFileOcrInfoVO> listFileOrcInfo(@Param("enableOrgIds") Set<Long> enableOrgIds);

    List<FiSourceFileInfoVO> getFileInfos(@Param("fileId") Long fileId);

    void updateFileOcrInfoReceive(@Param("fileManageId") String fileManageId);

    void updateFileOcrInfoStatus(@Param("fileManageId") String fileManageId,
                                 @Param("pushStatus") Integer pushStatus,
                                 @Param("pushMsg") String pushMsg);

    /**
     * 根据erp主键查询单据的发票信息集合
     */
    List<InvoiceFileVO> getInvoiceByPk(@Param("pk") String pk);

    /**
     * 查询是否有需要查验的单据的发票
     *
     * @param pkPayablebill
     * @param dictTypeList
     * @return
     */
    List<Long> getSourceInvoiceList(@Param("pk") String pkPayablebill,@Param("dictTypeList") List<String> dictTypeList);
}

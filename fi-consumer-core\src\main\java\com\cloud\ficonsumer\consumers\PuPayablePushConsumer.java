package com.cloud.ficonsumer.consumers;

import com.cloud.apiexchange.client.consumer.IMqConsumer;
import com.cloud.apiexchange.client.consumer.MqResult;
import com.cloud.apiexchange.client.consumer.annotation.MqConsumer;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.service.FiSourcePuPayableService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.cloud.ficonsumer.enums.Constants.TOPIC_PU_PAYABLE_TRANS;

@Slf4j
@MqConsumer(topic = TOPIC_PU_PAYABLE_TRANS, group = Constants.Group.group, transaction = false)
@Component
@AllArgsConstructor
public class PuPayablePushConsumer implements IMqConsumer {

    private FiSourcePuPayableService fiSourcePuPayableService;

    @Override
    public MqResult consume(String pkPayablebill) {
        LogUtil.info(log, "【采购-发票校验申请服务】进行转换推送开始:", pkPayablebill);
        try {
            fiSourcePuPayableService.push(pkPayablebill);
        } catch (Exception e) {
            LogUtil.info(log, "【采购-发票校验申请服务】进行转换推送失败:", e);
            return new MqResult(MqResult.FAIL_CODE, AdminUtils.truncateString(e.getMessage(),1000));
        }
        return new MqResult(MqResult.SUCCESS_CODE,"【采购-发票校验申请服务】进行转换推送成功");
    }
}

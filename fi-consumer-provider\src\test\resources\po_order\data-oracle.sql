INSERT INTO PO_ORDER
(APPR<PERSON><PERSON>R, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, B<PERSON>NA<PERSON>LOSE, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>AKER, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>H, BR<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>RN, BRETURN, BS<PERSON><PERSON><PERSON>TO<PERSON>, CCONTRAC<PERSON>EXTPAT<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>VISEPS<PERSON>, CTRADEWORDID, CTRANT<PERSON>EID, <PERSON><PERSON><PERSON><PERSON><PERSON>, DC<PERSON>OSEDAT<PERSON>, D<PERSON>KEDATE, DR, FHTAXTYPEFLAG, FORDERSTATUS, IPRINTCOUNT, MODIFIEDTIME, MODIFIER, NHTAXRATE, NORGPREPAYLIMIT, NTOTALASTNUM, NTOTALORIGMNY, NTO<PERSON><PERSON><PERSON>CE, NTOTALVOLUME, NTOTALWEIGHT, NVERSION, PK_BALATYPE, P<PERSON>_<PERSON>NKDOC, PK_BUSITYPE, PK_DELIVERADD, PK_DEP<PERSON>, PK_DEPT_V, <PERSON><PERSON>_FREECUST, PK_FREEZEPSNDOC, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, P<PERSON>_<PERSON>VC<PERSON>PL<PERSON><PERSON>R, PK_OR<PERSON><PERSON>, P<PERSON>_<PERSON><PERSON>, PK_ORG_V, PK_PAYTERM, PK_PROJECT, PK_RECVCUSTOMER, PK_SUPPLIER, PK_TRANSPORTTYPE, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, TAUDITTIME, TFREEZETIME, TREVISIONTIME, TS, VBILLCODE, VCOOPORDERCODE, VDEF1, VDEF10, VDEF11, VDEF12, VDEF13, VDEF14, VDEF15, VDEF16, VDEF17, VDEF18, VDEF19, VDEF2, VDEF20, VDEF3, VDEF4, VDEF5, VDEF6, VDEF7, VDEF8, VDEF9, VFROZENREASON, VMEMO, VTRANTYPECODE, NACCPAYREQMNY, NACCPAYMNY, NINVOICEMNY, VDEF21, VDEF22, VDEF23, VDEF24, VDEF25, VDEF26, VDEF27, VDEF28, VDEF29, VDEF30, VDEF31, VDEF32, VDEF33, VDEF34, VDEF35, VDEF36, VDEF37, VDEF38, VDEF39, VDEF40, VDEF41, VDEF42, VDEF43, VDEF44, VDEF45, VDEF46, VDEF47, VDEF48, VDEF49, VDEF50, VDEF51, VDEF52, VDEF53, VDEF54, VDEF55, VDEF56, VDEF57, VDEF58, VDEF59, VDEF60, VDEF61, VDEF62, VDEF63, VDEF64, VDEF65, VDEF66, VDEF67, VDEF68, VDEF69, VDEF70, VDEF71, VDEF72, VDEF73, VDEF74, VDEF75, VDEF76, VDEF77, VDEF78, VDEF79, VDEF80)
VALUES('1001A710000000JAFQ2S', 'N', 'N', 'Y', 'N', '1001A710000000JAFQ2S', 'Y', 'N', 'N', 'N', 'N', NULL, '~', '1002Z0100000000001K1', '2025-04-09 09:12:17', '1001A710000000JAFQ2S', '~', '~', '1001A710000000K7SDS3', '2025-04-09 09:10:01', '2025-04-09 09:47:04', '2025-04-09 09:10:01', 0, 1, 3, NULL, NULL, '~', NULL, NULL, 3, 32700, 0, 0, 0, 1, '~', '~', '1001A710000000JTPY0P', '~', '1001A1100000000E7T9E', '0001A11000000000CZPC', '~', '~', '0001A110000000000CE4', '1001A1100000000I8JIF', '1001A710000000LD4YOE', '0001A110000000004OK3', '0001A110000000004OK2', '~', '~', '~', '1001A1100000000I8JIF', '~', 'f693d7a1-5fcc-4900-bb16-4c222cc510d5', 0, '2025040907012ad1-dc60-4f9e-b214-d4f8f578e361', 0, '2025-04-09 09:12:27', NULL, NULL, '2025-04-09 09:47:12', 'CD2025040900104915', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 'N', '~', '~', '~', NULL, NULL, '21-Cxx-20', NULL, 32700, 32700, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~');

INSERT INTO PO_ORDER_B
(BARRIVECLOSE, BBORROWPUR, BCOMPARE, BINVOICECLOSE, BLARGESS, BPAYCLOSE, BRECEIVEPLAN, BSTOCKCLOSE, BTRANSCLOSED, BTRIATRADEFLAG, CASSCUSTID, CASTUNITID, CCONTRACTID, CCONTRACTROWID, CCURRENCYID, CDESTIAREAID, CDESTICOUNTRYID, CDEVADDRID, CDEVAREAID, CECBILLBID, CECBILLID, CECTYPECODE, CFFILEID, CFIRSTBID, CFIRSTID, CFIRSTTYPECODE, CHANDLER, CORIGAREAID, CORIGCOUNTRYID, CORIGCURRENCYID, CPRAYBILLBID, CPRAYBILLCODE, CPRAYBILLHID, CPRAYBILLROWNO, CPRAYTYPECODE, CPRICEAUDIT_BB1ID, CPRICEAUDIT_BID, CPRICEAUDITID, CPRODUCTORID, CPROJECTID, CPROJECTTASKID, CQPBASESCHEMEID, CQTUNITID, CQUALITYLEVELID, CRECECOUNTRYID, CROWNO, CSENDCOUNTRYID, CSOURCEBID, CSOURCEID, CSOURCETYPECODE, CTAXCODEID, CTAXCOUNTRYID, CUNITID, CVENDDEVADDRID, CVENDDEVAREAID, DBILLDATE, DCORRECTDATE, DPLANARRVDATE, DR, FBUYSELLFLAG, FISACTIVE, FTAXTYPEFLAG, NACCCANCELINVMNY, NACCUMARRVNUM, NACCUMDEVNUM, NACCUMINVOICEMNY, NACCUMINVOICENUM, NACCUMPICKUPNUM, NACCUMRPNUM, NACCUMSTORENUM, NACCUMWASTNUM, NASTNUM, NBACKARRVNUM, NBACKSTORENUM, NCALCOSTMNY, NCALTAXMNY, NEXCHANGERATE, NFEEMNY, NGLOBALEXCHGRATE, NGLOBALMNY, NGLOBALTAXMNY, NGROUPEXCHGRATE, NGROUPMNY, NGROUPTAXMNY, NITEMDISCOUNTRATE, NMNY, NNETPRICE, NNOSUBTAX, NNOSUBTAXRATE, NNUM, NORIGMNY, NORIGNETPRICE, NORIGPRICE, NORIGTAXMNY, NORIGTAXNETPRICE, NORIGTAXPRICE, NPACKNUM, NPRICE, NQTNETPRICE, NQTORIGNETPRICE, NQTORIGPRICE, NQTORIGTAXNETPRC, NQTORIGTAXPRICE, NQTPRICE, NQTTAXNETPRICE, NQTTAXPRICE, NQTUNITNUM, NSUPRSNUM, NTAX, NTAXMNY, NTAXNETPRICE, NTAXPRICE, NTAXRATE, NVOLUMN, NWEIGHT, PK_APFINANCEORG, PK_APFINANCEORG_V, PK_APLIABCENTER, PK_APLIABCENTER_V, PK_ARRLIABCENTER, PK_ARRLIABCENTER_V, PK_ARRVSTOORG, PK_ARRVSTOORG_V, PK_BATCHCODE, PK_DISCOUNT, PK_FLOWSTOCKORG, PK_FLOWSTOCKORG_V, PK_GROUP, PK_MATERIAL, PK_ORDER, PK_ORDER_B, PK_ORG, PK_ORG_V, PK_PSFINANCEORG, PK_PSFINANCEORG_V, PK_RECEIVEADDRESS, PK_RECVSTORDOC, PK_REQCORP, PK_REQDEPT, PK_REQDEPT_V, PK_REQSTOORG, PK_REQSTOORG_V, PK_REQSTORDOC, PK_SRCMATERIAL, PK_SRCORDER_B, PK_SUPPLIER, TS, VBATCHCODE, VBDEF1, VBDEF10, VBDEF11, VBDEF12, VBDEF13, VBDEF14, VBDEF15, VBDEF16, VBDEF17, VBDEF18, VBDEF19, VBDEF2, VBDEF20, VBDEF3, VBDEF4, VBDEF5, VBDEF6, VBDEF7, VBDEF8, VBDEF9, VBMEMO, VCHANGERATE, VCONTRACTCODE, VECBILLCODE, VFIRSTCODE, VFIRSTROWNO, VFIRSTTRANTYPE, VFREE1, VFREE10, VFREE2, VFREE3, VFREE4, VFREE5, VFREE6, VFREE7, VFREE8, VFREE9, VPRICEAUDITCODE, VQTUNITRATE, VSOURCECODE, VSOURCEROWNO, VSOURCETRANTYPE, VVENDDEVADDR, VVENDINVENTORYCODE, VVENDINVENTORYNAME, VBDEF21, VBDEF22, VBDEF23, VBDEF24, VBDEF25, VBDEF26, VBDEF27, VBDEF28, VBDEF29, VBDEF30, VBDEF31, VBDEF32, VBDEF33, VBDEF34, VBDEF35, VBDEF36, VBDEF37, VBDEF38, VBDEF39, VBDEF41, VBDEF42, VBDEF43, VBDEF44, VBDEF45, VBDEF46, VBDEF47, VBDEF48, VBDEF49, VBDEF50, VBDEF51, VBDEF52, VBDEF53, VBDEF54, VBDEF55, VBDEF56, VBDEF57, VBDEF58, VBDEF59, VBDEF60, VBDEF61, VBDEF62, VBDEF63, VBDEF64, VBDEF65, VBDEF66, VBDEF67, VBDEF68, VBDEF69, VBDEF70, VBDEF71, VBDEF72, VBDEF73, VBDEF74, VBDEF75, VBDEF76, VBDEF77, VBDEF78, VBDEF79, VBDEF80, VBDEF40)
VALUES('Y', 'N', 'N', 'Y', 'N', 'Y', 'N', 'Y', 'N', 'N', '~', '0001Z0100000000000XT', '~', '~', '1002Z0100000000001K1', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '1001A710000000JAFQ2S', '~', '~', '1002Z0100000000001K1', NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '0001Z0100000000000XT', '~', '0001Z010000000079UJJ', '10', '0001Z010000000079UJJ', '~', '~', '~', '1002Z01000000001CNE2', '0001Z010000000079UJJ', '0001Z0100000000000XT', '~', '~', '2025-04-09 09:10:01', NULL, '2025-04-09 09:10:01', 0, 2, 1, 1, 32700, 3, NULL, 32700, 3, NULL, NULL, 3, 0, 3, NULL, NULL, 30000, 30000, 1, NULL, NULL, NULL, NULL, 1, 30000, 32700, 100, 30000, 10000, 0, 0, 3, 30000, 10000, 10000, 32700, 10900, 10900, NULL, 10000, 10000, 10000, 10000, 10900, 10900, 10000, 10900, 10900, 3, NULL, 2700, 32700, 10900, 10900, 9, 0, 0, '0001A110000000004OK3', '0001A110000000004OK2', '~', '~', '~', '~', '0001A110000000004OK3', '0001A110000000004OK2', '~', '~', '0001A110000000004OK3', '0001A110000000004OK2', '0001A110000000000CE4', '1001A710000000K5LDVT', '1001A710000000LD4YOE', '1001A710000000LD4YOF', '0001A110000000004OK3', '0001A110000000004OK2', '0001A110000000004OK3', '0001A110000000004OK2', '~', '~', '0001A110000000004OK3', '~', '~', '0001A110000000004OK3', '0001A110000000004OK2', '~', '1001A710000000K5LDVT', NULL, '1001A1100000000I8JIF', '2025-04-09 09:47:12', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', NULL, '1/1', NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '1/1', NULL, '~', '~', '~', NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~');

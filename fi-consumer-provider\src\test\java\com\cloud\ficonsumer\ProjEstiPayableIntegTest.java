package com.cloud.ficonsumer;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.entity.FiSourceProjEstiPayable;
import com.cloud.ficonsumer.entity.FiSourceProjEstiPayableDetail;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.service.FiSourceProjEstiPayableDetailService;
import com.cloud.ficonsumer.service.FiSourceProjEstiPayableService;
import com.cloud.ficonsumer.vo.FiSourceProjEstiPayableVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ProjEstiPayableIntegTest {
    
    @SpyBean
    private DmMapper dmMapper;
    
    @SpyBean
    private ApiProjectService apiProjectService;
    
    @SpyBean
    private FiSourceProjEstiPayableService fiSourceProjEstiPayableService;
    
    @SpyBean
    private FiSourceProjEstiPayableDetailService fiSourceProjEstiPayableDetailService;

    @BeforeEach
    public void initData() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);
        
        // 初始化 erp 库数据：项目暂估应付 oracle 数据库
        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator erpPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("projestipayable/schema-oracle.sql"),
                    new ClassPathResource("projestipayable/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(erpPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 初始化 mysql 库数据：项目暂估应付 mysql 数据库
        DynamicDataSourceContextHolder.push("mysql");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("projestipayable/schema-mysql.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    @Test
    public void testProcessProjEstiPayable() {
        // 获取ERP中的项目暂估应付数据
        List<FiSourceProjEstiPayableVO> fiSourceProjEstiPayableVOS = dmMapper.listProjEstiPayable();
        assertEquals(3, fiSourceProjEstiPayableVOS.size(), "ERP项目暂估应付表 中应存在三条数据");

        // 处理每条数据
        for (FiSourceProjEstiPayableVO projEstiPayable : fiSourceProjEstiPayableVOS) {
            apiProjectService.processProjEstiPayable(projEstiPayable);
        }

        // 验证MySQL中的数据
        List<FiSourceProjEstiPayable> list = fiSourceProjEstiPayableService.list();
        List<FiSourceProjEstiPayableDetail> detailList = fiSourceProjEstiPayableDetailService.list();
        assertEquals(3, list.size(), "MySQL项目暂估应付表 表中应存在三条数据");
        assertEquals(3, detailList.size(), "MySQL项目暂估应付明细表 表中应存在三条数据");

        // 验证ERP中的接收状态
        fiSourceProjEstiPayableVOS = dmMapper.listProjEstiPayable();
        for (FiSourceProjEstiPayableVO projEstiPayable : fiSourceProjEstiPayableVOS) {
            assertEquals(projEstiPayable.getDef20(), "1", "ERP项目暂估应付表 中接收状态已变更为1");
        }
    }
}
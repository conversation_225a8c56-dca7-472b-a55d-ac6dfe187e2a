

package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.apiexchange.entity.ApiInfoHis;
import com.cloud.ficonsumer.entity.ApiCompare;
import com.cloud.ficonsumer.vo.ApiCompareVO;

import java.util.List;

/**
 * 对照表
 *
 * <AUTHOR> code generator
 * @date 2024-07-12 10:14:10
 */
public interface ApiCompareService extends IService<ApiCompare> {

    /**
     * 新增对照数据
     * @param apiCompare
     * @return
     */
    Boolean saveApiCompare(ApiCompareVO apiCompare);

    /**
     * 编辑对照数据
     * @param apiCompare
     * @return
     */
    Boolean editApiCompare(ApiCompareVO apiCompare);

    /**
     * 根据id删除数据
     * @param id
     * @return
     */
    Boolean delById(Long id);

    /**
     * 通过id批量删除对照表
     *
     * @param ids
     * @return
     */
    boolean delBatchById(List<Long> ids);

    /**
     * 获取对照列表
     * @param page
     * @param apiCompare
     * @return
     */
    Page<ApiCompare> getApiComparePage(Page page, ApiCompareVO apiCompare);

    ApiExchange getApiExchangeByCode(String servCode);

    int saveApiInfoHis(ApiInfoHis apiMsg);

    /**
     * 根据类型和值转换
     *
     * @return
     */
    String getTurnByType(String value, String type);

    String getProjectCode(String pkProject);
}

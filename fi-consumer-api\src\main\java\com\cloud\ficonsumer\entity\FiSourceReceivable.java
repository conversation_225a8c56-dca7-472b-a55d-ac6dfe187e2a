

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目应收单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:25
 */
@Data
@TableName("fi_source_receivable")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "项目应收单源数据主表")
public class FiSourceReceivable extends BaseEntity<FiSourceReceivable> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 结算信息主键
     */
    @ApiModelProperty(value="结算信息主键")
    private String pkRecbill;

    /**
     * 单据类型
     * F0-Cxx-01 项目应收单
     * F0-Cxx-02 销售应收单
     */
    @ApiModelProperty(value="单据类型")
    private String pkTradetype;

    /**
     * 收支项目
     */
    @ApiModelProperty(value="收支项目")
    private String pkSubjcode;

    /**
     * 是否退回重传标识
     */
    @ApiModelProperty(value="是否退回重传标识")
    private String backTag;

    /**
     * 客户名称
     */
    @ApiModelProperty(value="客户名称")
    private String customer;

    /**
     * 组织
     */
    @ApiModelProperty(value="组织")
    private String pkOrg;

    /**
     * 财务组织名称
     */
    @ApiModelProperty(value="财务组织名称")
    private String pkOrgName;

    /**
     * 应收财务组织
     */
    @ApiModelProperty(value="应收财务组织")
    private String pkOrgV;

    /**
     * 应收财务组织
     */
    @ApiModelProperty(value="应收财务组织")
    private String contractno;

    /**
     * 制单人
     */
    @ApiModelProperty(value="制单人")
    private String project;

    /**
     * 制单人
     */
    @ApiModelProperty(value="制单人")
    private String billmaker;

    /**
     * 单据号
     */
    @ApiModelProperty(value="单据号")
    private String billno;

    /**
     * 摘要
     */
    @ApiModelProperty(value="摘要")
    private String scomment;

    /**
     * 组织本币金额
     */
    @ApiModelProperty(value="组织本币金额")
    private String localMoney;

    /**
     * 平台业务事项
     */
    @ApiModelProperty(value="平台业务事项")
    private String def47;

    /**
     * 推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中
     */
    @ApiModelProperty(value="推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中")
    private Integer pushStatus;

    /**
     * 推送成功失败信息
     */
    @ApiModelProperty(value="推送成功失败信息")
    private String pushMsg;

    /**
     * 推送时间
     */
    @ApiModelProperty(value="推送时间")
    private String pushTime;

    /**
     * 重试次数
     */
    @ApiModelProperty(value="重试次数")
    private Long tryNumber;

    /**
     * 0 未就绪 1 就绪
     */
    @ApiModelProperty(value="0 未就绪 1 就绪")
    private Long readiness;

}

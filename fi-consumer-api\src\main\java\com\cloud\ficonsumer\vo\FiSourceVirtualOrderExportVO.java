package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/9.
 */
@Data
@ColumnWidth(25)
public class FiSourceVirtualOrderExportVO {

    @ExcelProperty(value="组织名称")
    private String pkOrgName;

    @ExcelProperty(value = "订单编号")
    private String billCode;

    @ExcelProperty(value = "制单日期")
    private String billmaketime;

    @ExcelProperty(value="供应商名称")
    private String pkSupplierName;

    @ExcelProperty(value = "不含税合同金额")
    private String currMny;

    @ExcelProperty(value = "税额")
    private String tax;

    @ExcelProperty(value = "推送时间")
    private String pushTime;

    @ExcelProperty(value = "同步状态")
    private String pushStatusName;

    @ExcelProperty(value = "同步消息")
    private String pushMsg;

    /**
     * 创建时间
     */
    @ExcelProperty(value="拉取时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ExcelIgnore
    private Integer pushStatus;
}

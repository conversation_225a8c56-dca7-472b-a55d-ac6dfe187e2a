package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "发票信息")
public class YgInvoiceInfoVO {

    @ApiModelProperty(value = "发票类型", required = true)
    private String invoType;
    
    @ApiModelProperty(value = "发票号码", required = true)
    private String invoiceNo;
    
    @ApiModelProperty(value = "发票代码")
    private String invoCode;
    
    @ApiModelProperty(value = "开票日期", required = true)
    private String invDate;
    
    @ApiModelProperty(value = "税项")
    private String taxItem;
}

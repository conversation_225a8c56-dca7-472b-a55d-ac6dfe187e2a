package com.cloud.ficonsumer.builder;

import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.builder.context.YgInvoiceCheckContext;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.vo.ImageDetail;
import com.cloud.ficonsumer.vo.YgInvoiceCheckVO;
import com.cloud.ficonsumer.vo.YgPaymentInfoVO;
import com.cloud.ficonsumer.vo.YgPurchaseOrderDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class YgInvoiceCheckVOBuilder {

    /**
     * 构建发票校验VO的主入口
     */
    public YgInvoiceCheckVO build(FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withConvertData(convert, convertDetails)
                .withBasicInfo()
                .withCosTypeInfo()
                .withInvoiceInfo()
                .withAccountInfo()
                .withTopBill()
                .withSourceBill()
                .withImageInfo()
                .withValidation()
                .build();

        try {
            return buildWithContext(context);
        } catch (Exception e) {
            log.error("构建发票校验VO异常, 单据号: {}", convert.getBillno(), e);
            throw new CheckedException(MessageFormat.format(
                    "构建发票校验VO异常, 应付单单据号: {0}, 异常信息: {1}", convert.getBillno(), e.getMessage()), e);
        }
    }

    /**
     * 构建发票校验VO的主入口（支持源数据）
     */
    public YgInvoiceCheckVO build(FiSourcePuPayable source, List<FiSourcePuPayableDetail> sourceDetails,
                                  FiConvertPuPayable convert, List<FiConvertPuPayableDetail> convertDetails) {
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withSourceData(source, sourceDetails)
                .withConvertData(convert, convertDetails)
                .withBasicInfo()
                .withCosTypeInfo()
                .withInvoiceInfo()
                .withAccountInfo()
                .withTopBill()
                .withSourceBill()
                .withImageInfo()
                .withValidation()
                .build();

        try {
            return buildWithContext(context);
        } catch (Exception e) {
            log.error("构建发票校验VO异常, 单据号: {}", convert.getBillno(), e);
            throw new CheckedException(MessageFormat.format(
                    "构建发票校验VO异常, 应付单单据号: {0}, 异常信息: {1}", convert.getBillno(), e.getMessage()), e);
        }
    }

    private YgInvoiceCheckVO buildWithContext(YgInvoiceCheckContext context) {
        log.info("开始构建发票校验VO, 应付单单据号: {}", context.getConvert().getBillno());

        YgInvoiceCheckVO vo = new YgInvoiceCheckVO();

        buildBasicInfo(vo, context);
        buildInvoiceInfo(vo, context);
        buildPaymentInfo(vo, context);
        buildPurchaseOrderInfo(vo, context);
        buildImageInfo(vo, context);

        log.info("发票校验VO构建完成, 应付单单据号: {}", context.getConvert().getBillno());
        return vo;
    }

    public void buildBasicInfo(YgInvoiceCheckVO vo, YgInvoiceCheckContext context) {
        log.debug("开始构建基础信息, 应付单单据号: {}", context.getConvert().getBillno());

        FiConvertPuPayable convert = context.getConvert();
        List<FiConvertPuPayableDetail> details = context.getConvertDetails();

        String contractNo = details.get(0).getContractno();
        BigDecimal rate = details.get(0).getRate().setScale(2, RoundingMode.HALF_UP);
        String orderType = details.get(0).getSrcTradetype();

        vo.setBizappid("19773");                    // 应用ID
        vo.setDwdh(context.getMdnOrgCode());        // 单位代号
        vo.setHdlgNmId(convert.getBillmaker());     // 经办人ID
        vo.setBiType(3);                            // 业务类型：3-发票校验
        vo.setOuterCode("ZJYJ");                    // 外部系统标识：ZJYJ-资金预警
        vo.setDisCode(convert.getBillno());         // 单据编号
        vo.setConNam(StringUtils.isNotEmpty(contractNo) && contractNo.startsWith("SG")
                ? contractNo : null);               // 合同编号（仅SG开头）
        vo.setOrderType(orderType);                 // 订单类型：1-物资 2-服务 3-寄售
        vo.setPayType(StringUtils.equals("1", orderType) ?
                "00000008" : "00000002");           // 支付类型
        vo.setTotAmoPaid(BigDecimal.ZERO);          // 付款总金额
        vo.setOrderPayDate(LocalDate.now()          // 订单支付日期（当前日期后30天）
                .plusDays(30)
                .format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        vo.setBackTag(convert.getBackTag());        // 退单标识
        vo.setTarnCurren(convert.getPkCurrtype());  // 交易币种
        vo.setExchRat(rate);                        // 汇率
        vo.setCosType(context.getCosType3());       // 费用类型
        vo.setDetCosType(context.getCosTypeName4());// 明细费用类型
        vo.setDetCosTypeCode(context.getCosType4());// 明细费用类型编码

        if (context.isProjectPayable()) {
            vo.setOrderType("2");                         // 项目分包应付-订单类型：2-服务
            vo.setMainDeptType(convert.getDef52());       // 部门分类
            vo.setEnginProje(context.getProjectCode()); // 工程项目编码
        }

        log.debug("基础信息构建完成, 应付单单据号: {}", convert.getBillno());
    }

    public void buildInvoiceInfo(YgInvoiceCheckVO vo, YgInvoiceCheckContext context) {
        log.debug("开始构建发票信息, 应付单单据号: {}", context.getConvert().getBillno());

        vo.setSub1(context.getInvoiceMap().values().stream()
                .filter(list -> !list.isEmpty())
                .flatMap(list -> list.stream()
                        // 物资类采购，税项为默认值：0A010199
                        .peek(invoiceInfo -> {
                            if ("1".equals(vo.getOrderType())) {
                                invoiceInfo.setTaxItem("0A010199");
                            }
                        }))
                .collect(Collectors.toList()));

        log.debug("发票信息构建完成, 发票数量: {}", vo.getSub1().size());
    }

    public void buildPaymentInfo(YgInvoiceCheckVO vo, YgInvoiceCheckContext context) {
        log.debug("开始构建付款信息");

        vo.setSub3(Collections.singletonList(createMergedPaymentInfo(context)));

        log.debug("完成构建付款信息");
    }

    private YgPaymentInfoVO createMergedPaymentInfo(YgInvoiceCheckContext context) {
        YgPaymentInfoVO paymentInfo = new YgPaymentInfoVO();

        // 设置供应商信息
        paymentInfo.setSupp(context.getSupplierCode()); // 供应商编码
        paymentInfo.setAmtThisPay(BigDecimal.valueOf(0.00d)); // 本次付款金额
        paymentInfo.setFinMode(null); // 融资方式

        // 计算合并值
        BigDecimal totalRevDep = BigDecimal.ZERO;
        BigDecimal totalCurwriOffAdvPmtTax = BigDecimal.ZERO;

        // 合并所有明细的值
        for (FiConvertPuPayableDetail detail : context.getConvertDetails()) {
            BigDecimal revDep = parseBigDecimal(detail.getDef38());
            BigDecimal curwriOffAdvPmtTax = parseBigDecimal(detail.getDef24());

            if (revDep != null) {
                totalRevDep = totalRevDep.add(revDep);
            }

            if (curwriOffAdvPmtTax != null) {
                totalCurwriOffAdvPmtTax = totalCurwriOffAdvPmtTax.add(curwriOffAdvPmtTax);
            }
        }

        // 设置合并后的值
        paymentInfo.setRevDep(totalRevDep); // 质保金金额
        paymentInfo.setCurwriOffAdvPmtTax(totalCurwriOffAdvPmtTax); // 当期核销预付款含税金额

        // 其他信息设置
        paymentInfo.setCosType(context.getCosType3()); // 费用类型
        paymentInfo.setDetCosType(context.getCosTypeName4()); // 明细费用类型
        paymentInfo.setDetCosTypeCode(context.getCosType4()); // 明细费用类型编码
        paymentInfo.setMainDeptType(context.getConvert().getDef52()); // 部门分类

        // 供应商银行账户相关
        context.getRecaccountMap().forEach((pk, account) -> {
            paymentInfo.setRAcc(account.getAccnum());
            paymentInfo.setRcvrAccName(account.getAccname());
            paymentInfo.setBank(account.getCombinenum());
        });

        // 项目信息
        if (context.isProjectPayable()) {
            paymentInfo.setPrjName(context.getProjectCode()); // 项目名称
        }

        return paymentInfo;
    }

    public void buildPurchaseOrderInfo(YgInvoiceCheckVO vo, YgInvoiceCheckContext context) {
        log.debug("开始构建采购订单入库明细信息");

        String srcBillType = context.getConvertDetails().get(0).getSrcBilltype();

        List<YgPurchaseOrderDetailVO> details = new ArrayList<>();

        // 采购应付
        if (context.isPurchasePayable()) {
            buildPurchaseOrderDetails(details, context, vo);
        }
        // 项目分包应付
        else if (context.isProjectPayable()) {
            if ("4D42".equals(srcBillType)) {
                // 付款合同
                buildPaymentContractDetails(details, context);
            } else if ("4D83".equals(srcBillType)) {
                // 费用结算单
                buildCostSettlementDetails(details, context);
            }
        }

        vo.setSub6(details);

        // 单头订单号
        vo.setPurOrdNo(details.get(0).getPurOrdNum());

        log.debug("采购订单入库明细信息构建完成, 明细数量: {}", details.size());
    }

    private void buildPurchaseOrderDetails(List<YgPurchaseOrderDetailVO> details, YgInvoiceCheckContext context, YgInvoiceCheckVO vo) {
        Map<String, PoOrderB> poOrderBMap = context.getPoOrderBMap();
        if (poOrderBMap.isEmpty()) {
            log.warn("未找到相关的采购订单明细信息");
            return;
        }

        Map<String, IaI2billB> iaI2billBMap = context.getIaI2billBMap();
        if (iaI2billBMap.isEmpty()) {
            log.warn("未找到相关的入库单明细信息");
        }

        Map<String, FiConvertPuPayableDetail> detailMap = context.getConvertDetails().stream()
                .filter(detail -> StringUtils.isNotBlank(detail.getSrcItemid()))
                .collect(Collectors.toMap(
                        FiConvertPuPayableDetail::getSrcItemid,     // key: 采购订单明细主键
                        Function.identity(),                        // value: 明细对象本身
                        (v1, v2) -> v1
                ));

        for (Map.Entry<String, PoOrderB> entry : poOrderBMap.entrySet()) {
            String pkOrderB = entry.getKey();
            PoOrderB poOrderB = entry.getValue();
            IaI2billB iaI2billB = iaI2billBMap.get(pkOrderB);
            FiConvertPuPayableDetail puPayableDetail = detailMap.get(pkOrderB);

            YgPurchaseOrderDetailVO detail = createPurchaseOrderDetail(poOrderB, iaI2billB, puPayableDetail, vo);
            details.add(detail);
        }
    }

    private void buildPaymentContractDetails(List<YgPurchaseOrderDetailVO> details, YgInvoiceCheckContext context) {
        Map<String, PmContr> pmContrMap = context.getPmContrMap();
        if (pmContrMap.isEmpty()) {
            log.warn("未找到相关的付款合同信息");
            return;
        }

        // 获取付款合同相关的明细记录，用于获取lineNumber
        Map<String, FiConvertPuPayableDetail> detailMap = context.getConvertDetails().stream()
                .filter(detail -> StringUtils.isNotBlank(detail.getSrcBillid()))
                .collect(Collectors.toMap(
                        FiConvertPuPayableDetail::getSrcBillid,     // key: 付款合同主键
                        Function.identity(),                        // value: 明细对象本身
                        (v1, v2) -> v1
                ));

        for (Map.Entry<String, PmContr> entry : pmContrMap.entrySet()) {
            String pkContr = entry.getKey();
            PmContr contr = entry.getValue();
            FiConvertPuPayableDetail puPayableDetail = detailMap.get(pkContr);

            YgPurchaseOrderDetailVO detail = new YgPurchaseOrderDetailVO();
            detail.setPurOrdNum(contr.getBillCode());                               // 设置订单编号 (合同编号)
            detail.setLineItem(String.valueOf(puPayableDetail.getLineNumber()));    //行号
            detail.setPurNum(puPayableDetail.getNotaxCr());                         // 设置入库允许过账数量 (不含税金额)
            detail.setPurExTaxAmount(puPayableDetail.getNotaxCr());                 // 设置入库允许过账金额 (不含税金额)
            detail.setPurAmount(puPayableDetail.getMoneyCr());                      // 设置入库明细含税金额
            detail.setRecepId("否");                                                // 项目分包应付，默认：服务类，否

            details.add(detail);
        }
    }

    private void buildCostSettlementDetails(List<YgPurchaseOrderDetailVO> details, YgInvoiceCheckContext context) {
        Map<String, PmFeebalanceB> pmFeebalanceMap = context.getPmFeebalanceBMap();
        if (pmFeebalanceMap.isEmpty()) {
            log.warn("未找到相关的费用结算单信息");
            return;
        }

        // 获取费用结算单相关的明细记录，用于获取lineNumber
        Map<String, FiConvertPuPayableDetail> puPayableDetailMap = context.getConvertDetails().stream()
                .filter(detail -> StringUtils.isNotBlank(detail.getSrcBillid()))
                .collect(Collectors.toMap(
                        FiConvertPuPayableDetail::getSrcBillid,     // key: 费用结算单主键
                        Function.identity(),                        // value: 明细对象本身
                        (v1, v2) -> v1
                ));

        for (Map.Entry<String, PmFeebalanceB> entry : pmFeebalanceMap.entrySet()) {
            String pkFeebalanceB = entry.getKey();
            PmFeebalanceB pmFeebalanceB = entry.getValue();
            FiConvertPuPayableDetail puPayableDetail = puPayableDetailMap.get(pkFeebalanceB);
            // 费用结算推送情况
            FiSourceCostOrderDetail costOrderDetail = context.getFiSourceCostOrderDetailMap().get(pkFeebalanceB);

            YgPurchaseOrderDetailVO detail = new YgPurchaseOrderDetailVO();
            detail.setPurOrdNum(pmFeebalanceB.getBillCode());                       // 设置订单编号 (费用结算单编号)
            detail.setLineItem(costOrderDetail.getProtocolList());                  // 行号
            detail.setPurNum(puPayableDetail.getNotaxCr());                         // 设置入库允许过账数量 (不含税金额)
            detail.setPurExTaxAmount(puPayableDetail.getNotaxCr());                 // 设置入库允许过账金额 (不含税金额)
            detail.setPurAmount(puPayableDetail.getMoneyCr());                      // 设置入库明细含税金额
            detail.setRecepId("否");                                                // 项目分包应付，默认：服务类，否

            details.add(detail);
        }
    }

    private YgPurchaseOrderDetailVO createPurchaseOrderDetail(PoOrderB poOrderB, IaI2billB iaI2billB,
                                                              FiConvertPuPayableDetail puPayableDetail, YgInvoiceCheckVO vo) {
        YgPurchaseOrderDetailVO detail = new YgPurchaseOrderDetailVO();

        detail.setPurOrdNum(poOrderB.getVbillcode());                                       // 订单编号
        detail.setLineItem(String.valueOf(poOrderB.getCrowno()));                           // 行号
        detail.setPurExTaxAmount(puPayableDetail.getNotaxCr());                             // 入库允许过账金额
        detail.setPurAmount(puPayableDetail.getMoneyCr());                                  // 入库明细含税金额
        detail.setRecepId(StringUtils.equals("1", vo.getOrderType()) ? "是" : "否");         // 物资类-是，服务类-否
        detail.setPurNum(puPayableDetail.getQuantityCr());                                  // 入库允许过账数量

        // 只有当iaI2billB不为null时才设置入库单相关信息
        if (iaI2billB != null) {
            detail.setMtDocNum(iaI2billB.getVbillcode());                                   // 入库单号
            detail.setMtDocNumLine(iaI2billB.getCrowno());                                  // 入库单行号
        }

        return detail;
    }

    /**
     * 构建影像信息
     */
    private void buildImageInfo(YgInvoiceCheckVO vo, YgInvoiceCheckContext context) {
        log.debug("开始构建影像信息");

        List<ImageDetail> imageDetails = context.getImageResults().stream()
                .map(this::createImageDetail)
                .collect(Collectors.toList());

        vo.setSub7(imageDetails);
        vo.setFdzs(imageDetails.size());    // 附件张数
        log.debug("影像信息构建完成, 影像数量: {}", imageDetails.size());
    }

    private ImageDetail createImageDetail(BdUdsResult bdUdsResult) {
        ImageDetail image = new ImageDetail();
        image.setImageType(YgConfig.imageType);
        image.setFileResId(bdUdsResult.getDocumentId());
        image.setFileName(bdUdsResult.getFileName());
        image.setFileSource("UDS");
        image.setFromFileSystemId(YgConfig.fromFileSystemId);
        return image;
    }

    private BigDecimal parseBigDecimal(String value) {
        if (value == null) {
            return null;
        }
        String val = value.trim();
        if (val.isEmpty() || val.equalsIgnoreCase("n/a")
                || val.equalsIgnoreCase("nn/a") || val.equalsIgnoreCase("null")
                || val.equalsIgnoreCase("～") || val.equalsIgnoreCase("~")) {
            return null;
        }
        return new BigDecimal(val);
    }
}
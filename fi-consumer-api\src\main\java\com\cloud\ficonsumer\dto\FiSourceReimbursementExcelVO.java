package com.cloud.ficonsumer.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/13.
 */
@Data
@ColumnWidth(25)
public class FiSourceReimbursementExcelVO {

    @ExcelProperty(value = "订单编号")
    private String billno;

    /**
     * 财务组织名称
     */
    @ExcelProperty(value="财务组织名称")
    private String pkOrgName;

    @ExcelProperty(value = "制单人名称")
    private String billmakerName;

    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 组织本币金额
     */
    @ExcelProperty(value = "组织本币金额")
    private String localMoney;

    /**
     * 推送时间
     */
    @ExcelProperty(value = "推送时间")
    private String pushTime;

    @ExcelProperty(value = "同步状态")
    private String pushStatusName;

    @ExcelProperty(value = "同步消息")
    private String pushMsg;

    @ExcelProperty(value = "返回状态")
    private String returnStatusName;

    @ExcelProperty(value = "返回消息")
    private String returnMsg;

    @ExcelProperty(value = "返回单号")
    private String returnBill;

    @ExcelProperty(value = "返回接收时间")
    private String returnTime;

    /**
     * 创建时间
     */
    @ExcelProperty(value="拉取时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ExcelIgnore
    private Integer pushStatus;
}

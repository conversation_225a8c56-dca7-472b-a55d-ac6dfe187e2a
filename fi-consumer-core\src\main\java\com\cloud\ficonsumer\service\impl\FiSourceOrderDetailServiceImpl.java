
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceOrderDetail;
import com.cloud.ficonsumer.mapper.FiSourceOrderDetailMapper;
import com.cloud.ficonsumer.service.FiSourceOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * 订单源数据详情表
 *
 * <AUTHOR>
 * @date 2025-01-16 16:53:12
 */
@Service
public class FiSourceOrderDetailServiceImpl extends ServiceImpl<FiSourceOrderDetailMapper, FiSourceOrderDetail> implements FiSourceOrderDetailService {

}

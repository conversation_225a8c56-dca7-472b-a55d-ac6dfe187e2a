

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.cloud.common.data.dict.DictTransConstant;
import com.cloud.cloud.common.data.dict.annotation.Dict;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 对照表
 *
 * <AUTHOR> code generator
 * @date 2024-07-12 10:14:10
 */
@Data
@TableName("api_compare")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "对照表")
public class ApiCompare extends BaseEntity<ApiCompare> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 档案类型(取字典项值)
     */
    @ApiModelProperty(value="档案类型(取字典项值)")
    @Dict(dictTable = DictTransConstant.DICT_TABLE_DICT,dicCode ="api_file_type")
    private String fileType;

    /**
     * 来源系统主键
     */
    @ApiModelProperty(value="来源系统主键")
    private String sourceSystemId;

    /**
     * 来源系统id
     */
    @ApiModelProperty(value="来源系统id")
    private String sourceSystemCode;

    /**
     * 来源系统名称
     */
    @ApiModelProperty(value="来源系统名称")
    private String sourceSystemName;

    /**
     * 目标系统主键
     */
    @ApiModelProperty(value="目标系统主键")
    private String targetSystemId;

    /**
     * 目标系统id
     */
    @ApiModelProperty(value="目标系统id")
    private String targetSystemCode;

    /**
     * 目标系统名称
     */
    @ApiModelProperty(value="目标系统名称")
    private String targetSystemName;

}

-- cloud_main.fi_convert_accounting definition

CREATE TABLE `fi_convert_accounting` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_voucher` varchar(20) DEFAULT NULL COMMENT '凭证主键',
  `num` int(11) DEFAULT NULL COMMENT '凭证编码',
  `pk_vouchertype` varchar(20) DEFAULT NULL COMMENT '凭证类别',
  `pk_org` varchar(20) DEFAULT NULL COMMENT '财务组织',
  `free5` varchar(60) DEFAULT NULL COMMENT '业务单元',
  `explanation` varchar(800) DEFAULT NULL COMMENT '摘要',
  `prepareddate` varchar(20) DEFAULT NULL COMMENT '制单日期',
  `tallydate` varchar(20) DEFAULT NULL COMMENT '记账日期',
  `period` varchar(20) DEFAULT NULL COMMENT '会计期间',
  `pk_prepared` varchar(20) DEFAULT NULL COMMENT '制单人',
  `pk_checked` varchar(20) DEFAULT NULL COMMENT '审核人',
  `pk_currtype` varchar(20) DEFAULT NULL COMMENT '币种',
  `attachment` int(11) DEFAULT NULL COMMENT '附单据数',
  `year` varchar(10) DEFAULT NULL COMMENT '会计年度',
  `adjustperiod` varchar(10) DEFAULT '0' COMMENT '调整期间',
  `offervoucher` varchar(20) DEFAULT NULL COMMENT '冲销凭证',
  `code` int(11) DEFAULT NULL COMMENT '成功失败标识，0是成功；1是失败',
  `profCenter` varchar(20) DEFAULT NULL COMMENT '利润中心',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `push_order_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_order_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `push_time` varchar(256) DEFAULT NULL COMMENT '推送时间',
  `findocid` varchar(100) DEFAULT NULL COMMENT '中台凭证号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='凭证记账源转换数据主表';


-- cloud_main.fi_convert_accounting_detail definition

CREATE TABLE `fi_convert_accounting_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_voucher` varchar(20) DEFAULT NULL COMMENT '凭证主键',
  `num` int(11) DEFAULT NULL COMMENT '凭证编码',
  `pk_accasoa` varchar(20) DEFAULT NULL COMMENT '科目',
  `amount` decimal(28,8) DEFAULT NULL COMMENT '原币',
  `busidate` varchar(20) DEFAULT NULL COMMENT '业务日期',
  `assid` varchar(20) DEFAULT NULL COMMENT '辅助核算',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `pk_detail` varchar(100) DEFAULT NULL COMMENT '分录主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='凭证记账转换数据明细表';


-- cloud_main.fi_convert_accounting_docfree definition

CREATE TABLE `fi_convert_accounting_docfree` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `assid` varchar(20) DEFAULT NULL COMMENT '辅助核算',
  `type` varchar(30) DEFAULT NULL COMMENT '凭证编码',
  `value` varchar(50) DEFAULT NULL COMMENT '核算维度类型',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `pk_detail` varchar(100) DEFAULT NULL COMMENT '分录主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='凭证记账辅助核算转换数据明细表';


-- cloud_main.fi_convert_cost_order definition

CREATE TABLE `fi_convert_cost_order` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_feebalance` varchar(40) DEFAULT NULL COMMENT '单据主键',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `bill_code` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `protocol_name` varchar(40) DEFAULT NULL COMMENT '合同编号',
  `billmaketime` varchar(19) DEFAULT NULL COMMENT '制单日期',
  `billmaker` varchar(20) DEFAULT NULL COMMENT '制单人',
  `pk_supplier` varchar(20) DEFAULT NULL COMMENT '供应商',
  `curr_mny` varchar(40) DEFAULT NULL COMMENT '不含税合同金额',
  `tax` varchar(40) DEFAULT NULL COMMENT '税额',
  `tot_taxmny` varchar(32) DEFAULT NULL COMMENT '含税总金额',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='费用结算单转换数据主表';


-- cloud_main.fi_convert_cost_order_detail definition

CREATE TABLE `fi_convert_cost_order_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_feebalance_b` varchar(40) DEFAULT NULL COMMENT '单据详情主键',
  `pk_feebalance` varchar(40) DEFAULT NULL COMMENT '单据主键',
  `bill_code` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `protocol_list` varchar(20) DEFAULT NULL COMMENT '行号',
  `pk_project` varchar(40) DEFAULT NULL COMMENT '项目',
  `curr_mny` varchar(32) DEFAULT NULL COMMENT '不含税合同金额',
  `taxrate` varchar(32) DEFAULT NULL COMMENT '税率',
  `tax` varchar(32) DEFAULT NULL COMMENT '税额',
  `curr_taxmny` varchar(32) DEFAULT NULL COMMENT '含税金额',
  `taxprice` varchar(32) DEFAULT NULL COMMENT '含税单价',
  `curr_num` varchar(32) DEFAULT NULL COMMENT '数量',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `pk_measdoc` varchar(20) DEFAULT NULL COMMENT '单位',
  `pk_material` varchar(20) DEFAULT NULL COMMENT '物料版本信息',
  `name` varchar(300) DEFAULT NULL COMMENT '物料名称',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `material_code` varchar(20) DEFAULT NULL COMMENT '物料编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='费用结算单转换数据详情表';


-- cloud_main.fi_convert_file_info definition

CREATE TABLE `fi_convert_file_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `file_manage_id` text NOT NULL COMMENT 'FileManage系统表主键',
  `org_id` int(11) DEFAULT NULL COMMENT '单位id',
  `relation_id` int(11) DEFAULT NULL COMMENT '关联id',
  `extends_id` int(11) DEFAULT NULL COMMENT '继承id',
  `image_code` varchar(50) DEFAULT NULL COMMENT '影像类型编码',
  `image_name` varchar(50) DEFAULT NULL COMMENT '影像类型名称',
  `is_upload` varchar(2) DEFAULT '0' COMMENT '是否刚性控制 1-是 0-否',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
  `bucket_name` varchar(255) DEFAULT NULL COMMENT '保存路径',
  `original` varchar(255) DEFAULT NULL COMMENT '原文件名',
  `file_type` varchar(255) DEFAULT NULL COMMENT '文件类型',
  `file_size` varchar(50) DEFAULT NULL COMMENT '文件大小',
  `upload_type` varchar(16) DEFAULT NULL COMMENT '上传方式（0-手动上传 1-扫描录入）',
  `module_type` varchar(255) DEFAULT NULL COMMENT '模块对应的表',
  `module_id` int(11) DEFAULT NULL COMMENT '模块业务id',
  `type` varchar(10) DEFAULT '0' COMMENT '模块子类型（0-未知，1-录音，2-图片）',
  `show_name` varchar(255) DEFAULT NULL COMMENT '显示文件名',
  `business_type` varchar(255) DEFAULT NULL COMMENT '业务类型',
  `erp_file_check` varchar(2) DEFAULT NULL COMMENT '文件一致性校验结果 1-通过 0-不通过',
  `create_by_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `update_by_name` varchar(50) DEFAULT NULL COMMENT '最后修改人姓名',
  `source_id` varchar(40) DEFAULT NULL COMMENT '来源主键id',
  `source_relation_id` varchar(40) DEFAULT NULL COMMENT '来源关联id',
  `ocr_type` varchar(20) DEFAULT NULL COMMENT 'ocr类型',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `del_flag` bigint(20) DEFAULT '0' COMMENT '删除标识',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='文件信息转换';


-- cloud_main.fi_convert_file_ocr_info definition

CREATE TABLE `fi_convert_file_ocr_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `file_manage_id` text NOT NULL COMMENT 'FileManage系统表主键',
  `einvoiceid` varchar(100) DEFAULT NULL COMMENT '发票ID',
  `uniquecodeofinvoice` varchar(100) DEFAULT NULL COMMENT '发票唯一标识',
  `createtime` datetime DEFAULT NULL COMMENT '数据插入时间',
  `bukrs` varchar(50) DEFAULT NULL COMMENT '公司代码',
  `einvoicenumber` varchar(100) DEFAULT NULL COMMENT '发票号码',
  `einvoicecode` varchar(100) DEFAULT NULL COMMENT '发票代码',
  `invoicedate` varchar(20) DEFAULT NULL COMMENT '开票日期',
  `invoicetype` varchar(50) DEFAULT NULL COMMENT '发票类型',
  `checkcode` varchar(100) DEFAULT NULL COMMENT '校验码',
  `salername` varchar(200) DEFAULT NULL COMMENT '销方名称',
  `salertaxno` varchar(50) DEFAULT NULL COMMENT '销方税号',
  `saleraddressphone` varchar(200) DEFAULT NULL COMMENT '销方地址、电话',
  `saleraccount` varchar(200) DEFAULT NULL COMMENT '销方开户行及账号',
  `buyername` varchar(200) DEFAULT NULL COMMENT '购方名称',
  `buyertaxno` varchar(50) DEFAULT NULL COMMENT '购方税号',
  `buyeraddressphone` varchar(200) DEFAULT NULL COMMENT '购方地址、电话',
  `buyeraccount` varchar(200) DEFAULT NULL COMMENT '购方开户行及账号',
  `invoiceamount` varchar(20) DEFAULT NULL COMMENT '发票金额(不含税)',
  `taxamount` varchar(20) DEFAULT NULL COMMENT '发票税额',
  `totalamount` varchar(20) DEFAULT NULL COMMENT '价税合计',
  `dt` varchar(50) DEFAULT NULL COMMENT 'dt',
  `file_id` varchar(50) DEFAULT NULL COMMENT '文件id',
  `sync_status` varchar(2) DEFAULT '0' COMMENT '同步状态',
  `invoice_type_code` varchar(10) DEFAULT NULL COMMENT '发票类型编码',
  `elec_invoice_no` varchar(20) DEFAULT NULL COMMENT '全电发票号码',
  `invoice_status` varchar(10) DEFAULT NULL COMMENT '发票状态',
  `dparty` varchar(50) DEFAULT NULL COMMENT '开票人',
  `tax_cal_code` varchar(50) DEFAULT NULL COMMENT '税收分类编码',
  `special_policy_sign` int(1) DEFAULT NULL COMMENT '特殊政策标识 0：否 1：是',
  `tax_rate_flag` int(1) DEFAULT NULL COMMENT '零税率标识',
  `body` varchar(1024) DEFAULT NULL COMMENT '发票表体',
  `unit_code` varchar(100) DEFAULT NULL COMMENT '发票使用单位',
  `pk_dept` varchar(100) DEFAULT NULL COMMENT '部门主键',
  `integration_status` varchar(2) DEFAULT '0' COMMENT '集成状态',
  `push_status` int(11) DEFAULT NULL COMMENT '推送状态(4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败)',
  `push_msg` text COMMENT '推送成功失败信息',
  `return_msg` text COMMENT '返回同步消息',
  `return_status` varchar(2) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(20) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(20) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `del_flag` bigint(20) DEFAULT '0' COMMENT '删除标识',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='进项发票转换数据';


-- cloud_main.fi_convert_new_store definition

CREATE TABLE `fi_convert_new_store` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '应付财务组织',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT '应付单主键',
  `src_billid` varchar(40) DEFAULT NULL COMMENT '源头单据主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `billdate` char(19) DEFAULT NULL COMMENT '单据日期',
  `billno` varchar(40) DEFAULT NULL COMMENT '单据号',
  `pk_tradetype` varchar(40) DEFAULT NULL COMMENT '应付类型',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `push_order_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_order_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购入库源数据主表';


-- cloud_main.fi_convert_new_store_detail definition

CREATE TABLE `fi_convert_new_store_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT '应付单主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `billcode` varchar(40) DEFAULT NULL COMMENT '合同编号',
  `rowno` varchar(40) DEFAULT NULL COMMENT '行号',
  `protocal_list` varchar(40) DEFAULT NULL COMMENT '行号',
  `crowno` varchar(20) DEFAULT NULL COMMENT '采购单行号',
  `payableitem_rowno` char(20) DEFAULT NULL COMMENT '应付单行标识',
  `quantity_cr` decimal(20,8) DEFAULT NULL COMMENT '贷方数量',
  `local_notax_cr` decimal(28,8) DEFAULT NULL COMMENT '组织本币无税金额',
  `material` varchar(20) DEFAULT NULL COMMENT '物料',
  `direction` int(11) DEFAULT NULL COMMENT '方向',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购入库详情数据主表';


-- cloud_main.fi_convert_order definition

CREATE TABLE `fi_convert_order` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_order` varchar(40) DEFAULT NULL COMMENT '订单主键',
  `pk_project` varchar(40) DEFAULT NULL COMMENT '项目主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `dmakedate` varchar(19) DEFAULT NULL COMMENT '制单日期',
  `billmaker` varchar(20) DEFAULT NULL COMMENT '制单人',
  `pk_supplier` varchar(20) DEFAULT NULL COMMENT '供应商',
  `corigcurrencyid` varchar(20) DEFAULT NULL COMMENT '币种',
  `vcontractcode` varchar(40) DEFAULT NULL COMMENT '合同号',
  `norigmny` varchar(32) DEFAULT NULL COMMENT '无税金额',
  `nexchangerate` varchar(32) DEFAULT NULL COMMENT '调整方式',
  `ntax` varchar(32) DEFAULT NULL COMMENT '税额',
  `breturn` varchar(32) DEFAULT NULL COMMENT '退货',
  `ctrantypeid` varchar(32) DEFAULT NULL COMMENT '订单类型',
  `vtrantypecode` varchar(32) DEFAULT NULL COMMENT '单据类型编码',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='订单源数据主表';


-- cloud_main.fi_convert_order_detail definition

CREATE TABLE `fi_convert_order_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_order_b` varchar(40) DEFAULT NULL COMMENT '订单主键',
  `pk_order` varchar(40) DEFAULT NULL COMMENT '订单主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `crowno` varchar(20) DEFAULT NULL COMMENT '行号',
  `cprojectid` varchar(20) DEFAULT NULL COMMENT '项目',
  `norigmny` varchar(32) DEFAULT NULL COMMENT '无税金额',
  `nexchangerate` varchar(32) DEFAULT NULL COMMENT '调整方式',
  `ntax` varchar(32) DEFAULT NULL COMMENT '税额',
  `norigtaxmny` varchar(32) DEFAULT NULL COMMENT '价税合计',
  `pk_dept_v` varchar(20) DEFAULT NULL COMMENT '采购部门',
  `nqtorigtaxprice` varchar(32) DEFAULT NULL COMMENT '含税单价',
  `nastnum` varchar(32) DEFAULT NULL COMMENT '数量',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `breturn` varchar(20) DEFAULT NULL COMMENT '退货',
  `castunitid` varchar(20) DEFAULT NULL COMMENT '单位',
  `pk_material` varchar(20) DEFAULT NULL COMMENT '物料版本信息',
  `name` varchar(300) DEFAULT NULL COMMENT '物料名称',
  `ctrantypeid` varchar(20) DEFAULT NULL COMMENT '订单类型',
  `dbilldate` varchar(19) DEFAULT NULL COMMENT '订单日期',
  `nqtorigprice` varchar(32) DEFAULT NULL COMMENT '无税单价',
  `ntotalorigmny` varchar(32) DEFAULT NULL COMMENT '价税合计',
  `ntaxmny` varchar(32) DEFAULT NULL COMMENT '本币价税合计',
  `nmny` varchar(32) DEFAULT NULL COMMENT '本币无税金额',
  `forderstatus` varchar(32) DEFAULT NULL COMMENT '单据状态',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `ntaxrate` varchar(20) DEFAULT NULL COMMENT '税率',
  `material_code` varchar(20) DEFAULT NULL COMMENT '物料编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='订单转换数据详情表';


-- cloud_main.fi_convert_payment definition

CREATE TABLE `fi_convert_payment` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_settlement` varchar(40) DEFAULT NULL COMMENT '结算信息主键',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '财务组织',
  `pk_tradetype` varchar(40) DEFAULT NULL COMMENT '业务单交易类型',
  `pk_billoperator` varchar(20) DEFAULT NULL COMMENT '业务单据录入人',
  `pk_balatype` varchar(40) DEFAULT NULL COMMENT '结算方式',
  `pk_account` varchar(20) DEFAULT NULL COMMENT '本方账户',
  `orglocal` varchar(20) DEFAULT NULL COMMENT '本币金额',
  `busi_billdate` varchar(20) DEFAULT NULL COMMENT '业务单据日期',
  `pk_currtype` varchar(40) DEFAULT NULL COMMENT '币种',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='通用付款单转换数据主表';


-- cloud_main.fi_convert_payment_detail definition

CREATE TABLE `fi_convert_payment_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_detail` varchar(40) DEFAULT NULL COMMENT '结算信息详情主键',
  `pk_settlement` varchar(40) DEFAULT NULL COMMENT '结算信息主键',
  `tradername` varchar(40) DEFAULT NULL COMMENT '对方对象名称',
  `pk_costsubj` varchar(32) DEFAULT NULL COMMENT '收支项目',
  `paylocal` varchar(32) DEFAULT NULL COMMENT '付款组织本币金额',
  `oppaccname` varchar(32) DEFAULT NULL COMMENT '对方账户户名',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='通用付款单详情源数据主表';


-- cloud_main.fi_convert_pu_payable definition

CREATE TABLE `fi_convert_pu_payable` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_org` varchar(20) DEFAULT NULL COMMENT '应付财务组织',
  `billmaker` varchar(20) DEFAULT NULL COMMENT '制单人',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT '应付单标识',
  `def6` varchar(20) DEFAULT NULL COMMENT '费用类型',
  `pk_tradetypeid` varchar(20) DEFAULT NULL COMMENT '应付类型',
  `pk_tradetype` varchar(20) DEFAULT NULL COMMENT '应付类型编码',
  `pk_currtype` varchar(20) DEFAULT NULL COMMENT '币种',
  `money` decimal(28,8) DEFAULT NULL COMMENT '原币金额',
  `billstatus` int(11) DEFAULT NULL COMMENT '单据状态',
  `billno` varchar(40) DEFAULT NULL COMMENT '单据号',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `back_tag` varchar(40) DEFAULT NULL COMMENT '是否退回重传标识',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `push_time` varchar(50) DEFAULT NULL COMMENT '推送时间',
  `return_bill` varchar(255) DEFAULT NULL COMMENT '返回同步消息',
  `return_time` varchar(50) DEFAULT NULL COMMENT '返回时间',
  `def47` varchar(255) DEFAULT NULL COMMENT '付款性质',
  `def52` varchar(255) DEFAULT NULL COMMENT '部门分类',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购应付转换数据详情';


-- cloud_main.fi_convert_pu_payable_detail definition

CREATE TABLE `fi_convert_pu_payable_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_deptid_v` varchar(20) DEFAULT NULL COMMENT '部门',
  `contractno` varchar(40) DEFAULT NULL COMMENT '合同号',
  `purchaseorder` varchar(40) DEFAULT NULL COMMENT '订单号',
  `invoiceno` varchar(40) DEFAULT NULL COMMENT '发票号',
  `outstoreno` varchar(40) DEFAULT NULL COMMENT '出库单号',
  `supplier` varchar(20) DEFAULT NULL COMMENT '供应商版本',
  `material` varchar(40) DEFAULT NULL COMMENT '物料',
  `quantity_cr` decimal(28,8) DEFAULT NULL COMMENT '贷方数量',
  `money_cr` decimal(28,8) DEFAULT NULL COMMENT '贷方原币金额',
  `notax_cr` decimal(28,8) DEFAULT NULL COMMENT '贷方原币无税金额',
  `local_money_cr` decimal(28,8) DEFAULT NULL COMMENT '组织本币金额',
  `local_notax_cr` decimal(28,8) DEFAULT NULL COMMENT '组织本币无税金额',
  `local_tax_cr` decimal(28,8) DEFAULT NULL COMMENT '税额',
  `taxrate` decimal(28,8) DEFAULT NULL COMMENT '税率',
  `pk_subjcode` varchar(20) DEFAULT NULL COMMENT '收支项目',
  `pk_payableitem` varchar(40) DEFAULT NULL COMMENT '应付单行标识',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT '应付单表示',
  `rate` decimal(28,8) DEFAULT NULL COMMENT '税率',
  `project` varchar(40) DEFAULT NULL COMMENT '项目',
  `crowno` varchar(40) DEFAULT NULL COMMENT '采购订单行号',
  `top_billtype` varchar(40) DEFAULT NULL COMMENT '上层单据类型',
  `top_billid` varchar(40) DEFAULT NULL COMMENT '上层单据主键',
  `top_itemid` varchar(40) DEFAULT NULL COMMENT '上层单据行主键',
  `src_billtype` varchar(40) DEFAULT NULL COMMENT '源头单据类型',
  `src_billid` varchar(40) DEFAULT NULL COMMENT '源头单据主键',
  `src_itemid` varchar(40) DEFAULT NULL COMMENT '源头单据行主键',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `src_tradetype` varchar(20) DEFAULT NULL COMMENT '源头交易类型',
  `direction` int(11) DEFAULT '-1' COMMENT '方向 1=借方，-1=贷方',
  `line_number` int(11) DEFAULT '-1' COMMENT '行号',
  `rowno` varchar(40) DEFAULT NULL COMMENT '行号',
  `store_billno` varchar(40) DEFAULT NULL COMMENT '入库单编号',
  `store_rowno` varchar(40) DEFAULT NULL COMMENT '入库单行号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购应付明细转换数据详情';


-- cloud_main.fi_convert_pu_paybill definition

CREATE TABLE `fi_convert_pu_paybill` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_settlement` varchar(64) NOT NULL COMMENT '结算单主键',
  `pk_org` varchar(64) DEFAULT NULL COMMENT '财务组织',
  `pk_billoperator` varchar(64) DEFAULT NULL COMMENT '业务单据录入人',
  `primal` decimal(18,2) DEFAULT NULL COMMENT '原币金额',
  `expense_type` varchar(64) DEFAULT NULL COMMENT '费用类型',
  `pk_tradetype` varchar(64) DEFAULT NULL COMMENT '应付类型编码',
  `integration_status` varchar(32) DEFAULT NULL COMMENT '集成状态',
  `push_status` int(11) DEFAULT NULL COMMENT '推送状态 4:推送中、1:转换失败、2:推送失败、3:推送成功、5:推送财务失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(255) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(32) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(20) DEFAULT NULL COMMENT '重试次数',
  `readiness` bigint(20) DEFAULT NULL COMMENT '0 未就绪 1 就绪',
  `back_tag` varchar(40) DEFAULT NULL COMMENT '是否退回重传标识',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `billcode` varchar(255) DEFAULT NULL,
  `vbillcode` varchar(255) DEFAULT NULL,
  `cbillcode` varchar(100) DEFAULT NULL COMMENT '合同号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='采购付款转换数据详情';


-- cloud_main.fi_convert_pu_paybill_detail definition

CREATE TABLE `fi_convert_pu_paybill_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_settlement` varchar(64) NOT NULL COMMENT '结算单主键',
  `pk_deptid_v` varchar(64) DEFAULT NULL COMMENT '部门',
  `prepay` varchar(32) DEFAULT NULL COMMENT '付款性质',
  `memo` varchar(255) DEFAULT NULL COMMENT '摘要',
  `project` varchar(64) DEFAULT NULL COMMENT '项目',
  `contract_no` varchar(64) DEFAULT NULL COMMENT '合同号',
  `pk_recpaytype` varchar(64) DEFAULT NULL COMMENT '付款业务类型',
  `supplier` varchar(128) DEFAULT NULL COMMENT '供应商',
  `oppaccount` varchar(64) DEFAULT NULL COMMENT '对方账号',
  `oppaccname` varchar(128) DEFAULT NULL COMMENT '对方账户户名',
  `pk_oppbank` varchar(64) DEFAULT NULL COMMENT '对方银行',
  `pay` decimal(28,8) DEFAULT NULL COMMENT '付款原币金额',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `changerate` decimal(28,8) DEFAULT NULL COMMENT '实付套汇汇率',
  `pk_currtype_last` varchar(100) DEFAULT NULL,
  `pk_deptid` varchar(64) DEFAULT NULL COMMENT '部门',
  `src_billid` varchar(64) DEFAULT NULL COMMENT '上游单据主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='采购付款明细转换数据详情';


-- cloud_main.fi_convert_receivable definition

CREATE TABLE `fi_convert_receivable` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_recbill` varchar(40) DEFAULT NULL COMMENT '结算信息主键',
  `pk_org_v` varchar(40) DEFAULT NULL COMMENT '应收财务组织',
  `contractno` varchar(40) DEFAULT NULL COMMENT '应收财务组织',
  `project` varchar(40) DEFAULT NULL COMMENT '制单人',
  `billmaker` varchar(40) DEFAULT NULL COMMENT '制单人',
  `billno` varchar(40) DEFAULT NULL COMMENT '单据号',
  `scomment` varchar(40) DEFAULT NULL COMMENT '摘要',
  `local_money` varchar(40) DEFAULT NULL COMMENT '组织本币金额',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `pk_tradetype` varchar(40) DEFAULT NULL COMMENT '单据类型',
  `pk_subjcode` varchar(40) DEFAULT NULL COMMENT '收支项目',
  `back_tag` varchar(40) DEFAULT NULL COMMENT '是否退回重传标识',
  `customer` varchar(40) DEFAULT NULL COMMENT '客户名称',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='项目应收单转换数据主表';


-- cloud_main.fi_convert_receivable_detail definition

CREATE TABLE `fi_convert_receivable_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_recitem` varchar(40) DEFAULT NULL COMMENT 'pk详情主键',
  `pk_recbill` varchar(40) DEFAULT NULL COMMENT 'pk主键',
  `contractno` varchar(40) DEFAULT NULL COMMENT '应收财务组织',
  `project` varchar(40) DEFAULT NULL COMMENT '制单人',
  `pk_subjcode` varchar(40) DEFAULT NULL COMMENT '收支项目',
  `local_notax_de` varchar(40) DEFAULT NULL COMMENT '组织本币无税金额',
  `local_tax_de` varchar(40) DEFAULT NULL COMMENT '税额',
  `local_money_de` varchar(40) DEFAULT NULL COMMENT '组织本币金额',
  `invoiceno` varchar(40) DEFAULT NULL COMMENT '发票号',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `customer` varchar(40) DEFAULT NULL COMMENT '客户名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='项目应收详情单转换数据主表';


-- cloud_main.fi_convert_reimbursement definition

CREATE TABLE `fi_convert_reimbursement` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT 'pk主键',
  `pk_org_v` varchar(40) DEFAULT NULL COMMENT '应付财务组织版本',
  `billmaker` varchar(40) DEFAULT NULL COMMENT '制单人',
  `pk_psndoc` varchar(40) DEFAULT NULL COMMENT '业务员',
  `def5` varchar(101) DEFAULT NULL COMMENT '合同类型',
  `pk_currtype` varchar(40) DEFAULT NULL COMMENT '币种',
  `project` varchar(40) DEFAULT NULL COMMENT '项目',
  `supplier` varchar(40) DEFAULT NULL COMMENT '供应商版本',
  `pk_deptid` varchar(40) DEFAULT NULL COMMENT '部门',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `local_money` varchar(40) DEFAULT NULL COMMENT '组织本币金额',
  `pk_subjcode` varchar(40) DEFAULT NULL COMMENT '收支项目',
  `back_tag` varchar(40) DEFAULT NULL COMMENT '是否退回重传标识',
  `objtype` varchar(40) DEFAULT NULL COMMENT '往来对象',
  `pk_tradetype` varchar(40) DEFAULT NULL COMMENT '单据类型',
  `scomment` varchar(400) DEFAULT NULL COMMENT '摘要',
  `billmaker_t` varchar(40) DEFAULT NULL COMMENT '制单人',
  `pk_deptid_v` varchar(40) DEFAULT NULL COMMENT '部门',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `billno` varchar(40) DEFAULT NULL COMMENT '单据编号',
  `def47` varchar(40) DEFAULT NULL COMMENT '平台业务事项',
  `def2` varchar(40) DEFAULT NULL COMMENT '付款方式',
  `def52` varchar(40) DEFAULT NULL COMMENT '部门分类',
  `pk_org_name` varchar(100) DEFAULT NULL COMMENT '组织名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='通用报销单转换数据主表';


-- cloud_main.fi_convert_reimbursement_detail definition

CREATE TABLE `fi_convert_reimbursement_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_payableitem` varchar(40) DEFAULT NULL COMMENT 'pk详情主键',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT 'pk主键',
  `pk_psndoc` varchar(40) DEFAULT NULL COMMENT '业务员',
  `invoiceno` varchar(40) DEFAULT NULL COMMENT '发票号',
  `recaccount` varchar(40) DEFAULT NULL COMMENT '收款银行账户',
  `pk_balatype` varchar(40) DEFAULT NULL COMMENT '结算方式',
  `payaccount` varchar(40) DEFAULT NULL COMMENT '付款银行账户',
  `contractno` varchar(40) DEFAULT NULL COMMENT '合同号',
  `pk_subjcode` varchar(40) DEFAULT NULL COMMENT '收支项目',
  `pk_tradetypeid` varchar(40) DEFAULT NULL COMMENT '应付类型',
  `local_money` varchar(40) DEFAULT NULL COMMENT '组织本币金额',
  `money_de` varchar(40) DEFAULT NULL COMMENT '借方原币金额',
  `local_notax_de` varchar(40) DEFAULT NULL COMMENT '组织本币无税金额',
  `local_tax_de` varchar(40) DEFAULT NULL COMMENT '税额',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `local_money_cr` varchar(40) DEFAULT NULL COMMENT '组织本币金额',
  `local_notax_cr` varchar(40) DEFAULT NULL COMMENT '组织本币无税金额',
  `local_tax_cr` varchar(40) DEFAULT NULL COMMENT '税额',
  `supplier` varchar(40) DEFAULT NULL COMMENT '供应商版本',
  `objtype` varchar(400) DEFAULT NULL COMMENT '往来对象',
  `pk_deptid` varchar(40) DEFAULT NULL COMMENT '部门',
  `pk_deptid_v` varchar(40) DEFAULT NULL COMMENT '部门',
  `def50` varchar(40) DEFAULT NULL COMMENT '车船税',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='通用报销单详情转换数据主表';


-- cloud_main.fi_convert_store definition

CREATE TABLE `fi_convert_store` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cbillid` varchar(40) DEFAULT NULL COMMENT '入库单主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `pk_org` varchar(20) DEFAULT NULL COMMENT '财务组织',
  `cvendorid` varchar(40) DEFAULT NULL COMMENT '供应商',
  `cdeptvid` varchar(40) DEFAULT NULL COMMENT '部门',
  `cstockorgvid` varchar(40) DEFAULT NULL COMMENT '库存组织',
  `dbilldate` varchar(19) DEFAULT NULL COMMENT '单据日期',
  `cstordocid` varchar(40) DEFAULT NULL COMMENT '仓库',
  `cstordocmanid` varchar(40) DEFAULT NULL COMMENT '库管员',
  `cpsnid` varchar(40) DEFAULT NULL COMMENT '业务员',
  `vnote` varchar(200) DEFAULT NULL COMMENT '备注',
  `dmakedate` varchar(19) DEFAULT NULL COMMENT '制单日期',
  `bestimateflag` varchar(20) DEFAULT NULL COMMENT '暂估标志',
  `freplenishflag` varchar(20) DEFAULT NULL COMMENT '采购退库',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `code` varchar(40) DEFAULT NULL COMMENT '单号转换为用途',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `push_order_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_order_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `vcontractcode` varchar(40) DEFAULT NULL COMMENT '合同编号',
  `nnum` varchar(40) DEFAULT NULL COMMENT '主数量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购入库源数据主表';


-- cloud_main.fi_convert_store_detail definition

CREATE TABLE `fi_convert_store_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cbill_bid` varchar(40) DEFAULT NULL COMMENT '采购入库单明细主键',
  `cbillid` varchar(40) DEFAULT NULL COMMENT '入库单主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `crowno_order` varchar(20) DEFAULT NULL COMMENT '行号',
  `nnum` varchar(40) DEFAULT NULL COMMENT '主数量',
  `nqtorigtaxprice` varchar(40) DEFAULT NULL COMMENT '含税单价',
  `nprice` varchar(40) DEFAULT NULL COMMENT '单价',
  `ntaxrate` varchar(40) DEFAULT NULL COMMENT '税率',
  `ntax` varchar(40) DEFAULT NULL COMMENT '税额',
  `norigtaxmny` varchar(40) DEFAULT NULL COMMENT '价税合计',
  `nmny` varchar(40) DEFAULT NULL COMMENT '金额',
  `vbatchcode` varchar(200) DEFAULT NULL COMMENT '批次号',
  `cprojectid` varchar(19) DEFAULT NULL COMMENT '项目',
  `cinventoryvid` varchar(20) DEFAULT NULL COMMENT '物料多版本',
  `crowno` varchar(20) DEFAULT NULL COMMENT '行号',
  `pk_marbasclass` varchar(400) DEFAULT NULL COMMENT '规格',
  `materialspec` varchar(20) DEFAULT NULL COMMENT '行号',
  `pk_measdoc` varchar(20) DEFAULT NULL COMMENT '主计量单位',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `vsrctype` varchar(40) DEFAULT NULL COMMENT '上游单据类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购入库详情数据主表';


-- cloud_main.fi_convert_virtual_order definition

CREATE TABLE `fi_convert_virtual_order` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_contr` varchar(40) DEFAULT NULL COMMENT '单据主键',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `bill_code` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `billmaketime` varchar(19) DEFAULT NULL COMMENT '制单日期',
  `billmaker` varchar(20) DEFAULT NULL COMMENT '制单人',
  `pk_supplier` varchar(20) DEFAULT NULL COMMENT '供应商',
  `curr_mny` varchar(40) DEFAULT NULL COMMENT '不含税合同金额',
  `tax` varchar(40) DEFAULT NULL COMMENT '税额',
  `pk_contracttype` varchar(32) DEFAULT NULL COMMENT '合同类型',
  `tot_taxmny` varchar(32) DEFAULT NULL COMMENT '含税总金额',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `pk_project` varchar(40) DEFAULT NULL COMMENT '项目',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='虚拟采购订单转换数据主表';


-- cloud_main.fi_convert_virtual_order_detail definition

CREATE TABLE `fi_convert_virtual_order_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_contr_works` varchar(40) DEFAULT NULL COMMENT '单据详情主键',
  `pk_contr` varchar(40) DEFAULT NULL COMMENT '单据主键',
  `bill_code` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `rowno` varchar(20) DEFAULT NULL COMMENT '行号',
  `pk_project` varchar(40) DEFAULT NULL COMMENT '项目',
  `curr_mny` varchar(32) DEFAULT NULL COMMENT '不含税合同金额',
  `taxrate` varchar(32) DEFAULT NULL COMMENT '税率',
  `tax` varchar(32) DEFAULT NULL COMMENT '税额',
  `curr_taxmny` varchar(32) DEFAULT NULL COMMENT '含税金额',
  `taxprice` varchar(32) DEFAULT NULL COMMENT '含税单价',
  `curr_num` varchar(32) DEFAULT NULL COMMENT '数量',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `pk_measdoc` varchar(20) DEFAULT NULL COMMENT '单位',
  `pk_material` varchar(20) DEFAULT NULL COMMENT '物料版本信息',
  `name` varchar(300) DEFAULT NULL COMMENT '物料名称',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `material_code` varchar(20) DEFAULT NULL COMMENT '物料编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='虚拟采购订单转换数据详情表';


-- cloud_main.fi_invoice_posting definition


CREATE TABLE `fi_invoice_posting` (
`id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
`busibillno` varchar(20) DEFAULT NULL COMMENT '业务单据ID',
`appid` varchar(20) DEFAULT NULL COMMENT '应用ID',
`typeid` varchar(40) DEFAULT NULL COMMENT '业务类型编码',
`xrech` varchar(20) DEFAULT NULL COMMENT '标识：记账发票',
`tbtkz` varchar(20) DEFAULT NULL COMMENT '标识：后续借/贷',
`comcod` varchar(20) DEFAULT NULL COMMENT '公司代码MDM',
`prctr` varchar(20) DEFAULT NULL COMMENT '利润中心MDM',
`apldate` varchar(20) DEFAULT NULL COMMENT '凭证日期',
`voudat` varchar(20) DEFAULT NULL COMMENT '过账日期',
`supcracc` varchar(20) DEFAULT NULL COMMENT '出票方',
`refdocnum` varchar(20) DEFAULT NULL COMMENT '附件张数',
`invveramt` varchar(20) DEFAULT NULL COMMENT '金额',
`taxamt` varchar(20) DEFAULT NULL COMMENT '税额',
`trancurren` varchar(20) DEFAULT NULL COMMENT '货币',
`basedate` varchar(20) DEFAULT NULL COMMENT '付款基准日期',
`terpay` varchar(20) DEFAULT NULL COMMENT '付款条件',
`paymmet` varchar(20) DEFAULT NULL COMMENT '付款方式',
`doctype` varchar(20) DEFAULT NULL COMMENT '凭证类型',
`instruction` varchar(50) DEFAULT NULL COMMENT '凭证抬头文本',
`taxrtcd` varchar(20) DEFAULT NULL COMMENT '税率代码',
`taxitem` varchar(20) DEFAULT NULL COMMENT '税项',
`managerid` varchar(20) DEFAULT NULL COMMENT '经办人编号',
`app` varchar(20) DEFAULT NULL COMMENT '制证人',
`checker` varchar(20) DEFAULT NULL COMMENT '审核人',
`financer` varchar(20) DEFAULT NULL COMMENT '财务主管',
`agMaintFeeUseMark` varchar(50) DEFAULT NULL COMMENT '农维费使用标志',
`mainDeptType` varchar(20) DEFAULT NULL COMMENT '部门分类',
`cosType` varchar(20) DEFAULT NULL COMMENT '业务事项',
`detCosType` varchar(50) DEFAULT NULL COMMENT '明细业务事项',
`integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
`push_status` tinyint(4) DEFAULT NULL COMMENT '4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败',
`push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
`return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
`return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
`try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
`readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
`create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
`update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
`del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购应付源数据详情';


-- cloud_main.fi_invoice_posting_item definition
-- upmsdev.fi_invoice_posting_item definition

CREATE TABLE `fi_invoice_posting_item` (
`id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
`busibillno` varchar(20) DEFAULT NULL COMMENT '业务单据ID',
`invoicedocitem` varchar(20) DEFAULT NULL COMMENT '发票行项目',
`accvouno` varchar(40) DEFAULT NULL COMMENT '凭证编号',
`fisyea` varchar(40) DEFAULT NULL COMMENT '财年',
`purordno` varchar(40) DEFAULT NULL COMMENT '采购订单号',
`ordprojid` varchar(40) DEFAULT NULL COMMENT '采购凭证的项目编号',
`mtcode` varchar(20) DEFAULT NULL COMMENT '物料',
`comcod` varchar(40) DEFAULT NULL COMMENT '公司代码',
`facnum` varchar(40) DEFAULT NULL COMMENT '工厂',
`taxrtcd` varchar(40) DEFAULT NULL COMMENT '税率代码',
`purorditemamt` varchar(40) DEFAULT NULL COMMENT '金额',
`ordqua` varchar(40) DEFAULT NULL COMMENT '数量',
`refdocnum` varchar(40) DEFAULT NULL COMMENT '参考凭证',
`mtdocyear` varchar(40) DEFAULT NULL COMMENT '参考凭证财年',
`referdocno` varchar(40) DEFAULT NULL COMMENT '参考凭证行项目',
`instruction` varchar(50) DEFAULT NULL COMMENT '行项目文本',
`chkid` varchar(300) DEFAULT NULL COMMENT '对账编号',
`inteOrdCode` varchar(40) DEFAULT NULL COMMENT '内部订单',
`create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
`update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
`del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购应付明细源数据详情';


-- cloud_main.fi_org_convert definition

CREATE TABLE `fi_org_convert` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `org_id` bigint(20) DEFAULT NULL COMMENT '业+组织ID',
  `pk_org` varchar(64) DEFAULT NULL COMMENT '组织主键',
  `org_code` varchar(64) DEFAULT NULL COMMENT '组织编码',
  `org_name` varchar(255) DEFAULT NULL COMMENT '组织名称',
  `mdm_org_code` varchar(64) DEFAULT NULL COMMENT 'MDM组织编码',
  `init_code` varchar(64) DEFAULT NULL COMMENT '组织级参数编码',
  `param_value` varchar(255) DEFAULT NULL COMMENT '组织级参数值',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_pk_org_init_code` (`pk_org`,`init_code`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='智慧平台组织转换信息';


-- cloud_main.fi_source_accounting definition

CREATE TABLE `fi_source_accounting` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_voucher` varchar(20) DEFAULT NULL COMMENT '凭证主键',
  `num` int(11) DEFAULT NULL COMMENT '凭证编码',
  `pk_vouchertype` varchar(20) DEFAULT NULL COMMENT '凭证类别',
  `pk_org` varchar(20) DEFAULT NULL COMMENT '财务组织',
  `free5` varchar(60) DEFAULT NULL COMMENT '业务单元',
  `explanation` varchar(800) DEFAULT NULL COMMENT '摘要',
  `prepareddate` varchar(20) DEFAULT NULL COMMENT '制单日期',
  `tallydate` varchar(20) DEFAULT NULL COMMENT '记账日期',
  `period` varchar(20) DEFAULT NULL COMMENT '会计期间',
  `pk_prepared` varchar(20) DEFAULT NULL COMMENT '制单人',
  `pk_checked` varchar(20) DEFAULT NULL COMMENT '审核人',
  `pk_currtype` varchar(20) DEFAULT NULL COMMENT '币种',
  `attachment` int(11) DEFAULT NULL COMMENT '附单据数',
  `year` varchar(10) DEFAULT NULL COMMENT '会计年度',
  `adjustperiod` varchar(10) DEFAULT '0' COMMENT '调整期间',
  `offervoucher` varchar(20) DEFAULT NULL COMMENT '冲销凭证',
  `code` int(11) DEFAULT NULL COMMENT '成功失败标识，0是成功；1是失败',
  `profCenter` varchar(20) DEFAULT NULL COMMENT '利润中心',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `push_order_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_order_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `push_time` varchar(256) DEFAULT NULL COMMENT '推送时间',
  `findocid` varchar(100) DEFAULT NULL COMMENT '中台凭证号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='凭证记账源数据主表';


-- cloud_main.fi_source_accounting_detail definition

CREATE TABLE `fi_source_accounting_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_voucher` varchar(20) DEFAULT NULL COMMENT '凭证主键',
  `num` int(11) DEFAULT NULL COMMENT '凭证编码',
  `pk_accasoa` varchar(20) DEFAULT NULL COMMENT '科目',
  `amount` decimal(28,8) DEFAULT NULL COMMENT '原币',
  `busidate` varchar(20) DEFAULT NULL COMMENT '业务日期',
  `assid` varchar(20) DEFAULT NULL COMMENT '辅助核算',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `pk_detail` varchar(100) DEFAULT NULL COMMENT '分录主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='凭证记账源数据明细表';


-- cloud_main.fi_source_accounting_docfree definition

CREATE TABLE `fi_source_accounting_docfree` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `assid` varchar(20) DEFAULT NULL COMMENT '辅助核算',
  `type` varchar(30) DEFAULT NULL COMMENT '凭证编码',
  `value` varchar(50) DEFAULT NULL COMMENT '核算维度类型',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `pk_detail` varchar(100) DEFAULT NULL COMMENT '分录主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='凭证记账辅助核算源数据明细表';


-- cloud_main.fi_source_convert_detail definition

CREATE TABLE `fi_source_convert_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cbill_bid` varchar(40) DEFAULT NULL COMMENT '采购入库单明细主键',
  `cbillid` varchar(40) DEFAULT NULL COMMENT '入库单主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `crowno_order` varchar(20) DEFAULT NULL COMMENT '行号',
  `nnum` varchar(40) DEFAULT NULL COMMENT '主数量',
  `nqtorigtaxprice` varchar(40) DEFAULT NULL COMMENT '含税单价',
  `nprice` varchar(40) DEFAULT NULL COMMENT '单价',
  `ntaxrate` varchar(40) DEFAULT NULL COMMENT '税率',
  `ntax` varchar(40) DEFAULT NULL COMMENT '税额',
  `norigtaxmny` varchar(40) DEFAULT NULL COMMENT '价税合计',
  `nmny` varchar(40) DEFAULT NULL COMMENT '金额',
  `vbatchcode` varchar(200) DEFAULT NULL COMMENT '批次号',
  `cprojectid` varchar(19) DEFAULT NULL COMMENT '项目',
  `cinventoryvid` varchar(20) DEFAULT NULL COMMENT '物料多版本',
  `crowno` varchar(20) DEFAULT NULL COMMENT '行号',
  `pk_marbasclass` varchar(400) DEFAULT NULL COMMENT '规格',
  `materialspec` varchar(20) DEFAULT NULL COMMENT '行号',
  `pk_measdoc` varchar(20) DEFAULT NULL COMMENT '主计量单位',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采购入库详情数据转换';


-- cloud_main.fi_source_cost_order definition

CREATE TABLE `fi_source_cost_order` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_feebalance` varchar(40) DEFAULT NULL COMMENT '单据主键',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `bill_code` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `protocol_name` varchar(40) DEFAULT NULL COMMENT '合同编号',
  `billmaketime` varchar(19) DEFAULT NULL COMMENT '制单日期',
  `billmaker` varchar(20) DEFAULT NULL COMMENT '制单人',
  `pk_supplier` varchar(20) DEFAULT NULL COMMENT '供应商',
  `curr_mny` varchar(40) DEFAULT NULL COMMENT '不含税合同金额',
  `tax` varchar(40) DEFAULT NULL COMMENT '税额',
  `tot_taxmny` varchar(32) DEFAULT NULL COMMENT '含税总金额',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `push_time` varchar(19) DEFAULT NULL COMMENT '推送时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='费用结算单源数据主表';


-- cloud_main.fi_source_cost_order_detail definition

CREATE TABLE `fi_source_cost_order_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_feebalance_b` varchar(40) DEFAULT NULL COMMENT '单据详情主键',
  `pk_feebalance` varchar(40) DEFAULT NULL COMMENT '单据主键',
  `bill_code` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `protocol_list` varchar(20) DEFAULT NULL COMMENT '行号',
  `pk_project` varchar(40) DEFAULT NULL COMMENT '项目',
  `curr_mny` varchar(32) DEFAULT NULL COMMENT '不含税合同金额',
  `taxrate` varchar(32) DEFAULT NULL COMMENT '税率',
  `tax` varchar(32) DEFAULT NULL COMMENT '税额',
  `curr_taxmny` varchar(32) DEFAULT NULL COMMENT '含税金额',
  `taxprice` varchar(32) DEFAULT NULL COMMENT '含税单价',
  `curr_num` varchar(32) DEFAULT NULL COMMENT '数量',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `pk_measdoc` varchar(20) DEFAULT NULL COMMENT '单位',
  `pk_material` varchar(20) DEFAULT NULL COMMENT '物料版本信息',
  `name` varchar(300) DEFAULT NULL COMMENT '物料名称',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `material_code` varchar(20) DEFAULT NULL COMMENT '物料编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='费用结算单源数据详情表';


-- cloud_main.fi_source_file_info definition

CREATE TABLE `fi_source_file_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `file_manage_id` bigint(20) NOT NULL COMMENT 'FileManage系统表主键',
  `org_id` int(11) DEFAULT NULL COMMENT '单位id',
  `relation_id` int(11) DEFAULT NULL COMMENT '关联id',
  `extends_id` int(11) DEFAULT NULL COMMENT '继承id',
  `image_code` varchar(50) DEFAULT NULL COMMENT '影像类型编码',
  `image_name` varchar(50) DEFAULT NULL COMMENT '影像类型名称',
  `is_upload` varchar(2) DEFAULT '0' COMMENT '是否刚性控制 1-是 0-否',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名',
  `bucket_name` varchar(255) DEFAULT NULL COMMENT '保存路径',
  `original` varchar(255) DEFAULT NULL COMMENT '原文件名',
  `file_type` varchar(255) DEFAULT NULL COMMENT '文件类型',
  `file_size` varchar(50) DEFAULT NULL COMMENT '文件大小',
  `upload_type` varchar(16) DEFAULT NULL COMMENT '上传方式（0-手动上传 1-扫描录入）',
  `module_type` varchar(255) DEFAULT NULL COMMENT '模块对应的表',
  `module_id` int(11) DEFAULT NULL COMMENT '模块业务id',
  `type` varchar(10) DEFAULT '0' COMMENT '模块子类型（0-未知，1-录音，2-图片）',
  `show_name` varchar(255) DEFAULT NULL COMMENT '显示文件名',
  `business_type` varchar(255) DEFAULT NULL COMMENT '业务类型',
  `erp_file_check` varchar(2) DEFAULT NULL COMMENT '文件一致性校验结果 1-通过 0-不通过',
  `create_by_name` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `update_by_name` varchar(50) DEFAULT NULL COMMENT '最后修改人姓名',
  `source_id` varchar(40) DEFAULT NULL COMMENT '来源主键id',
  `source_relation_id` varchar(40) DEFAULT NULL COMMENT '来源关联id',
  `ocr_type` varchar(20) DEFAULT NULL COMMENT 'ocr类型',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `del_flag` bigint(20) DEFAULT '0' COMMENT '删除标识',
  `bill_id` varchar(50) DEFAULT NULL COMMENT '单据主键',
  `business_code` varchar(100) DEFAULT NULL COMMENT '单据类型编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='文件信息源';


-- cloud_main.fi_source_file_ocr_info definition

CREATE TABLE `fi_source_file_ocr_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `file_manage_id` text NOT NULL COMMENT 'FileManage系统表主键',
  `einvoiceid` varchar(100) DEFAULT NULL COMMENT '发票ID',
  `uniquecodeofinvoice` varchar(100) DEFAULT NULL COMMENT '发票唯一标识',
  `createtime` datetime DEFAULT NULL COMMENT '数据插入时间',
  `bukrs` varchar(50) DEFAULT NULL COMMENT '公司代码',
  `einvoicenumber` varchar(100) DEFAULT NULL COMMENT '发票号码',
  `einvoicecode` varchar(100) DEFAULT NULL COMMENT '发票代码',
  `invoicedate` varchar(20) DEFAULT NULL COMMENT '开票日期',
  `invoicetype` varchar(50) DEFAULT NULL COMMENT '发票类型',
  `checkcode` varchar(100) DEFAULT NULL COMMENT '校验码',
  `salername` varchar(200) DEFAULT NULL COMMENT '销方名称',
  `salertaxno` varchar(50) DEFAULT NULL COMMENT '销方税号',
  `saleraddressphone` varchar(200) DEFAULT NULL COMMENT '销方地址、电话',
  `saleraccount` varchar(200) DEFAULT NULL COMMENT '销方开户行及账号',
  `buyername` varchar(200) DEFAULT NULL COMMENT '购方名称',
  `buyertaxno` varchar(50) DEFAULT NULL COMMENT '购方税号',
  `buyeraddressphone` varchar(200) DEFAULT NULL COMMENT '购方地址、电话',
  `buyeraccount` varchar(200) DEFAULT NULL COMMENT '购方开户行及账号',
  `invoiceamount` varchar(20) DEFAULT NULL COMMENT '发票金额(不含税)',
  `taxamount` varchar(20) DEFAULT NULL COMMENT '发票税额',
  `totalamount` varchar(20) DEFAULT NULL COMMENT '价税合计',
  `dt` varchar(50) DEFAULT NULL COMMENT 'dt',
  `file_id` varchar(50) DEFAULT NULL COMMENT '文件id',
  `sync_status` varchar(2) DEFAULT '0' COMMENT '同步状态',
  `invoice_type_code` varchar(10) DEFAULT NULL COMMENT '发票类型编码',
  `elec_invoice_no` varchar(20) DEFAULT NULL COMMENT '全电发票号码',
  `invoice_status` varchar(10) DEFAULT NULL COMMENT '发票状态',
  `dparty` varchar(50) DEFAULT NULL COMMENT '开票人',
  `tax_cal_code` varchar(50) DEFAULT NULL COMMENT '税收分类编码',
  `special_policy_sign` int(1) DEFAULT NULL COMMENT '特殊政策标识 0：否 1：是',
  `tax_rate_flag` int(1) DEFAULT NULL COMMENT '零税率标识',
  `body` varchar(1024) DEFAULT NULL COMMENT '发票表体',
  `unit_code` varchar(100) DEFAULT NULL COMMENT '发票使用单位',
  `pk_dept` varchar(100) DEFAULT NULL COMMENT '部门主键',
  `integration_status` varchar(2) DEFAULT '0' COMMENT '集成状态',
  `push_status` int(11) DEFAULT NULL COMMENT '推送状态(4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败)',
  `push_msg` text COMMENT '推送成功失败信息',
  `return_msg` text COMMENT '返回同步消息',
  `return_status` varchar(2) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(20) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(20) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `del_flag` bigint(20) DEFAULT '0' COMMENT '删除标识',
  `bill_id` varchar(50) DEFAULT NULL COMMENT '单据主键',
  `business_code` varchar(100) DEFAULT NULL COMMENT '单据类型编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='进项发票源数据详情';


-- cloud_main.fi_source_new_store definition

CREATE TABLE `fi_source_new_store` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '应付财务组织',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT '应付单主键',
  `src_billid` varchar(40) DEFAULT NULL COMMENT '源头单据主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `billdate` char(19) DEFAULT NULL COMMENT '单据日期',
  `billno` varchar(40) DEFAULT NULL COMMENT '单据号',
  `pk_tradetype` varchar(40) DEFAULT NULL COMMENT '应付类型',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `push_order_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_order_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购入库源数据主表';


-- cloud_main.fi_source_new_store_detail definition

CREATE TABLE `fi_source_new_store_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT '应付单主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `billcode` varchar(40) DEFAULT NULL COMMENT '合同编号',
  `rowno` varchar(40) DEFAULT NULL COMMENT '行号',
  `protocal_list` varchar(40) DEFAULT NULL COMMENT '行号',
  `crowno` varchar(20) DEFAULT NULL COMMENT '采购单行号',
  `payableitem_rowno` char(20) DEFAULT NULL COMMENT '应付单行标识',
  `quantity_cr` decimal(20,8) DEFAULT NULL COMMENT '贷方数量',
  `local_notax_cr` decimal(28,8) DEFAULT NULL COMMENT '组织本币无税金额',
  `material` varchar(20) DEFAULT NULL COMMENT '物料',
  `direction` int(11) DEFAULT NULL COMMENT '方向',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购入库详情数据主表';


-- cloud_main.fi_source_order definition

CREATE TABLE `fi_source_order` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_order` varchar(40) DEFAULT NULL COMMENT '订单主键',
  `pk_project` varchar(40) DEFAULT NULL COMMENT '项目主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `dmakedate` varchar(19) DEFAULT NULL COMMENT '制单日期',
  `billmaker` varchar(20) DEFAULT NULL COMMENT '制单人',
  `pk_supplier` varchar(20) DEFAULT NULL COMMENT '供应商',
  `corigcurrencyid` varchar(20) DEFAULT NULL COMMENT '币种',
  `vcontractcode` varchar(40) DEFAULT NULL COMMENT '合同号',
  `norigmny` varchar(32) DEFAULT NULL COMMENT '无税金额',
  `nexchangerate` varchar(32) DEFAULT NULL COMMENT '调整方式',
  `ntax` varchar(32) DEFAULT NULL COMMENT '税额',
  `breturn` varchar(32) DEFAULT NULL COMMENT '退货',
  `ctrantypeid` varchar(32) DEFAULT NULL COMMENT '订单类型',
  `vtrantypecode` varchar(32) DEFAULT NULL COMMENT '单据类型编码',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `push_time` varchar(256) DEFAULT NULL COMMENT '推送时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='订单源数据主表';


-- cloud_main.fi_source_order_detail definition

CREATE TABLE `fi_source_order_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_order_b` varchar(40) DEFAULT NULL COMMENT '订单主键',
  `pk_order` varchar(40) DEFAULT NULL COMMENT '订单主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `crowno` varchar(20) DEFAULT NULL COMMENT '行号',
  `cprojectid` varchar(20) DEFAULT NULL COMMENT '项目',
  `norigmny` varchar(32) DEFAULT NULL COMMENT '无税金额',
  `nexchangerate` varchar(32) DEFAULT NULL COMMENT '调整方式',
  `ntax` varchar(32) DEFAULT NULL COMMENT '税额',
  `norigtaxmny` varchar(32) DEFAULT NULL COMMENT '价税合计',
  `pk_dept_v` varchar(20) DEFAULT NULL COMMENT '采购部门',
  `nqtorigtaxprice` varchar(32) DEFAULT NULL COMMENT '含税单价',
  `nastnum` varchar(32) DEFAULT NULL COMMENT '数量',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `breturn` varchar(20) DEFAULT NULL COMMENT '退货',
  `castunitid` varchar(20) DEFAULT NULL COMMENT '单位',
  `pk_material` varchar(20) DEFAULT NULL COMMENT '物料版本信息',
  `name` varchar(300) DEFAULT NULL COMMENT '物料名称',
  `ctrantypeid` varchar(20) DEFAULT NULL COMMENT '订单类型',
  `dbilldate` varchar(19) DEFAULT NULL COMMENT '订单日期',
  `nqtorigprice` varchar(32) DEFAULT NULL COMMENT '无税单价',
  `ntotalorigmny` varchar(32) DEFAULT NULL COMMENT '价税合计',
  `ntaxmny` varchar(32) DEFAULT NULL COMMENT '本币价税合计',
  `nmny` varchar(32) DEFAULT NULL COMMENT '本币无税金额',
  `forderstatus` varchar(32) DEFAULT NULL COMMENT '单据状态',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `ntaxrate` varchar(20) DEFAULT NULL COMMENT '税率',
  `material_code` varchar(20) DEFAULT NULL COMMENT '物料编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='订单源数据详情表';


-- cloud_main.fi_source_payment definition

CREATE TABLE `fi_source_payment` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_settlement` varchar(40) DEFAULT NULL COMMENT '结算信息主键',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '财务组织',
  `pk_tradetype` varchar(40) DEFAULT NULL COMMENT '业务单交易类型',
  `pk_billoperator` varchar(20) DEFAULT NULL COMMENT '业务单据录入人',
  `pk_balatype` varchar(40) DEFAULT NULL COMMENT '结算方式',
  `pk_account` varchar(20) DEFAULT NULL COMMENT '本方账户',
  `orglocal` varchar(20) DEFAULT NULL COMMENT '本币金额',
  `busi_billdate` varchar(20) DEFAULT NULL COMMENT '业务单据日期',
  `pk_currtype` varchar(40) DEFAULT NULL COMMENT '币种',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='通用付款单源数据主表';


-- cloud_main.fi_source_payment_detail definition

CREATE TABLE `fi_source_payment_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_detail` varchar(40) DEFAULT NULL COMMENT '结算信息详情主键',
  `pk_settlement` varchar(40) DEFAULT NULL COMMENT '结算信息主键',
  `tradername` varchar(40) DEFAULT NULL COMMENT '对方对象名称',
  `pk_costsubj` varchar(32) DEFAULT NULL COMMENT '收支项目',
  `paylocal` varchar(32) DEFAULT NULL COMMENT '付款组织本币金额',
  `oppaccname` varchar(32) DEFAULT NULL COMMENT '对方账户户名',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='通用付款单详情源数据主表';


-- cloud_main.fi_source_pu_payable definition

CREATE TABLE `fi_source_pu_payable` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_org` varchar(20) DEFAULT NULL COMMENT '应付财务组织',
  `billmaker` varchar(20) DEFAULT NULL COMMENT '制单人',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT '应付单标识',
  `def6` varchar(20) DEFAULT NULL COMMENT '费用类型',
  `pk_tradetypeid` varchar(20) DEFAULT NULL COMMENT '应付类型',
  `pk_tradetype` varchar(20) DEFAULT NULL COMMENT '应付类型编码',
  `pk_currtype` varchar(20) DEFAULT NULL COMMENT '币种',
  `money` decimal(28,8) DEFAULT NULL COMMENT '原币金额',
  `billstatus` int(11) DEFAULT NULL COMMENT '单据状态',
  `billno` varchar(40) DEFAULT NULL COMMENT '单据号',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '4:推送中、1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `back_tag` varchar(40) DEFAULT NULL COMMENT '是否退回重传标识',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `push_time` varchar(50) DEFAULT NULL COMMENT '推送时间',
  `return_bill` varchar(255) DEFAULT NULL COMMENT '返回同步消息',
  `return_time` varchar(50) DEFAULT NULL COMMENT '返回时间',
  `def47` varchar(255) DEFAULT NULL COMMENT '付款性质',
  `def52` varchar(255) DEFAULT NULL COMMENT '部门分类',
  `ywid` varchar(255) DEFAULT NULL COMMENT '远光回调ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购应付源数据详情';


-- cloud_main.fi_source_pu_payable_detail definition

CREATE TABLE `fi_source_pu_payable_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_deptid_v` varchar(20) DEFAULT NULL COMMENT '部门',
  `contractno` varchar(40) DEFAULT NULL COMMENT '合同号',
  `purchaseorder` varchar(40) DEFAULT NULL COMMENT '订单号',
  `invoiceno` varchar(40) DEFAULT NULL COMMENT '发票号',
  `outstoreno` varchar(40) DEFAULT NULL COMMENT '出库单号',
  `supplier` varchar(20) DEFAULT NULL COMMENT '供应商版本',
  `material` varchar(40) DEFAULT NULL COMMENT '物料',
  `quantity_cr` decimal(28,8) DEFAULT NULL COMMENT '贷方数量',
  `money_cr` decimal(28,8) DEFAULT NULL COMMENT '贷方原币金额',
  `notax_cr` decimal(28,8) DEFAULT NULL COMMENT '贷方原币无税金额',
  `local_money_cr` decimal(28,8) DEFAULT NULL COMMENT '组织本币金额',
  `local_notax_cr` decimal(28,8) DEFAULT NULL COMMENT '组织本币无税金额',
  `local_tax_cr` decimal(28,8) DEFAULT NULL COMMENT '税额',
  `taxrate` decimal(28,8) DEFAULT NULL COMMENT '税率',
  `pk_subjcode` varchar(20) DEFAULT NULL COMMENT '收支项目',
  `pk_payableitem` varchar(40) DEFAULT NULL COMMENT '应付单行标识',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT '应付单表示',
  `rate` decimal(28,8) DEFAULT NULL COMMENT '税率',
  `project` varchar(40) DEFAULT NULL COMMENT '项目',
  `crowno` varchar(40) DEFAULT NULL COMMENT '采购订单行号',
  `top_billtype` varchar(40) DEFAULT NULL COMMENT '上层单据类型',
  `top_billid` varchar(40) DEFAULT NULL COMMENT '上层单据主键',
  `top_itemid` varchar(40) DEFAULT NULL COMMENT '上层单据行主键',
  `src_billtype` varchar(40) DEFAULT NULL COMMENT '源头单据类型',
  `src_billid` varchar(40) DEFAULT NULL COMMENT '源头单据主键',
  `src_itemid` varchar(40) DEFAULT NULL COMMENT '源头单据行主键',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `src_tradetype` varchar(20) DEFAULT NULL COMMENT '源头交易类型',
  `direction` int(11) DEFAULT '-1' COMMENT '方向，1=借方，-1=贷方',
  `line_number` int(11) DEFAULT '-1' COMMENT '行号',
  `rowno` varchar(40) DEFAULT NULL COMMENT '行号',
  `store_billno` varchar(40) DEFAULT NULL COMMENT '入库单编号',
  `store_rowno` varchar(40) DEFAULT NULL COMMENT '入库单行号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购应付明细源数据详情';


-- cloud_main.fi_source_pu_paybill definition

CREATE TABLE `fi_source_pu_paybill` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_settlement` varchar(64) NOT NULL COMMENT '结算单主键',
  `pk_org` varchar(64) DEFAULT NULL COMMENT '财务组织',
  `pk_billoperator` varchar(64) DEFAULT NULL COMMENT '业务单据录入人',
  `primal` decimal(18,2) DEFAULT NULL COMMENT '原币金额',
  `expense_type` varchar(64) DEFAULT NULL COMMENT '费用类型',
  `pk_tradetype` varchar(64) DEFAULT NULL COMMENT '应付类型编码',
  `integration_status` varchar(32) DEFAULT NULL COMMENT '集成状态',
  `push_status` int(11) DEFAULT NULL COMMENT '推送状态 4:推送中、1:转换失败、2:推送失败、3:推送成功、5:推送财务失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(255) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(32) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(20) DEFAULT NULL COMMENT '重试次数',
  `readiness` bigint(20) DEFAULT NULL COMMENT '0 未就绪 1 就绪',
  `back_tag` varchar(40) DEFAULT NULL COMMENT '是否退回重传标识',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `billcode` varchar(255) DEFAULT NULL,
  `vbillcode` varchar(255) DEFAULT NULL,
  `cbillcode` varchar(100) DEFAULT NULL COMMENT '合同号',
  `push_time` varchar(100) DEFAULT NULL,
  `return_bill` varchar(100) DEFAULT NULL,
  `return_time` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='采购付款源数据详情';


-- cloud_main.fi_source_pu_paybill_detail definition

CREATE TABLE `fi_source_pu_paybill_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_settlement` varchar(64) NOT NULL COMMENT '结算单主键',
  `pk_deptid_v` varchar(64) DEFAULT NULL COMMENT '部门',
  `prepay` varchar(32) DEFAULT NULL COMMENT '付款性质',
  `memo` varchar(255) DEFAULT NULL COMMENT '摘要',
  `project` varchar(64) DEFAULT NULL COMMENT '项目',
  `contract_no` varchar(64) DEFAULT NULL COMMENT '合同号',
  `pk_recpaytype` varchar(64) DEFAULT NULL COMMENT '付款业务类型',
  `supplier` varchar(128) DEFAULT NULL COMMENT '供应商',
  `oppaccount` varchar(64) DEFAULT NULL COMMENT '对方账号',
  `oppaccname` varchar(128) DEFAULT NULL COMMENT '对方账户户名',
  `pk_oppbank` varchar(64) DEFAULT NULL COMMENT '对方银行',
  `pay` decimal(28,8) DEFAULT NULL COMMENT '付款原币金额',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `changerate` decimal(28,8) DEFAULT NULL COMMENT '实付套汇汇率',
  `pk_currtype_last` varchar(100) DEFAULT NULL,
  `pk_deptid` varchar(64) DEFAULT NULL COMMENT '部门',
  `src_billid` varchar(64) DEFAULT NULL COMMENT '上游单据主键',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='采购付款明细转换数据详情';


-- cloud_main.fi_source_receivable definition

CREATE TABLE `fi_source_receivable` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_recbill` varchar(40) DEFAULT NULL COMMENT '结算信息主键',
  `pk_org_v` varchar(40) DEFAULT NULL COMMENT '应收财务组织',
  `contractno` varchar(40) DEFAULT NULL COMMENT '应收财务组织',
  `project` varchar(40) DEFAULT NULL COMMENT '制单人',
  `billmaker` varchar(40) DEFAULT NULL COMMENT '制单人',
  `billno` varchar(40) DEFAULT NULL COMMENT '单据号',
  `scomment` varchar(40) DEFAULT NULL COMMENT '摘要',
  `local_money` varchar(40) DEFAULT NULL COMMENT '组织本币金额',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `pk_tradetype` varchar(40) DEFAULT NULL COMMENT '单据类型',
  `pk_subjcode` varchar(40) DEFAULT NULL COMMENT '收支项目',
  `back_tag` varchar(40) DEFAULT NULL COMMENT '是否退回重传标识',
  `customer` varchar(40) DEFAULT NULL COMMENT '客户名称',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='项目应收单源数据主表';


-- cloud_main.fi_source_receivable_detail definition

CREATE TABLE `fi_source_receivable_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_recitem` varchar(40) DEFAULT NULL COMMENT 'pk详情主键',
  `pk_recbill` varchar(40) DEFAULT NULL COMMENT 'pk主键',
  `contractno` varchar(40) DEFAULT NULL COMMENT '应收财务组织',
  `project` varchar(40) DEFAULT NULL COMMENT '制单人',
  `pk_subjcode` varchar(40) DEFAULT NULL COMMENT '收支项目',
  `local_notax_de` varchar(40) DEFAULT NULL COMMENT '组织本币无税金额',
  `local_tax_de` varchar(40) DEFAULT NULL COMMENT '税额',
  `local_money_de` varchar(40) DEFAULT NULL COMMENT '组织本币金额',
  `invoiceno` varchar(40) DEFAULT NULL COMMENT '发票号',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `customer` varchar(40) DEFAULT NULL COMMENT '客户名称',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='项目应收详情单源数据主表';


-- cloud_main.fi_source_reimbursement definition

CREATE TABLE `fi_source_reimbursement` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT 'pk主键',
  `pk_org_v` varchar(40) DEFAULT NULL COMMENT '应付财务组织版本',
  `billmaker` varchar(40) DEFAULT NULL COMMENT '制单人',
  `pk_psndoc` varchar(40) DEFAULT NULL COMMENT '业务员',
  `def5` varchar(101) DEFAULT NULL COMMENT '合同类型',
  `pk_currtype` varchar(40) DEFAULT NULL COMMENT '币种',
  `project` varchar(40) DEFAULT NULL COMMENT '项目',
  `supplier` varchar(40) DEFAULT NULL COMMENT '供应商版本',
  `pk_deptid` varchar(40) DEFAULT NULL COMMENT '部门',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `local_money` varchar(40) DEFAULT NULL COMMENT '组织本币金额',
  `pk_subjcode` varchar(40) DEFAULT NULL COMMENT '收支项目',
  `back_tag` varchar(40) DEFAULT NULL COMMENT '是否退回重传标识',
  `objtype` varchar(40) DEFAULT NULL COMMENT '往来对象',
  `pk_tradetype` varchar(40) DEFAULT NULL COMMENT '单据类型',
  `scomment` varchar(400) DEFAULT NULL COMMENT '摘要',
  `billmaker_t` varchar(40) DEFAULT NULL COMMENT '制单人',
  `pk_deptid_v` varchar(40) DEFAULT NULL COMMENT '部门',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `billno` varchar(40) DEFAULT NULL COMMENT '单据编号',
  `def47` varchar(40) DEFAULT NULL COMMENT '平台业务事项',
  `def2` varchar(40) DEFAULT NULL COMMENT '付款方式',
  `def52` varchar(40) DEFAULT NULL COMMENT '部门分类',
  `pk_org_name` varchar(100) DEFAULT NULL COMMENT '组织名称',
  `return_bill` varchar(256) DEFAULT NULL COMMENT '返回单据号',
  `return_time` varchar(19) DEFAULT NULL COMMENT '返回接收时间',
  `push_time` varchar(19) DEFAULT NULL COMMENT '推送时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='通用报销单源数据主表';


-- cloud_main.fi_source_reimbursement_detail definition

CREATE TABLE `fi_source_reimbursement_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_payableitem` varchar(40) DEFAULT NULL COMMENT 'pk详情主键',
  `pk_payablebill` varchar(40) DEFAULT NULL COMMENT 'pk主键',
  `pk_psndoc` varchar(40) DEFAULT NULL COMMENT '业务员',
  `invoiceno` varchar(40) DEFAULT NULL COMMENT '发票号',
  `recaccount` varchar(40) DEFAULT NULL COMMENT '收款银行账户',
  `pk_balatype` varchar(40) DEFAULT NULL COMMENT '结算方式',
  `payaccount` varchar(40) DEFAULT NULL COMMENT '付款银行账户',
  `contractno` varchar(40) DEFAULT NULL COMMENT '合同号',
  `pk_subjcode` varchar(40) DEFAULT NULL COMMENT '收支项目',
  `pk_tradetypeid` varchar(40) DEFAULT NULL COMMENT '应付类型',
  `local_money` varchar(40) DEFAULT NULL COMMENT '组织本币金额',
  `money_de` varchar(40) DEFAULT NULL COMMENT '借方原币金额',
  `local_notax_de` varchar(40) DEFAULT NULL COMMENT '组织本币无税金额',
  `local_tax_de` varchar(40) DEFAULT NULL COMMENT '税额',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `local_money_cr` varchar(40) DEFAULT NULL COMMENT '组织本币金额',
  `local_notax_cr` varchar(40) DEFAULT NULL COMMENT '组织本币无税金额',
  `local_tax_cr` varchar(40) DEFAULT NULL COMMENT '税额',
  `supplier` varchar(40) DEFAULT NULL COMMENT '供应商版本',
  `objtype` varchar(400) DEFAULT NULL COMMENT '往来对象',
  `pk_deptid` varchar(40) DEFAULT NULL COMMENT '部门',
  `pk_deptid_v` varchar(40) DEFAULT NULL COMMENT '部门',
  `def50` varchar(40) DEFAULT NULL COMMENT '车船税',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='通用报销单详情源数据主表';


-- cloud_main.fi_source_store definition

CREATE TABLE `fi_source_store` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cbillid` varchar(40) DEFAULT NULL COMMENT '入库单主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `pk_org` varchar(20) DEFAULT NULL COMMENT '财务组织',
  `cvendorid` varchar(40) DEFAULT NULL COMMENT '供应商',
  `cdeptvid` varchar(40) DEFAULT NULL COMMENT '部门',
  `cstockorgvid` varchar(40) DEFAULT NULL COMMENT '库存组织',
  `dbilldate` varchar(19) DEFAULT NULL COMMENT '单据日期',
  `cstordocid` varchar(40) DEFAULT NULL COMMENT '仓库',
  `cstordocmanid` varchar(40) DEFAULT NULL COMMENT '库管员',
  `cpsnid` varchar(40) DEFAULT NULL COMMENT '业务员',
  `vnote` varchar(200) DEFAULT NULL COMMENT '备注',
  `dmakedate` varchar(19) DEFAULT NULL COMMENT '制单日期',
  `bestimateflag` varchar(20) DEFAULT NULL COMMENT '暂估标志',
  `freplenishflag` varchar(20) DEFAULT NULL COMMENT '采购退库',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `code` varchar(40) DEFAULT NULL COMMENT '单号转换为用途',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `push_order_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_order_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `vcontractcode` varchar(40) DEFAULT NULL COMMENT '合同编号',
  `nnum` varchar(40) DEFAULT NULL COMMENT '主数量',
  `push_time` varchar(19) DEFAULT NULL COMMENT '推送时间',
  `push_order_time` varchar(19) DEFAULT NULL COMMENT '推送时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购入库源数据主表';


-- cloud_main.fi_source_store_detail definition

CREATE TABLE `fi_source_store_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `cbill_bid` varchar(40) DEFAULT NULL COMMENT '采购入库单明细主键',
  `cbillid` varchar(40) DEFAULT NULL COMMENT '入库单主键',
  `vbillcode` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `crowno_order` varchar(20) DEFAULT NULL COMMENT '行号',
  `nnum` varchar(40) DEFAULT NULL COMMENT '主数量',
  `nqtorigtaxprice` varchar(40) DEFAULT NULL COMMENT '含税单价',
  `nprice` varchar(40) DEFAULT NULL COMMENT '单价',
  `ntaxrate` varchar(40) DEFAULT NULL COMMENT '税率',
  `ntax` varchar(40) DEFAULT NULL COMMENT '税额',
  `norigtaxmny` varchar(40) DEFAULT NULL COMMENT '价税合计',
  `nmny` varchar(40) DEFAULT NULL COMMENT '金额',
  `vbatchcode` varchar(200) DEFAULT NULL COMMENT '批次号',
  `cprojectid` varchar(19) DEFAULT NULL COMMENT '项目',
  `cinventoryvid` varchar(20) DEFAULT NULL COMMENT '物料多版本',
  `crowno` varchar(20) DEFAULT NULL COMMENT '行号',
  `pk_marbasclass` varchar(400) DEFAULT NULL COMMENT '规格',
  `materialspec` varchar(20) DEFAULT NULL COMMENT '行号',
  `pk_measdoc` varchar(20) DEFAULT NULL COMMENT '主计量单位',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `vsrctype` varchar(40) DEFAULT NULL COMMENT '上游单据类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='采购入库详情数据主表';


-- cloud_main.fi_source_virtual_order definition

CREATE TABLE `fi_source_virtual_order` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_contr` varchar(40) DEFAULT NULL COMMENT '单据主键',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `bill_code` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `billmaketime` varchar(19) DEFAULT NULL COMMENT '制单日期',
  `billmaker` varchar(20) DEFAULT NULL COMMENT '制单人',
  `pk_supplier` varchar(20) DEFAULT NULL COMMENT '供应商',
  `curr_mny` varchar(40) DEFAULT NULL COMMENT '不含税合同金额',
  `tax` varchar(40) DEFAULT NULL COMMENT '税额',
  `pk_contracttype` varchar(32) DEFAULT NULL COMMENT '合同类型',
  `tot_taxmny` varchar(32) DEFAULT NULL COMMENT '含税总金额',
  `integration_status` varchar(128) DEFAULT NULL COMMENT '集成状态',
  `push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
  `push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
  `return_msg` varchar(256) DEFAULT NULL COMMENT '返回同步消息',
  `return_status` varchar(16) DEFAULT NULL COMMENT '返回同步状态',
  `try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
  `readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `push_time` varchar(19) DEFAULT NULL COMMENT '推送时间',
  `pk_project` varchar(40) DEFAULT NULL COMMENT '项目',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='虚拟采购订单源数据主表';


-- cloud_main.fi_source_virtual_order_detail definition

CREATE TABLE `fi_source_virtual_order_detail` (
  `id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `pk_contr_works` varchar(40) DEFAULT NULL COMMENT '单据详情主键',
  `pk_contr` varchar(40) DEFAULT NULL COMMENT '单据主键',
  `bill_code` varchar(40) DEFAULT NULL COMMENT '订单编号',
  `rowno` varchar(20) DEFAULT NULL COMMENT '行号',
  `pk_project` varchar(40) DEFAULT NULL COMMENT '项目',
  `curr_mny` varchar(32) DEFAULT NULL COMMENT '不含税合同金额',
  `taxrate` varchar(32) DEFAULT NULL COMMENT '税率',
  `tax` varchar(32) DEFAULT NULL COMMENT '税额',
  `curr_taxmny` varchar(32) DEFAULT NULL COMMENT '含税金额',
  `taxprice` varchar(32) DEFAULT NULL COMMENT '含税单价',
  `curr_num` varchar(32) DEFAULT NULL COMMENT '数量',
  `pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
  `pk_org_v` varchar(20) DEFAULT NULL COMMENT '采购组织',
  `pk_measdoc` varchar(20) DEFAULT NULL COMMENT '单位',
  `pk_material` varchar(20) DEFAULT NULL COMMENT '物料版本信息',
  `name` varchar(300) DEFAULT NULL COMMENT '物料名称',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
  `del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
  `material_code` varchar(20) DEFAULT NULL COMMENT '物料编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='虚拟采购订单源数据详情表';


-- cloud_main.fi_uds_file_record definition

CREATE TABLE `fi_uds_file_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `bucket_name` varchar(255) NOT NULL COMMENT 'OSS存储桶名称',
  `file_name` varchar(255) NOT NULL COMMENT 'OSS文件名称',
  `original` varchar(255) NOT NULL COMMENT 'OSS原始文件名',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `file_size` varchar(50) DEFAULT NULL COMMENT '文件大小',
  `document_id` varchar(100) DEFAULT NULL COMMENT 'UDS文档ID',
  `version_id` varchar(100) DEFAULT NULL COMMENT 'UDS版本ID',
  `uds_file_name` varchar(255) DEFAULT NULL COMMENT 'UDS返回的文件名',
  `meta_type_name` varchar(50) DEFAULT NULL COMMENT 'UDS元数据类型名称',
  `push_status` varchar(2) DEFAULT '0' COMMENT '推送状态(0-未推送 1-推送成功)',
  `push_msg` text COMMENT '推送失败信息',
  `push_time` datetime DEFAULT NULL COMMENT '推送时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标识',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bucket_file` (`bucket_name`,`file_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='UDS文件记录';


-- 2025-04-24 杜凯祥 增加制单人
ALTER TABLE fi_source_store ADD billmaker varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '制单人';
ALTER TABLE fi_convert_store ADD billmaker varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '制单人';

-- 2025-04-27 杜凯祥 新增集团组织对照关系
-- 2025-04-27 添加组织编码和名称字段
ALTER TABLE fi_source_pu_payable
    ADD COLUMN pk_org_code varchar(255) DEFAULT NULL COMMENT '应付财务组织编码' AFTER pk_org,
    ADD COLUMN pk_org_name varchar(255) DEFAULT NULL COMMENT '应付财务组织名称' AFTER pk_org_code;
ALTER TABLE fi_convert_pu_payable
    ADD COLUMN pk_org_code varchar(255) DEFAULT NULL COMMENT '应付财务组织编码' AFTER pk_org,
    ADD COLUMN pk_org_name varchar(255) DEFAULT NULL COMMENT '应付财务组织名称' AFTER pk_org_code;
ALTER TABLE fi_source_pu_paybill
    ADD COLUMN pk_org_code varchar(255) DEFAULT NULL COMMENT '应付财务组织编码' AFTER pk_org,
    ADD COLUMN pk_org_name varchar(255) DEFAULT NULL COMMENT '应付财务组织名称' AFTER pk_org_code;
ALTER TABLE fi_convert_pu_paybill
    ADD COLUMN pk_org_code varchar(255) DEFAULT NULL COMMENT '应付财务组织编码' AFTER pk_org,
    ADD COLUMN pk_org_name varchar(255) DEFAULT NULL COMMENT '应付财务组织名称' AFTER pk_org_code;

-- 2025-04-27 新增组织编码和名称
ALTER TABLE fi_source_accounting
    ADD COLUMN pk_org_code varchar(255) DEFAULT NULL COMMENT '应付财务组织编码' AFTER pk_org,
    ADD COLUMN pk_org_name varchar(255) DEFAULT NULL COMMENT '应付财务组织名称' AFTER pk_org_code;

ALTER TABLE fi_convert_accounting
    ADD COLUMN pk_org_code varchar(255) DEFAULT NULL COMMENT '应付财务组织编码' AFTER pk_org,
    ADD COLUMN pk_org_name varchar(255) DEFAULT NULL COMMENT '应付财务组织名称' AFTER pk_org_code;

-- 2025-04-27 新增集团组织对照关系
CREATE TABLE `fi_org_compare` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
`org_id` bigint(20) DEFAULT NULL COMMENT '业+组织ID',
`upms_code` bigint(20) DEFAULT NULL COMMENT '业+组织编码',
`upms_name` bigint(20) DEFAULT NULL COMMENT '业+组织名称',
`pk_group` varchar(64) DEFAULT NULL COMMENT '集团主键',
`group_code` varchar(64) DEFAULT NULL COMMENT '集团编码',
`group_name` varchar(255) DEFAULT NULL COMMENT '集团名称',
`pk_org` varchar(64) DEFAULT NULL COMMENT '组织主键',
`org_code` varchar(64) DEFAULT NULL COMMENT '组织编码',
`org_name` varchar(255) DEFAULT NULL COMMENT '组织名称',
`mdm_org_code` varchar(64) DEFAULT NULL COMMENT 'MDM组织编码',
`create_by` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
`update_by` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
`del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组织对照关系';

-- 2025-04-27 杜凯祥 增加“组织名称”
ALTER TABLE fi_convert_store ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';
ALTER TABLE fi_source_store ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';
ALTER TABLE fi_convert_order ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';
ALTER TABLE fi_source_order ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';
ALTER TABLE fi_convert_cost_order ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';
ALTER TABLE fi_source_cost_order ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';
ALTER TABLE fi_convert_virtual_order ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';
ALTER TABLE fi_source_virtual_order ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';
ALTER TABLE fi_convert_receivable ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';
ALTER TABLE fi_source_receivable ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';

-- 2025-04-27 ylx 付款单同步 def47-付款性质 def52-部门分类
ALTER TABLE fi_source_pu_paybill ADD def47 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '付款性质';
ALTER TABLE fi_convert_pu_paybill ADD def47 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '付款性质';
ALTER TABLE fi_source_pu_paybill ADD def52 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '部门分类';
ALTER TABLE fi_convert_pu_paybill ADD def52 varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '部门分类';

-- 2025-04-28 杜凯祥 “服务物资发票过账服务”后没有组织，将组织转换存入
ALTER TABLE fi_invoice_posting ADD pk_org varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织';
ALTER TABLE fi_invoice_posting ADD pk_org_name varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '组织名称';
CREATE UNIQUE INDEX fi_org_compare_pk_org_IDX USING BTREE ON fi_org_compare (pk_org,del_flag);
CREATE UNIQUE INDEX fi_org_compare_mdm_org_code_IDX USING BTREE ON fi_org_compare (mdm_org_code,del_flag);

-- 2025-05-07 增加组织级别
ALTER TABLE fi_org_compare ADD `level` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 0 NULL COMMENT '组织级别 0单位 1集团 2顶部组织';
update fi_org_compare set level='1'
where org_code in(730000,
                  740000,
                  750000,
                  780000,
                  790000,
                  800000,
                  810000,
                  820000,
                  830000,
                  840000,
                  850000,
                  860000,
                  870000,
                  880000,
                  890000,
                  900000,
                  910000,
                  990001,
                  800100,
                  800200,
                  800300,
                  800400,
                  800500,
                  800600);
update fi_org_compare set level='2'
where org_code in(990001);

-- 2025-05-12 增加摘要字段
ALTER TABLE fi_convert_reimbursement_detail ADD scomment varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '摘要';
ALTER TABLE fi_source_reimbursement_detail ADD scomment varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '摘要';

-- 2025-05-12 生产总部集团配置有点特殊，使用990000这个编码
update fi_org_compare set org_code ='990000'
where org_code ='990001';

-- 2025-05-14 采购入库详情增加“用途”字段
ALTER TABLE fi_source_store_detail ADD vbdef7 varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '用途';
ALTER TABLE fi_convert_store_detail ADD vbdef7 varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '用途';

-- 2025-05-14 应付同步发票校验申请，新增同步字段
ALTER TABLE fi_source_pu_payable_detail ADD COLUMN def24 VARCHAR(255) DEFAULT NULL COMMENT '当期核销预付款含税金额';
ALTER TABLE fi_source_pu_payable_detail ADD COLUMN def38 VARCHAR(255) DEFAULT NULL COMMENT '质保金金额';

ALTER TABLE fi_convert_pu_payable_detail ADD COLUMN def24 VARCHAR(255) DEFAULT NULL COMMENT '当期核销预付款含税金额';
ALTER TABLE fi_convert_pu_payable_detail ADD COLUMN def38 VARCHAR(255) DEFAULT NULL COMMENT '质保金金额';

-- 2025-05-16 增加排序
ALTER TABLE fi_org_compare ADD sort int(11) DEFAULT 1 NULL COMMENT '排序值';
update fi_org_compare set sort='1' where pk_group ='99';
update fi_org_compare set sort='2' where pk_group ='81';
update fi_org_compare set sort='3' where pk_group ='82';
update fi_org_compare set sort='4' where pk_group ='83';
update fi_org_compare set sort='5' where pk_group ='84';
update fi_org_compare set sort='6' where pk_group ='85';
update fi_org_compare set sort='7' where pk_group ='86';
update fi_org_compare set sort='8' where pk_group ='87';
update fi_org_compare set sort='9' where pk_group ='88';
update fi_org_compare set sort='10' where pk_group ='89';
update fi_org_compare set sort='11' where pk_group ='90';
update fi_org_compare set sort='12' where pk_group ='91';
update fi_org_compare set sort='13' where pk_group ='8001';
update fi_org_compare set sort='14' where pk_group ='8002';
update fi_org_compare set sort='15' where pk_group ='8003';
update fi_org_compare set sort='16' where pk_group ='8006';
update fi_org_compare set sort='17' where pk_group ='8004';
update fi_org_compare set sort='18' where pk_group ='78';
update fi_org_compare set sort='19' where pk_group ='79';
update fi_org_compare set sort='20' where pk_group ='75';
update fi_org_compare set sort='21' where pk_group ='8005';
update fi_org_compare set sort='22' where pk_group ='74';
update fi_org_compare set sort='23' where pk_group ='73';

-- 2025-05-20 feat(20): 采购付款，支付类型赋值规则变更
ALTER TABLE fi_source_pu_paybill_detail ADD COLUMN def38 VARCHAR(255) DEFAULT NULL COMMENT '质保金金额';
ALTER TABLE fi_convert_pu_paybill_detail ADD COLUMN def38 VARCHAR(255) DEFAULT NULL COMMENT '质保金金额';

-- 2025-05-21 凭证记账新增 借贷方向，分录序号
ALTER TABLE fi_convert_accounting_detail ADD crde varchar(100) NULL COMMENT '借贷方向';
ALTER TABLE fi_source_accounting_detail ADD crde varchar(100) NULL COMMENT '借贷方向';
ALTER TABLE fi_source_accounting_detail ADD flxh varchar(100) NULL COMMENT '分录序号';
ALTER TABLE fi_convert_accounting_detail ADD flxh varchar(100) NULL COMMENT '分录序号';

-- 2025-05-23 feat: 同步采购应付，需要判断源头订单是否已拉取到集成中台
ALTER TABLE fi_source_pu_payable ADD COLUMN purchaseorder VARCHAR(255) NULL COMMENT '订单编号';
ALTER TABLE fi_convert_pu_payable ADD COLUMN purchaseorder VARCHAR(255) NULL COMMENT '订单编号';

-- 2025-05-27 杜凯祥 应收单增加平台业务事项
ALTER TABLE fi_source_receivable ADD def47 varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '平台业务事项';
ALTER TABLE fi_convert_receivable ADD def47 varchar(40) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '平台业务事项';

-- 2025-05-28 ylx 采购应付传参新增供应商银行账户相关信息
ALTER TABLE fi_source_pu_payable_detail ADD COLUMN recaccount VARCHAR(255) NULL COMMENT '收款银行账户';
ALTER TABLE fi_convert_pu_payable_detail ADD COLUMN recaccount VARCHAR(255) NULL COMMENT '收款银行账户';

-- 2025-05-28 应收单增加摘要字段
ALTER TABLE fi_convert_receivable_detail ADD scomment varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '摘要';
ALTER TABLE fi_source_receivable_detail ADD scomment varchar(400) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '摘要';

-- 2025-06-03 修改新的订单逻辑（付款合同和费用结算单的数据都走这里）
CREATE TABLE `fi_convert_base_order_detail` (
`id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
`pur_ordNO` varchar(40) DEFAULT NULL COMMENT '采购订单号',
`pur_ord_item` varchar(40) DEFAULT NULL COMMENT '订单行号',
`prj_code` varchar(40) DEFAULT NULL COMMENT '项目编码',
`it_tot_am_ex_tax` varchar(20) DEFAULT NULL COMMENT '订单行不含税金额',
`taxrt` varchar(20) DEFAULT NULL COMMENT '税率',
`tax_amount` varchar(20) DEFAULT NULL COMMENT '税额',
`it_tot_am_inc_tax` varchar(20) DEFAULT NULL COMMENT '订单行含税金额',
`cele_id_in_pur_vou` varchar(32) DEFAULT NULL COMMENT '删除标识',
`cost_center_code` varchar(32) DEFAULT NULL COMMENT '成本中心',
`unit_price` varchar(32) DEFAULT NULL COMMENT '单价',
`amt` varchar(32) DEFAULT NULL COMMENT '数量',
`prv_mk_code` varchar(32) DEFAULT NULL COMMENT 'MDM基础组织编码',
`transmission_date` varchar(32) DEFAULT NULL COMMENT '传输日期',
`mt_code` varchar(32) DEFAULT NULL COMMENT '物料编号',
`recep_id` varchar(32) DEFAULT NULL COMMENT '收货标识',
`create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
`update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
`del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单源数据转换详情表';
-- upmsdev.fi_convert_order definition

CREATE TABLE `fi_convert_base_order` (
`id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
`prv_mk_code` varchar(40) DEFAULT NULL COMMENT 'MDM基础组织编码',
`pur_ordNO` varchar(40) DEFAULT NULL COMMENT '采购订单号',
`afl_unit` varchar(40) DEFAULT NULL COMMENT '订单编号',
`cre_time` varchar(20) DEFAULT NULL COMMENT '采购组织',
`pur_ord_typ` varchar(19) DEFAULT NULL COMMENT '凭证类型',
`contract_no` varchar(20) DEFAULT NULL COMMENT '合同编号',
`firm_code` varchar(20) DEFAULT NULL COMMENT '公司代码',
`cre_pers_nm` varchar(20) DEFAULT NULL COMMENT '创建对象的人员名称',
`sup_cod` varchar(40) DEFAULT NULL COMMENT '供应商编码',
`tot_am_ex_tax` varchar(32) DEFAULT NULL COMMENT '订单金额(不含税总额)',
`py_rt` varchar(32) DEFAULT NULL COMMENT '支付比例',
`curr_cod` varchar(32) DEFAULT NULL COMMENT '货币',
`exch_rat` varchar(32) DEFAULT NULL COMMENT '汇率',
`pur_ord_tax_amount` varchar(32) DEFAULT NULL COMMENT '订单税额',
`pur_ord_typ_desc` varchar(32) DEFAULT NULL COMMENT '采购订单类型描述',
`transmission_date` varchar(32) DEFAULT NULL COMMENT '传输日期',
`drawer_code` varchar(32) DEFAULT NULL COMMENT '出票方',
`pro_cen` varchar(32) DEFAULT NULL COMMENT '利润中心',
`pur_req` varchar(32) DEFAULT NULL COMMENT '请购单号',
`prj_type` varchar(32) DEFAULT NULL COMMENT '项目类别',
`pur_org` varchar(32) DEFAULT NULL COMMENT '采购组织',
`pur_gro` varchar(32) DEFAULT NULL COMMENT '采购组',
`mall_procure_order` varchar(32) DEFAULT NULL COMMENT '商城订单号',
`data_sou_sys` varchar(32) DEFAULT NULL COMMENT '数据来源系统',
`create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
`update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
`del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单源数据转换主表';

-- upmsdev.fi_source_order definition

CREATE TABLE `fi_source_base_order` (
`id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
`prv_mk_code` varchar(40) DEFAULT NULL COMMENT 'MDM基础组织编码',
`pur_ordNO` varchar(40) DEFAULT NULL COMMENT '采购订单号',
`afl_unit` varchar(40) DEFAULT NULL COMMENT '订单编号',
`cre_time` varchar(20) DEFAULT NULL COMMENT '采购组织',
`pur_ord_typ` varchar(19) DEFAULT NULL COMMENT '凭证类型',
`contract_no` varchar(20) DEFAULT NULL COMMENT '合同编号',
`firm_code` varchar(20) DEFAULT NULL COMMENT '公司代码',
`cre_pers_nm` varchar(20) DEFAULT NULL COMMENT '创建对象的人员名称',
`sup_cod` varchar(40) DEFAULT NULL COMMENT '供应商编码',
`tot_am_ex_tax` varchar(32) DEFAULT NULL COMMENT '订单金额(不含税总额)',
`py_rt` varchar(32) DEFAULT NULL COMMENT '支付比例',
`curr_cod` varchar(32) DEFAULT NULL COMMENT '货币',
`exch_rat` varchar(32) DEFAULT NULL COMMENT '汇率',
`pur_ord_tax_amount` varchar(32) DEFAULT NULL COMMENT '订单税额',
`pur_ord_typ_desc` varchar(32) DEFAULT NULL COMMENT '采购订单类型描述',
`transmission_date` varchar(32) DEFAULT NULL COMMENT '传输日期',
`drawer_code` varchar(32) DEFAULT NULL COMMENT '出票方',
`pro_cen` varchar(32) DEFAULT NULL COMMENT '利润中心',
`pur_req` varchar(32) DEFAULT NULL COMMENT '请购单号',
`prj_type` varchar(32) DEFAULT NULL COMMENT '项目类别',
`pur_org` varchar(32) DEFAULT NULL COMMENT '采购组织',
`pur_gro` varchar(32) DEFAULT NULL COMMENT '采购组',
`mall_procure_order` varchar(32) DEFAULT NULL COMMENT '商城订单号',
`data_sou_sys` varchar(32) DEFAULT NULL COMMENT '数据来源系统',
`pk_org` varchar(40) DEFAULT NULL COMMENT '组织',
`pk_org_name` varchar(100) DEFAULT NULL COMMENT '组织名称',
`push_time` varchar(256) DEFAULT NULL COMMENT '推送时间',
`push_status` tinyint(4) DEFAULT NULL COMMENT '1 推送中 2推送成功 3推送失败',
`push_msg` varchar(1024) DEFAULT NULL COMMENT '推送成功失败信息',
`try_number` bigint(32) DEFAULT '0' COMMENT '重试次数',
`readiness` bigint(32) DEFAULT '0' COMMENT '0 未就绪 1 就绪',
`create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
`update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
`del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单源数据主表';

-- upmsdev.fi_source_order_detail definition

CREATE TABLE `fi_source_base_order_detail` (
`id` bigint(32) NOT NULL AUTO_INCREMENT COMMENT '主键',
`pur_ordNO` varchar(40) DEFAULT NULL COMMENT '采购订单号',
`pur_ord_item` varchar(40) DEFAULT NULL COMMENT '订单行号',
`prj_code` varchar(40) DEFAULT NULL COMMENT '项目编码',
`it_tot_am_ex_tax` varchar(20) DEFAULT NULL COMMENT '订单行不含税金额',
`taxrt` varchar(20) DEFAULT NULL COMMENT '税率',
`tax_amount` varchar(20) DEFAULT NULL COMMENT '税额',
`it_tot_am_inc_tax` varchar(20) DEFAULT NULL COMMENT '订单行含税金额',
`cele_id_in_pur_vou` varchar(32) DEFAULT NULL COMMENT '删除标识',
`cost_center_code` varchar(32) DEFAULT NULL COMMENT '成本中心',
`unit_price` varchar(32) DEFAULT NULL COMMENT '单价',
`amt` varchar(32) DEFAULT NULL COMMENT '数量',
`prv_mk_code` varchar(32) DEFAULT NULL COMMENT 'MDM基础组织编码',
`transmission_date` varchar(32) DEFAULT NULL COMMENT '传输日期',
`mt_code` varchar(32) DEFAULT NULL COMMENT '物料编号',
`recep_id` varchar(32) DEFAULT NULL COMMENT '收货标识',
`create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
`update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
`update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
`del_flag` bigint(32) DEFAULT '0' COMMENT '是否删除 0: 正常 ',
PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单源数据转换详情表';




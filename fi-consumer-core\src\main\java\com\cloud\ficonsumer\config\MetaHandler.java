package com.cloud.ficonsumer.config;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.cloud.cloud.common.security.service.CloudUser;
import com.cloud.cloud.common.security.util.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 处理新增和更新的基础数据填充，配合BaseEntity和MyBatisPlusConfig使用
 */
@Component
@Slf4j
@Primary
public class MetaHandler implements MetaObjectHandler {


    /**
     * 新增数据执行
     *
     * @param metaObject
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        if (SecurityUtils.getAuthentication() != null) {
            String userName = SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof CloudUser
                    ? SecurityUtils.getUser().getUsername() : null;
            String realName = SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof CloudUser
                    ? SecurityUtils.getUser().getRealname() : null;
//                ((UserIdPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal()).getName();

            Date now = new Date();
            setFieldValIfNull("createTime", now, metaObject);
            setFieldValIfNull("createBy", userName, metaObject);
            setFieldValIfNull("createByName", realName, metaObject);
            setFieldValIfNull("updateByName", realName, metaObject);
            setFieldValIfNull("updateTime", now, metaObject);
            setFieldValIfNull("updateBy", userName, metaObject);
        }else {
            Date now = new Date();
            setFieldValIfNull("createTime", now, metaObject);
            setFieldValIfNull("updateTime", now, metaObject);
        }
    }

    /**
     * 更新数据执行
     *
     * @param metaObject
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        if (SecurityUtils.getAuthentication() != null) {
            String userName = SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof CloudUser
                    ? SecurityUtils.getUser().getUsername() : null;
            String realName = SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof CloudUser
                    ? SecurityUtils.getUser().getRealname() : null;

            setFieldValIfNull("updateByName",realName,metaObject);
            setFieldValIfNull("updateTime", new Date(), metaObject);
            setFieldValIfNull("updateBy", userName, metaObject);
        }else {
            setFieldValIfNull("updateTime", new Date(), metaObject);
        }
    }

    /**
     * 安全设置字段值，只在字段为空时设置
     *
     * @param fieldName 字段名
     * @param fieldVal 字段值
     * @param metaObject 元数据对象
     */
    private void setFieldValIfNull(String fieldName, Object fieldVal, MetaObject metaObject) {
        if (ObjectUtil.isNull(this.getFieldValByName(fieldName, metaObject))) {
            this.setFieldValByName(fieldName, fieldVal, metaObject);
        }
    }
}

package com.cloud.ficonsumer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Id;

/**
 * 采购发票实体类
 */
@Data
@ApiModel(value = "采购发票", description = "采购发票信息")
public class PoInvoice {

    @Id
    @ApiModelProperty(value = "采购发票主键", required = true)
    private String pkInvoice;

    @ApiModelProperty(value = "发票号")
    private String vbillcode;

    @ApiModelProperty(value = "发票日期")
    private String dbilldate;

    @ApiModelProperty(value = "发票分类: 0-增值税发票, 10-普通发票, 20-其它发票")
    private Integer finvoiceclass;
}

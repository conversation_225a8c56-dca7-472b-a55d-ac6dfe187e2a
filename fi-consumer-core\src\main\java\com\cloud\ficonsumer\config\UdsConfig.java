package com.cloud.ficonsumer.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/12.
 */
@Component
@ConfigurationProperties(prefix = "uds")
public class UdsConfig {

    public static  String apiServiceUrl;
    public static  String accessKey;
    public static  String secretKey;
    public static boolean enable;

    public boolean getEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public String getApiServiceUrl() {
        return apiServiceUrl;
    }

    public void setApiServiceUrl(String apiServiceUrl) {
        this.apiServiceUrl = apiServiceUrl;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }
}

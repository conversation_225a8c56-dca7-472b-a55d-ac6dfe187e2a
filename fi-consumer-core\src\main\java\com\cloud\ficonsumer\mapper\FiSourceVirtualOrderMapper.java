

package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiConvertVirtualOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceVirtualOrder;
import com.cloud.ficonsumer.vo.FiSourceVirtualOrderQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 虚拟采购订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:26
 */
@Mapper
public interface FiSourceVirtualOrderMapper extends CloudBaseMapper<FiSourceVirtualOrder> {

    @Select("<script>SELECT * " +
            " FROM fi_source_virtual_order t " +
            " WHERE t.del_flag = 0" +
            "<if test=\"param.pkContr !=null and param.pkContr !=''\"> " +
            " and pk_contr = #{param.pkContr} " +
            "</if> " +
            "<if test=\"param.billCode !=null and param.billCode !=''\"> " +
            " and bill_code like CONCAT('%',#{param.billCode},'%') " +
            "</if> " +
            "<if test=\"param.pkOrgList != null and param.pkOrgList.size() > 0\"> " +
            "   AND pk_org in " +
            "   <foreach collection=\"param.pkOrgList\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "      #{item} " +
            "   </foreach>" +
            " </if> " +
            "<if test=\"param.groupCodeFlag !=null and param.groupCodeFlag !=''\"> " +
            " and pk_org in (select pk_org " +
            "       from fi_org_compare " +
            "       where group_code = #{param.groupCodeFlag} and del_flag =0) " +
            "</if> " +
            "<if test=\"param.pushStatus !=null and param.pushStatus !=''\"> " +
            " and push_status = #{param.pushStatus} " +
            "</if> " +
            "<if test=\"param.pushStatusList != null and param.pushStatusList.size() > 0\">"+
            " and push_status in " +
            "<foreach collection=\"param.pushStatusList\" item=\"item\" open=\"(\" separator=\",\" close=\")\">"+
            "#{item}" +
            "</foreach>" +
            "</if> " +
            "<if test=\"param.createTimeStart !=null and param.createTimeStart !=''\"> " +
            " and create_time >= #{param.createTimeStart} " +
            "</if> " +
            "<if test=\"param.createTimeEnd !=null and param.createTimeEnd !=''\"> " +
            " and #{param.createTimeEnd} >= create_time " +
            "</if> " +
            " order by create_time desc  " +
            "</script>")
    Page<FiSourceVirtualOrderDTO> beforeConvertDataPage(Page page, @Param("param") FiSourceVirtualOrderQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_source_virtual_order t " +
            "    LEFT JOIN fi_source_virtual_order_detail td ON t.pk_contr = td.pk_contr " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.pk_contr = #{param.pkContr}" +
            "</script>")
    Page<FiSourceVirtualOrderDetailDTO> getBeforeConvertDataPage(Page page, @Param("param") FiSourceVirtualOrderQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_convert_virtual_order t " +
            "    LEFT JOIN fi_convert_virtual_order_detail td ON t.pk_contr = td.pk_contr " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.pk_contr = #{pk}" +
            "</script>")
    Page<FiConvertVirtualOrderDetailDTO> getConvertData(Page page, @Param("pk") String pk);
}

package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.mapper.FiConvertPuPayableMapper;
import com.cloud.ficonsumer.service.FiConvertPuPayableService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiConvertPuPayableServiceImpl extends ServiceImpl<FiConvertPuPayableMapper, FiConvertPuPayable> implements FiConvertPuPayableService {

    @Override
    public boolean pushAgain(String pkPayablebill) {
        return false;
    }
}

INSERT INTO SO_SALEORDER
(<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>LAG, BILLMAKER, B<PERSON><PERSON><PERSON><PERSON><PERSON>LAG, BOFFSETFLAG, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>TYPEID, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>EID, <PERSON><PERSON><PERSON>NELTYPEID, CC<PERSON><PERSON><PERSON>NKA<PERSON><PERSON>, CCUSTBAN<PERSON><PERSON>, CCUSTOMERID, CDEPTID, CDEPTVID, CEMPLOYEEID, CFREECUSTID, CHRECEIVEADDID, CHRECEIVECUSTID, <PERSON><PERSON><PERSON><PERSON><PERSON>USTI<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>Y<PERSON>, <PERSON><PERSON>Y<PERSON><PERSON>MI<PERSON>, CREA<PERSON><PERSON><PERSON><PERSON>, CREATO<PERSON>, CREVISERID, CS<PERSON><PERSON>ORDERID, CT<PERSON><PERSON>WORDID, CT<PERSON>NSPORTTYPEID, CTRANTYP<PERSON>D, <PERSON><PERSON><PERSON>ATE, DMAKEDATE, DR, <PERSON>PFS<PERSON>TUSFLAG, <PERSON><PERSON><PERSON><PERSON><PERSON>AG, <PERSON>RINTCOUNT, IVERSION, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, NDISCOUNTRAT<PERSON>, NL<PERSON><PERSON><PERSON><PERSON><PERSON>ORIGMN<PERSON>, NPR<PERSON><PERSON><PERSON>MNY, NPRECEIVEQUOTA, NPRECEIVERATE, NRECEIVEDMNY, NTOTALNUM, NTOTALORIGMNY, NTOTALORIGSUBMNY, NTOTALPIECE, NTOTALVOLUME, NTOTALWEIGHT, PK_GROUP, PK_ORG, PK_ORG_V, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, TAUDITTIME, TREVISETIME, TS, VBILLCODE, VBILLSRCTYPE, VCOOPPOHCODE, VCREDITNUM, VDEF1, VDEF10, VDEF11, VDEF12, VDEF13, VDEF14, VDEF15, VDEF16, VDEF17, VDEF18, VDEF19, VDEF2, VDEF20, VDEF3, VDEF4, VDEF5, VDEF6, VDEF7, VDEF8, VDEF9, VNOTE, VREVISEREASON, VTRANTYPECODE)
VALUES('1001A110000000011DB2', 'N', 'N', 'N', 'N', 'N', '1001A110000000011DB2', 'N', 'N', 'N', 'N', 'N', 'N', '~', '~', '~', '1001A1100000009QYLHL', '~', '~', '~', '1001A710000000K60KUI', '1001A11000000001RFWN', '0001A1100000000096KM', '~', '~', '~', '1001A710000000K60KUI', '1001A710000000K60KUI', '1002Z0100000000001K1', '~', '2025-02-18 14:18:14', '1001A110000000011DB2', '~', '1001A710000000LBO69G', '~', '~', '1001A11000000001L6SP', '2025-02-18 14:11:12', '2025-02-18 14:18:13', 0, 1, 2, NULL, NULL, NULL, '~', 100, 0, NULL, NULL, NULL, NULL, 1, 100, 0, 0, 0, 0, '0001A110000000000CE4', '0001A110000000006YU4', '0001A110000000006YU3', NULL, NULL, NULL, NULL, '2025-02-18 14:18:28', NULL, '2025-02-18 14:18:46', 'SO302025021800012011', '~', NULL, '~', 'N', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', '~', '~', '~', '~', '~', '~', '~', NULL, NULL, '30-Cxx-003');
INSERT INTO SO_SALEORDER
(APPROVER, BADVFEEFLAG, BARSETTLEFLAG, BCOOPTOPOFLAG, BCOSTSETTLEFLAG, BFREECUSTFLAG, BILLMAKER, BINVOICENDFLAG, BOFFSETFLAG, BOUTENDFLAG, BPOCOOPTOMEFLAG, BPRECEIVEFLAG, BSENDENDFLAG, CARSUBTYPEID, CBALANCETYPEID, CBILLSRCID, CBIZTYPEID, CCHANNELTYPEID, CCUSTBANKACCID, CCUSTBANKID, CCUSTOMERID, CDEPTID, CDEPTVID, CEMPLOYEEID, CFREECUSTID, CHRECEIVEADDID, CHRECEIVECUSTID, CINVOICECUSTID, CORIGCURRENCYID, CPAYTERMID, CREATIONTIME, CREATOR, CREVISERID, CSALEORDERID, CTRADEWORDID, CTRANSPORTTYPEID, CTRANTYPEID, DBILLDATE, DMAKEDATE, DR, FPFSTATUSFLAG, FSTATUSFLAG, IPRINTCOUNT, IVERSION, MODIFIEDTIME, MODIFIER, NDISCOUNTRATE, NLRGTOTALORIGMNY, NPRECEIVEMNY, NPRECEIVEQUOTA, NPRECEIVERATE, NRECEIVEDMNY, NTOTALNUM, NTOTALORIGMNY, NTOTALORIGSUBMNY, NTOTALPIECE, NTOTALVOLUME, NTOTALWEIGHT, PK_GROUP, PK_ORG, PK_ORG_V, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, TAUDITTIME, TREVISETIME, TS, VBILLCODE, VBILLSRCTYPE, VCOOPPOHCODE, VCREDITNUM, VDEF1, VDEF10, VDEF11, VDEF12, VDEF13, VDEF14, VDEF15, VDEF16, VDEF17, VDEF18, VDEF19, VDEF2, VDEF20, VDEF3, VDEF4, VDEF5, VDEF6, VDEF7, VDEF8, VDEF9, VNOTE, VREVISEREASON, VTRANTYPECODE)
VALUES('1001A110000000011DB2', 'N', 'N', 'N', 'N', 'N', '1001A110000000011DB2', 'N', 'N', 'N', 'N', 'N', 'N', '~', '~', '~', '1001O610000000H2UL9N', '~', '~', '~', '1001A710000000K60KUI', '1001A11000000001RGQ5', '0001A7100000000IG81K', '~', '~', '~', '1001A710000000K60KUI', '1001A710000000K60KUI', '1002Z0100000000001K1', '~', '2025-02-17 09:52:30', '1001A110000000011DB2', '~', '1001A710000000LBICCW', '~', '~', '1001A1100000009EFWOA', '2025-02-17 09:32:47', '2025-02-17 09:52:30', 0, 1, 2, NULL, NULL, NULL, '~', 100, 0, NULL, NULL, NULL, NULL, 1, 100, 0, 0, 0, 0, '0001A110000000000CE4', '0001A110000000006YU4', '0001A110000000006YU3', NULL, NULL, NULL, NULL, '2025-02-17 09:52:30', NULL, '2025-02-17 09:52:35', 'SO302025021700012010', '~', NULL, '~', 'N', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', '~', '~', '~', '~', '~', '~', '~', NULL, NULL, '30-Cxx-006');
INSERT INTO SO_SALEORDER
(APPROVER, BADVFEEFLAG, BARSETTLEFLAG, BCOOPTOPOFLAG, BCOSTSETTLEFLAG, BFREECUSTFLAG, BILLMAKER, BINVOICENDFLAG, BOFFSETFLAG, BOUTENDFLAG, BPOCOOPTOMEFLAG, BPRECEIVEFLAG, BSENDENDFLAG, CARSUBTYPEID, CBALANCETYPEID, CBILLSRCID, CBIZTYPEID, CCHANNELTYPEID, CCUSTBANKACCID, CCUSTBANKID, CCUSTOMERID, CDEPTID, CDEPTVID, CEMPLOYEEID, CFREECUSTID, CHRECEIVEADDID, CHRECEIVECUSTID, CINVOICECUSTID, CORIGCURRENCYID, CPAYTERMID, CREATIONTIME, CREATOR, CREVISERID, CSALEORDERID, CTRADEWORDID, CTRANSPORTTYPEID, CTRANTYPEID, DBILLDATE, DMAKEDATE, DR, FPFSTATUSFLAG, FSTATUSFLAG, IPRINTCOUNT, IVERSION, MODIFIEDTIME, MODIFIER, NDISCOUNTRATE, NLRGTOTALORIGMNY, NPRECEIVEMNY, NPRECEIVEQUOTA, NPRECEIVERATE, NRECEIVEDMNY, NTOTALNUM, NTOTALORIGMNY, NTOTALORIGSUBMNY, NTOTALPIECE, NTOTALVOLUME, NTOTALWEIGHT, PK_GROUP, PK_ORG, PK_ORG_V, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, TAUDITTIME, TREVISETIME, TS, VBILLCODE, VBILLSRCTYPE, VCOOPPOHCODE, VCREDITNUM, VDEF1, VDEF10, VDEF11, VDEF12, VDEF13, VDEF14, VDEF15, VDEF16, VDEF17, VDEF18, VDEF19, VDEF2, VDEF20, VDEF3, VDEF4, VDEF5, VDEF6, VDEF7, VDEF8, VDEF9, VNOTE, VREVISEREASON, VTRANTYPECODE)
VALUES('1001A1100000000EWNHD', 'N', 'N', 'N', 'N', 'N', '1001A1100000000EWNHD', 'N', 'N', 'N', 'N', 'N', 'N', '~', '~', '~', '1001O610000000H2UL9N', '~', '~', '~', '1001A710000000K60KUI', '1001A11000000001D5LP', '0001A1100000000086CL', '0001A11000000000B7CV', '~', '~', '1001A710000000K60KUI', '1001A710000000K60KUI', '1002Z0100000000001K1', '~', '2025-01-22 15:25:12', '1001A1100000000EWNHD', '~', '1001A710000000LAQ7Z8', '~', '~', '1001A1100000009EFWOA', '2025-01-22 15:16:12', '2025-01-22 15:25:10', 0, 1, 2, NULL, NULL, NULL, '~', 100, 0, NULL, NULL, NULL, NULL, 10, 1000, 0, 0, 0, 0, '0001A110000000000CE4', '0001A110000000006YPN', '0001A110000000006YPM', NULL, NULL, NULL, NULL, '2025-01-22 15:25:42', NULL, '2025-01-22 15:26:07', 'SO302025012200012009', '~', NULL, '~', 'N', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', '~', '~', '~', '~', '~', '~', '~', NULL, NULL, '30-Cxx-006');
INSERT INTO SO_SALEORDER_B
(BARRANGEDFLAG, BBARSETTLEFLAG, BBCOSTSETTLEFLAG, BBINDFLAG, BBINVOICENDFLAG, BBOUTENDFLAG, BBSENDENDFLAG, BDISCOUNTFLAG, BJCZXSFLAG, BLABORFLAG, BLARGESSFLAG, BLRGCASHFLAG, BTRIATRADEFLAG, CARORGID, CARORGVID, CARRANGEPERSONID, CASTUNITID, CBINDSRCID, CBUYLARGESSACTID, CBUYLARGESSID, CBUYPROMOTTYPEID, CCTMANAGEBID, CCTMANAGEID, CCURRENCYID, CCUSTMATERIALID, CEXCHANGESRCRETID, CFACTORYID, CFIRSTBID, CFIRSTID, CLARGESSSRCID, CMATERIALID, CMATERIALVID, CMFFILEID, CORIGAREAID, CORIGCOUNTRYID, CPRCPROMOTTYPEID, CPRICEFORMID, CPRICEITEMID, CPRICEITEMTABLEID, CPRICEPOLICYID, CPRICEPROMTACTID, CPRODLINEID, CPRODUCTORID, CPROFITCENTERID, CPROFITCENTERVID, CPROJECTID, CPROMOTPRICEID, CQTUNITID, CQUALITYLEVELID, CRECECOUNTRYID, CRECEIVEADDDOCID, CRECEIVEADDRID, CRECEIVEAREAID, CRECEIVECUSTID, CRETPOLICYID, CRETREASONID, CROWNO, CSALEORDERBID, CSALEORDERID, CSENDCOUNTRYID, CSENDSTOCKORGID, CSENDSTOCKORGVID, CSENDSTORDOCID, CSETTLEORGID, CSETTLEORGVID, CSPROFITCENTERID, CSPROFITCENTERVID, CSRCBID, CSRCID, CTAXCODEID, CTAXCOUNTRYID, CTRAFFICORGID, CTRAFFICORGVID, CUNITID, CVENDORID, DBILLDATE, DR, DRECEIVEDATE, DSENDDATE, FBUYSELLFLAG, FLARGESSTYPEFLAG, FRETEXCHANGE, FROWSTATUS, FTAXTYPEFLAG, NACCPRICE, NASKQTORIGNETPRICE, NASKQTORIGPRICE, NASKQTORIGTAXPRC, NASKQTORIGTXNTPRC, NASTNUM, NCALTAXMNY, NDISCOUNT, NDISCOUNTRATE, NEXCHANGERATE, NGLOBALEXCHGRATE, NGLOBALMNY, NGLOBALTAXMNY, NGROUPEXCHGRATE, NGROUPMNY, NGROUPTAXMNY, NITEMDISCOUNTRATE, NLARGESSMNY, NLARGESSTAXMNY, NMFFILEPRICE, NMNY, NNETPRICE, NNUM, NORIGDISCOUNT, NORIGMNY, NORIGNETPRICE, NORIGPRICE, NORIGTAXMNY, NORIGTAXNETPRICE, NORIGTAXPRICE, NPIECE, NPRICE, NQTNETPRICE, NQTORIGNETPRICE, NQTORIGPRICE, NQTORIGTAXNETPRC, NQTORIGTAXPRICE, NQTPRICE, NQTTAXNETPRICE, NQTTAXPRICE, NQTUNITNUM, NTAX, NTAXMNY, NTAXNETPRICE, NTAXPRICE, NTAXRATE, NVOLUME, NWEIGHT, PK_BATCHCODE, PK_GROUP, PK_ORG, TLASTARRANGETIME, TS, VBATCHCODE, VBDEF1, VBDEF10, VBDEF11, VBDEF12, VBDEF13, VBDEF14, VBDEF15, VBDEF16, VBDEF17, VBDEF18, VBDEF19, VBDEF2, VBDEF20, VBDEF3, VBDEF4, VBDEF5, VBDEF6, VBDEF7, VBDEF8, VBDEF9, VBREVISEREASON, VCHANGERATE, VCLOSEREASON, VCTCODE, VCUSTOMBILLCODE, VFIRSTCODE, VFIRSTROWNO, VFIRSTTRANTYPE, VFIRSTTYPE, VFREE1, VFREE10, VFREE2, VFREE3, VFREE4, VFREE5, VFREE6, VFREE7, VFREE8, VFREE9, VQTUNITRATE, VRETURNMODE, VROWNOTE, VSRCCODE, VSRCROWNO, VSRCTRANTYPE, VSRCTYPE)
VALUES('Y', 'N', 'N', 'N', 'N', 'Y', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', '0001A110000000006YPN', '0001A110000000006YPM', '1001A1100000000EWNHD', '0001Z0100000000000XT', '~', '~', '~', '~', '~', '~', '1002Z0100000000001K1', '~', '~', '~', '~', '~', '~', '1001A710000000K7GCFL', '1001A710000000K7GCFL', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '1001O610000000H7Z2N9', '~', '0001Z0100000000000XT', '~', '0001Z010000000079UJJ', '~', '~', '~', '1001A710000000K60KUI', '~', '~', '10', '1001A710000000LAQ7Z7', '1001A710000000LAQ7Z8', '0001Z010000000079UJJ', '0001A110000000006YPN', '0001A110000000006YPM', '0001A110000000006YPN', '0001A110000000006YPN', '0001A110000000006YPM', '~', '~', '~', '~', '1002Z01000000001CNE2', '0001Z010000000079UJJ', '~', '~', '0001Z0100000000000XT', '~', '2025-01-22 15:16:12', 0, '2025-01-22 23:59:59', '2025-01-22 23:59:59', 1, NULL, 0, 2, 1, 0, NULL, NULL, NULL, NULL, 10, 884.96, 0, 100, 1, NULL, NULL, NULL, 1, 884.96, 1000, 100, NULL, NULL, NULL, 884.96, 88.49557522, 10, 0, 884.96, 88.49557522, 88.49557522, 1000, 100, 100, NULL, 88.49557522, 88.49557522, 88.49557522, 88.49557522, 100, 100, 88.49557522, 100, 100, 10, 115.04, 1000, 100, 100, 13, NULL, NULL, '~', '0001A110000000000CE4', '0001A110000000006YPN', '2025-01-22 15:33:52', '2025-01-22 16:28:02', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', NULL, '1/1', NULL, NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '1/1', NULL, NULL, NULL, '~', '~', '~');
INSERT INTO SO_SALEORDER_B
(BARRANGEDFLAG, BBARSETTLEFLAG, BBCOSTSETTLEFLAG, BBINDFLAG, BBINVOICENDFLAG, BBOUTENDFLAG, BBSENDENDFLAG, BDISCOUNTFLAG, BJCZXSFLAG, BLABORFLAG, BLARGESSFLAG, BLRGCASHFLAG, BTRIATRADEFLAG, CARORGID, CARORGVID, CARRANGEPERSONID, CASTUNITID, CBINDSRCID, CBUYLARGESSACTID, CBUYLARGESSID, CBUYPROMOTTYPEID, CCTMANAGEBID, CCTMANAGEID, CCURRENCYID, CCUSTMATERIALID, CEXCHANGESRCRETID, CFACTORYID, CFIRSTBID, CFIRSTID, CLARGESSSRCID, CMATERIALID, CMATERIALVID, CMFFILEID, CORIGAREAID, CORIGCOUNTRYID, CPRCPROMOTTYPEID, CPRICEFORMID, CPRICEITEMID, CPRICEITEMTABLEID, CPRICEPOLICYID, CPRICEPROMTACTID, CPRODLINEID, CPRODUCTORID, CPROFITCENTERID, CPROFITCENTERVID, CPROJECTID, CPROMOTPRICEID, CQTUNITID, CQUALITYLEVELID, CRECECOUNTRYID, CRECEIVEADDDOCID, CRECEIVEADDRID, CRECEIVEAREAID, CRECEIVECUSTID, CRETPOLICYID, CRETREASONID, CROWNO, CSALEORDERBID, CSALEORDERID, CSENDCOUNTRYID, CSENDSTOCKORGID, CSENDSTOCKORGVID, CSENDSTORDOCID, CSETTLEORGID, CSETTLEORGVID, CSPROFITCENTERID, CSPROFITCENTERVID, CSRCBID, CSRCID, CTAXCODEID, CTAXCOUNTRYID, CTRAFFICORGID, CTRAFFICORGVID, CUNITID, CVENDORID, DBILLDATE, DR, DRECEIVEDATE, DSENDDATE, FBUYSELLFLAG, FLARGESSTYPEFLAG, FRETEXCHANGE, FROWSTATUS, FTAXTYPEFLAG, NACCPRICE, NASKQTORIGNETPRICE, NASKQTORIGPRICE, NASKQTORIGTAXPRC, NASKQTORIGTXNTPRC, NASTNUM, NCALTAXMNY, NDISCOUNT, NDISCOUNTRATE, NEXCHANGERATE, NGLOBALEXCHGRATE, NGLOBALMNY, NGLOBALTAXMNY, NGROUPEXCHGRATE, NGROUPMNY, NGROUPTAXMNY, NITEMDISCOUNTRATE, NLARGESSMNY, NLARGESSTAXMNY, NMFFILEPRICE, NMNY, NNETPRICE, NNUM, NORIGDISCOUNT, NORIGMNY, NORIGNETPRICE, NORIGPRICE, NORIGTAXMNY, NORIGTAXNETPRICE, NORIGTAXPRICE, NPIECE, NPRICE, NQTNETPRICE, NQTORIGNETPRICE, NQTORIGPRICE, NQTORIGTAXNETPRC, NQTORIGTAXPRICE, NQTPRICE, NQTTAXNETPRICE, NQTTAXPRICE, NQTUNITNUM, NTAX, NTAXMNY, NTAXNETPRICE, NTAXPRICE, NTAXRATE, NVOLUME, NWEIGHT, PK_BATCHCODE, PK_GROUP, PK_ORG, TLASTARRANGETIME, TS, VBATCHCODE, VBDEF1, VBDEF10, VBDEF11, VBDEF12, VBDEF13, VBDEF14, VBDEF15, VBDEF16, VBDEF17, VBDEF18, VBDEF19, VBDEF2, VBDEF20, VBDEF3, VBDEF4, VBDEF5, VBDEF6, VBDEF7, VBDEF8, VBDEF9, VBREVISEREASON, VCHANGERATE, VCLOSEREASON, VCTCODE, VCUSTOMBILLCODE, VFIRSTCODE, VFIRSTROWNO, VFIRSTTRANTYPE, VFIRSTTYPE, VFREE1, VFREE10, VFREE2, VFREE3, VFREE4, VFREE5, VFREE6, VFREE7, VFREE8, VFREE9, VQTUNITRATE, VRETURNMODE, VROWNOTE, VSRCCODE, VSRCROWNO, VSRCTRANTYPE, VSRCTYPE)
VALUES('Y', 'N', 'N', 'N', 'N', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', '0001A110000000006YU4', '0001A110000000006YU3', '1001A110000000011DB2', '1001A11000000000TDOE', '~', '~', '~', '~', '~', '~', '1002Z0100000000001K1', '~', '~', '~', '~', '~', '~', '1001A1100000001N6OAC', '1001A1100000001N6OAC', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '1001A110000000926TEE', '~', '1001A11000000000TDOE', '~', '0001Z010000000079UJJ', '~', '~', '~', '1001A710000000K60KUI', '~', '~', '10', '1001A710000000LBICCV', '1001A710000000LBICCW', '0001Z010000000079UJJ', '0001A110000000006YU4', '0001A110000000006YU3', '0001A110000000006YU4', '0001A110000000006YU4', '0001A110000000006YU3', '~', '~', '~', '~', '1002Z01000000001CNE2', '0001Z010000000079UJJ', '~', '~', '1001A11000000000TDOE', '~', '2025-02-17 09:32:47', 0, '2025-02-17 23:59:59', '2025-02-17 23:59:59', 1, NULL, 0, 2, 1, 0, NULL, NULL, NULL, NULL, 1, 88.5, 0, 100, 1, NULL, NULL, NULL, 1, 88.5, 100, 100, NULL, NULL, NULL, 88.5, 88.49557522, 1, 0, 88.5, 88.49557522, 88.49557522, 100, 100, 100, NULL, 88.49557522, 88.49557522, 88.49557522, 88.49557522, 100, 100, 88.49557522, 100, 100, 1, 11.5, 100, 100, 100, 13, NULL, NULL, '~', '0001A110000000000CE4', '0001A110000000006YU4', '2025-02-17 09:57:59', '2025-02-17 09:57:59', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', NULL, '1/1', NULL, NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '1/1', NULL, NULL, NULL, '~', '~', '~');
INSERT INTO SO_SALEORDER_B
(BARRANGEDFLAG, BBARSETTLEFLAG, BBCOSTSETTLEFLAG, BBINDFLAG, BBINVOICENDFLAG, BBOUTENDFLAG, BBSENDENDFLAG, BDISCOUNTFLAG, BJCZXSFLAG, BLABORFLAG, BLARGESSFLAG, BLRGCASHFLAG, BTRIATRADEFLAG, CARORGID, CARORGVID, CARRANGEPERSONID, CASTUNITID, CBINDSRCID, CBUYLARGESSACTID, CBUYLARGESSID, CBUYPROMOTTYPEID, CCTMANAGEBID, CCTMANAGEID, CCURRENCYID, CCUSTMATERIALID, CEXCHANGESRCRETID, CFACTORYID, CFIRSTBID, CFIRSTID, CLARGESSSRCID, CMATERIALID, CMATERIALVID, CMFFILEID, CORIGAREAID, CORIGCOUNTRYID, CPRCPROMOTTYPEID, CPRICEFORMID, CPRICEITEMID, CPRICEITEMTABLEID, CPRICEPOLICYID, CPRICEPROMTACTID, CPRODLINEID, CPRODUCTORID, CPROFITCENTERID, CPROFITCENTERVID, CPROJECTID, CPROMOTPRICEID, CQTUNITID, CQUALITYLEVELID, CRECECOUNTRYID, CRECEIVEADDDOCID, CRECEIVEADDRID, CRECEIVEAREAID, CRECEIVECUSTID, CRETPOLICYID, CRETREASONID, CROWNO, CSALEORDERBID, CSALEORDERID, CSENDCOUNTRYID, CSENDSTOCKORGID, CSENDSTOCKORGVID, CSENDSTORDOCID, CSETTLEORGID, CSETTLEORGVID, CSPROFITCENTERID, CSPROFITCENTERVID, CSRCBID, CSRCID, CTAXCODEID, CTAXCOUNTRYID, CTRAFFICORGID, CTRAFFICORGVID, CUNITID, CVENDORID, DBILLDATE, DR, DRECEIVEDATE, DSENDDATE, FBUYSELLFLAG, FLARGESSTYPEFLAG, FRETEXCHANGE, FROWSTATUS, FTAXTYPEFLAG, NACCPRICE, NASKQTORIGNETPRICE, NASKQTORIGPRICE, NASKQTORIGTAXPRC, NASKQTORIGTXNTPRC, NASTNUM, NCALTAXMNY, NDISCOUNT, NDISCOUNTRATE, NEXCHANGERATE, NGLOBALEXCHGRATE, NGLOBALMNY, NGLOBALTAXMNY, NGROUPEXCHGRATE, NGROUPMNY, NGROUPTAXMNY, NITEMDISCOUNTRATE, NLARGESSMNY, NLARGESSTAXMNY, NMFFILEPRICE, NMNY, NNETPRICE, NNUM, NORIGDISCOUNT, NORIGMNY, NORIGNETPRICE, NORIGPRICE, NORIGTAXMNY, NORIGTAXNETPRICE, NORIGTAXPRICE, NPIECE, NPRICE, NQTNETPRICE, NQTORIGNETPRICE, NQTORIGPRICE, NQTORIGTAXNETPRC, NQTORIGTAXPRICE, NQTPRICE, NQTTAXNETPRICE, NQTTAXPRICE, NQTUNITNUM, NTAX, NTAXMNY, NTAXNETPRICE, NTAXPRICE, NTAXRATE, NVOLUME, NWEIGHT, PK_BATCHCODE, PK_GROUP, PK_ORG, TLASTARRANGETIME, TS, VBATCHCODE, VBDEF1, VBDEF10, VBDEF11, VBDEF12, VBDEF13, VBDEF14, VBDEF15, VBDEF16, VBDEF17, VBDEF18, VBDEF19, VBDEF2, VBDEF20, VBDEF3, VBDEF4, VBDEF5, VBDEF6, VBDEF7, VBDEF8, VBDEF9, VBREVISEREASON, VCHANGERATE, VCLOSEREASON, VCTCODE, VCUSTOMBILLCODE, VFIRSTCODE, VFIRSTROWNO, VFIRSTTRANTYPE, VFIRSTTYPE, VFREE1, VFREE10, VFREE2, VFREE3, VFREE4, VFREE5, VFREE6, VFREE7, VFREE8, VFREE9, VQTUNITRATE, VRETURNMODE, VROWNOTE, VSRCCODE, VSRCROWNO, VSRCTRANTYPE, VSRCTYPE)
VALUES('N', 'N', 'N', 'N', 'Y', 'N', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', '0001A110000000006YU4', '0001A110000000006YU3', '~', '1001A11000000000TDOE', '~', '~', '~', '~', '~', '~', '1002Z0100000000001K1', '~', '~', '~', '~', '~', '~', '1001A1100000001USPUO', '1001A1100000001USPUO', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '1001A110000000926SUN', '~', '1001A11000000000TDOE', '~', '0001Z010000000079UJJ', '~', '~', '~', '1001A710000000K60KUI', '~', '~', '10', '1001A710000000LBO69F', '1001A710000000LBO69G', '0001Z010000000079UJJ', '0001A110000000006YU4', '0001A110000000006YU3', '1001A710000000JQNAIM', '0001A110000000006YU4', '0001A110000000006YU3', '~', '~', '~', '~', '1002Z01000000001CNE2', '0001Z010000000079UJJ', '~', '~', '1001A11000000000TDOE', '~', '2025-02-18 14:11:12', 0, '2025-02-18 23:59:59', '2025-02-18 23:59:59', 1, NULL, 0, 2, 1, 0, NULL, NULL, NULL, NULL, 1, 88.5, 0, 100, 1, NULL, NULL, NULL, 1, 88.5, 100, 100, NULL, NULL, NULL, 88.5, 88.49557522, 1, 0, 88.5, 88.49557522, 88.49557522, 100, 100, 100, NULL, 88.49557522, 88.49557522, 88.49557522, 88.49557522, 100, 100, 88.49557522, 100, 100, 1, 11.5, 100, 100, 100, 13, NULL, NULL, '~', '0001A110000000000CE4', '0001A110000000006YU4', NULL, '2025-02-18 14:21:17', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', NULL, '1/1', NULL, NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '1/1', NULL, NULL, NULL, '~', '~', '~');

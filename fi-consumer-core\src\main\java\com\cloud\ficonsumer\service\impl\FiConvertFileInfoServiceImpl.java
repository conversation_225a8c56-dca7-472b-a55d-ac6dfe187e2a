package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertFileInfo;
import com.cloud.ficonsumer.mapper.FiConvertFileInfoMapper;
import com.cloud.ficonsumer.service.FiConvertFileInfoService;
import org.springframework.stereotype.Service;

@Service
public class FiConvertFileInfoServiceImpl extends ServiceImpl<FiConvertFileInfoMapper, FiConvertFileInfo>
        implements FiConvertFileInfoService {
            
}

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27.
 */
@Data
public class FiPushStatusChangeVO {

    /**
     * 主键集合
     */
    @ApiModelProperty(value="主键集合")
    private List<Long> idList;

    /**
     * 返回报错集合
     */
    @ApiModelProperty(value="返回报错集合")
    private List<String> resultList;


    /**
     * 推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中
     */
    @ApiModelProperty(value="推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中")
    private Integer pushStatus;
}

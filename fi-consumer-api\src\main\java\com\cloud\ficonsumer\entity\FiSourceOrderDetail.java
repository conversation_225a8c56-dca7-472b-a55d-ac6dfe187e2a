

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 订单源数据详情表
 *
 * <AUTHOR>
 * @date 2025-01-16 16:53:12
 */
@Data
@TableName("fi_source_order_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "订单源数据详情表")
public class FiSourceOrderDetail extends BaseEntity<FiSourceOrderDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 订单主键
     */
    @ApiModelProperty(value="订单主键")
    private String pkOrder;

    /**
     * 订单主键
     */
    @ApiModelProperty(value="订单主键")
    private String pkOrderB;

    /**
     * 订单编号
     */
    @ApiModelProperty(value="订单编号")
    private String vbillcode;

    /**
     * 行号
     */
    @ApiModelProperty(value="行号")
    private String crowno;

    /**
     * 项目
     */
    @ApiModelProperty(value="项目")
    private String cprojectid;

    /**
     * 无税金额
     */
    @ApiModelProperty(value="无税金额")
    private String norigmny;

    /**
     * 调整方式
     */
    @ApiModelProperty(value="调整方式")
    private String nexchangerate;

    /**
     * 税率
     */
    @ApiModelProperty(value="税率")
    private String ntaxrate;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String ntax;

    /**
     * 价税合计
     */
    @ApiModelProperty(value="价税合计")
    private String norigtaxmny;

    /**
     * 采购部门
     */
    @ApiModelProperty(value="采购部门")
    private String pkDeptV;

    /**
     * 含税单价
     */
    @ApiModelProperty(value="含税单价")
    private String nqtorigtaxprice;

    /**
     * 数量
     */
    @ApiModelProperty(value="数量")
    private String nastnum;

    /**
     * 采购组织
     */
    @ApiModelProperty(value="采购组织")
    private String pkOrgV;

    /**
     * 退货
     */
    @ApiModelProperty(value="退货")
    private String breturn;

    /**
     * 单位
     */
    @ApiModelProperty(value="单位")
    private String castunitid;

    /**
     * 物料版本信息
     */
    @ApiModelProperty(value="物料版本信息")
    private String pkMaterial;

    /**
     * 物料编码
     */
    @ApiModelProperty(value="物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value="物料名称")
    private String name;

    /**
     * 订单类型
     */
    @ApiModelProperty(value="订单类型")
    private String ctrantypeid;

    /**
     * 订单日期
     */
    @ApiModelProperty(value="订单日期")
    private String dbilldate;

    /**
     * 无税单价
     */
    @ApiModelProperty(value="无税单价")
    private String nqtorigprice;

    /**
     * 价税合计
     */
    @ApiModelProperty(value="价税合计")
    private String ntotalorigmny;

    /**
     * 本币价税合计
     */
    @ApiModelProperty(value="本币价税合计")
    private String ntaxmny;

    /**
     * 本币无税金额
     */
    @ApiModelProperty(value="本币无税金额")
    private String nmny;

    /**
     * 单据状态
     */
    @ApiModelProperty(value="单据状态")
    private String forderstatus;

}

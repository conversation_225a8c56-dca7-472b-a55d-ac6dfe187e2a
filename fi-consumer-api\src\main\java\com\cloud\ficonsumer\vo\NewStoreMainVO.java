package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2025/3/12.
 */
@Data
public class NewStoreMainVO {

    /**
     * 单据编号
     */
    @ApiModelProperty(value="单据编号")
    private String documentNo;

    /**
     * 单位编码
     */
    @ApiModelProperty(value="单位编码")
    private String UnitCode;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value="供应商编码")
    private String supplierCode;

    /**
     * 利润中心
     */
    @ApiModelProperty(value="利润中心")
    private String profCen;

    /**
     * 成本中心
     */
    @ApiModelProperty(value="成本中心")
    private String costCenter;

    /**
     * 库存组织
     */
    @ApiModelProperty(value="库存组织")
    private String invtryOrg;

    /**
     * 业务日期
     */
    @ApiModelProperty(value="业务日期")
    private String busDate;

    /**
     * 合同编号
     */
    @ApiModelProperty(value="合同编号")
    private String contractNo;

    /**
     * 出库仓库
     */
    @ApiModelProperty(value="出库仓库")
    private String inWarehouse;

    /**
     * 仓管员
     */
    @ApiModelProperty(value="仓管员")
    private String useCustdiaObject;

    /**
     * 经办人
     */
    @ApiModelProperty(value="经办人")
    private String maintMgrObject;

    /**
     * 单据状态
     */
    @ApiModelProperty(value="单据状态")
    private String docuStatus;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String note;

    private List<StoreDetailVO> item;

}

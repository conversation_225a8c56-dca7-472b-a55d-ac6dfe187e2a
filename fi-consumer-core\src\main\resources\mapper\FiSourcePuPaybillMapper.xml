<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloud.ficonsumer.mapper.FiSourcePuPaybillMapper">

    <select id="beforeConvertDataPage" resultType="com.cloud.ficonsumer.vo.FiSourcePuPaybillVO">
        SELECT
        t.*
        FROM fi_source_pu_paybill t
        WHERE t.del_flag = 0
        <if test="param.billcode != null and param.billcode != ''">
            AND t.billcode LIKE CONCAT('%', #{param.billcode}, '%')
        </if>
        <if test="param.returnStatus != null and param.returnStatus != ''">
            AND t.supplier_code = #{param.returnStatus}
        </if>
        <if test="param.pkOrgList != null and param.pkOrgList.size() > 0">
            AND t.pk_org in
            <foreach collection="param.pkOrgList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.groupCodeFlag != null and param.groupCodeFlag != ''">
            AND t.pk_org in (select pk_org from fi_org_compare where group_code = #{param.groupCodeFlag} and del_flag = 0)
        </if>
        <if test="param.pkTradetype != null and param.pkTradetype != ''">
            AND t.pk_tradetype = #{param.pkTradetype}
        </if>
        <if test="param.pushStatus != null and param.pushStatus != ''">
            AND t.push_status = #{param.pushStatus}
        </if>
        <if test="param.returnStatus != null and param.returnStatus != ''">
            AND t.return_status = #{param.returnStatus}
        </if>
        <if test="param.pushStatusList != null and param.pushStatusList.size() > 0">
            AND t.push_status in
            <foreach collection="param.pushStatusList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="param.createTimeStart != null and param.createTimeStart != ''">
            and t.create_time >= #{param.createTimeStart}
        </if>
        <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
            and #{param.createTimeEnd} >= t.create_time
        </if>
    </select>

    <select id="getBeforeConvertDataPage" resultType="com.cloud.ficonsumer.vo.FiSourcePuPaybillDetailVO">
        SELECT t.*
        FROM fi_source_pu_paybill_detail t
        WHERE t.pk_settlement = #{param.pkSettlement}
          and t.del_flag = 0
    </select>

    <select id="getConvertList" resultType="com.cloud.ficonsumer.vo.FiConvertPuPaybillVO">
        SELECT t.*
        FROM fi_convert_pu_paybill t
        WHERE t.pk_settlement = #{pkSettlement}
          and t.del_flag = 0
    </select>

    <select id="getConvertData" resultType="com.cloud.ficonsumer.vo.FiConvertPuPaybillDetailVO">
        SELECT t.*
        FROM fi_convert_pu_paybill_detail t
        WHERE t.pk_settlement = #{pkSettlement}
          and t.del_flag = 0
    </select>

    <!-- 根据来源单据ID查询未记账的预付款付款单明细 -->
    <select id="getNoAccountedPrepayBySrcBillIds" resultType="com.cloud.ficonsumer.vo.FiSourcePuPaybillDetailVO">
        SELECT
        d.src_billid as srcBillid,
        d.prepay,
        d.pk_settlement as pkSettlement
        FROM fi_source_pu_paybill_detail d
        LEFT JOIN fi_source_pu_paybill h ON d.pk_settlement = h.pk_settlement
        WHERE d.del_flag = 0
        AND h.del_flag = 0
        AND (h.return_status != 4 or h.return_status is null)  <!-- 未记账状态 -->
        <if test="query != null and query.prepay != null">
            AND d.prepay = #{query.prepay}
        </if>
        <if test="query != null and query.srcBillIds != null and query.srcBillIds.size() > 0">
            AND d.src_billid IN
            <foreach collection="query.srcBillIds" item="billId" open="(" separator="," close=")">
                #{billId}
            </foreach>
        </if>
    </select>
</mapper>

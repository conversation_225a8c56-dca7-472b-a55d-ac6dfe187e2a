package com.cloud.ficonsumer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 采购订单明细实体类
 */
@Data
@ApiModel(value = "采购订单明细")
public class PoOrderB {

    @ApiModelProperty(value = "订单号")
    private String vbillcode;

    @ApiModelProperty(value = "行号")
    private String crowno;

    @ApiModelProperty(value = "物料主键")
    private String pkMaterial;

    @ApiModelProperty(value = "订单明细主键")
    private String pkOrderB;

    @ApiModelProperty(value = "订单主键")
    private String pkOrder;

    @ApiModelProperty(value = "订单类型")
    private String ctrantypeid;

    @ApiModelProperty(value = "同步集成中台状态")
    private String vdef39;
}
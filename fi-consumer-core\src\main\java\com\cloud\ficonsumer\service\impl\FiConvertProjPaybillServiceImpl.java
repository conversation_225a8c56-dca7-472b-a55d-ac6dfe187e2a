package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertProjPaybill;
import com.cloud.ficonsumer.mapper.FiConvertProjPaybillMapper;
import com.cloud.ficonsumer.service.FiConvertProjPaybillService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiConvertProjPaybillServiceImpl extends ServiceImpl<FiConvertProjPaybillMapper, FiConvertProjPaybill> implements FiConvertProjPaybillService {

}

package com.cloud.ficonsumer;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.entity.FiSourceProjPaybill;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.service.FiSourceProjPaybillDetailService;
import com.cloud.ficonsumer.service.FiSourceProjPaybillService;
import com.cloud.ficonsumer.vo.FiSourceProjPaybillVO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.Statement;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ProjPaybillIntegTest {

    @SpyBean
    private DmMapper dmMapper;

    @SpyBean
    private ApiProjectService apiProjectService;

    @SpyBean
    private FiSourceProjPaybillService fiSourceProjPaybillService;

    @SpyBean
    private FiSourceProjPaybillDetailService fiSourceProjPaybillDetailService;

    @BeforeEach
    public void initData() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);

        // 初始化 ERP 数据库（采购付款数据）
        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator erpPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("projpaybill/schema-oracle.sql"),
                    new ClassPathResource("projpaybill/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(erpPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 初始化 MySQL 数据库（采购付款 schema）
        DynamicDataSourceContextHolder.push("mysql");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("projpaybill/schema-mysql.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 同步 ERP 采购付款数据到 MySQL
        List<FiSourceProjPaybillVO> fiSourceProjPaybillList = dmMapper.listProjPaybill();
        for (FiSourceProjPaybillVO fiSourceProjPaybill : fiSourceProjPaybillList) {
            apiProjectService.processProjPaybill(fiSourceProjPaybill);
        }
    }

    @AfterEach
    public void cleanData() {
        DynamicRoutingDataSource dynamicDataSource = (DynamicRoutingDataSource) SpringContextHolder.getBean(DataSource.class);

        DataSource erpDataSource = dynamicDataSource.getDataSource("erp");
        try (Connection conn = erpDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 H2 数据库失败", e);
        }

        DataSource mysqlDataSource = dynamicDataSource.getDataSource("mysql");
        try (Connection conn = mysqlDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 H2 数据库失败", e);
        }
    }

    @Test
    public void testIntegrationSync() {
        // 获取所有同步后的采购付款记录
        List<FiSourceProjPaybill> list = fiSourceProjPaybillService.list();
        assertFalse(list.isEmpty(), "ERP 同步的采购付款数据不应为空");

        // 验证某个特定记录（根据 ERP数据文件中的主键，此处假设为 "1005A1100000000EV72P"）
        String pkPaybill = "1005A1100000000EV72P";
        FiSourceProjPaybill record = fiSourceProjPaybillService.getOne(
                Wrappers.<FiSourceProjPaybill>lambdaQuery().eq(FiSourceProjPaybill::getPkPaybill, pkPaybill)
        );
        assertNotNull(record, "指定主键的采购付款记录不应为空");
        // 假设 process 方法已将集成状态置为 "0"（成功），而推送状态尚未触发（为空）
        assertEquals("0", record.getIntegrationStatus(), "集成状态应为 0（成功）");
        assertNull(record.getPushStatus(), "推送状态应为空，表示尚未进行推送");
    }
}
package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/17.
 */
@Data
public class YgCommonResult {

    @ApiModelProperty("0成功，1失败")
    private String resultState;
    @ApiModelProperty("返回信息")
    private String resultMsg;
    @ApiModelProperty("报错信息")
    private List<String> errList;

    /**
     * 反馈为空，通过异步业务处理状态同步服务反馈到前端系统
     */
    @ApiModelProperty("业务单据ID")
    private String ywid;
    /**
     * 成功时必填
     */
    @ApiModelProperty("单据类型")
    private String typeId;
    /**
     * 前端系统单据编号
     */
    @ApiModelProperty("前端系统单据编号")
    private String disCode;
    /**
     * 0接收成功
     * 1接收失败
     */
    @ApiModelProperty("响应状态码")
    private String code;
    /**
     * 失败详情展示
     */
    @ApiModelProperty("描述信息")
    private String message;
}

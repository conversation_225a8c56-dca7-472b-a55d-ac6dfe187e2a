package com.cloud.ficonsumer.utils;

import cn.hutool.core.lang.UUID;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * redis 工具类
 */
@Slf4j
@UtilityClass
public class RedisUtil {
    private final RedisTemplate redisTemplate = SpringContextHolder.getApplicationContext().getBean(RedisTemplate.class);
    private final Redisson redisson = SpringContextHolder.getApplicationContext().getBean(Redisson.class);

    /**
     * set
     *
     * @param key
     * @param value
     * @param <K>
     * @param <V>
     */
    public <K, V> void set(K key, V value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * set
     *
     * @param key
     * @param value
     * @param timeout
     * @param <K>
     * @param <V>
     */
    public <K, V> void set(K key, V value, Duration timeout) {
        redisTemplate.opsForValue().set(key, value, timeout);
    }

    /**
     * get
     *
     * @param key
     * @return
     */
    public Object get(Object key) {
        ValueOperations valueOperations = redisTemplate.opsForValue();
        return valueOperations.get(key);
    }

    /**
     * delete
     *
     * @param key
     */
    public void delete(Object key) {
        redisTemplate.delete(key);
    }

    /**
     * 获取锁
     *
     * @param name
     * @return
     */
    public RLock getLock(String name) {
        return redisson.getLock(name);
    }

    /**
     * 公共部分获取key
     *
     * @param redisPrefix 缓存前缀
     * @param duration    有效时间
     */
    public RedisEntity getRedisKey(String redisPrefix, Duration duration, Object object) {
        UUID id = UUID.randomUUID();
        String redisKey = redisPrefix + id;
        set(redisKey, object, duration);
        return new RedisEntity(id.toString(), redisKey);
    }


    /**
     * redis锁
     * @param key
     * @param seconds
     * @return
     */
    public Boolean redisLock(String key, int seconds) {
        if (redisTemplate.opsForValue().setIfAbsent(key, "1")) {
            redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
            return true;
        }
        return false;
    }

    /**
     * 响应子类
     */
    @Data
    @Accessors(chain = true)
    public class RedisEntity {
        private String uuid;
        private String redisKey;
        private Object object;

        public RedisEntity(String uuid, String redisKey) {
            this.uuid = uuid;
            this.redisKey = redisKey;
        }
    }

}

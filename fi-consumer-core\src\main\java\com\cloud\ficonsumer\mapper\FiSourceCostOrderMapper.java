

package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiConvertCostOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceCostOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceCostOrderDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceCostOrder;
import com.cloud.ficonsumer.vo.FiSourceCostOrderQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 虚拟采购订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:00
 */
@Mapper
public interface FiSourceCostOrderMapper extends CloudBaseMapper<FiSourceCostOrder> {

    @Select("<script>SELECT * " +
            " FROM fi_source_cost_order t " +
            " WHERE t.del_flag = 0" +
            "<if test=\"param.pkFeebalance !=null and param.pkFeebalance !=''\"> " +
            " and pk_feebalance = #{param.pkFeebalance} " +
            "</if> " +
            "<if test=\"param.billCode !=null and param.billCode !=''\"> " +
            " and bill_code like CONCAT('%',#{param.billCode},'%') " +
            "</if> " +
            "<if test=\"param.pkOrgList != null and param.pkOrgList.size() > 0\"> " +
            "   AND pk_org in " +
            "   <foreach collection=\"param.pkOrgList\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "      #{item} " +
            "   </foreach>" +
            " </if> " +
            "<if test=\"param.groupCodeFlag !=null and param.groupCodeFlag !=''\"> " +
            " and pk_org in (select pk_org " +
            "       from fi_org_compare " +
            "       where group_code = #{param.groupCodeFlag} and del_flag =0) " +
            "</if> " +
            "<if test=\"param.pushStatus !=null and param.pushStatus !=''\"> " +
            " and push_status = #{param.pushStatus} " +
            "</if> " +
            "<if test=\"param.pushStatusList != null and param.pushStatusList.size() > 0\">"+
            " and push_status in " +
            "<foreach collection=\"param.pushStatusList\" item=\"item\" open=\"(\" separator=\",\" close=\")\">"+
            "#{item}" +
            "</foreach>" +
            "</if> " +
            "<if test=\"param.createTimeStart !=null and param.createTimeStart !=''\"> " +
            " and create_time >= #{param.createTimeStart} " +
            "</if> " +
            "<if test=\"param.createTimeEnd !=null and param.createTimeEnd !=''\"> " +
            " and #{param.createTimeEnd} >= create_time " +
            "</if> " +
            " order by create_time desc  " +
            "</script>")
    Page<FiSourceCostOrderDTO> beforeConvertDataPage(Page page, @Param("param") FiSourceCostOrderQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_source_cost_order t " +
            "    LEFT JOIN fi_source_cost_order_detail td ON t.pk_feebalance = td.pk_feebalance " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.pk_feebalance = #{param.pkFeebalance}" +
            "</script>")
    Page<FiSourceCostOrderDetailDTO> getBeforeConvertDataPage(Page page, @Param("param") FiSourceCostOrderQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_convert_cost_order t " +
            "    LEFT JOIN fi_convert_cost_order_detail td ON t.pk_feebalance = td.pk_feebalance " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.pk_feebalance = #{pk}" +
            "</script>")
    Page<FiConvertCostOrderDetailDTO> getConvertData(Page page, @Param("pk") String pk);
}



package com.cloud.ficonsumer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloud.ficonsumer.dto.FiConvertReceivableDTO;
import com.cloud.ficonsumer.dto.FiConvertReceivableDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceReceivableDTO;
import com.cloud.ficonsumer.dto.FiSourceReceivableDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceReceivable;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceReceivableQueryVO;

import java.util.List;

/**
 * 项目应收单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:25
 */
public interface FiSourceReceivableService extends IService<FiSourceReceivable> {

    /**
     * 项目应收单重新推送
     * @param s
     */
    boolean pushAgain(String s);

    Page<FiSourceReceivableDTO> beforeConvertDataPage(Page page, FiSourceReceivableQueryVO queryVO);

    Page<FiSourceReceivableDetailDTO> getBeforeConvertDataPage(Page page, FiSourceReceivableQueryVO queryVO);

    List<FiConvertReceivableDTO> getConvertList(String pk);

    Page<FiConvertReceivableDetailDTO> getConvertData(Page page, String pk);

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO);
}

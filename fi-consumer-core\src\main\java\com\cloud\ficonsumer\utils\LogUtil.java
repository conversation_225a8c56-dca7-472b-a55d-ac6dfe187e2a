package com.cloud.ficonsumer.utils;

import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.MDC;

/**
 * 日志工具类
 *
 * <AUTHOR>
 */
@UtilityClass
public class LogUtil {

    private static final String TRACE_ID = "traceId";

    /**
     * 打印日志信息
     *
     * @param logger
     * @param msg
     */
    public void info(Logger logger, String msg) {
        logger.info(getTraceMsg(msg));
    }

    /**
     * 打印日志信息
     *
     * @param logger
     * @param obj
     */
    public void info(Logger logger, Object obj) {
        logger.info(getTraceMsg() + ", {}", obj);
    }

    /**
     * 打印日志信息
     *
     * @param logger
     * @param obj
     */
    public void info(Logger logger, String msg, Object obj) {
        logger.info(getTraceMsg(msg) + ", {}", obj);
    }

    /**
     * 打印日志信息
     *
     * @param logger 日志对象
     * @param format 显示格式
     * @param msg    信息内容
     */
    public void info(Logger logger, String format, Object... msg) {
        logger.info("traceId -> "+getTraceId() + "\n" +format,msg);
    }
    /**
     * 打印报错日志
     *
     * @param logger
     * @param t
     */
    public void error(Logger logger, Throwable t) {
        logger.error(getTraceMsg(), t);
    }

    /**
     * 打印报错日志
     *
     * @param logger
     * @param msg
     * @param t
     */
    public void error(Logger logger, String msg, Throwable t) {
        logger.error(getTraceMsg(msg), t);
    }

    /**
     * 打印报错日志
     *
     * @param logger
     * @param msg
     * @param t
     */
    public void error(Logger logger,Throwable t, String msg) {
        logger.error(getTraceMsg(msg), t);
    }

    /**
     * 打印报错日志
     *
     * @param logger 日志对象
     * @param format 显示格式
     * @param msg    信息内容
     */
    public void error(Logger logger, String format, Object... msg) {
        logger.error("traceId -> "+getTraceId() + "\n" +format,msg);
    }

    /**
     * @param msg
     * @return
     */
    public String getTraceMsg(String msg) {
        return String.format("traceId=%s, msg=%s", getTraceId(), msg);
    }

    /**
     * @return
     */
    private String getTraceMsg() {
        return String.format("traceId=%s", getTraceId());
    }

    /**
     * @return
     */
    public String getTraceId() {
        return MDC.get(TRACE_ID);
    }
}
package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/20.
 */
@Data
public class YgReimbursementVO {
    /**
     * 详情
     */
    @ApiModelProperty(value="详情")
    private List<YgReimbursementDetailVO> generbillistatCostDet;

    /**
     * 发票信息
     */
    @ApiModelProperty(value="发票信息")
    private List<InvoiceVO> generbillistatInvoice;

    /**
     * 核销
     */
    @ApiModelProperty(value="核销")
    private List<AdvPayVO> generbillistatAdvPay;

    /**
     * 关联合同信息
     */
    @ApiModelProperty(value="关联合同信息")
    private List<ConDetVO> generbillistatConDet;

    /**
     * 付款信息
     */
    @ApiModelProperty(value="付款信息")
    private List<PayDetVO> generbillistatPayDet;

    /**
     * 关联原始凭据
     */
    @ApiModelProperty(value="关联原始凭据")
    private List<ImageDetail> fileInfo;

    /**
     * 分摊信息
     */
    @ApiModelProperty(value="分摊信息")
    private List<ShareInfos> generbillistatCostShare;

    /**
     * 关联事前申请
     */
    @ApiModelProperty(value="关联事前申请")
    private List<StatApplyVO> generbillistatApply;

    /**
     * 关联事前申请
     */
    @ApiModelProperty(value="关联事前申请")
    private List<StatLoanRepayVO> generbillistatLoanRepay;

    /**
     * 批量支付信息
     */
    @ApiModelProperty(value="批量支付信息")
    private List<StatBatchPayVO> generbillistatBatchPay;

    /**
     * 审批轨迹详情
     */
    @ApiModelProperty(value="审批轨迹详情")
    private List<ApprvDetails> apprv;

    /**
     * 应用ID
     */
    @ApiModelProperty(value="应用ID")
    private String appId;

    /**
     * 交易编号
     */
    @ApiModelProperty(value="交易编号")
    private String transId;

    /**
     * 来源系统
     */
    @ApiModelProperty(value="来源系统")
    private String sourSystem;

    /**
     * 费用类型
     */
    @ApiModelProperty(value="费用类型")
    private String cosType;

    /**
     * 单位代号
     */
    @ApiModelProperty(value="单位代号")
    private String dwdh;

    /**
     * 经办人
     */
    @ApiModelProperty(value="经办人")
    private String userId;

    /**
     * 推送待办用户处理ID
     */
    @ApiModelProperty(value="推送待办用户处理ID")
    private String hdlgNm;

    /**
     * 报销人
     */
    @ApiModelProperty(value="报销人")
    private String claimant;

    /**
     * 预约付款时间
     */
    @ApiModelProperty(value="预约付款时间")
    private String applicantTime;

    /**
     * 结算对象
     */
    @ApiModelProperty(value="结算对象")
    private String settleObj;

    /**
     * 结算对象
     */
    @ApiModelProperty(value="是否垫付")
    private String isAdvanPym;

    /**
     * 结算对象
     */
    @ApiModelProperty(value="是否农维费支出")
    private String isWtAgMntExs;

    /**
     * 结算对象
     */
    @ApiModelProperty(value="是否允许多费用类型报销")
    private String isMulCosTypeReim;

    /**
     * 结算对象
     */
    @ApiModelProperty(value="多人报销开关")
    private String isMultiReimburse;

    /**
     * 是否存在发票
     */
    @ApiModelProperty(value="是否存在发票")
    private String isInvoice;

    /**
     * 结算依据
     */
    @ApiModelProperty(value="结算依据")
    private String settleAccording;

    /**
     * 明细费用类型
     */
    @ApiModelProperty(value="明细费用类型")
    private String detCosType;

    /**
     * 明细费用类型编码
     */
    @ApiModelProperty(value="明细费用类型编码")
    private String detCosTypeCode;

    /**
     * 项目
     */
    @ApiModelProperty(value="项目")
    private String tProject;

    /**
     * 内部订单编码
     */
    @ApiModelProperty(value="内部订单编码")
    private String inteOrdCode;

    /**
     * WBS元素
     */
    @ApiModelProperty(value="WBS元素")
    private String wbsElement;

    /**
     * 工单
     */
    @ApiModelProperty(value="工单")
    private String workOrder;

    /**
     * 是否退回重传标识
     */
    @ApiModelProperty(value="是否退回重传标识")
    private String backTag;

    /**
     * 金额
     */
    @ApiModelProperty(value="金额")
    private String amount;

    /**
     * 职工所属部门
     */
    @ApiModelProperty(value="职工所属部门")
    private String beDept;

    /**
     * 归口部门
     */
    @ApiModelProperty(value="归口部门")
    private String belDept;

    /**
     * 预算期间
     */
    @ApiModelProperty(value="预算期间")
    private String bgtDur;

    /**
     * 业务类型
     */
    @ApiModelProperty(value="业务类型")
    private String biType;

    /**
     * 事由
     */
    @ApiModelProperty(value="事由")
    private String cause;

    /**
     * 单位
     */
    @ApiModelProperty(value="单位")
    private String company;

    /**
     * 所属部门
     */
    @ApiModelProperty(value="所属部门")
    private String dept;

    /**
     * 部门级成本中心
     */
    @ApiModelProperty(value="部门级成本中心")
    private String deptCostCenter;

    /**
     * 待复制的源ywkey，把此ywkey下文件复制到目标ywkey下，如合同、事前申请单
     */
    @ApiModelProperty(value="待复制的源ywkey")
    private String sourceBusinessKey;

    /**
     * 经办部门
     */
    @ApiModelProperty(value="经办部门")
    private String hdlgDept;

    /**
     * 经办部门编码
     */
    @ApiModelProperty(value="经办部门编码")
    private String hdlgDeptCod;

    /**
     * 部门分类
     */
    @ApiModelProperty(value="部门分类")
    private String deptType;

    /**
     * 是否核算到成本中心
     */
    @ApiModelProperty(value="是否核算到成本中心")
    private String isAccToCostCenter;

    /**
     * 是否已计提
     */
    @ApiModelProperty(value="是否已计提")
    private String isAcr;

    /**
     * 是否成本确认
     */
    @ApiModelProperty(value="是否成本确认")
    private String isCostConfir;

    /**
     * 是否零星费用
     */
    @ApiModelProperty(value="是否零星费用")
    private String isOddFees;

    /**
     * 是否简易供应商
     */
    @ApiModelProperty(value="是否简易供应商")
    private String isSSupp;

    /**
     * 是否供应商
     */
    @ApiModelProperty(value="是否供应商")
    private String isSupp;

    /**
     * 币种
     */
    @ApiModelProperty(value="币种")
    private String moneyDm;

    /**
     * 无纸化标识
     */
    @ApiModelProperty(value="无纸化标识")
    private String paperlessType;

    /**
     * 项目分类ID
     */
    @ApiModelProperty(value="项目分类ID")
    private String prjClassifId;

    /**
     * 项目编码
     */
    @ApiModelProperty(value="项目编码")
    private String prjCode;

    /**
     * 项目分类编号
     */
    @ApiModelProperty(value="项目分类编号")
    private String projectClassNumber;

    /**
     * 来源标识
     */
    @ApiModelProperty(value="来源标识")
    private String sourceIdentificate;

    /**
     * 本位币代码
     */
    @ApiModelProperty(value="本位币代码")
    private String staCyCode;

    /**
     * 租赁方式
     */
    @ApiModelProperty(value="租赁方式")
    private String leaseMet;

    /**
     * 租赁方式细分
     */
    @ApiModelProperty(value="租赁方式细分")
    private String leaseMetDetail;

    /**
     * 内部客户标识
     */
    @ApiModelProperty(value="内部客户标识")
    private String isInternalCustomer;

    /**
     * 是否存量业务
     */
    @ApiModelProperty(value="是否存量业务")
    private String isExistingBusiness;

    /**
     * 产品类型
     */
    @ApiModelProperty(value="产品类型")
    private String prdType;

    /**
     * 产品类型细分
     */
    @ApiModelProperty(value="产品类型细分")
    private String prdTypeDetail;

    /**
     * 业务渠道
     */
    @ApiModelProperty(value="业务渠道")
    private String businessChannel;

    /**
     * 对账编号
     */
    @ApiModelProperty(value="对账编号")
    private String chkId;

    /**
     * 险种
     */
    @ApiModelProperty(value="险种")
    private String insuTyp;

    /**
     * 保险类型
     */
    @ApiModelProperty(value="保险类型")
    private String insuranceType;

    /**
     * 业务性质
     */
    @ApiModelProperty(value="业务性质")
    private String busNat;

    /**
     * 代理人
     */
    @ApiModelProperty(value="代理人")
    private String agent;

    /**
     * 共保人
     */
    @ApiModelProperty(value="共保人")
    private String coinsurer;

    /**
     * 款项性质
     */
    @ApiModelProperty(value="款项性质")
    private String fundNatr;

    /**
     * 承保对象
     */
    @ApiModelProperty(value="承保对象")
    private String insuredObject;

    /**
     * 车牌号
     */
    @ApiModelProperty(value="车牌号")
    private String numPlate;

    /**
     * 机组
     */
    @ApiModelProperty(value="机组")
    private String unit;

    /**
     * 单机
     */
    @ApiModelProperty(value="单机")
    private String singleMachine;

    /**
     * 单体工程编码
     */
    @ApiModelProperty(value="单体工程编码")
    private String sinProjCod;

    /**
     * 建管单位
     */
    @ApiModelProperty(value="建管单位")
    private String projManageUnit;

    /**
     * 需求单位
     */
    @ApiModelProperty(value="需求单位")
    private String demUnit;

}

package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/24.
 */
@Data
public class HistoryOrderVO {

    /**
     * MDM基础组织编码
     */
    @ApiModelProperty(value="MDM基础组织编码")
    private String prvMkCode;

    /**
     * 采购订单号
     */
    @ApiModelProperty(value="采购订单号")
    private String purOrdNo;

    /**
     * 采购凭证的项目编号
     */
    @ApiModelProperty(value="采购凭证的项目编号")
    private String ordProjId;

    /**
     * 物料凭证年度
     */
    @ApiModelProperty(value="物料凭证年度")
    private String mtdocYear;

    /**
     * 物料凭证编号
     */
    @ApiModelProperty(value="物料凭证编号")
    private String mtDocNum;

    /**
     * 物料凭证中的项目
     */
    @ApiModelProperty(value="物料凭证中的项目")
    private String mtDocNumLine;

    /**
     * 凭证中的过账日期
     */
    @ApiModelProperty(value="凭证中的过账日期")
    private String posDatInTheDoc;

    /**
     * 数量1
     */
    @ApiModelProperty(value="数量1")
    private String purReqQty;

    /**
     * 按本位币计的金额1
     */
    @ApiModelProperty(value="按本位币计的金额1")
    private String difAmoInLocCur;

    /**
     * 凭证货币金额1
     */
    @ApiModelProperty(value="凭证货币金额1")
    private String amoInDocCur;

    /**
     * 附件张数
     */
    @ApiModelProperty(value="附件张数")
    private String refDocNum;

    /**
     * 物料编号
     */
    @ApiModelProperty(value="物料编号")
    private String mtCode;

    /**
     * 交货单的计量单位
     */
    @ApiModelProperty(value="交货单的计量单位")
    private String uniOfMeaFroDelNot;

    /**
     * 批号
     */
    @ApiModelProperty(value="批号")
    private String batchNumber;

    /**
     * 借贷方标识
     */
    @ApiModelProperty(value="借贷方标识")
    private String returnsItem;

    /**
     * 参考凭证会计年度
     */
    @ApiModelProperty(value="参考凭证会计年度")
    private String fisYeaOfCurPer;

    /**
     * 参考凭证的凭证号
     */
    @ApiModelProperty(value="参考凭证的凭证号")
    private String docNoOfARefDoc;

    /**
     * 参考凭证项目
     */
    @ApiModelProperty(value="参考凭证项目")
    private String iteOfARefDoc;

    /**
     * 帐户分配的顺序编号
     */
    @ApiModelProperty(value="帐户分配的顺序编号")
    private String seqNoAccAssig;

    /**
     * 采购订单历史分类
     */
    @ApiModelProperty(value="采购订单历史分类")
    private String purOrdHisCat;

    /**
     * 采购订单的历史记录
     */
    @ApiModelProperty(value="采购订单的历史记录")
    private String traTypPurOrdHis;

    /**
     * 移动类型(库存管理)
     */
    @ApiModelProperty(value="移动类型(库存管理)")
    private String movementType;

    /**
     * 使用采购订单价格单位的数量
     */
    @ApiModelProperty(value="使用采购订单价格单位的数量")
    private String quaInPurOrdPriUni;

    /**
     * 货币码
     */
    @ApiModelProperty(value="货币码")
    private String numericCod;

    /**
     * 用本位币计算的GR/IR帐户结算价值
     */
    @ApiModelProperty(value="用本位币计算的GR/IR帐户结算价值")
    private String grAccCleValInLocCur;

    /**
     * 用订货单位计算的收货冻结库存
     */
    @ApiModelProperty(value="用订货单位计算的收货冻结库存")
    private String gooRecBloStoInOrdUni;

    /**
     * 使用订单价格单位的收货冻结库存数量
     */
    @ApiModelProperty(value="使用订单价格单位的收货冻结库存数量")
    private String quaInGrBloStoInOrdPriUni;

    /**
     * 评估类型
     */
    @ApiModelProperty(value="评估类型")
    private String ValuTyp;

    /**
     * 交货已完成\标识
     */
    @ApiModelProperty(value="货已完成\\标识")
    private String deliveryCompletIdentifcation;

    /**
     * 移动原因
     */
    @ApiModelProperty(value="移动原因")
    private String movReason;

    /**
     * 会计凭证输入日期
     */
    @ApiModelProperty(value="会计凭证输入日期")
    private String accountingVoucherEntryDate;

    /**
     * 输入时间
     */
    @ApiModelProperty(value="输入时间")
    private String inputTime;

    /**
     * 输入的发票价值(以本币计)
     */
    @ApiModelProperty(value="输入的发票价值(以本币计)")
    private String invValEnt;

    /**
     * 以外币计的发票价值
     */
    @ApiModelProperty(value="以外币计的发票价值")
    private String invValInForCur;

    /**
     * 工厂
     */
    @ApiModelProperty(value="工厂")
    private String facNum;

    /**
     * 允许对基于GR的IV进行GR 冲销，而不管发票
     */
    @ApiModelProperty(value="允许对基于GR的IV进行GR 冲销，而不管发票")
    private String allowGRRegarInv;

    /**
     * 供应商确认的顺序编号
     */
    @ApiModelProperty(value="供应商确认的顺序编号")
    private String seqNumOfVenCon;

    /**
     * 单据条件数
     */
    @ApiModelProperty(value="单据条件数")
    private String numOfTheDocCon;

    /**
     * 销售/购买税代码
     */
    @ApiModelProperty(value="销售/购买税代码")
    private String taxId;

    /**
     * 交货单的计量单位数量
     */
    @ApiModelProperty(value="交货单的计量单位数量")
    private String delMeaUniNum;

    /**
     * 物料号
     */
    @ApiModelProperty(value="物料号")
    private String matnumcorToManParNum;

    /**
     * 在GR/IR暂记待结帐户的结算价值（业务货币）
     */
    @ApiModelProperty(value="在GR/IR暂记待结帐户的结算价值（业务货币）")
    private String cleValOnGRCleAcc;

    /**
     * 本位币码
     */
    @ApiModelProperty(value="本位币码")
    private String locCurKey;

    /**
     * 数量2
     */
    @ApiModelProperty(value="数量2")
    private String floattity;

    /**
     * 凭证中的凭证日期
     */
    @ApiModelProperty(value="凭证中的凭证日期")
    private String vouDat;

    /**
     * 发票确认中未计划的科目分配
     */
    @ApiModelProperty(value="发票确认中未计划的科目分配")
    private String unpAccAssFroInvVer;

    /**
     * 创建对象的人员名称
     */
    @ApiModelProperty(value="创建对象的人员名称")
    private String preparedBy;

    /**
     * 服务号
     */
    @ApiModelProperty(value="服务号")
    private String sRVPOS;

    /**
     * 服务的包编号
     */
    @ApiModelProperty(value="服务的包编号")
    private String packageNumber;

    /**
     * 服务行号
     */
    @ApiModelProperty(value="服务行号")
    private String pkLineNum;

    /**
     * 采购订单帐户分配编号
     */
    @ApiModelProperty(value="采购订单帐户分配编号")
    private String numOfPoAccAss;

    /**
     * 返回标识
     */
    @ApiModelProperty(value="返回标识")
    private String retInd;

    /**
     * 数量3
     */
    @ApiModelProperty(value="数量3")
    private String floattity1;

    /**
     * 按本位币计的金额2
     */
    @ApiModelProperty(value="按本位币计的金额2")
    private String amoInLocCur;

    /**
     * 凭证货币金额2
     */
    @ApiModelProperty(value="凭证货币金额2")
    private String amoInDocCur1;

    /**
     * 以订单为单位的评估收货的锁定库存
     */
    @ApiModelProperty(value="以订单为单位的评估收货的锁定库存")
    private String valGooRecBloStoInOrdUni;

    /**
     * 以订单价格为单位的评估收货锁定库存的数量
     */
    @ApiModelProperty(value="以订单价格为单位的评估收货锁定库存的数量")
    private String quaInOrdPriUni;

    /**
     * 汇率差额
     */
    @ApiModelProperty(value="汇率差额")
    private String excRatDifAmo;

    /**
     * 以凭证货币计的保留金额
     */
    @ApiModelProperty(value="以凭证货币计的保留金额")
    private String retAmoInDocCur;

    /**
     * 以公司代码货币计的保留金额
     */
    @ApiModelProperty(value="以公司代码货币计的保留金额")
    private String retAmoInComCodCur;

    /**
     * 以凭证货币计的已过帐保留金额
     */
    @ApiModelProperty(value="以凭证货币计的已过帐保留金额")
    private String posRetAmoInDocCur;

    /**
     * 以公司代码货币计的已过帐证券保持金额
     */
    @ApiModelProperty(value="以公司代码货币计的已过帐证券保持金额")
    private String posSecRetAmoInComCodCur;

    /**
     * 汇率
     */
    @ApiModelProperty(value="汇率")
    private String exchRat;

    /**
     * 原始发票项目
     */
    @ApiModelProperty(value="原始发票项目")
    private String oriOfAnInvIte;

    /**
     * 交货
     */
    @ApiModelProperty(value="交货")
    private String Delivery;

    /**
     * 交货项目
     */
    @ApiModelProperty(value="交货项目")
    private String deliIte;

    /**
     * 年份
     */
    @ApiModelProperty(value="年份")
    private String year;

}

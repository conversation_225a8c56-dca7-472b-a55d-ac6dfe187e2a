INSERT INTO CMP_SETTLEMENT
(AD<PERSON><PERSON><PERSON>TU<PERSON>, <PERSON><PERSON><PERSON>VE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, B<PERSON><PERSON>OD<PERSON>, BUSI_AUDITDATE, BUSI_BILLDATE, B<PERSON>IS<PERSON>TUS, CODE, <PERSON><PERSON>IT<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>PA<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CO<PERSON>PA<PERSON><PERSON>D<PERSON><PERSON>, <PERSON><PERSON>IG<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CREATO<PERSON>, CREDITORREFERENCE, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF3, DEF4, DEF5, DEF6, DEF7, DEF8, DEF9, D<PERSON>ECTION, DR, E<PERSON>ECTSTATUS, EXPECTDEALDATE, FTS_BILLTYPE, G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GROUPLOCAL, INVOICENO, ISAUTOSIGN, ISBACK, ISBUSIEFFECT, ISCOMMPAY, ISHADBEENRETURNED, ISINDEPENDENT, ISRESET, ISRETURNED, ISSETTLEEFFECT, ISVERIFY, LASTUPDATEDATE, LASTUPDATER, <PERSON><PERSON><PERSON><PERSON><PERSON>TIME, MODIFIER, NTBERRMSG, ORGLOCAL, PAYREASON, PK_AUDITOR, PK_BILLOPERATOR, PK_BILLTYPE, PK_BILLTYPEID, PK_BUSIBILL, PK_BUSITYPE, PK_EXECUTOR, PK_FTSBILL, PK_FTSBILLTYPE, PK_GROUP, PK_OPERATOR, PK_ORG, PK_ORG_V, PK_PCORG, PK_PCORG_V, PK_SETTLEMENT, PK_SIGNER, PK_TRADETYPE, PK_TRADETYPEID, PRIMAL, RETURNREASON, REVERSALREASON, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, SDDREVERSALDATE, SDDREVERSALER, SDDREVERSALFLAG, SETTLEBILLTYPE, SETTLEDATE, SETTLENUM, SETTLESTATUS, SETTLETYPE, SIGNDATE, STRUCTUREDSTANDARD, SYSTEMCODE, TRADERTYPECODE, TS, VBILLSTATUS)
VALUES(0, NULL, '~', 0, NULL, 'D32021072300035801', '2025-01-09 11:00:33', '2025-01-09 11:00:23', 8, 'AANOQ0MAE1BTWVdfSFVBTkdTSFVBTkdMSU4AAAAgxxYX2ANKuByB3+PoiAAmQDAeoQCiPMmkWVZA
oLlDuGI=', NULL, '~', NULL, NULL, NULL, '~', '2025-01-09 11:00:42', '1001A110000000CKRKRC', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 1, 0, 10, NULL, '~', NULL, NULL, NULL, 'Y', NULL, 'Y', 'N', NULL, 'N', NULL, NULL, NULL, 'N', '2025-01-09 11:00:33', '1001A110000000027GLX', '2025-01-09 11:00:42', '1001A110000000027GLX', NULL, 1000000, NULL, '1001A110000000027GLX', '1001A110000000CKRKRC', 'F3', '0000Z3000000000000F3', '1001A110000000GZCBG9', '0001A110000000000HPT', '~', NULL, '~', '0001A110000000000CE4', '1001A110000000CKRKRC', '0001A110000000006YPN', '0001A110000000006YPM', '~', '~', '1001A110000000GZCBGF', '1001A110000000027GLX', 'F3-Cxx-02', '1001A110000000000M0D', 1000000, NULL, NULL, 'aa639504-177d-4217-91fc-f7bdb28d08d4', 0, '20250109499895f7-1574-4521-8954-edfe535f59a1', 0, NULL, '~', 'N', '2201', NULL, NULL, 0, NULL, '2025-01-09 11:00:33', NULL, '1', 'F3-Cxx-02', '2025-01-09 11:01:08', NULL);
INSERT INTO CMP_SETTLEMENT
(ADUITSTATUS, APPROVEDATE, APPROVER, ARITHMETIC, BACKREASON, BILLCODE, BUSI_AUDITDATE, BUSI_BILLDATE, BUSISTATUS, CODE, COMMITDATE, COMMITER, COMMPAYBEGINDATE, COMMPAYENDDATE, CONSIGNAGREEMENT, COSTCENTER, CREATIONTIME, CREATOR, CREDITORREFERENCE, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF3, DEF4, DEF5, DEF6, DEF7, DEF8, DEF9, DIRECTION, DR, EFFECTSTATUS, EXPECTDEALDATE, FTS_BILLTYPE, GLOBALOACL, GROUPLOCAL, INVOICENO, ISAUTOSIGN, ISBACK, ISBUSIEFFECT, ISCOMMPAY, ISHADBEENRETURNED, ISINDEPENDENT, ISRESET, ISRETURNED, ISSETTLEEFFECT, ISVERIFY, LASTUPDATEDATE, LASTUPDATER, MODIFIEDTIME, MODIFIER, NTBERRMSG, ORGLOCAL, PAYREASON, PK_AUDITOR, PK_BILLOPERATOR, PK_BILLTYPE, PK_BILLTYPEID, PK_BUSIBILL, PK_BUSITYPE, PK_EXECUTOR, PK_FTSBILL, PK_FTSBILLTYPE, PK_GROUP, PK_OPERATOR, PK_ORG, PK_ORG_V, PK_PCORG, PK_PCORG_V, PK_SETTLEMENT, PK_SIGNER, PK_TRADETYPE, PK_TRADETYPEID, PRIMAL, RETURNREASON, REVERSALREASON, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, SDDREVERSALDATE, SDDREVERSALER, SDDREVERSALFLAG, SETTLEBILLTYPE, SETTLEDATE, SETTLENUM, SETTLESTATUS, SETTLETYPE, SIGNDATE, STRUCTUREDSTANDARD, SYSTEMCODE, TRADERTYPECODE, TS, VBILLSTATUS)
VALUES(0, NULL, '~', 0, NULL, 'D32024012300000069', '2024-01-23 09:21:45', '2024-01-23 09:21:45', 8, 'AANOQ0MABGdsMDIAAAAgd0avAhq9IkEefqu/OsINDlnhrekoHJ8l4JzEHB1bAVc=', NULL, '~', NULL, NULL, NULL, '~', '2024-01-23 09:21:49', '1001O610000000H80VBD', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 1, 0, 10, NULL, '~', NULL, NULL, NULL, 'Y', NULL, 'Y', 'N', NULL, 'N', NULL, NULL, NULL, 'Y', '2025-01-08 15:05:51', '1001A710000000JO5IEV', '2025-01-08 15:09:44', '1001A710000000JO5IEV', NULL, 1, NULL, '1001O610000000H80VBD', '1001O610000000H80VBD', 'F3', '0000Z3000000000000F3', '1001A710000000JX4CMJ', '1001A710000000IZKV44', '1001A710000000JO5IEV', NULL, '~', '0001A110000000000CE4', '1001O610000000H80VBD', '0001A110000000004GXH', '0001A110000000004GXG', '~', '~', '1001A710000000JX4CMQ', '1001O610000000H80VBD', 'F3-Cxx-02', '1001A110000000000M0D', 1, NULL, NULL, 'c3226a8b-8b41-4623-bd8a-da495bb5d5ec', 0, '20240123f6493179-84ba-48a4-9b20-62420c22ca89', 0, NULL, '~', NULL, '2201', '2025-01-08 15:09:43', NULL, 5, 0, '2024-01-23 09:21:45', NULL, '1', 'F3-Cxx-02', '2025-01-08 15:09:45', NULL);
INSERT INTO CMP_SETTLEMENT
(ADUITSTATUS, APPROVEDATE, APPROVER, ARITHMETIC, BACKREASON, BILLCODE, BUSI_AUDITDATE, BUSI_BILLDATE, BUSISTATUS, CODE, COMMITDATE, COMMITER, COMMPAYBEGINDATE, COMMPAYENDDATE, CONSIGNAGREEMENT, COSTCENTER, CREATIONTIME, CREATOR, CREDITORREFERENCE, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF3, DEF4, DEF5, DEF6, DEF7, DEF8, DEF9, DIRECTION, DR, EFFECTSTATUS, EXPECTDEALDATE, FTS_BILLTYPE, GLOBALOACL, GROUPLOCAL, INVOICENO, ISAUTOSIGN, ISBACK, ISBUSIEFFECT, ISCOMMPAY, ISHADBEENRETURNED, ISINDEPENDENT, ISRESET, ISRETURNED, ISSETTLEEFFECT, ISVERIFY, LASTUPDATEDATE, LASTUPDATER, MODIFIEDTIME, MODIFIER, NTBERRMSG, ORGLOCAL, PAYREASON, PK_AUDITOR, PK_BILLOPERATOR, PK_BILLTYPE, PK_BILLTYPEID, PK_BUSIBILL, PK_BUSITYPE, PK_EXECUTOR, PK_FTSBILL, PK_FTSBILLTYPE, PK_GROUP, PK_OPERATOR, PK_ORG, PK_ORG_V, PK_PCORG, PK_PCORG_V, PK_SETTLEMENT, PK_SIGNER, PK_TRADETYPE, PK_TRADETYPEID, PRIMAL, RETURNREASON, REVERSALREASON, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, SDDREVERSALDATE, SDDREVERSALER, SDDREVERSALFLAG, SETTLEBILLTYPE, SETTLEDATE, SETTLENUM, SETTLESTATUS, SETTLETYPE, SIGNDATE, STRUCTUREDSTANDARD, SYSTEMCODE, TRADERTYPECODE, TS, VBILLSTATUS)
VALUES(0, NULL, '~', 0, NULL, 'D32025010800000018', '2025-01-08 15:28:25', '2025-01-08 15:24:35', 8, 'AANOQ0MACHlhbmdmdXl1AAAAIJnYWtZ4nfbsQAy1oofbvOJqyT1PnFvu3xLnZTh+ecUt', NULL, '~', NULL, NULL, NULL, '~', '2025-01-08 15:28:29', '1001A710000000J5RPFG', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 1, 0, 10, NULL, '~', NULL, NULL, '23423466', 'Y', NULL, 'Y', 'N', NULL, 'N', NULL, NULL, NULL, 'Y', '2025-01-08 16:44:15', '1001A710000000JO5IEV', '2025-01-10 09:44:12', '1001A710000000JO5IEV', NULL, 300, NULL, '1001A710000000J5RPFG', '1001A710000000J5RPFG', 'F3', '0000Z3000000000000F3', '1001A710000000LAB6V0', '0001A110000000000HPT', '1001A710000000JO5IEV', NULL, '~', '0001A110000000000CE4', '1001A710000000J5RPFG', '0001A110000000004GXH', '0001A110000000004GXG', '~', '~', '1001A710000000LAB6V7', '1001A710000000J5RPFG', 'F3-Cxx-02', '1001A110000000000M0D', 300, NULL, NULL, '2713c944-bf8f-40f7-b95f-5a6376d3aa6f', 0, '202501082a54738f-fd74-4620-83dc-251107c9ef5c', 0, NULL, '~', 'N', '2201', '2025-01-10 09:44:06', NULL, 5, 0, '2025-01-08 15:28:25', NULL, '1', 'F3-Cxx-02', '2025-01-10 09:44:14', NULL);
INSERT INTO AP_PAYBILL
(ACCESSORYNUM, APPROVEDATE, APPROVER, APPROVESTATUS, BILLCLASS, BILLDATE, BILLMAKER, BILLNO, BILLPERIOD, BILLSTATUS, BILLYEAR, COMMPAYBEGINDATE, COMMPAYENDDATE, CONFIRMUSER, CONSIGNAGREEMENT, COORDFLAG, CREATIONTIME, CREATOR, CREDITORREFERENCE, CREDITREFSTD, CUSTDELEGATE, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DR, EFFECTDATE, EFFECTSTATUS, EFFECTUSER, EXPECTDEALDATE, FAILUREREASON, GLOBALLOCAL, GROUPLOCAL, INVOICENO, ISFLOWBILL, ISFORCE, ISFROMINDEPENDENT, ISINIT, ISMANDATEPAY, ISNETPAYREADY, ISONLINEPAY, ISREDED, ISREFUND, ISURGENT, LASTADJUSTUSER, LASTAPPROVEID, LOCAL_MONEY, MODIFIEDTIME, MODIFIER, MONEY, OFFICIALPRINTDATE, OFFICIALPRINTUSER, OUTBUSITYPE, PAYDATE, PAYMAN, PK_BILLTYPE, PK_BUSITYPE, PK_CORP, PK_CURRTYPE, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYBILL, PK_PCORG, PK_PCORG_V, PK_TRADETYPE, PK_TRADETYPEID, RECECOUNTRYID, REVERSALREASON, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, SCOMMENT, SDDREVERSALFLAG, SETT_ORG, SETT_ORG_V, SETTLEFLAG, SETTLENUM, SETTLETYPE, SIGNDATE, SIGNPERIOD, SIGNUSER, SIGNYEAR, SRC_SYSCODE, SUBJCODE, SYSCODE, TAXCOUNTRYID, TS)
VALUES(NULL, '2025-01-09 11:00:33', '1001A110000000027GLX', 1, 'fk', '2025-01-09 11:00:23', '1001A110000000CKRKRC', 'D32021072300035801', '01', 8, '2025', NULL, NULL, '~', '~', NULL, '2021-07-23 13:13:17', '1001A110000000CKRKRC', NULL, NULL, NULL, '~', '~', '~', '~', 'N', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '收款对象名称与收款账户户名不一致，请核实', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 0, '2025-01-09 11:00:33', 10, '1001A110000000027GLX', NULL, NULL, 0, 1000000, NULL, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', '~', '1001A110000000027GLX', 1000000, '2025-01-09 11:00:34', '1001A110000000027GLX', 1000000, NULL, '~', '~                   ', NULL, '~', 'F3', '0001A110000000000HPT', '~', '1002Z0100000000001K1', '0001A110000000006YPN', '0001A110000000006YPM', '0001A110000000000CE4', '0001A110000000006YPN', '0001A110000000006YPM', '1001A110000000GZCBG9', '~', '~', 'F3-Cxx-02', '1001A110000000000M0D', '0001Z010000000079UJJ', NULL, 'aa639504-177d-4217-91fc-f7bdb28d08d4', 0, '20250109499895f7-1574-4521-8954-edfe535f59a1', 0, '~', 'N', '0001A110000000006YPN', '0001A110000000006YPM', 0, NULL, 0, '2025-01-09 11:00:33', NULL, '1001A110000000027GLX', NULL, 1, '~', 1, '0001Z010000000079UJJ', '2025-01-09 11:01:15');
INSERT INTO AP_PAYBILL
(ACCESSORYNUM, APPROVEDATE, APPROVER, APPROVESTATUS, BILLCLASS, BILLDATE, BILLMAKER, BILLNO, BILLPERIOD, BILLSTATUS, BILLYEAR, COMMPAYBEGINDATE, COMMPAYENDDATE, CONFIRMUSER, CONSIGNAGREEMENT, COORDFLAG, CREATIONTIME, CREATOR, CREDITORREFERENCE, CREDITREFSTD, CUSTDELEGATE, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DR, EFFECTDATE, EFFECTSTATUS, EFFECTUSER, EXPECTDEALDATE, FAILUREREASON, GLOBALLOCAL, GROUPLOCAL, INVOICENO, ISFLOWBILL, ISFORCE, ISFROMINDEPENDENT, ISINIT, ISMANDATEPAY, ISNETPAYREADY, ISONLINEPAY, ISREDED, ISREFUND, ISURGENT, LASTADJUSTUSER, LASTAPPROVEID, LOCAL_MONEY, MODIFIEDTIME, MODIFIER, MONEY, OFFICIALPRINTDATE, OFFICIALPRINTUSER, OUTBUSITYPE, PAYDATE, PAYMAN, PK_BILLTYPE, PK_BUSITYPE, PK_CORP, PK_CURRTYPE, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYBILL, PK_PCORG, PK_PCORG_V, PK_TRADETYPE, PK_TRADETYPEID, RECECOUNTRYID, REVERSALREASON, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, SCOMMENT, SDDREVERSALFLAG, SETT_ORG, SETT_ORG_V, SETTLEFLAG, SETTLENUM, SETTLETYPE, SIGNDATE, SIGNPERIOD, SIGNUSER, SIGNYEAR, SRC_SYSCODE, SUBJCODE, SYSCODE, TAXCOUNTRYID, TS)
VALUES(NULL, '2024-01-23 09:21:45', '1001O610000000H80VBD', 1, 'fk', '2024-01-23 09:21:45', '1001O610000000H80VBD', 'D32024012300000069', '01', 8, '2024', NULL, NULL, '~', '~', NULL, '2024-01-23 09:21:48', '1001O610000000H80VBD', NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '1001A1100000000DXOH1', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', '~', '~', 0, '2024-01-23 09:21:45', 10, '1001O610000000H80VBD', NULL, NULL, 0, 1, NULL, 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', '~', '1001O610000000H80VBD', 1, NULL, '~', 1, NULL, '~', '~                   ', '2025-01-08 15:05:51', '1001A710000000JO5IEV', 'F3', '1001A710000000IZKV44', '~', '1002Z0100000000001K1', '0001A110000000004GXH', '0001A110000000004GXG', '0001A110000000000CE4', '0001A110000000004GXH', '0001A110000000004GXG', '1001A710000000JX4CMJ', '~', '~', 'F3-Cxx-02', '1001A110000000000M0D', '0001Z010000000079UJJ', NULL, 'c3226a8b-8b41-4623-bd8a-da495bb5d5ec', 0, '20240123f6493179-84ba-48a4-9b20-62420c22ca89', 0, NULL, 'N', '0001A110000000004GXH', '0001A110000000004GXG', 1, NULL, NULL, '2024-01-23 09:21:45', NULL, '1001O610000000H80VBD', NULL, 1, '~', 1, '0001Z010000000079UJJ', '2025-01-08 15:09:45');
INSERT INTO AP_PAYBILL
(ACCESSORYNUM, APPROVEDATE, APPROVER, APPROVESTATUS, BILLCLASS, BILLDATE, BILLMAKER, BILLNO, BILLPERIOD, BILLSTATUS, BILLYEAR, COMMPAYBEGINDATE, COMMPAYENDDATE, CONFIRMUSER, CONSIGNAGREEMENT, COORDFLAG, CREATIONTIME, CREATOR, CREDITORREFERENCE, CREDITREFSTD, CUSTDELEGATE, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DR, EFFECTDATE, EFFECTSTATUS, EFFECTUSER, EXPECTDEALDATE, FAILUREREASON, GLOBALLOCAL, GROUPLOCAL, INVOICENO, ISFLOWBILL, ISFORCE, ISFROMINDEPENDENT, ISINIT, ISMANDATEPAY, ISNETPAYREADY, ISONLINEPAY, ISREDED, ISREFUND, ISURGENT, LASTADJUSTUSER, LASTAPPROVEID, LOCAL_MONEY, MODIFIEDTIME, MODIFIER, MONEY, OFFICIALPRINTDATE, OFFICIALPRINTUSER, OUTBUSITYPE, PAYDATE, PAYMAN, PK_BILLTYPE, PK_BUSITYPE, PK_CORP, PK_CURRTYPE, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYBILL, PK_PCORG, PK_PCORG_V, PK_TRADETYPE, PK_TRADETYPEID, RECECOUNTRYID, REVERSALREASON, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, SCOMMENT, SDDREVERSALFLAG, SETT_ORG, SETT_ORG_V, SETTLEFLAG, SETTLENUM, SETTLETYPE, SIGNDATE, SIGNPERIOD, SIGNUSER, SIGNYEAR, SRC_SYSCODE, SUBJCODE, SYSCODE, TAXCOUNTRYID, TS)
VALUES(NULL, '2025-01-08 15:28:25', '1001A710000000J5RPFG', 1, 'fk', '2025-01-08 15:24:35', '1001A710000000J5RPFG', 'D32025010800000018', '01', 8, '2025', NULL, NULL, '~', '~', NULL, '2025-01-08 15:27:28', '1001A710000000J5RPFG', NULL, NULL, NULL, '~', '~', '~', '~', 'N', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '收款对象名称与收款账户户名不一致，请核实', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', '~', '~', 0, '2025-01-08 15:28:25', 10, '1001A710000000J5RPFG', NULL, NULL, 0, 300, '23423466', 'Y', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', '~', '1001A710000000J5RPFG', 300, '2025-01-08 15:28:28', '1001A710000000J5RPFG', 300, NULL, '~', '~                   ', '2025-01-08 16:44:15', '1001A710000000JO5IEV', 'F3', '0001A110000000000HPT', '~', '1002Z0100000000001K1', '0001A110000000004GXH', '0001A110000000004GXG', '0001A110000000000CE4', '0001A110000000004GXH', '0001A110000000004GXG', '1001A710000000LAB6V0', '~', '~', 'F3-Cxx-02', '1001A110000000000M0D', '0001Z010000000079UJJ', NULL, '2713c944-bf8f-40f7-b95f-5a6376d3aa6f', 0, '202501082a54738f-fd74-4620-83dc-251107c9ef5c', 0, '未填写请购理由', 'N', '~', '0001A110000000004GXG', 1, NULL, NULL, '2025-01-08 15:28:25', NULL, '1001A710000000J5RPFG', NULL, 1, '~', 1, '0001Z010000000079UJJ', '2025-01-10 09:44:25');
INSERT INTO CMP_DETAIL
(ACCOUNTNUM, ACCOUNTTYPE, AGENTRECEIVELOCAL, AGENTRECEIVEPRIMAL, BANKMSG, BANKRELATED_CODE, BILLCODE, BILLDATE, BILLTIME, BUSILINENO, CHANGEBALANCE, CHANGERATE, CHECKCOUNT, CHECKDATE, CODE, COMMITDATE, COMMPAYER, COMMPAYSTATUS, COMMPAYTIME, COMMPAYTYPE, CREATIONTIME, CREATOR, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF3, DEF4, DEF5, DEF6, DEF7, DEF8, DEF9, DEF_NOTE, DEF_SNO, DIRECT_ECDS, DIRECTION, DR, EDITEDFLAG, EXECDATE, EXECTIME, EXPECTATE_DATE, FAILUREREASON, FUNDFORMCODE, FUNDSFLAG, FUNDTYPE, GLOBALCOMMRECEIVELOCAL, GLOBALPAYLOCAL, GLOBALPAYLOCAL_LAST, GLOBALPAYRATE_LAST, GLOBALRATE, GLOBALRECEIVELOCAL, GROUPCOMMRECEIVELOCAL, GROUPPAYLOCAL, GROUPPAYLOCAL_LAST, GROUPPAYRATE_LAST, GROUPRATE, GROUPRECEIVELOCAL, HANDWORKEDUPDATE, ISACCCORD, ISBILLRECORD, ISNETBANKPAY, ISREFUSED, ISSAMEBANK, ISSAMECITY, LASTEST_PAYDATE, LOCALRATE, MEMO, MODIFIEDTIME, MODIFIER, MODIFYFLAG, NOTEDIRECTION, NOTENUMBER, OPPACCNAME, OPPACCOUNT, OPPBANK, ORGBUSIDATE, ORGBUSITIME, ORGSETTLEDATE, ORGSETTLETIME, ORGSIGNDATE, ORGSIGNTIME, PAY, PAY_LAST, PAY_TYPE, PAYBILLCODE, PAYDATE, PAYLOCAL, PAYLOCAL_LAST, PAYLOCALRATE_LAST, PAYREASON, PK_ACCOUNT, PK_AGENTSETTLEACC, PK_BALATYPE, PK_BANK, PK_BILL, PK_BILLBALATYPE, PK_BILLDETAIL, PK_BILLTYPE, PK_BILLTYPEID, PK_BUSIORG, PK_BUSIORG_V, PK_BUSITYPE, PK_CASHACCOUNT, PK_CASHFLOW, PK_COSTSUBJ, PK_CUBASDOC, PK_CURRTYPE, PK_CURRTYPE_LAST, PK_DEPTDOC, PK_DETAIL, PK_FIORG, PK_FTSBILL, PK_FTSBILLDETAIL, PK_GROUP, PK_INNERACCOUNT, PK_INNERORG, PK_INVBASDOC, PK_INVCL, PK_JOB, PK_JOBPHASE, PK_NOTENUMBER, PK_NOTETYPE, PK_OPPACCOUNT, PK_OPPBANK, PK_ORG, PK_ORG_V, PK_PAYBILL, PK_PAYTYPE, PK_PAYUSER, PK_PCORG, PK_PCORG_V, PK_PLANSUBJ, PK_PSNDOC, PK_RECEIVETYPE, PK_RESCENTER, PK_SETTLEMENT, PK_SUGGESTNOTETYPE, PK_TRADER, PROCESSTYPE, RECEIVE, RECEIVELOCAL, REFUSEREASON, SETTLELINENO, SETTLESTATUS, SIGNDATE, SYSTEMCODE, TALLYDATE, TALLYSTATUS, TRADERNAME, TRADERTYPE, TRANSERIAL, TRANSTYPE, TS)
VALUES('330016****205961***', NULL, 0, 0, NULL, '01H5LE', 'D32021072300035801', '2025-01-09 11:00:23', NULL, 0, 0, 1, NULL, NULL, 'AANOQ0MAE1BTWVdfSFVBTkdTSFVBTkdMSU4AAAAgxxYX2ANKuByB3+PoiAAmQDAeoQCiPMmkWVZA
oLlDuGI=', NULL, '~', NULL, NULL, NULL, NULL, '~', '~', '~', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, NULL, 'N', 1, 0, NULL, NULL, NULL, NULL, NULL, 1, 1, '~', NULL, 0, 0, 0, 0, NULL, NULL, 1000000, 1000000, 1, 1, NULL, NULL, 'N', 'N', NULL, NULL, NULL, NULL, NULL, 1, NULL, '2025-01-09 11:00:42', '1001A110000000027GLX', 'n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n', NULL, NULL, '浙江*******司物资分公司', '330016****705300***', '中国建设银行股份有限公司杭州宝石支行', NULL, NULL, NULL, NULL, NULL, NULL, 1000000, 1000000, NULL, NULL, NULL, 1000000, 1000000, 1, NULL, '1001A1100000000F2IQX', '~', '1001A1100000005IS4S2', '1001A11000000000VKZD', '1001A110000000GZCBG9', '1001A1100000005IS4S2', '1001A110000000GZCBGA', 'F3-Cxx-02', '~', '~', '~', '0001A110000000000HPT', NULL, NULL, '1001A1100000000IW7LP', '1001A1100000000KKNH2', '1002Z0100000000001K1', '1002Z0100000000001K1', '1001A11000000001D5LJ', '1001A110000000GZCBGH', '~', '~', '~', '0001A110000000000CE4', '~', '~', '~', '~', NULL, NULL, '~', '~', '1001A1100000000F2IS6', '1001A11000000000VL2J', '0001A110000000006YPN', '0001A110000000006YPM', '~', NULL, '~', '~', '~', NULL, '0001A11000000000B79Z', NULL, '~', '1001A110000000GZCBGF', '~', '1001A1100000000KKNH2', NULL, NULL, NULL, NULL, 0, NULL, '2025-01-09 11:00:33', 'ap', NULL, 2, '****************公司', 1, NULL, 0, '2025-01-09 11:01:08');
INSERT INTO CMP_DETAIL
(ACCOUNTNUM, ACCOUNTTYPE, AGENTRECEIVELOCAL, AGENTRECEIVEPRIMAL, BANKMSG, BANKRELATED_CODE, BILLCODE, BILLDATE, BILLTIME, BUSILINENO, CHANGEBALANCE, CHANGERATE, CHECKCOUNT, CHECKDATE, CODE, COMMITDATE, COMMPAYER, COMMPAYSTATUS, COMMPAYTIME, COMMPAYTYPE, CREATIONTIME, CREATOR, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF3, DEF4, DEF5, DEF6, DEF7, DEF8, DEF9, DEF_NOTE, DEF_SNO, DIRECT_ECDS, DIRECTION, DR, EDITEDFLAG, EXECDATE, EXECTIME, EXPECTATE_DATE, FAILUREREASON, FUNDFORMCODE, FUNDSFLAG, FUNDTYPE, GLOBALCOMMRECEIVELOCAL, GLOBALPAYLOCAL, GLOBALPAYLOCAL_LAST, GLOBALPAYRATE_LAST, GLOBALRATE, GLOBALRECEIVELOCAL, GROUPCOMMRECEIVELOCAL, GROUPPAYLOCAL, GROUPPAYLOCAL_LAST, GROUPPAYRATE_LAST, GROUPRATE, GROUPRECEIVELOCAL, HANDWORKEDUPDATE, ISACCCORD, ISBILLRECORD, ISNETBANKPAY, ISREFUSED, ISSAMEBANK, ISSAMECITY, LASTEST_PAYDATE, LOCALRATE, MEMO, MODIFIEDTIME, MODIFIER, MODIFYFLAG, NOTEDIRECTION, NOTENUMBER, OPPACCNAME, OPPACCOUNT, OPPBANK, ORGBUSIDATE, ORGBUSITIME, ORGSETTLEDATE, ORGSETTLETIME, ORGSIGNDATE, ORGSIGNTIME, PAY, PAY_LAST, PAY_TYPE, PAYBILLCODE, PAYDATE, PAYLOCAL, PAYLOCAL_LAST, PAYLOCALRATE_LAST, PAYREASON, PK_ACCOUNT, PK_AGENTSETTLEACC, PK_BALATYPE, PK_BANK, PK_BILL, PK_BILLBALATYPE, PK_BILLDETAIL, PK_BILLTYPE, PK_BILLTYPEID, PK_BUSIORG, PK_BUSIORG_V, PK_BUSITYPE, PK_CASHACCOUNT, PK_CASHFLOW, PK_COSTSUBJ, PK_CUBASDOC, PK_CURRTYPE, PK_CURRTYPE_LAST, PK_DEPTDOC, PK_DETAIL, PK_FIORG, PK_FTSBILL, PK_FTSBILLDETAIL, PK_GROUP, PK_INNERACCOUNT, PK_INNERORG, PK_INVBASDOC, PK_INVCL, PK_JOB, PK_JOBPHASE, PK_NOTENUMBER, PK_NOTETYPE, PK_OPPACCOUNT, PK_OPPBANK, PK_ORG, PK_ORG_V, PK_PAYBILL, PK_PAYTYPE, PK_PAYUSER, PK_PCORG, PK_PCORG_V, PK_PLANSUBJ, PK_PSNDOC, PK_RECEIVETYPE, PK_RESCENTER, PK_SETTLEMENT, PK_SUGGESTNOTETYPE, PK_TRADER, PROCESSTYPE, RECEIVE, RECEIVELOCAL, REFUSEREASON, SETTLELINENO, SETTLESTATUS, SIGNDATE, SYSTEMCODE, TALLYDATE, TALLYSTATUS, TRADERNAME, TRADERTYPE, TRANSERIAL, TRANSTYPE, TS)
VALUES(NULL, NULL, 0, 0, NULL, '015MGJ', 'D32024012300000069', '2024-01-23 09:21:45', NULL, 0, 0, 1, NULL, NULL, 'AANOQ0MABGdsMDIAAAAgd0avAhq9IkEefqu/OsINDlnhrekoHJ8l4JzEHB1bAVc=', NULL, '~', NULL, NULL, NULL, NULL, '~', '~', '~', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, NULL, 'N', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, 1, '~', NULL, 0, 0, 0, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, 'N', 'N', NULL, NULL, NULL, NULL, NULL, 1, NULL, '2025-01-08 15:09:44', '1001A710000000JO5IEV', 'n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n', NULL, NULL, '中国**********行', '************', '中国工商银行股份有限公司北京通州支行新华分理处', NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, 1, 1, 1, NULL, '~', '~', '~', '~', '1001A710000000JX4CMJ', '~', '1001A710000000JX4CMK', 'F3-Cxx-02', '~', '~', '~', '1001A710000000IZKV44', NULL, NULL, '1001A1100000000IW7LO', '1001A1100000000I8JIL', '1002Z0100000000001K1', '1002Z0100000000001K1', '1001A110000000011Y2K', '1001A710000000JX4CMS', '~', '~', '1001A710000000JX4CMA', '0001A110000000000CE4', '~', '~', '~', '~', NULL, NULL, '~', '~', '1001A710000000JU64I9', '1001A11000000000TLI3', '0001A110000000004GXH', '0001A110000000004GXG', '~', NULL, '~', '~', '~', NULL, '0001A11000000000A262', NULL, '~', '1001A710000000JX4CMQ', '~', '1001A1100000000I8JIL', NULL, NULL, NULL, NULL, 0, 5, '2024-01-23 09:21:45', 'ap', '2025-01-08 15:09:43', 4, '中国**********行', 1, NULL, 1, '2025-01-08 15:09:45');
INSERT INTO CMP_DETAIL
(ACCOUNTNUM, ACCOUNTTYPE, AGENTRECEIVELOCAL, AGENTRECEIVEPRIMAL, BANKMSG, BANKRELATED_CODE, BILLCODE, BILLDATE, BILLTIME, BUSILINENO, CHANGEBALANCE, CHANGERATE, CHECKCOUNT, CHECKDATE, CODE, COMMITDATE, COMMPAYER, COMMPAYSTATUS, COMMPAYTIME, COMMPAYTYPE, CREATIONTIME, CREATOR, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF3, DEF4, DEF5, DEF6, DEF7, DEF8, DEF9, DEF_NOTE, DEF_SNO, DIRECT_ECDS, DIRECTION, DR, EDITEDFLAG, EXECDATE, EXECTIME, EXPECTATE_DATE, FAILUREREASON, FUNDFORMCODE, FUNDSFLAG, FUNDTYPE, GLOBALCOMMRECEIVELOCAL, GLOBALPAYLOCAL, GLOBALPAYLOCAL_LAST, GLOBALPAYRATE_LAST, GLOBALRATE, GLOBALRECEIVELOCAL, GROUPCOMMRECEIVELOCAL, GROUPPAYLOCAL, GROUPPAYLOCAL_LAST, GROUPPAYRATE_LAST, GROUPRATE, GROUPRECEIVELOCAL, HANDWORKEDUPDATE, ISACCCORD, ISBILLRECORD, ISNETBANKPAY, ISREFUSED, ISSAMEBANK, ISSAMECITY, LASTEST_PAYDATE, LOCALRATE, MEMO, MODIFIEDTIME, MODIFIER, MODIFYFLAG, NOTEDIRECTION, NOTENUMBER, OPPACCNAME, OPPACCOUNT, OPPBANK, ORGBUSIDATE, ORGBUSITIME, ORGSETTLEDATE, ORGSETTLETIME, ORGSIGNDATE, ORGSIGNTIME, PAY, PAY_LAST, PAY_TYPE, PAYBILLCODE, PAYDATE, PAYLOCAL, PAYLOCAL_LAST, PAYLOCALRATE_LAST, PAYREASON, PK_ACCOUNT, PK_AGENTSETTLEACC, PK_BALATYPE, PK_BANK, PK_BILL, PK_BILLBALATYPE, PK_BILLDETAIL, PK_BILLTYPE, PK_BILLTYPEID, PK_BUSIORG, PK_BUSIORG_V, PK_BUSITYPE, PK_CASHACCOUNT, PK_CASHFLOW, PK_COSTSUBJ, PK_CUBASDOC, PK_CURRTYPE, PK_CURRTYPE_LAST, PK_DEPTDOC, PK_DETAIL, PK_FIORG, PK_FTSBILL, PK_FTSBILLDETAIL, PK_GROUP, PK_INNERACCOUNT, PK_INNERORG, PK_INVBASDOC, PK_INVCL, PK_JOB, PK_JOBPHASE, PK_NOTENUMBER, PK_NOTETYPE, PK_OPPACCOUNT, PK_OPPBANK, PK_ORG, PK_ORG_V, PK_PAYBILL, PK_PAYTYPE, PK_PAYUSER, PK_PCORG, PK_PCORG_V, PK_PLANSUBJ, PK_PSNDOC, PK_RECEIVETYPE, PK_RESCENTER, PK_SETTLEMENT, PK_SUGGESTNOTETYPE, PK_TRADER, PROCESSTYPE, RECEIVE, RECEIVELOCAL, REFUSEREASON, SETTLELINENO, SETTLESTATUS, SIGNDATE, SYSTEMCODE, TALLYDATE, TALLYSTATUS, TRADERNAME, TRADERTYPE, TRANSERIAL, TRANSTYPE, TS)
VALUES(NULL, NULL, 0, 0, NULL, '01H5L2', 'D32025010800000018', '2025-01-08 15:24:35', NULL, 0, 0, 1, NULL, NULL, 'AANOQ0MACHlhbmdmdXl1AAAAIJnYWtZ4nfbsQAy1oofbvOJqyT1PnFvu3xLnZTh+ecUt', NULL, '~', NULL, NULL, NULL, NULL, '~', '~', '~', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, NULL, 'N', 1, 0, NULL, NULL, NULL, NULL, NULL, NULL, 1, '~', NULL, 0, 0, 0, 0, NULL, NULL, 300, 300, 0, 0, NULL, NULL, 'N', 'N', NULL, NULL, NULL, NULL, NULL, 1, '未填写请购理由', '2025-01-10 09:44:12', '1001A710000000JO5IEV', 'n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n,n', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 300, 300, NULL, NULL, NULL, 300, 300, 1, NULL, '~', '~', '~', '~', '1001A710000000LAB6V0', '~', '1001A710000000LAB6V1', 'F3-Cxx-02', '~', '0001A110000000004GXH', '0001A110000000004GXG', '0001A110000000000HPT', NULL, NULL, '1001A1100000000IW7LP', '1001A710000000K7198Y', '1002Z0100000000001K1', '1002Z0100000000001K1', '1001A1100000000101AO', '1001A710000000LAB6V9', '~', '~', '1001A710000000LAAY0V', '0001A110000000000CE4', '~', '~', '1001A110000000AW339O', '1001A11000000000T9DI', NULL, NULL, '~', '~', '~', '~', '0001A110000000004GXH', '0001A110000000004GXG', '~', NULL, '~', '~', '~', NULL, '~', NULL, '~', '1001A710000000LAB6V7', '~', '1001A710000000K7198Y', NULL, NULL, NULL, NULL, 0, 5, '2025-01-08 15:28:25', 'ap', '2025-01-10 09:44:06', 4, '史媛媛2', 1, NULL, 1, '2025-01-10 09:44:14');
INSERT INTO AP_PAYITEM
(AGENTRECEIVELOCAL, AGENTRECEIVEPRIMAL, ASSETPACTNO, BANKRELATED_CODE, BANKROLLPROJET, BILLCLASS, BILLDATE, BILLNO, BUSIDATE, BUYSELLFLAG, CASHACCOUNT, CASHITEM, CHECKDIRECTION, CHECKELEMENT, CHECKNO, CHECKTYPE, COMMPAYER, COMMPAYSTATUS, COMMPAYTYPE, CONFERNUM, CONTRACTNO, COORDFLAG, COSTCENTER, CUSTOMER, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DIRECT_CHECKNO, DIRECTION, DR, EQUIPMENTCODE, FACARD, FORCEMONEY, FREECUST, GLOBALAGENTRECEIVELOCAL, GLOBALBALANCE, GLOBALDEBIT, GLOBALNOTAX_DE, GLOBALRATE, GLOBALTAX_DE, GROUPAGENTRECEIVELOCAL, GROUPBALANCE, GROUPDEBIT, GROUPNOTAX_DE, GROUPRATE, GROUPTAX_DE, INNERORDERNO, INVOICENO, ISDISCOUNT, ISFORCE, ISREFUSED, ISURGENT, LOCAL_MONEY_BAL, LOCAL_MONEY_DE, LOCAL_NOTAX_DE, LOCAL_PRICE, LOCAL_TAX_DE, LOCAL_TAXPRICE, MATERIAL, MATERIAL_SRC, MONEY_BAL, MONEY_DE, NOSUBTAX, NOSUBTAXRATE, NOTAX_DE, OBJTYPE, OCCUPATIONMNY, ORDERCUBASDOC, OUTSTORENO, PAUSETRANSACT, PAYACCOUNT, PAYDATE, PAYFLAG, PAYMAN, PAYREASON, PK_BALATYPE, PK_BILLTYPE, PK_CURRTYPE, PK_DEPTID, PK_DEPTID_V, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_JOB, PK_JOBPHASE, PK_ORG, PK_ORG_V, PK_PAYBILL, PK_PAYITEM, PK_PAYTERM, PK_PCORG, PK_PCORG_V, PK_PSNDOC, PK_RECPAYTYPE, PK_SSITEM, PK_SUBJCODE, PK_TRADETYPE, PK_TRADETYPEID, POSTPRICE, POSTPRICENOTAX, POSTQUANTITY, POSTUNIT, PREPAY, PRICE, PRODUCTLINE, PROJECT, PROJECT_TASK, PU_DEPTID, PU_DEPTID_V, PU_ORG, PU_ORG_V, PU_PSNDOC, PURCHASEORDER, QUANTITY_BAL, QUANTITY_CR, QUANTITY_DE, RATE, RECACCOUNT, REFUSE_REASON, ROWNO, ROWTYPE, SCOMMENT, SENDCOUNTRYID, SETT_ORG, SETT_ORG_V, SETTLECURR, SETTLEMONEY, SRC_BILLID, SRC_BILLTYPE, SRC_ITEMID, SRC_TRADETYPE, SUBJCODE, SUPPLIER, TAX_DE, TAXCODEID, TAXNUM, TAXPRICE, TAXRATE, TAXTYPE, TOP_BILLID, TOP_BILLTYPE, TOP_ITEMID, TOP_TERMCH, TOP_TRADETYPE, TRANSERIAL, TS, VATCODE, VENDORVATCODE)
VALUES(0, 0, NULL, NULL, '~', 'fk', '2025-01-09 11:00:23', 'D32021072300035801', '2021-07-23 12:19:18', 2, '~', '~', NULL, '~', '~', '~', '~', NULL, NULL, NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 'N', 1, 0, NULL, '~', 0, '~', 0, 0, 0, 0, 0, 0, 0, 1000000, 1000000, 1000000, 1, 0, NULL, NULL, 'N', NULL, 'N', 'N', 1000000, 1000000, 1000000, 0, 0, 0, '~', '~', 1000000, 1000000, 0, 0, 1000000, 1, 1000000, '~', NULL, 'N', '1001A1100000000F2IQX', NULL, 1, '~', NULL, '1001A1100000005IS4S2', 'F3', '1002Z0100000000001K1', '1001A11000000001D5LJ', '0001A1100000000086CC', '0001A110000000006YPN', '0001A110000000006YPM', '0001A110000000000CE4', NULL, NULL, '0001A110000000006YPN', '0001A110000000006YPM', '1001A110000000GZCBG9', '1001A110000000GZCBGA', '~', '~', '~', '0001A11000000000B79Z', 'GLOBZ300000000000001', NULL, '1001A1100000000IW7LP', 'F3-Cxx-02', '1001A110000000000M0D', NULL, NULL, NULL, '~', 0, 0, '~                   ', '~', '~', '~', '~', '~', '~                   ', '~', NULL, 0, NULL, NULL, 1, '1001A1100000000F2IS6', NULL, 0, NULL, NULL, '0001Z010000000079UJJ', '0001A110000000006YPN', '0001A110000000006YPM', '~', 0, NULL, '~', NULL, '~', '~', '1001A1100000000KKNH2', 0, '1001A110000000AV9PSC', NULL, 0, 0, 1, NULL, '~', NULL, '~', '~', NULL, '2025-01-09 11:01:15', NULL, NULL);
INSERT INTO AP_PAYITEM
(AGENTRECEIVELOCAL, AGENTRECEIVEPRIMAL, ASSETPACTNO, BANKRELATED_CODE, BANKROLLPROJET, BILLCLASS, BILLDATE, BILLNO, BUSIDATE, BUYSELLFLAG, CASHACCOUNT, CASHITEM, CHECKDIRECTION, CHECKELEMENT, CHECKNO, CHECKTYPE, COMMPAYER, COMMPAYSTATUS, COMMPAYTYPE, CONFERNUM, CONTRACTNO, COORDFLAG, COSTCENTER, CUSTOMER, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DIRECT_CHECKNO, DIRECTION, DR, EQUIPMENTCODE, FACARD, FORCEMONEY, FREECUST, GLOBALAGENTRECEIVELOCAL, GLOBALBALANCE, GLOBALDEBIT, GLOBALNOTAX_DE, GLOBALRATE, GLOBALTAX_DE, GROUPAGENTRECEIVELOCAL, GROUPBALANCE, GROUPDEBIT, GROUPNOTAX_DE, GROUPRATE, GROUPTAX_DE, INNERORDERNO, INVOICENO, ISDISCOUNT, ISFORCE, ISREFUSED, ISURGENT, LOCAL_MONEY_BAL, LOCAL_MONEY_DE, LOCAL_NOTAX_DE, LOCAL_PRICE, LOCAL_TAX_DE, LOCAL_TAXPRICE, MATERIAL, MATERIAL_SRC, MONEY_BAL, MONEY_DE, NOSUBTAX, NOSUBTAXRATE, NOTAX_DE, OBJTYPE, OCCUPATIONMNY, ORDERCUBASDOC, OUTSTORENO, PAUSETRANSACT, PAYACCOUNT, PAYDATE, PAYFLAG, PAYMAN, PAYREASON, PK_BALATYPE, PK_BILLTYPE, PK_CURRTYPE, PK_DEPTID, PK_DEPTID_V, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_JOB, PK_JOBPHASE, PK_ORG, PK_ORG_V, PK_PAYBILL, PK_PAYITEM, PK_PAYTERM, PK_PCORG, PK_PCORG_V, PK_PSNDOC, PK_RECPAYTYPE, PK_SSITEM, PK_SUBJCODE, PK_TRADETYPE, PK_TRADETYPEID, POSTPRICE, POSTPRICENOTAX, POSTQUANTITY, POSTUNIT, PREPAY, PRICE, PRODUCTLINE, PROJECT, PROJECT_TASK, PU_DEPTID, PU_DEPTID_V, PU_ORG, PU_ORG_V, PU_PSNDOC, PURCHASEORDER, QUANTITY_BAL, QUANTITY_CR, QUANTITY_DE, RATE, RECACCOUNT, REFUSE_REASON, ROWNO, ROWTYPE, SCOMMENT, SENDCOUNTRYID, SETT_ORG, SETT_ORG_V, SETTLECURR, SETTLEMONEY, SRC_BILLID, SRC_BILLTYPE, SRC_ITEMID, SRC_TRADETYPE, SUBJCODE, SUPPLIER, TAX_DE, TAXCODEID, TAXNUM, TAXPRICE, TAXRATE, TAXTYPE, TOP_BILLID, TOP_BILLTYPE, TOP_ITEMID, TOP_TERMCH, TOP_TRADETYPE, TRANSERIAL, TS, VATCODE, VENDORVATCODE)
VALUES(0, 0, NULL, '015MGJ', '~', 'fk', '2024-01-23 09:21:45', 'D32024012300000069', '2024-01-23 09:21:45', 2, '~', '~', NULL, '~', '~', '~', '~', NULL, NULL, NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', 'N', 1, 0, NULL, '~', 0, '~', 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 0, NULL, NULL, 'N', NULL, 'N', 'N', 0, 1, 1, 1, 0, 1, '~', '~', 0, 1, 0, 0, 1, 1, 0, '~', NULL, 'N', '~', '2025-01-08 15:05:51', 3, '1001A710000000JO5IEV', NULL, '~', 'F3', '1002Z0100000000001K1', '1001A110000000011Y2K', '0001A110000000007UCZ', '0001A110000000004GXH', '0001A110000000004GXG', '0001A110000000000CE4', NULL, NULL, '0001A110000000004GXH', '0001A110000000004GXG', '1001A710000000JX4CMJ', '1001A710000000JX4CMK', '~', '~', '~', '0001A11000000000A262', 'GLOBZ300000000000001', NULL, '1001A1100000000IW7LO', 'F3-Cxx-02', '1001A110000000000M0D', NULL, NULL, NULL, '~', 0, 1, '~                   ', '~', '~', '~', '~', '~', '~                   ', '~', NULL, 0, NULL, 1, 1, '1001A710000000JU64I9', NULL, 0, 0, NULL, '0001Z010000000079UJJ', '~', '0001A110000000004GXG', '1002Z0100000000001K1', 1, '1001A710000000JX4CM9', 'F1', '1001A710000000JX4CMA', 'F1-Cxx-02', '~', '1001A1100000000I8JIL', 0, '1001A110000000AV9PSC', NULL, 1, 0, 1, '1001A710000000JX4CM9', 'F1', '1001A710000000JX4CMA', '~', 'F1-Cxx-02', NULL, '2025-01-08 15:09:45', NULL, NULL);
INSERT INTO AP_PAYITEM
(AGENTRECEIVELOCAL, AGENTRECEIVEPRIMAL, ASSETPACTNO, BANKRELATED_CODE, BANKROLLPROJET, BILLCLASS, BILLDATE, BILLNO, BUSIDATE, BUYSELLFLAG, CASHACCOUNT, CASHITEM, CHECKDIRECTION, CHECKELEMENT, CHECKNO, CHECKTYPE, COMMPAYER, COMMPAYSTATUS, COMMPAYTYPE, CONFERNUM, CONTRACTNO, COORDFLAG, COSTCENTER, CUSTOMER, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DIRECT_CHECKNO, DIRECTION, DR, EQUIPMENTCODE, FACARD, FORCEMONEY, FREECUST, GLOBALAGENTRECEIVELOCAL, GLOBALBALANCE, GLOBALDEBIT, GLOBALNOTAX_DE, GLOBALRATE, GLOBALTAX_DE, GROUPAGENTRECEIVELOCAL, GROUPBALANCE, GROUPDEBIT, GROUPNOTAX_DE, GROUPRATE, GROUPTAX_DE, INNERORDERNO, INVOICENO, ISDISCOUNT, ISFORCE, ISREFUSED, ISURGENT, LOCAL_MONEY_BAL, LOCAL_MONEY_DE, LOCAL_NOTAX_DE, LOCAL_PRICE, LOCAL_TAX_DE, LOCAL_TAXPRICE, MATERIAL, MATERIAL_SRC, MONEY_BAL, MONEY_DE, NOSUBTAX, NOSUBTAXRATE, NOTAX_DE, OBJTYPE, OCCUPATIONMNY, ORDERCUBASDOC, OUTSTORENO, PAUSETRANSACT, PAYACCOUNT, PAYDATE, PAYFLAG, PAYMAN, PAYREASON, PK_BALATYPE, PK_BILLTYPE, PK_CURRTYPE, PK_DEPTID, PK_DEPTID_V, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_JOB, PK_JOBPHASE, PK_ORG, PK_ORG_V, PK_PAYBILL, PK_PAYITEM, PK_PAYTERM, PK_PCORG, PK_PCORG_V, PK_PSNDOC, PK_RECPAYTYPE, PK_SSITEM, PK_SUBJCODE, PK_TRADETYPE, PK_TRADETYPEID, POSTPRICE, POSTPRICENOTAX, POSTQUANTITY, POSTUNIT, PREPAY, PRICE, PRODUCTLINE, PROJECT, PROJECT_TASK, PU_DEPTID, PU_DEPTID_V, PU_ORG, PU_ORG_V, PU_PSNDOC, PURCHASEORDER, QUANTITY_BAL, QUANTITY_CR, QUANTITY_DE, RATE, RECACCOUNT, REFUSE_REASON, ROWNO, ROWTYPE, SCOMMENT, SENDCOUNTRYID, SETT_ORG, SETT_ORG_V, SETTLECURR, SETTLEMONEY, SRC_BILLID, SRC_BILLTYPE, SRC_ITEMID, SRC_TRADETYPE, SUBJCODE, SUPPLIER, TAX_DE, TAXCODEID, TAXNUM, TAXPRICE, TAXRATE, TAXTYPE, TOP_BILLID, TOP_BILLTYPE, TOP_ITEMID, TOP_TERMCH, TOP_TRADETYPE, TRANSERIAL, TS, VATCODE, VENDORVATCODE)
VALUES(0, 0, NULL, '01H5L2', '~', 'fk', '2025-01-08 15:24:35', 'D32025010800000018', '2025-01-08 15:24:35', 2, '~', '~', NULL, '~', '~', '~', '~', NULL, NULL, NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '15.00', '~', '285.00', '~', '~', '~', '~', '~', '1001A11000000002AKXX', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '34.51', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '57ae02f189af4c9cb5b6751dc6ab5e1f', '~', '~', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '电缆4*13', NULL, '~', 'N', 1, 0, NULL, '~', 0, '~', 0, 0, 0, 0, 0, 0, 0, 0, 300, 265.49, 0, 0, NULL, '23423466', 'N', NULL, 'N', 'N', 0, 300, 265.49, 265.48672566, 34.51, 300, '1001A110000000AW339O', '1001A110000000AW339O', 0, 300, 0, 0, 265.49, 1, 0, '1001A710000000K7198Y', NULL, 'N', '~', '2025-01-08 16:44:15', 3, '1001A710000000JO5IEV', NULL, '~', 'F3', '1002Z0100000000001K1', '1001A1100000000101AO', '0001A110000000007S1E', '0001A110000000004GXH', '0001A110000000004GXG', '0001A110000000000CE4', NULL, NULL, '0001A110000000004GXH', '0001A110000000004GXG', '1001A710000000LAB6V0', '1001A710000000LAB6V1', '~', '~', '~', '~', 'GLOBZ300000000000001', NULL, '1001A1100000000IW7LP', 'F3-Cxx-02', '1001A110000000000M0D', NULL, NULL, 1, '1001A11000000000TDOE', 0, 265.48672566, '~                   ', '~', '~', '1001A1100000000101AO', '0001A110000000007S1E', '0001A110000000004GXH', '0001A110000000004GXG', '~', 'CD2025010700104586', 0, NULL, 1, 1, '~', NULL, 0, 0, '未填写请购理由', '0001Z010000000079UJJ', '~', '0001A110000000004GXG', '1002Z0100000000001K1', 300, '1001A710000000LA8WWC', '21', '1001A710000000LA8WWD', '1001A710000000K78GFA', '~', '1001A710000000K7198Y', 0, '1002Z01000000001CNE2', NULL, 300, 13, 1, '1001A710000000LAAY0U', 'F1', '1001A710000000LAAY0V', '~', 'F1-Cxx-02', NULL, '2025-01-10 09:44:25', NULL, NULL);

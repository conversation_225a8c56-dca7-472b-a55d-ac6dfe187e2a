package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "销售订单子表源数据详情")
public class FiSourceSaleOrderDetail extends BaseEntity<FiSourceSaleOrderDetail> {

    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value = "销售订单主表ID")
    private String csaleorderid;

    @ApiModelProperty(value = "销售订单子表ID")
    private String csaleorderbid;

    @ApiModelProperty(value = "行号")
    private String crowno;

    @ApiModelProperty(value = "物料ID")
    private String cmaterialvid;

    @ApiModelProperty(value = "数量")
    private BigDecimal nastnum;

    @ApiModelProperty(value = "报价")
    private BigDecimal nqtorigprice;

    @ApiModelProperty(value = "金额")
    private BigDecimal norigmny;

    @ApiModelProperty(value = "税率")
    private BigDecimal ntaxrate;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal nqtorigtaxprice;

    @ApiModelProperty(value = "含税金额")
    private BigDecimal norigtaxmny;
    
    @ApiModelProperty(value = "项目ID")
    private String cprojectid;

    @ApiModelProperty(value = "自定义项9,备用字段")
    private String vbdef9;
}
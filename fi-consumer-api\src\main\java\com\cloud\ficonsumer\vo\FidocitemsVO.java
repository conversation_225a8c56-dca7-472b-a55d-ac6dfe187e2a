package com.cloud.ficonsumer.vo;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class FidocitemsVO {
    /**
     * SAP凭证ID
     */
    @ApiModelProperty(value="SAP凭证ID")
    private String sapdocid;

    /**
     * 公司代码
     */
    @ApiModelProperty(value="公司代码")
    private String company;

    /**
     * 会计年度
     */
    @ApiModelProperty(value="会计年度")
    private Integer zyear;


    /**
     * 借贷标识
     */
    @ApiModelProperty(value="借贷标识")
    private String crde;

    /**
     * 科目
     */
    @ApiModelProperty(value="科目")
    private String account;

    /**
     * 本位币金额
     */
    @ApiModelProperty(value="本位币金额")
    private BigDecimal amount;

    /**
     * 数量
     */
    @ApiModelProperty(value="数量")
    private String quantity;

    /**
     * 基本计量单位
     */
    @ApiModelProperty(value="基本计量单位")
    private String numtype;

    /**
     * 外币金额
     */
    @ApiModelProperty(value="外币金额")
    private BigDecimal wbamount;

    /**
     * 人民币金额
     */
    @ApiModelProperty(value="人民币金额")
    private BigDecimal rmbamount;

    /**
     * 反记账
     */
    @ApiModelProperty(value="反记账")
    private String negposting;

    /**
     * 凭证分录序号
     */
    @ApiModelProperty(value="凭证分录序号")
    private String flxh;

    /**
     * 银行对账码
     */
    @ApiModelProperty(value="银行对账码")
    private String dzkey;

    /**
     * 抵消业务标识
     */
    @ApiModelProperty(value="抵消业务标识")
    private String ywtype;

    /**
     * 业务协同码1
     */
    @ApiModelProperty(value="业务协同码1")
    private String ywxtkey1;

    /**
     * 业务协同码2
     */
    @ApiModelProperty(value="业务协同码2")
    private String ywxtkey2;

    /**
     * 内部往来-客户
     */
    @ApiModelProperty(value="内部往来-客户")
    private String nbwlkh;

    /**
     * 内部往来-供应商
     */
    @ApiModelProperty(value="内部往来-供应商")
    private String nbwlgys;

    /**
     * 业务日期
     */
    @ApiModelProperty(value="业务日期")
    private String bushaptime;

    /**
     * 辅助核算列表
     */
    @ApiModelProperty(value="辅助核算列表")
    private List<FzhsVO> fzhs;
}

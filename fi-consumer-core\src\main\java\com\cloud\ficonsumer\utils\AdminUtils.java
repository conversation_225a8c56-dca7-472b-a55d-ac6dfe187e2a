package com.cloud.ficonsumer.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cloud.cloud.admin.api.entity.SysDepart;
import com.cloud.cloud.admin.api.feign.RemoteDepartService;
import com.cloud.cloud.common.core.apis.dto.DepartVo;
import com.cloud.cloud.common.core.constant.SecurityConstants;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.cloud.common.security.util.SecurityUtils;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.controller.UserInfoVO;
import com.cloud.ficonsumer.entity.ApiCompare;
import com.cloud.ficonsumer.entity.FiOrgCompare;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.FiOrgCompareService;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

@Slf4j
@UtilityClass
public class AdminUtils {

    /**
     * 封装集团、组织、单位等信息
     *
     * @param userInfoVO
     */
    public void handleOrg(UserInfoVO userInfoVO) {
        //获取用户当前选中的组织
        DepartVo departVo = SecurityUtils.getUserSetOrg();
        if (departVo == null || StrUtil.isEmpty(departVo.getOrgType2())) {
            throw new CheckedException("请配置用户的所属组织 !");
        } else {
            //获得上级组织
            List<SysDepart> supDepartList = AdminUtils.getSupDepartListByOrgCode(departVo.getOrgCode());
            //通过当前登录用户的组织，获取顶级组织
            userInfoVO.setTopOrgDepart(AdminUtils.getTopOrg(supDepartList, departVo.getOrgCode()));
            //通过当前登录用户的组织，获取所属组织（集团）
            userInfoVO.setGroupOrgDepart(AdminUtils.getDepartVoByOrgType(supDepartList, Constants.DepartType.GROUP_ORG));
            //通过当前登录用户的组织，获取所属组织（单位）
            userInfoVO.setOrgDepart(AdminUtils.getDepartVoByOrgType(supDepartList, Constants.DepartType.ORG));
            //通过当前登录用户的组织，获取所属部门
            userInfoVO.setDepart(AdminUtils.getDepartVoByOrgType(supDepartList, Constants.DepartType.DEPART_ORG));
        }
        DepartVo groupOrgDepart = userInfoVO.getGroupOrgDepart();
        AssertUtils.notNull(groupOrgDepart.getId(), "请切换到正确的所属地市平台");
        DepartVo orgDepart = userInfoVO.getOrgDepart();
        AssertUtils.notNull(orgDepart.getId(), "请切换到正确的所属单位");
    }

    /**
     * 获取当前登录单位组织的departCode值
     */
    public String getLoginUserDepartCode() {
        //获取用户当前选中的组织
        DepartVo departVo = SecurityUtils.getUserSetOrg();
        if (departVo == null || StrUtil.isEmpty(departVo.getOrgType2())) {
            throw new CheckedException("请配置用户的所属组织 !");
        } else if(StrUtil.isBlank(departVo.getDepartCode())) {
            throw new CheckedException("请配置用户当前组织属性关联关系");
        }
        return departVo.getDepartCode();
    }

    /**
     * 获取当前登录单位组织的departCode值
     */
    public String getLoginUserGroupDepartCode() {
        DepartVo groupOrgDepart;
        //获取用户当前选中的组织
        DepartVo departVo = SecurityUtils.getUserSetOrg();
        if (departVo == null || StrUtil.isEmpty(departVo.getOrgType2())) {
            throw new CheckedException("请配置用户的所属组织 !");
        } else {
            //获得上级组织
            List<SysDepart> supDepartList = AdminUtils.getSupDepartListByOrgCode(departVo.getOrgCode());
            groupOrgDepart = AdminUtils.getDepartVoByOrgType(supDepartList, Constants.DepartType.GROUP_ORG);
        }
        AssertUtils.notNull(groupOrgDepart.getId(), "请切换到正确的所属地市平台");
        return groupOrgDepart.getDepartCode();
    }

    /**
     * 不为空判断
     *
     * @param fieldValue
     * @return
     */
    public boolean isNotBlank(String fieldValue) {
        if(Objects.isNull(fieldValue) || StringUtils.isBlank(fieldValue) ||
                fieldValue.trim().equals("N/A") || fieldValue.trim().equals("NN/A") ||
                fieldValue.trim().equals("null") || fieldValue.trim().equals("NULL") ||
                fieldValue.trim().equals("～")||fieldValue.trim().equals("~")){
            return false;
        }
        return true;
    }

    /**
     * 通过组织编码,获得上级组织列表
     *
     * @param orgCode
     * @return
     */
    public List<SysDepart> getSupDepartListByOrgCode(String orgCode) {
        if (StrUtil.isBlank(orgCode)) {
            return null;
        }
        RemoteDepartService remoteDepartService = SpringContextHolder.getApplicationContext().getBean(RemoteDepartService.class);
        R<List<SysDepart>> departList = remoteDepartService.getSupDepartListByOrgCode(orgCode, SecurityConstants.FROM_IN);
        AssertUtils.success(departList, "根据组织编码查询上级组织接口失败");
        return AssertUtils.parseArray(departList.getData(), SysDepart.class);
    }

    /**
     * 过滤顶部组织
     *
     * @param supDepartList
     * @return
     */
    private DepartVo getTopOrg(List<SysDepart> supDepartList, String orgCode) {
        DepartVo vo = new DepartVo();
        List<SysDepart> departs = supDepartList.stream()
                .filter(item -> item.getParentId().longValue() == 0
                        && StrUtil.startWith(orgCode, item.getOrgCode())).collect(toList());
        if (CollectionUtil.isNotEmpty(departs)) {
            BeanUtils.copyProperties(departs.get(0), vo);
        }
        return vo;
    }

    /**
     * 根据组织类型2 获取所属的集团组织，产业单位，部门等组织
     *
     * @param departList
     * @param orgTypes
     * @return
     */
    private DepartVo getDepartVoByOrgType(List<SysDepart> departList, List<String> orgTypes) {
        List<SysDepart> departs = departList.stream()
                .filter(item -> orgTypes.contains(item.getOrgType2()))
                .sorted(Comparator.comparing(SysDepart::getOrgCode))
                .collect(toList());
        DepartVo vo = new DepartVo();
        if (CollectionUtil.isNotEmpty(departs)) {
            // 取最后一个，解决产业单位下，又挂产业单位的情况
            BeanUtils.copyProperties(departs.get(departs.size() - 1), vo);
        }
        return vo;
    }


    /**
     * 根据ERP组织编码批量获取组织信息
     *
     * @param erpOrgCodes 组织编码集合
     * @return 组织编码与组织信息的映射
     */
    public Map<String, SysDepart> getDepartByErpOrgCodes(Set<String> erpOrgCodes) {
        try {
            RemoteDepartService remoteDepartService = SpringContextHolder.getApplicationContext()
                    .getBean(RemoteDepartService.class);

            R<List<SysDepart>> result = remoteDepartService.getAll(SecurityConstants.FROM_IN);
            if (result == null || result.getData() == null) {
                throw new CheckedException("获取业+组织全量数据失败");
            }

            return result.getData()
                    .stream()
                    .filter(sysDepart -> StringUtils.isNotEmpty(sysDepart.getDepartCode())
                            && erpOrgCodes.contains(sysDepart.getDepartCode()))
                    .collect(Collectors.toMap(
                            SysDepart::getDepartCode,
                            Function.identity(),
                            (existing, replacement) -> existing) // 处理重复key的情况，保留第一个遇到的值
                    );
        } catch (Exception e) {
            log.error("根据ERP组织编码批量获取组织信息", e);
            return new HashMap<>();
        }
    }

    /**
     * 通过组织编码,获得组织信息
     *
     * @param orgCode
     * @return
     */
    public SysDepart getDepartByOrgCode(String orgCode) {
        if (null == orgCode) {
            return null;
        }
        RemoteDepartService remoteDepartService = SpringContextHolder.getApplicationContext().getBean(RemoteDepartService.class);
        R<List<SysDepart>> sysDepartR = remoteDepartService.getByCodeInner(orgCode, SecurityConstants.FROM_IN);
        AssertUtils.notNull(sysDepartR, "根据组织编码查询组织接口失败");
        if (CollectionUtil.isEmpty(sysDepartR.getData())) {
            throw new CheckedException("根据组织编码查询组织接口失败");
        }
        return sysDepartR.getData().get(0);
    }

    /**
     * 根据erp主键获取管控主键
     *
     * @param pkOrg
     * @return
     */
    public String getPkControlOrgByErpOrg(String tableName, String pkOrg) {
        SysDepart sysDepart = AdminUtils.getDepartByOrgCode(pkOrg);
        if (StringUtils.isBlank(sysDepart.getErpPkOrg())) {
            throw new CheckedException("当前登录组织信息不全");
        }
        String controlPkOrg = "";
        if (StringUtils.isBlank(pkOrg)) {
            throw new CheckedException("当前组织信息维护不全");
        } else {
            ApiCompareCache apiCompareCache = SpringContextHolder.getApplicationContext().getBean(ApiCompareCache.class);
            ApiCompare compareValue = apiCompareCache.getDictionaryValue(tableName, "pk_org", sysDepart.getErpPkOrg());
            controlPkOrg = Objects.isNull(compareValue) ? null : compareValue.getTargetSystemId();
        }
        if (StringUtils.isBlank(controlPkOrg)) {
            throw new CheckedException("对照表中未配置单位");
        }
        return controlPkOrg;
    }

    /**
     * 字符串截取
     *
     * @param input
     * @param maxLength
     * @return
     */
    public String truncateString(String input, int maxLength) {
        if (null == input) {
            return input;
        }
        if (input.length() <= maxLength) {
            return input;
        } else {
            return input.substring(0, maxLength);
        }
    }

    /**
     * 通过erp组织主键,获得组织信息
     *
     * @param pkOrg
     * @return
     */
    public SysDepart getDepartByPkOrg(String pkOrg) {
        if (null == pkOrg) {
            return null;
        }
        RemoteDepartService remoteDepartService = SpringContextHolder.getApplicationContext().getBean(RemoteDepartService.class);
        SysDepart sysDepart = new SysDepart();
        sysDepart.setErpPkOrg(pkOrg);
        R<List<SysDepart>> sysDepartR = remoteDepartService.getAll(sysDepart, SecurityConstants.FROM_IN);
        AssertUtils.notNull(sysDepartR, "根据erp组织主键查询组织接口失败");
        if (CollectionUtil.isEmpty(sysDepartR.getData())) {
            throw new CheckedException("根据erp组织主键查询组织接口失败");
        }
        return sysDepartR.getData().get(0);
    }

    /**
     * 通过组织id,获得组织信息
     *
     * @param orgId
     * @return
     */
    public SysDepart getDepartByOrgId(Long orgId) {
        if (null == orgId) {
            return null;
        }
        RemoteDepartService remoteDepartService = SpringContextHolder.getApplicationContext().getBean(RemoteDepartService.class);
        R<SysDepart> sysDepartR = remoteDepartService.getByIdInner(orgId, SecurityConstants.FROM_IN);
        AssertUtils.notNull(sysDepartR, "根据组织id查询组织接口失败");
        return sysDepartR.getData();
    }


    /**
     * 处理组织查询是，传入的orgCode转换为ERP组织主键
     * 如果是全局组织或组织控制未启用则返回null
     *
     * @param orgCode 组织编码
     * @return ERP组织主键
     */
    public String processToPkOrg(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            throw new CheckedException("请传当前登录组织编码");
        }

        if (!YgConfig.orgEnable) {
            return null;
        }

        SysDepart departVo = AdminUtils.getDepartByOrgCode(orgCode);
        if (departVo.getOrgCode().equals(YgConfig.allOrgCode)) {
            return null;
        }

        String pkOrg = getDmMapper().getPkOrgByCode(departVo.getDepartCode());
        if (StringUtils.isBlank(pkOrg)) {
            throw new CheckedException("当前登录组织信息不全");
        }

        return pkOrg;
    }

    /**
     * 处理组织查询是，传入的orgCode转换为组织主键
     * 如果是全局组织或组织控制未启用则返回null
     *
     * @param orgCode 组织编码
     * @return 组织主键
     */
    public Long processToOrgId(String orgCode) {
        if (StringUtils.isBlank(orgCode)) {
            throw new CheckedException("请传当前登录组织编码");
        }

        if (!YgConfig.orgEnable) {
            return null;
        }

        SysDepart departVo = AdminUtils.getDepartByOrgCode(orgCode);
        if (departVo.getOrgCode().equals(YgConfig.allOrgCode)) {
            return null;
        }

        return departVo.getId();
    }

    private DmMapper getDmMapper() {
        return SpringContextHolder.getBean(DmMapper.class);
    }

    public String getGroupCode(String group) {
        FiOrgCompareService fiOrgCompareService = SpringContextHolder.getApplicationContext().getBean(FiOrgCompareService.class);
        FiOrgCompare fiOrgCompare = fiOrgCompareService.getById(Long.valueOf(group));
        if(null == fiOrgCompare) {
            throw new CheckedException("集团主键入参错误");
        }else if(fiOrgCompare.getLevel().equals("2")) {
            return "";
        }
        return fiOrgCompare.getGroupCode();
    }
}

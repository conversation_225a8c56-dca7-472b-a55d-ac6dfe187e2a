package com.cloud.ficonsumer.listener;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiSourceOrderDTO;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceOrderService;
import com.cloud.ficonsumer.vo. FiSourceOrderExportVO;
import com.cloud.ficonsumer.vo.FiSourceOrderQueryVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/9.
 */
@Slf4j
public class FiSourceOrderExportListener extends AbstractExportListener<FiSourceOrderExportVO> {

    private final FiSourceOrderService fiSourceOrderService;
    private final FiSourceOrderQueryVO queryDTO;
    private final Page<FiSourceOrderDTO> page;
    private static final int PAGE_SIZE = 5000;

    public FiSourceOrderExportListener(UUID id, FiSourceOrderService fiSourceOrderService,
                                       Page<FiSourceOrderDTO> page,
                                       FiSourceOrderQueryVO queryDTO) {
        super(id, "采购订单导出");
        this.page = page;
        this.fiSourceOrderService = fiSourceOrderService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiSourceOrderExportVO> doHandleData() {
        List<FiSourceOrderDTO> vos = new ArrayList<>();
        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiSourceOrderDTO> pageResult = fiSourceOrderService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiSourceOrderDTO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiSourceOrderDTO> pageResult;
            do {
                pageResult = fiSourceOrderService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }
        List<FiSourceOrderExportVO> result = BeanUtil.copyToList(vos, FiSourceOrderExportVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}



package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.FiConvertReceivableDTO;
import com.cloud.ficonsumer.dto.FiConvertReceivableDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceReceivableDTO;
import com.cloud.ficonsumer.dto.FiSourceReceivableDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceReceivable;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceReceivableService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceReceivableQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 项目应收单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:25
 */
@RestController
@AllArgsConstructor
@RequestMapping("/receivable" )
@Api(value = "receivable", tags = "项目应收单源数据主表管理")
public class FiSourceReceivableController {

    private final  FiSourceReceivableService fiSourceReceivableService;

    /**
     * 报错重推
     *
     * @return
     */
    @ApiOperation(value = "报错重推", notes = "报错重推")
    @GetMapping("/pushAgain/{pkRecbill}")
    public R<Boolean> pushAgain(@PathVariable("pkRecbill") String pkRecbill) {
        return R.ok(fiSourceReceivableService.pushAgain(pkRecbill));
    }

    /**
     * 转换前分页查询
     *
     * @return
     */
    @ApiOperation(value = "转换前分页查询", notes = "转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiSourceReceivableDTO>> beforeConvertDataPage(Page page, FiSourceReceivableQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        return R.ok(fiSourceReceivableService.beforeConvertDataPage(page,queryVO));
    }

    /**
     * 转换前数据
     * @return
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage" )
    public R<Page<FiSourceReceivableDetailDTO>> getBeforeConvertDataPage(Page page, FiSourceReceivableQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getPkRecbill(), "主键不能为空");
        return R.ok(fiSourceReceivableService.getBeforeConvertDataPage(page,queryVO));
    }

    /**
     * 主体表转换后单条字段
     * @return
     */
    @ApiOperation(value = "主体表转换后单条字段", notes = "主体表转换后单条字段")
    @GetMapping("/getConvertList" )
    public R<List<FiConvertReceivableDTO>> getConvertList(String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceReceivableService.getConvertList(pk));
    }
    /**
     * 获取转换后详情分页数据
     * @return
     */
    @ApiOperation(value = "获取转换后详情分页数据", notes = "获取转换后详情分页数据")
    @GetMapping("/getConvertData" )
    public R<Page<FiConvertReceivableDetailDTO>> getConvertData(Page page, String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceReceivableService.getConvertData(page,pk));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result = fiSourceReceivableService.update(Wrappers.<FiSourceReceivable>lambdaUpdate()
                .set(FiSourceReceivable::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiSourceReceivable::getPushMsg, "成功")
                .in(FiSourceReceivable::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiSourceReceivableService.pushAgainBatch(queryVO));
    }
}

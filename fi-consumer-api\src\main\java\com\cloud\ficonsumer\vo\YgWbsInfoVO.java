package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "WBS信息")
public class YgWbsInfoVO {
    @ApiModelProperty(value = "WBS")
    private String wbsElement;
    
    @ApiModelProperty(value = "WBS描述")
    private String wbsDesc;
    
    @ApiModelProperty(value = "成本金额")
    private BigDecimal reimbAmt;
    
    @ApiModelProperty(value = "应付金额")
    private BigDecimal talInvAmt;
}
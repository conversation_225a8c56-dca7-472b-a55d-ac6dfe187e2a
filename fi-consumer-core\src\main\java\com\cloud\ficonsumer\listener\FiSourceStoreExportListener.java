package com.cloud.ficonsumer.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiSourceStoreDTO;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceStoreService;
import com.cloud.ficonsumer.vo.*;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
public class FiSourceStoreExportListener extends AbstractExportListener<FiSourceStoreExportVO> {



    private final FiSourceStoreService fiSourceStoreService;
    private final FiSourceStoreQueryVO queryDTO;
    private final Page<FiSourceStoreVO> page;
    private static final int PAGE_SIZE = 1000;

    public FiSourceStoreExportListener(UUID id,
                                       FiSourceStoreService fiSourceStoreService,
                                       Page<FiSourceStoreVO> page,
                                       FiSourceStoreQueryVO queryDTO) {
        super(id, "采购应付转换前数据");
        this.page = page;
        this.fiSourceStoreService = fiSourceStoreService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiSourceStoreExportVO> doHandleData() {
        List<FiSourceStoreDTO> vos = new ArrayList<>();

        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiSourceStoreDTO> pageResult = fiSourceStoreService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiSourceStoreDTO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiSourceStoreDTO> pageResult;
            do {
                pageResult = fiSourceStoreService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }

        List<FiSourceStoreExportVO> result = BeanUtil.copyToList(vos, FiSourceStoreExportVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}
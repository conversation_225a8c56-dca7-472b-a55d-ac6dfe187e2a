package com.cloud.ficonsumer.utils;

import cn.hutool.core.io.IoUtil;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cloud.cloud.common.core.exception.CheckedException;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/10/8 10:12
 */
@Slf4j
@UtilityClass
public class ExcelUtils {
//    @Autowired
//    private OssTemplate ossTemplate;
//    @Autowired
//    private OssProperties ossProperties;
//    @Autowired
//    private RedisTemplate redisTemplate;

    public static final String EXCEL_IMPORT = "excelImport:";
	public static final String EXCEL_EXPORT = "excelExport:";

    /**
     * 下载模板
     *
     * @param inputStream
     * @param fileName
     * @param response
     * @throws Exception
     */
    public void downloadExcel(InputStream inputStream, String fileName, HttpServletResponse response) throws Exception {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        IoUtil.copy(inputStream, response.getOutputStream());
    }

    /**
     *获取模板流
     * @param fileName
     * @param response
     * @return
     */
    public OutputStream getOutputStream(String fileName, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf8");
            String enCodefileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + enCodefileName + ".xlsx");
            return response.getOutputStream();
        } catch (IOException e) {
            throw new CheckedException("导出excel表格失败!", e);
        }
    }

    /**
     * 检查文件
     *
     * @param file
     */
    public static void checkFile(MultipartFile file) {
        //判断文件是否存在
        if (null == file) {
            throw new CheckedException("excel文件不能为空");
        }
        //获得文件名
        String fileName = file.getOriginalFilename();
        //判断文件是否是excel文件
        if (!fileName.endsWith(ExcelTypeEnum.XLS.getValue()) && !fileName.endsWith(ExcelTypeEnum.XLSX.getValue())) {
            throw new CheckedException("请上传正确的excel文件");
        }
    }

    /**
     * 对象不为null
     *
     * @param obj
     * @param msg
     */
    public void notNull(Object obj, String msg) {
        if (obj == null) {
            throw new ExcelAnalysisException(msg);
        }
    }

    /**
     * 字符串不为null
     *
     * @param str
     * @param msg
     */
    public void notBlank(String str, String msg) {
        if (StringUtils.isBlank(str)) {
            throw new ExcelAnalysisException(msg);
        }
    }

    /**
     * 校验数据
     *
     * @param obj
     * @param validator
     */
    public void validate(Object obj, Validator validator) {
        Set<ConstraintViolation<Object>> violations = validator.validate(obj);
        for (ConstraintViolation<Object> violation : violations) {
            throw new ExcelAnalysisException(violation.getMessage());
        }
    }

}

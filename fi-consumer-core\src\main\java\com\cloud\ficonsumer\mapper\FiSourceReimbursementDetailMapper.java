

package com.cloud.ficonsumer.mapper;

import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.entity.FiSourceReimbursementDetail;
import com.cloud.ficonsumer.vo.BillStatisticsVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 通用报销单详情源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:40
 */
@Mapper
public interface FiSourceReimbursementDetailMapper extends CloudBaseMapper<FiSourceReimbursementDetail> {

    /**
     * 统计某一集团下的组织的成功率
     * @param pkGroup
     * @return
     */
    @Select("<script>select COUNT(1) AS sumNum, " +
            "    COUNT(CASE WHEN push_status != 3 THEN 1 END) AS failNum, " +
            "    COUNT(CASE WHEN push_status = 3 THEN 1 END) AS successNum " +
            "from fi_source_reimbursement " +
            "where del_flag=0 and pk_org in (select pk_org from fi_org_compare where pk_group = #{pkGroup} and del_flag =0) </script>")
    BillStatisticsVO getStatisticsByGroup(@Param("pkGroup") String pkGroup);
}

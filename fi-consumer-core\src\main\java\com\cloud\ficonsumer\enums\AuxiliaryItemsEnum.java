package com.cloud.ficonsumer.enums;

/**
 * 单据枚举
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
public enum AuxiliaryItemsEnum {

    DEF1("def1", "部门"),
    DEF2("def2", "银行账户"),
    DEF3("def3", "供应商档案"),
    DEF4("def4", "应收票据"),
    DEF5("def5", "客户档案"),
    DEF6("def6", "融资类型"),
    DEF7("def7", "增值税税码税率"),
    DEF8("def8", "税项"),
    DEF9("def9", "往来款性质"),
    DEF10("def10", "筹融资合同"),
    DEF11("def11", "应付债券"),
    DEF12("def12", "成本大类"),
    DEF13("def13", "项目"),
    DEF14("def14", "费用明细"),
    DEF15("def15", "房地产业务"),
    DEF16("def16", "内部订单"),
    DEF17("def17", "现金流量项目"),
    DEF18("def18", "产品服务"),
    DEF19("def19", "电能类型"),
    DEF20("def20", "人工福利薪酬"),
    DEF21("def21", "物业分公司专用"),
    DEF22("def22", "项目合同"),
    DEF23("def23", "业务线条"),
    DEF24("def24", "工单"),
    DEF25("def25", "人员档案"),
    DEF26("def26", "应付票据"),
    DEF27("def27", "资金拨付项目"),
    DEF28("def28", "长期借款合同"),
    DEF29("def29", "集团账户业务"),
    DEF30("def30", "采购订单"),
    DEF31("def31", "短期借款合同"),
    DEF32("def32", "市场成员"),
    DEF33("def33", "循环借款合同"),
    DEF34("def34", "外币借款合同"),
    ;
    private String code;
    private String name;

    AuxiliaryItemsEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code获取枚举类
     *
     * @param code
     * @return
     */
    public static AuxiliaryItemsEnum getByCode(Integer code) {
        for (AuxiliaryItemsEnum en : values()) {
            if (code.equals(en.getCode())) {
                return en;
            }
        }
        return null;
    }

    /**
     * 根据name获取枚举类
     *
     * @param name
     * @return
     */
    public static AuxiliaryItemsEnum getByName(String name) {
        for (AuxiliaryItemsEnum en : values()) {
            if (name.equals(en.getName())) {
                return en;
            }
        }
        return null;
    }

}

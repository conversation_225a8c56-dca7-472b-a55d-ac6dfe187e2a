package com.cloud.ficonsumer.pupayable;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.builder.YgInvoiceCheckVOBuilder;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import com.cloud.ficonsumer.vo.YgInvoiceCheckVO;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;

import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class PuPayablePushTest extends BasePuPayableTest {

    @SpyBean
    private ApiCompareCache apiCompareCache;

    @SpyBean
    private ApiCompareService apiCompareService;

    @SpyBean
    private YgInvoiceCheckVOBuilder ygInvoiceCheckVOBuilder;

    private String ygSuccessResp;
    private String ygFailResp;
    private static MockedStatic<YgUtil> mockedYgUtil;

    @BeforeAll
    public void initMockData() throws Exception {
        // 初始化响应数据
        ygSuccessResp = new String(Files.readAllBytes(new ClassPathResource("YgSuccessResp.json").getFile().toPath()), StandardCharsets.UTF_8);
        ygFailResp = new String(Files.readAllBytes(new ClassPathResource("YgFailResp.json").getFile().toPath()), StandardCharsets.UTF_8);

        // mock YgUtil
        mockedYgUtil = mockStatic(YgUtil.class);
        
        // Mock getTurnByType 方法调用
        doReturn("MOCK_Turn").when(apiCompareService).getTurnByType(anyString(), anyString());
    }

    @BeforeEach
    public void sync() {
        syncErpToMysql();
    }

    @Test
    public void testPushSuccess() {
        String pkPayablebill = "1005A1100000000EV72M";
        
        // 配置mock响应
        mockedYgUtil.when(() -> YgUtil.sendToYg(any(), any())).thenReturn(ygSuccessResp);

        // Mock build方法返回一个空的VO对象
        YgInvoiceCheckVO mockVO = new YgInvoiceCheckVO();
        doReturn(mockVO).when(ygInvoiceCheckVOBuilder).build(any(), any());

        // 执行测试结果
        boolean result = fiSourcePuPayableService.push(pkPayablebill);

        // 验证结果
        assertTrue(result, "推送成功");

        // 验证数据库状态
        FiSourcePuPayable updatedSource = fiSourcePuPayableService.getOne(Wrappers.<FiSourcePuPayable>lambdaQuery()
                .eq(FiSourcePuPayable::getPkPayablebill, pkPayablebill));
        assertEquals("0", updatedSource.getIntegrationStatus(), "集成状态应为成功");
        assertEquals(PushStatusEnum.PUSH_SUCCESS.getCode(), updatedSource.getPushStatus(), "推送状态应为成功");
        assertEquals(1L, updatedSource.getReadiness(), "readiness 值不正确");

        // 验证平台日志
        List<PlatFormLogVO> logs = dmMapper.selectPlatFormLog(pkPayablebill, null);
        assertFalse(logs.isEmpty(), "平台日志为空");
        assertEquals(Constants.SUCCESS, logs.get(0).getCode(), "日志状态应为成功");
        assertEquals(updatedSource.getPkTradetype(), logs.get(0).getPkBillType(), "日志单据类型不正确");
    }

    @Test
    public void testConvertFail() {
        String pkPayablebill = "1005A1100000000EV72M";

        // Mock转换异常
        doThrow(new CheckedException("采购应付集成信息转换失败"))
                .when(apiCompareCache).convertData(any(), any(), any());

        // 执行测试并验证异常
        CheckedException exception = assertThrows(CheckedException.class,
                () -> fiSourcePuPayableService.push(pkPayablebill));

        // 验证结果
        assertTrue(exception.getMessage().contains("采购应付集成信息转换失败"));

        // 验证数据库状态
        FiSourcePuPayable source = fiSourcePuPayableService.getOne(Wrappers.<FiSourcePuPayable>lambdaQuery()
                .eq(FiSourcePuPayable::getPkPayablebill, pkPayablebill));
        assertEquals("1", source.getIntegrationStatus());
        assertEquals(PushStatusEnum.CONVERT_FAIL.getCode(), source.getPushStatus());
        assertNotNull(source.getPushMsg());
    }

    @Test
    public void testPushFail() {
        String pkPayablebill = "1005A1100000000EV72M";

        // 配置mock响应
        mockedYgUtil.when(() -> YgUtil.sendToYg(any(), any())).thenReturn(ygFailResp);

        // Mock build方法返回一个空的VO对象
        YgInvoiceCheckVO mockVO = new YgInvoiceCheckVO();
        doReturn(mockVO).when(ygInvoiceCheckVOBuilder).build(any(), any());

        // 执行测试并验证异常
        CheckedException exception = assertThrows(CheckedException.class,
                () -> fiSourcePuPayableService.push(pkPayablebill));

        // 验证结果
        assertTrue(exception.getMessage().contains("响应失败，响应结果: "));

        // 验证数据库状态
        FiSourcePuPayable updatedSource = fiSourcePuPayableService.getOne(Wrappers.<FiSourcePuPayable>lambdaQuery()
                .eq(FiSourcePuPayable::getPkPayablebill, pkPayablebill));
        assertEquals("1", updatedSource.getIntegrationStatus());
        assertEquals(PushStatusEnum.PUSH_FAIL.getCode(), updatedSource.getPushStatus());

        // 验证平台日志
        List<PlatFormLogVO> logs = dmMapper.selectPlatFormLog(pkPayablebill, null);
        assertFalse(logs.isEmpty());
        assertEquals(Constants.FAIL, logs.get(0).getCode());
        assertEquals(updatedSource.getPkTradetype(), logs.get(0).getPkBillType());
        assertTrue(logs.get(0).getMsg().contains("响应失败，响应结果: "));
    }
}

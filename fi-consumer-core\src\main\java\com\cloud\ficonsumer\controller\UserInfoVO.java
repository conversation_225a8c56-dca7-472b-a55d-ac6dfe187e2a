package com.cloud.ficonsumer.controller;

import com.cloud.cloud.admin.api.entity.SysRole;
import com.cloud.cloud.common.core.apis.dto.DepartVo;
import com.cloud.cloud.admin.api.dto.UserInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "当前登录用户的信息")
public class UserInfoVO {

    @ApiModelProperty("用户信息（用户基本信息,权限标识集合,角色标识集合）")
    private UserInfo userInfo;

    @ApiModelProperty("用户信息的角色详情")
    private List<SysRole>  userRoleList;

    @ApiModelProperty("用户所属集团（顶级组织）")
    private DepartVo TopOrgDepart;

    @ApiModelProperty("用户所属集团（地市平台）")
    private DepartVo groupOrgDepart;

    @ApiModelProperty("用户所属组织（集团本部、业务单元）")
    private DepartVo orgDepart;

    @ApiModelProperty("用户所属部门")
    private DepartVo depart;
}

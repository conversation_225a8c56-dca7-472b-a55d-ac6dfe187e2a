package com.cloud.ficonsumer.mapper;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.ficonsumer.BaseIntegrationTest;
import com.cloud.ficonsumer.dto.FiSourcePuPayableQueryDTO;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.vo.FiSourcePuPayableVO;
import org.junit.jupiter.api.BeforeAll;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;

import javax.sql.DataSource;
import java.util.List;

public class FiSourcePuPayableMapperTest extends BaseIntegrationTest {

    @SpyBean
    DmMapper dmMapper;

    @SpyBean
    ApiProjectService apiProjectService;

    @Autowired
    private FiSourcePuPayableMapper fiSourcePuPayableMapper;

    @BeforeAll
    public void setup() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);

        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator erpPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("pupayable/schema-oracle.sql"),
                    new ClassPathResource("pupayable/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(erpPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        DynamicDataSourceContextHolder.push("apiexhcnage");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("pupayable/schema-mysql.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 同步ERP采购应付到MySQL
        List<FiSourcePuPayableVO> fiSourcePuPayableList = dmMapper.listPuPayable(new FiSourcePuPayableQueryDTO());
        for (FiSourcePuPayableVO fiSourcePuPayable : fiSourcePuPayableList) {
            apiProjectService.processPuPayable(fiSourcePuPayable);
        }

        // 将fi_source_pu_payable表return_status修改为2
        JdbcTemplate jdbcTemplate = new JdbcTemplate(dynamicDataSource);
        jdbcTemplate.execute("UPDATE fi_source_pu_payable SET return_status = '2'");
    }
}
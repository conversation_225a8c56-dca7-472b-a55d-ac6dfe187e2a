CREATE TABLE `fi_source_pu_paybill`
(
    `id`                 BIGINT      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_settlement`      VARCHAR(64) NOT NULL COMMENT '结算单主键',
    `pk_org`             VARCHAR(64)    DEFAULT NULL COMMENT '财务组织',
    `pk_billoperator`    VARCHAR(64)    DEFAULT NULL COMMENT '业务单据录入人',
    `primal`             DECIMAL(18, 2) DEFAULT NULL COMMENT '原币金额',
    `expense_type`       VARCHAR(64)    DEFAULT NULL COMMENT '费用类型',
    `pk_tradetype`       VARCHAR(64)    DEFAULT NULL COMMENT '应付类型编码',
    `integration_status` VARCHAR(32)    DEFAULT NULL COMMENT '集成状态',
    `push_status`        INT            DEFAULT NULL COMMENT '推送状态 4:推送中、1:转换失败、2:推送失败、3:推送成功、5:推送财务失败',
    `push_msg`           VARCHAR(1024)  DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`         VARCHAR(255)   DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      VARCHAR(32)    DEFAULT NULL COMMENT '返回同步状态',
    `try_number`         BIGINT         DEFAULT NULL COMMENT '重试次数',
    `readiness`          BIGINT         DEFAULT NULL COMMENT '0 未就绪 1 就绪',
    `back_tag`           varchar(1)     DEFAULT 'N' COMMENT '是否退回重传标识，Y-是，N-否；标识默认为N，回退成功后，更新为Y；单据集成成功后，重置为N',
    `create_by`          varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购付款源数据详情';

CREATE TABLE `fi_source_pu_paybill_detail`
(
    `id`            BIGINT      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_settlement` VARCHAR(64) NOT NULL COMMENT '结算单主键',
    `pk_deptid_v`   VARCHAR(64)    DEFAULT NULL COMMENT '部门',
    `prepay`        VARCHAR(32)    DEFAULT NULL COMMENT '付款性质',
    `memo`          VARCHAR(255)   DEFAULT NULL COMMENT '摘要',
    `project`       VARCHAR(64)    DEFAULT NULL COMMENT '项目',
    `contract_no`   VARCHAR(64)    DEFAULT NULL COMMENT '合同号',
    `pk_recpaytype` VARCHAR(64)    DEFAULT NULL COMMENT '付款业务类型',
    `supplier`      VARCHAR(128)   DEFAULT NULL COMMENT '供应商',
    `oppaccount`    VARCHAR(64)    DEFAULT NULL COMMENT '对方账号',
    `oppaccname`    VARCHAR(128)   DEFAULT NULL COMMENT '对方账户户名',
    `pk_oppbank`    VARCHAR(64)    DEFAULT NULL COMMENT '对方银行',
    `pay`           decimal(28, 8) DEFAULT NULL COMMENT '付款原币金额',
    `create_by`     varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`     varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`   datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`      bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购付款明细源数据详情';

CREATE TABLE `fi_convert_pu_paybill`
(
    `id`                 BIGINT      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_settlement`      VARCHAR(64) NOT NULL COMMENT '结算单主键',
    `pk_org`             VARCHAR(64)    DEFAULT NULL COMMENT '财务组织',
    `pk_billoperator`    VARCHAR(64)    DEFAULT NULL COMMENT '业务单据录入人',
    `primal`             DECIMAL(18, 2) DEFAULT NULL COMMENT '原币金额',
    `expense_type`       VARCHAR(64)    DEFAULT NULL COMMENT '费用类型',
    `pk_tradetype`       VARCHAR(64)    DEFAULT NULL COMMENT '应付类型编码',
    `integration_status` VARCHAR(32)    DEFAULT NULL COMMENT '集成状态',
    `push_status`        INT            DEFAULT NULL COMMENT '推送状态 4:推送中、1:转换失败、2:推送失败、3:推送成功、5:推送财务失败',
    `push_msg`           VARCHAR(1024)  DEFAULT NULL COMMENT '推送成功失败信息',
    `return_msg`         VARCHAR(255)   DEFAULT NULL COMMENT '返回同步消息',
    `return_status`      VARCHAR(32)    DEFAULT NULL COMMENT '返回同步状态',
    `try_number`         BIGINT         DEFAULT NULL COMMENT '重试次数',
    `readiness`          BIGINT         DEFAULT NULL COMMENT '0 未就绪 1 就绪',
    `back_tag`           varchar(1)     DEFAULT 'N' COMMENT '是否退回重传标识，Y-是，N-否；标识默认为N，回退成功后，更新为Y；单据集成成功后，重置为N',
    `create_by`          varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`          varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`        datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`           bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购付款转换数据详情';

CREATE TABLE `fi_convert_pu_paybill_detail`
(
    `id`            BIGINT      NOT NULL AUTO_INCREMENT COMMENT '主键',
    `pk_settlement` VARCHAR(64) NOT NULL COMMENT '结算单主键',
    `pk_deptid_v`   VARCHAR(64)    DEFAULT NULL COMMENT '部门',
    `prepay`        VARCHAR(32)    DEFAULT NULL COMMENT '付款性质',
    `memo`          VARCHAR(255)   DEFAULT NULL COMMENT '摘要',
    `project`       VARCHAR(64)    DEFAULT NULL COMMENT '项目',
    `contract_no`   VARCHAR(64)    DEFAULT NULL COMMENT '合同号',
    `pk_recpaytype` VARCHAR(64)    DEFAULT NULL COMMENT '付款业务类型',
    `supplier`      VARCHAR(128)   DEFAULT NULL COMMENT '供应商',
    `oppaccount`    VARCHAR(64)    DEFAULT NULL COMMENT '对方账号',
    `oppaccname`    VARCHAR(128)   DEFAULT NULL COMMENT '对方账户户名',
    `pk_oppbank`    VARCHAR(64)    DEFAULT NULL COMMENT '对方银行',
    `pay`           decimal(28, 8) DEFAULT NULL COMMENT '付款原币金额',
    `create_by`     varchar(50)    DEFAULT NULL COMMENT '创建人',
    `create_time`   datetime       DEFAULT CURRENT_TIMESTAMP COMMENT '创建日期',
    `update_by`     varchar(50)    DEFAULT NULL COMMENT '更新人',
    `update_time`   datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新日期',
    `del_flag`      bigint(32)     DEFAULT '0' COMMENT '是否删除 0: 正常 ',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='采购付款明细转换数据详情';
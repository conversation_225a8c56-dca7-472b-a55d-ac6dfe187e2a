server:
  port: 10001

spring:
  profiles:
    active: dev
  shardingsphere:
    enabled: false

#默认关闭seata
seata:
  enabled: false
---

spring:
  application:
    name: @artifactId@-dev
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS}
        namespace: ${NACOS_NAMESPACE}
        metadata:
          group: cgroup
          version: 3.8.2-dev
        group: ${NACOS_REG_GROUP:DEFAULT_GROUP}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        shared-configs: application-prod.${spring.cloud.nacos.config.file-extension}
        namespace: ${NACOS_NAMESPACE}
        group: ${NACOS_CFG_GROUP:DEFAULT_GROUP}
        context-path: ${NACOS_CONTEXTPATH:}
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
  config:
    activate:
      on-profile: dev
---
#nacos:
#  server-addr: ${NACOS}
spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS}
        namespace: ${NACOS_NAMESPACE}
        metadata:
          group: cgroup
          version: 3.8.2
        group: ${NACOS_REG_GROUP:DEFAULT_GROUP}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        shared-configs: application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        namespace: ${NACOS_NAMESPACE}
        group: ${NACOS_CFG_GROUP:DEFAULT_GROUP}
        context-path: ${NACOS_CONTEXTPATH:}
      username: ${NACOS_USERNAME}
      password: ${NACOS_PASSWORD}
  config:
    activate:
      on-profile: prod
cloud:
  #  xsequence:
  #    uid:
  #      enabled: true
  log:
    request:
      enabled: false
      level: none

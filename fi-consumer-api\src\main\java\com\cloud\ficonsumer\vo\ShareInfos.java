package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/2/28.
 */
@Data
public class ShareInfos {

    /**
     * 项目名称
     */
    @ApiModelProperty(value="项目名称")
    private String prjName;

    /**
     * 明细费用类型编码
     */
    @ApiModelProperty(value="明细费用类型编码")
    private String detCosTypeCode;

    /**
     * 项目
     */
    @ApiModelProperty(value="项目")
    private String tProject;

    /**
     * 费用类型
     */
    @ApiModelProperty(value="费用类型")
    private String cosType;

    /**
     * 可分摊金额
     */
    @ApiModelProperty(value="可分摊金额")
    private BigDecimal avableAmountToShare;

    /**
     * 成本中心
     */
    @ApiModelProperty(value="成本中心")
    private String costCenter;

    /**
     * 明细费用类型
     */
    @ApiModelProperty(value="明细费用类型")
    private String detCosType;

    /**
     * 使用性质
     */
    @ApiModelProperty(value="使用性质")
    private String natureUse;

    /**
     * 分摊金额
     */
    @ApiModelProperty(value="分摊金额")
    private BigDecimal amountToShare;

    /**
     * 成本中心所属单位
     */
    @ApiModelProperty(value="成本中心所属单位")
    private String costUnit;

    /**
     * 比例
     */
    @ApiModelProperty(value="比例")
    private BigDecimal proport;

}

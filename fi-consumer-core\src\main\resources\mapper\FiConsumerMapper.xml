<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloud.ficonsumer.mapper.FiConsumerMapper">
    <!-- 统计某一集团下的组织的成功率 -->
    <select id="getStatisticsByGroup" resultType="com.cloud.ficonsumer.vo.BillStatisticsVO">
        SELECT COUNT(1)                                     AS sumNum,
               COUNT(CASE WHEN push_status != 3 THEN 1 END) AS failNum,
               COUNT(CASE WHEN push_status = 3 THEN 1 END)  AS successNum
        FROM ${tableName}
        WHERE del_flag = 0
          AND pk_org IN (SELECT pk_org
                         FROM fi_org_compare
                         WHERE pk_group = #{pkGroup}
                           AND del_flag = 0)
    </select>
</mapper>
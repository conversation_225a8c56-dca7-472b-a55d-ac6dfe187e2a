package com.cloud.ficonsumer.listener;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiSourceReceivableDTO;
import com.cloud.ficonsumer.dto.FiSourceReceivableExcelVO;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceReceivableService;
import com.cloud.ficonsumer.vo.FiSourceReceivableQueryVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/9.
 */
@Slf4j
public class FiSourceReceivableExportListener extends AbstractExportListener<FiSourceReceivableExcelVO> {

    private final FiSourceReceivableService fiSourceReceivableService;
    private final FiSourceReceivableQueryVO queryDTO;
    private final Page<FiSourceReceivableDTO> page;
    private static final int PAGE_SIZE = 5000;

    public FiSourceReceivableExportListener(UUID id, FiSourceReceivableService fiSourceReceivableService,
                                            Page<FiSourceReceivableDTO> page,
                                            FiSourceReceivableQueryVO queryDTO) {
        super(id, "导出应收单查询列表");
        this.page = page;
        this.fiSourceReceivableService = fiSourceReceivableService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiSourceReceivableExcelVO> doHandleData() {
        List<FiSourceReceivableDTO> vos = new ArrayList<>();
        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiSourceReceivableDTO> pageResult = fiSourceReceivableService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiSourceReceivableDTO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiSourceReceivableDTO> pageResult;
            do {
                pageResult = fiSourceReceivableService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }
        List<FiSourceReceivableExcelVO> result = BeanUtil.copyToList(vos, FiSourceReceivableExcelVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}

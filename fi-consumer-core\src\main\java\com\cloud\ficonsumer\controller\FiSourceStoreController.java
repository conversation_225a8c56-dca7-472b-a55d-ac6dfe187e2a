

package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.ficonsumer.dto.*;
import com.cloud.ficonsumer.entity.FiSourceStore;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceStoreService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import com.cloud.ficonsumer.vo.FiPushStatusChangeVO;
import com.cloud.ficonsumer.vo.FiSourceStoreQueryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 采购入库源数据主表
 *
 * <AUTHOR>
 * @date 2025-03-13 15:02:35
 */
@RestController
@AllArgsConstructor
@RequestMapping("/store" )
@Api(value = "store", tags = "采购入库源数据主表管理")
public class FiSourceStoreController {

    private final FiSourceStoreService fiSourceStoreService;

    /**
     * 报错重推
     *
     * @return
     */
    @ApiOperation(value = "报错重推", notes = "报错重推")
    @GetMapping("/pushAgain/{cbillid}")
    public R<Boolean> pushAgain(@PathVariable("cbillid") String cbillid) {
        return R.ok(fiSourceStoreService.pushAgain(cbillid));
    }

    /**
     * 订单转换前分页查询
     *
     * @return
     */
    @ApiOperation(value = "订单转换前分页查询", notes = "订单转换前分页查询")
    @GetMapping("/beforeConvertDataPage")
    public R<Page<FiSourceStoreDTO>> beforeConvertDataPage(Page page, FiSourceStoreQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getGroup(), "集团主键不能为空");
        if(CollectionUtil.isEmpty(queryVO.getPkOrgList())) {
            String groupCodeFlag = AdminUtils.getGroupCode(queryVO.getGroup());
            queryVO.setGroupCodeFlag(groupCodeFlag);
        }
        return R.ok(fiSourceStoreService.beforeConvertDataPage(page,queryVO));
    }

    /**
     * 转换前数据
     * @return
     */
    @ApiOperation(value = "转换前数据详情查询", notes = "转换前数据详情查询")
    @GetMapping("/getBeforeConvertDataPage" )
    public R<Page<FiSourceStoreDetailDTO>> getBeforeConvertDataPage(Page page, FiSourceStoreQueryVO queryVO) {
        AssertUtils.notBlank(queryVO.getCbillid(), "主键不能为空");
        return R.ok(fiSourceStoreService.getBeforeConvertDataPage(page,queryVO));
    }

    /**
     * 主体表转换后单条字段
     * @return
     */
    @ApiOperation(value = "主体表转换后单条字段", notes = "主体表转换后单条字段")
    @GetMapping("/getConvertList" )
    public R<List<FiConvertStoreDTO>> getConvertList(String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceStoreService.getConvertList(pk));
    }
    /**
     * 获取转换后详情分页数据
     * @return
     */
    @ApiOperation(value = "获取转换后详情分页数据", notes = "获取转换后详情分页数据")
    @GetMapping("/getConvertData" )
    public R<Page<FiConvertStoreDetailDTO>> getConvertData(Page page, String pk) {
        AssertUtils.notBlank(pk, "主键不能为空");
        return R.ok(fiSourceStoreService.getConvertData(page,pk));
    }

    /**
     * 批量修改单据状态
     *
     * @return
     */
    @ApiOperation(value = "批量修改单据状态", notes = "批量修改单据状态")
    @PostMapping("/updatePushStatus")
    public R<Boolean> updatePushStatus(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        AssertUtils.notNull(queryVO.getPushStatus(), "推送状态不能为空");
        if(!(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()) || queryVO.getPushStatus().equals(PushStatusEnum.CONVERT_FAIL.getCode()))) {
            throw new CheckedException("推送状态错误");
        }
        boolean result =fiSourceStoreService.update(Wrappers.<FiSourceStore>lambdaUpdate()
                .set(FiSourceStore::getPushStatus, queryVO.getPushStatus())
                .set(queryVO.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode()),FiSourceStore::getPushMsg, "成功")
                .in(FiSourceStore::getId, queryVO.getIdList()));
        return R.ok(result);
    }

    /**
     * 批量报错重推
     *
     * @return
     */
    @ApiOperation(value = "批量报错重推", notes = "批量报错重推")
    @PostMapping("/pushAgainBatch")
    public R<FiPushStatusChangeVO> pushAgainBatch(@RequestBody FiPushStatusChangeVO queryVO) {
        AssertUtils.notNull(queryVO.getIdList(), "主键集合不能为空");
        return R.ok(fiSourceStoreService.pushAgainBatch(queryVO));
    }
}

INSERT INTO AP_PAYAB<PERSON><PERSON>LL
(ACCESSORY<PERSON><PERSON>, APPR<PERSON><PERSON><PERSON><PERSON>, APPROVER, <PERSON><PERSON><PERSON><PERSON><PERSON>AT<PERSON>, B<PERSON><PERSON>LASS, BILLDATE, B<PERSON>LMAKER, BILLNO, B<PERSON>LPER<PERSON>D, <PERSON><PERSON><PERSON>TATUS, BILLYEAR, <PERSON><PERSON><PERSON><PERSON><PERSON>R, <PERSON>OR<PERSON><PERSON>G, CREA<PERSON><PERSON><PERSON><PERSON>, CREATO<PERSON>, DEF1, <PERSON>F10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, <PERSON>F47, <PERSON>F48, DEF49, <PERSON>F5, <PERSON>F50, <PERSON>F51, <PERSON>F52, <PERSON>F<PERSON>, <PERSON>F54, <PERSON>F<PERSON>, <PERSON>F56, <PERSON>F57, <PERSON>F58, <PERSON>F59, <PERSON>F6, <PERSON>F60, DEF61, DEF62, <PERSON>F63, <PERSON>F64, DEF65, DEF66, DEF67, <PERSON>F68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DR, EFFECTDATE, EFFECTSTATUS, EFFECTUSER, GLOBALLOCAL, GROUPLOCAL, INVOICENO, ISFLOWBILL, ISINIT, ISPUADJUST, ISREDED, ISREFUND, ISURGENT, LASTADJUSTUSER, LASTAPPROVEID, LOCAL_MONEY, MODIFIEDTIME, MODIFIER, MONEY, OFFICIALPRINTDATE, OFFICIALPRINTUSER, OUTBUSITYPE, PK_BALATYPE, PK_BILLTYPE, PK_BUSITYPE, PK_CURRTYPE, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYABLEBILL, PK_PCORG, PK_TRADETYPE, PK_TRADETYPEID, RECECOUNTRYID, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, SCOMMENT, SETT_ORG, SETT_ORG_V, SRC_SYSCODE, START_PERIOD, SUBJCODE, SYSCODE, TAXCOUNTRYID, TOTAL_PERIOD, TS, VID_PCORG, DEF81, DEF82, DEF83, DEF84, DEF85, DEF86, DEF87, DEF88, DEF89, DEF90, DEF91, DEF92, DEF93, DEF94, DEF95, DEF96, DEF97, DEF98, DEF99, DEF100)
VALUES(NULL, '2025-04-09 09:25:24', '1001A710000000JAFQ2S', 1, 'yf', '2025-04-09 09:23:30', '1001A710000000JAFQ2S', 'D120250409000000042', '04', 1, '2025', '~                   ', NULL, '2025-04-09 09:23:51', '1001A710000000JAFQ2S', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '收款对象名称与收款账户户名不一致，请核实', '~', '~', '~', '~', '0', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 0, '2025-04-09 09:25:24', 10, '1001A710000000JAFQ2S', 0, 32700, 'CG1555', 'N', 'N', 'N', 'N', 'N', 'N', '~', '1001A710000000JAFQ2S', 32700, NULL, '~', 32700, NULL, '~', '~                   ', NULL, 'F1', '1001A710000000K5JA79', '1002Z0100000000001K1', '0001A110000000004OK3', '0001A110000000004OK2', '0001A110000000000CE4', '0001A110000000004OK3', '0001A110000000004OK2', '1001A710000000LD4ZMM', '~', 'F1-Cxx-02', '1001A110000000000W38', '0001Z010000000079UJJ', 'bb08a183-22b0-412a-af24-a197ac67f7e2', 0, '2025040907012ad1-dc60-4f9e-b214-d4f8f578e361', 0, '~', '0001A110000000004OK3', '0001A110000000004OK2', 4, '~', '~', 1, '0001Z010000000079UJJ', NULL, '2025-04-09 09:47:14', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~');

INSERT INTO AP_PAYABLEITEM
(ASSETPACTNO, BANKROLLPROJET, BILLCLASS, BILLDATE, BILLNO, BUSIDATE, BUYSELLFLAG, CALTAXMNY, CASHITEM, CBS, CHECKELEMENT, CHECKTYPE, CONFERNUM, CONTRACTNO, COORDFLAG, COSTCENTER, CUSTOMER, DEF1, DEF10, DEF11, DEF12, DEF13, DEF14, DEF15, DEF16, DEF17, DEF18, DEF19, DEF2, DEF20, DEF21, DEF22, DEF23, DEF24, DEF25, DEF26, DEF27, DEF28, DEF29, DEF3, DEF30, DEF31, DEF32, DEF33, DEF34, DEF35, DEF36, DEF37, DEF38, DEF39, DEF4, DEF40, DEF41, DEF42, DEF43, DEF44, DEF45, DEF46, DEF47, DEF48, DEF49, DEF5, DEF50, DEF51, DEF52, DEF53, DEF54, DEF55, DEF56, DEF57, DEF58, DEF59, DEF6, DEF60, DEF61, DEF62, DEF63, DEF64, DEF65, DEF66, DEF67, DEF68, DEF69, DEF7, DEF70, DEF71, DEF72, DEF73, DEF74, DEF75, DEF76, DEF77, DEF78, DEF79, DEF8, DEF80, DEF9, DIRECTION, DR, EQUIPMENTCODE, FACARD, FREECUST, GLOBALBALANCE, GLOBALCREBIT, GLOBALNOTAX_CRE, GLOBALRATE, GLOBALTAX_CRE, GROUPBALANCE, GROUPCREBIT, GROUPNOTAX_CRE, GROUPRATE, GROUPTAX_CRE, INNERORDERNO, INVOICENO, ISURGENT, LOCAL_MONEY_BAL, LOCAL_MONEY_CR, LOCAL_NOTAX_CR, LOCAL_PRICE, LOCAL_TAX_CR, LOCAL_TAXPRICE, MATERIAL, MATERIAL_SRC, MONEY_BAL, MONEY_CR, NOSUBTAX, NOSUBTAXRATE, NOTAX_CR, OBJTYPE, OCCUPATIONMNY, OPPTAXFLAG, ORDERCUBASDOC, OUTSTORENO, PAUSETRANSACT, PAYACCOUNT, PK_BALATYPE, PK_BILLTYPE, PK_CURRTYPE, PK_DEPTID, PK_DEPTID_V, PK_FIORG, PK_FIORG_V, PK_GROUP, PK_ORG, PK_ORG_V, PK_PAYABLEBILL, PK_PAYABLEITEM, PK_PAYTERM, PK_PCORG, PK_PCORG_V, PK_PSNDOC, PK_SSITEM, PK_SUBJCODE, PK_TRADETYPE, PK_TRADETYPEID, POSTPRICE, POSTPRICENOTAX, POSTQUANTITY, POSTUNIT, PRICE, PRODUCTLINE, PROJECT, PROJECT_TASK, PU_DEPTID, PU_DEPTID_V, PU_ORG, PU_ORG_V, PU_PSNDOC, PURCHASEORDER, QUANTITY_BAL, QUANTITY_CR, RATE, RECACCOUNT, ROWNO, ROWTYPE, SCOMMENT, SENDCOUNTRYID, SETT_ORG, SETT_ORG_V, SETTLECURR, SETTLEMONEY, SETTLENO, SRC_BILLID, SRC_BILLTYPE, SRC_ITEMID, SRC_TRADETYPE, SUBJCODE, SUPPLIER, TAXCODEID, TAXNUM, TAXPRICE, TAXRATE, TAXTYPE, TOP_BILLID, TOP_BILLTYPE, TOP_ITEMID, TOP_TRADETYPE, TS, VATCODE, VENDORVATCODE)
VALUES(NULL, '~', 'yf', '2025-04-09 09:23:30', 'D120250409000000042', '2025-04-09 09:23:30', 2, 30000, '~', '~', '~', '~', NULL, NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '1635.00', '~', '31065.00', '~', '~', '~', '~', '~', '1001A710000000K16R42', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', -1, 0, NULL, '~', '~', 0, 0, 0, 0, 0, 0, 32700, 30000, 1, 0, NULL, 'CG1555', 'N', 0, 32700, 30000, NULL, 2700, NULL, '1001A710000000K5LDVT', '1001A710000000K5LDVT', 0, 32700, 0, 0, 30000, 1, 0, 'N', '1001A1100000000I8JIF', NULL, 'N', '~', '~', 'F1', '1002Z0100000000001K1', '1001A1100000000E7T8M', '0001A11000000000CZO5', '0001A110000000004OK3', '0001A110000000004OK2', '0001A110000000000CE4', '0001A110000000004OK3', '0001A110000000004OK2', '1001A710000000LD4ZMM', '1001A710000000LD4ZMN', '~', '~', '~', '0001A11000000000DG25', NULL, '1001A11000000091A3EC', 'F1-Cxx-02', '1001A110000000000W38', 10900, 10000, 3, '0001Z0100000000000XT', 10000, '~', '~', '~', '1001A1100000000E7T8M', '0001A11000000000CZO5', '0001A110000000004OK3', '0001A110000000004OK2', '0001A11000000000DG25', 'CD2025040900104915', 0, 3, 1, '~', 0, 0, '~', '0001Z010000000079UJJ', '0001A110000000004OK3', '0001A110000000004OK2', '~', 0, '~', '1001A710000000LD4YOE', '21', '1001A710000000LD4YOF', '1001A710000000K7SDS3', '~', '1001A1100000000I8JIF', '1002Z01000000001CNE0', NULL, 10900, 9, 1, '1001A710000000LD51O9', '25', '1001A710000000LD51OA', '25-01', '2025-04-09 09:47:14', NULL, NULL);

package com.cloud.ficonsumer.service.impl;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.entity.FiConvertSaleOrder;
import com.cloud.ficonsumer.entity.FiConvertSaleOrderDetail;
import com.cloud.ficonsumer.entity.FiSourceSaleOrder;
import com.cloud.ficonsumer.entity.FiSourceSaleOrderDetail;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceSaleOrderMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.*;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import com.cloud.ficonsumer.vo.YgResult;
import com.cloud.ficonsumer.vo.YgSaleOrderVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourceSaleOrderServiceImpl extends ServiceImpl<FiSourceSaleOrderMapper, FiSourceSaleOrder> implements FiSourceSaleOrderService {

    private final FiSourceSaleOrderDetailService detailService;
    private final FiConvertSaleOrderService convertService;
    private final FiConvertSaleOrderDetailService convertDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;
    private final YgConfig ygConfig;


    @Override
    public boolean push(String csaleorderid) {
        FiSourceSaleOrder source = this.getOne(Wrappers.<FiSourceSaleOrder>lambdaQuery()
                .eq(FiSourceSaleOrder::getCsaleorderid, csaleorderid));
        List<FiSourceSaleOrderDetail> sourceDetails = detailService.list(Wrappers.<FiSourceSaleOrderDetail>lambdaQuery()
                .eq(FiSourceSaleOrderDetail::getCsaleorderid, csaleorderid));

        // 1. 转换数据
        try {
            convertData(source, sourceDetails);
        } catch (Exception e) {
            handleConversionException(source, e);
        }

        // 2. 推送数据到智慧平台
        try {
            pushToYgPlatform(source);
            updatePushSuccess(source);
        } catch (Exception e) {
            handlePushException(source, e);
        }

        return true;
    }

    private void updatePushSuccess(FiSourceSaleOrder source) {
        source.setIntegrationStatus("0");
        source.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
        source.setPushMsg("");
        source.setReadiness(1L);
        this.updateById(source);

        insertPlatformLog(source, Constants.SUCCESS, "【销售订单接收服务】集成财务中台成功");
    }


    private void handleConversionException(FiSourceSaleOrder source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        insertPlatformLog(source, Constants.FAIL, pushMsg);
        LogUtil.info(log, "销售订单集成信息转换失败:", e);
        throw new CheckedException("销售订单集成信息转换失败:" + pushMsg);
    }

    private void handlePushException(FiSourceSaleOrder source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        insertPlatformLog(source, Constants.FAIL, pushMsg);
        LogUtil.info(log, "销售订单集成信息推送失败:", e);
        throw new CheckedException("销售订单集成信息推送失败:" + pushMsg);
    }

    private void insertPlatformLog(FiSourceSaleOrder source, String code, String msg) {
        PlatFormLogVO platFormLogVO = new PlatFormLogVO();
        platFormLogVO.setPkLog(UUID.randomUUID().toString());
        platFormLogVO.setPkBill(source.getCsaleorderid());
        platFormLogVO.setPkBillType(BillTypeEnum.SALE_ORDER.getCode());
        platFormLogVO.setCode(code);
        platFormLogVO.setMsg(msg);
        dmMapper.insertPlatFormLog(platFormLogVO);
    }

    private Boolean pushToYgPlatform(FiSourceSaleOrder source) throws Exception {
        if (!ygConfig.getEnable()) {
            return Boolean.FALSE;
        }

        FiConvertSaleOrder convert = convertService.getOne(Wrappers.<FiConvertSaleOrder>lambdaQuery()
                .eq(FiConvertSaleOrder::getCsaleorderid, source.getCsaleorderid()));

        List<FiConvertSaleOrderDetail> details = convertDetailService.list(Wrappers.<FiConvertSaleOrderDetail>lambdaQuery()
                .eq(FiConvertSaleOrderDetail::getCsaleorderid, source.getCsaleorderid()));

        YgSaleOrderVO ygSaleOrderVO = createYgSaleOrderVO(convert, details);
        ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000025.getServCode());

        String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(ygSaleOrderVO));
        if (StringUtils.isEmpty(result)) {
            throw new CheckedException("响应失败，响应为空。");
        }
        YgResult ygResult = JSONObject.parseObject(result, YgResult.class);
        // 判断响应是否成功
        if ("6100000".equals(ygResult.getCode()) || "6710300".equals(ygResult.getCode())) {
            return Boolean.TRUE;
        } else if ("6990399".equals(ygResult.getCode())) {
            throw new CheckedException("响应失败，响应结果: " + JSON.toJSONString(ygResult));
        } else {
            throw new CheckedException("未知响应，响应结果: " + JSON.toJSONString(ygResult));
        }
    }


    private void convertData(FiSourceSaleOrder source, List<FiSourceSaleOrderDetail> sourceDetails) {
        String csaleorderid = source.getCsaleorderid();
        String billType = BillTypeEnum.SALE_ORDER.getCode();

        FiConvertSaleOrder fiConvert = new FiConvertSaleOrder();
        apiCompareCache.convertData(source, fiConvert, billType);

        FiConvertSaleOrder existing = convertService.getOne(Wrappers.<FiConvertSaleOrder>lambdaQuery()
                .eq(FiConvertSaleOrder::getCsaleorderid, csaleorderid));
        if (existing != null) {
            fiConvert.setId(existing.getId());
            convertDetailService.remove(Wrappers.<FiConvertSaleOrderDetail>lambdaQuery()
                    .eq(FiConvertSaleOrderDetail::getCsaleorderid, csaleorderid));
        }


        List<FiConvertSaleOrderDetail> convertDetailList = new ArrayList<>();
        for (FiSourceSaleOrderDetail sourceDetail : sourceDetails) {
            FiConvertSaleOrderDetail convertDetail = new FiConvertSaleOrderDetail();
            apiCompareCache.convertData(sourceDetail, convertDetail, billType);
            convertDetailList.add(convertDetail);
        }

        convertService.saveOrUpdate(fiConvert);
        convertDetailService.saveOrUpdateBatch(convertDetailList);
    }


    private YgSaleOrderVO createYgSaleOrderVO(FiConvertSaleOrder convert, List<FiConvertSaleOrderDetail> details) {
        YgSaleOrderVO saleOrderVO = new YgSaleOrderVO();

        // 设置主表字段
        saleOrderVO.setUnitCode(convert.getPkOrgV());          // 单位编码
        saleOrderVO.setEcomPlatform(1); // 电商平台
        saleOrderVO.setBusiMode("");     // 业务模式
        saleOrderVO.setOrderTime(DateUtils.format(convert.getDbilldate(), "yyyy-MM-dd"));      // 订单日期
        saleOrderVO.setOrderId(convert.getVbillcode());        // 订单编号
        saleOrderVO.setMallOrderNo("");  // 商城订单编号
        saleOrderVO.setCustomId(convert.getCcustomerid());     // 客户ID
        saleOrderVO.setSalesman(convert.getCemployeeid());     // 销售员
        saleOrderVO.setSaleDept(convert.getCdeptvid());        // 销售部门
        saleOrderVO.setProfCen(convert.getPkOrgV());          // 利润中心
        saleOrderVO.setCostCenter(convert.getCdeptvid());      // 成本中心
        saleOrderVO.setRemark(convert.getVnote());            // 备注
        saleOrderVO.setDocuStatus(convert.getFstatusflag());   // 单据状态

        // 设置物料明细列表
        List<YgSaleOrderVO.MaterialItemVO> materList = new ArrayList<>();
        if (details != null && !details.isEmpty()) {
            for (FiConvertSaleOrderDetail detail : details) {
                YgSaleOrderVO.MaterialItemVO item = new YgSaleOrderVO.MaterialItemVO();

                item.setExtOrdDtlNo(detail.getCrowno());           // 外部订单行号
                item.setMt(detail.getCmaterialvid());              // 物料编码
                item.setQuantity(detail.getNastnum());             // 数量
                item.setUnPrExcTax(detail.getNqtorigprice());      // 不含税单价
                item.setAmtExTax(detail.getNorigmny());            // 不含税金额
                item.setTaxrt(detail.getNtaxrate());               // 税率
                item.setTaxIncPric(detail.getNqtorigtaxprice());   // 含税单价
                item.setTaxAmt(detail.getNorigtaxmny());          // 含税金额
                item.setProjectId(detail.getCprojectid());         // 项目ID
                item.setRemark(detail.getVbdef9());               // 备注

                // 设置是否需交货，需要根据数量判断并转换枚举
                if (detail.getNastnum() != null && detail.getNastnum().compareTo(BigDecimal.ZERO) < 0) {
                    item.setIsNeedDlvy("00000000"); // 数量为负数时，默认为否
                } else {
                    item.setIsNeedDlvy("");
                }

                materList.add(item);
            }
        }

        saleOrderVO.setMaterList(materList);

        return saleOrderVO;
    }
}

package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiConvertOrderDTO;
import com.cloud.ficonsumer.dto.FiConvertOrderDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceOrderDTO;
import com.cloud.ficonsumer.dto.FiSourceOrderDetailDTO;
import com.cloud.ficonsumer.entity.ApiCompare;
import com.cloud.ficonsumer.entity.FiConvertOrder;
import com.cloud.ficonsumer.entity.FiConvertOrderDetail;
import com.cloud.ficonsumer.entity.FiSourceOrder;
import com.cloud.ficonsumer.entity.FiSourceOrderDetail;
import com.cloud.ficonsumer.entity.FiSourceStore;
import com.cloud.ficonsumer.enums.*;
import com.cloud.ficonsumer.integration.dto.Param;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceOrderMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;

/**
 * 订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-16 16:53:16
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiSourceOrderServiceImpl extends ServiceImpl<FiSourceOrderMapper, FiSourceOrder> implements FiSourceOrderService {

    private final FiSourceOrderDetailService fiSourceOrderDetailService;
    private final FiConvertOrderService fiConvertOrderService;
    private final FiConvertOrderDetailService fiConvertOrderDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;
    private final YgConfig ygConfig;

    /**
     * 报错重推
     *
     * @param pk
     * @return
     */
    @Override
    public boolean pushAgain(String pk) {
        FiSourceOrder sourceMainData = this.getOne(Wrappers.<FiSourceOrder>lambdaQuery().eq(FiSourceOrder::getPkOrder, pk));
        List<FiSourceOrderDetail> details = fiSourceOrderDetailService.list(Wrappers.<FiSourceOrderDetail>lambdaQuery().eq(FiSourceOrderDetail::getPkOrder, pk));
        //重新转换
        try {
            this.turnData(sourceMainData,details);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            this.updateById(sourceMainData);
            LogUtil.info(log, "采购订单集成信息转换失败:", e);
            throw new CheckedException("采购订单集成信息转换失败:"+pushMsg);
        }
        //重新推送智慧平台
        sourceMainData.setPushTime(DateUtil.formatDateTime(new Date()));
        try {
            YgResult ygResult = this.pushData(sourceMainData.getPkOrder());
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
            sourceMainData.setPushMsg("");
            sourceMainData.setReadiness(1l);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,sourceMainData.getVtrantypecode());
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000000");
                updatePlatFormLog.setMsg("");
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(sourceMainData.getVtrantypecode());
                platFormLogVO.setCode("000000");
                platFormLogVO.setMsg("");
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(),1000);
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk,sourceMainData.getVtrantypecode());
            if(CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000001");
                updatePlatFormLog.setMsg(pushMsg);
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            }else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(sourceMainData.getVtrantypecode());
                platFormLogVO.setCode("000001");
                platFormLogVO.setMsg(pushMsg);
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
            LogUtil.info(log, "采购订单集成信息推送失败:", e);
            throw new CheckedException("采购订单集成信息推送失败:"+pushMsg);
        }
        return true;
    }

    /**
     * 数据推送
     *
     * @param pk
     * @return
     * @throws Exception
     */
    private YgResult pushData(String pk) throws Exception {
        YgResult ygResult = new YgResult();
        if(ygConfig.getEnable()) {
            FiConvertOrder convertMainData = fiConvertOrderService.getOne(Wrappers.<FiConvertOrder>lambdaQuery().eq(FiConvertOrder::getPkOrder, pk));
            List<FiConvertOrderDetail> details = fiConvertOrderDetailService.list(Wrappers.<FiConvertOrderDetail>lambdaQuery().eq(FiConvertOrderDetail::getPkOrder,pk));
            YgOrderVO ygOrderVO = this.turnYgData(convertMainData,details);
            ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000011.getServCode());
            Param<YgOrderVO> param = Param.addData(apiExchange.getCode(), apiExchange.getAccessKey(), ygOrderVO);
            String result = YgUtil.sendToYg(apiExchange, JSON.toJSONString(ygOrderVO));
            ygResult = JSONObject.parseObject(result,YgResult.class);
            if(!ygResult.getCode().equals("000000")) {
                throw new CheckedException(ygResult.getmessage());
            }
        }
        return ygResult;
    }

    /**
     * 将转换后的数据用远光提供的格式推送
     *
     * @param convertMainData
     * @param details
     * @return
     */
    private YgOrderVO turnYgData(FiConvertOrder convertMainData, List<FiConvertOrderDetail> details) {
        YgOrderVO ygOrderVO = new YgOrderVO();
        String pkOrgV = apiCompareService.getTurnByType(convertMainData.getPkOrg(),Constants.PkOrg);
        if(StrUtil.isBlank(pkOrgV)) {
            throw new CheckedException("单位代号不可为空");
        }
        List<YgOrderDetailVO> items = new ArrayList<>();
        String prvMkCode = "00003010";
        ygOrderVO.setPrvMkCode(prvMkCode);
        ygOrderVO.setPurOrdNo(convertMainData.getVbillcode());
        ygOrderVO.setAflUnit(pkOrgV);
        ygOrderVO.setCreTime(convertMainData.getDmakedate());
        String ctrantypeidType = apiCompareService.getTurnByType(convertMainData.getCtrantypeid(), Constants.ctrantypeid);
        CtrantypeidEnum ctrantypeidEnum;
        if(YgConfig.environment.equals("dev")) {
            ctrantypeidEnum = CtrantypeidEnum.getByTestCode(ctrantypeidType);
        }else {
            ctrantypeidEnum = CtrantypeidEnum.getByCode(ctrantypeidType);
        }
        ygOrderVO.setPurOrdTyp(ctrantypeidEnum.getThirdCode());
        //CtrantypeidEnum 仿真环境和生产的不一致，
        if(StrUtil.isBlank(ygOrderVO.getPurOrdTyp())) {
            throw new CheckedException("订单类型不能为空");
        }
        String contractNo = null;
        if(StringUtils.isNotEmpty(convertMainData.getVcontractcode()) && convertMainData.getVcontractcode().startsWith("SG")) {
            contractNo = convertMainData.getVcontractcode();
        }
        ygOrderVO.setContractNo(contractNo);
//        ygOrderVO.setCrePersNm(convertMainData.getBillmaker());
        String pkSupplier = apiCompareService.getTurnByType(convertMainData.getPkSupplier(), Constants.PkSupplier);
        ygOrderVO.setSupCod(pkSupplier);
        ygOrderVO.setTotAmExTax(convertMainData.getNorigmny());
        ygOrderVO.setCurrCod(convertMainData.getCorigcurrencyid());
        ygOrderVO.setExchRat(convertMainData.getNexchangerate());
        ygOrderVO.setPurOrdTaxAmount(convertMainData.getNtax());
//        String purOrdTypDesc = apiCompareService.getTurnByType(convertMainData.getCtrantypeid(),Constants.ctrantypeid);
//        ygOrderVO.setPurOrdTypDesc(purOrdTypDesc);
        String transmissionDate = DateUtil.formatDate(new Date());
        ygOrderVO.setTransmissionDate(transmissionDate);
//        ygOrderVO.setProCen(pkOrgV);
        ygOrderVO.setDataSouSys("ZJYJ");
        for(FiConvertOrderDetail fiConvertOrderDetail : details) {
            YgOrderDetailVO ygOrderDetailVO = new YgOrderDetailVO();
            ygOrderDetailVO.setPurOrdNo(fiConvertOrderDetail.getVbillcode());
            ygOrderDetailVO.setPurOrdItem(fiConvertOrderDetail.getCrowno());
//            ygOrderDetailVO.setPrjCode(apiCompareService.getProjectCode(fiConvertOrderDetail.getCprojectid()));
            ygOrderDetailVO.setItTotAmExTax(fiConvertOrderDetail.getNorigmny());
            BigDecimal ntaxrate = BigDecimal.ZERO;
            if(StrUtil.isNotBlank(fiConvertOrderDetail.getNtaxrate())) {
                ntaxrate = new BigDecimal(fiConvertOrderDetail.getNtaxrate());
                ntaxrate = ntaxrate.divide(new BigDecimal(100),2, RoundingMode.HALF_UP);
            }
            ygOrderDetailVO.setTaxrt(ntaxrate.toPlainString());
            ygOrderDetailVO.setTaxAmount(fiConvertOrderDetail.getNtax());
            ygOrderDetailVO.setItTotAmIncTax(fiConvertOrderDetail.getNorigtaxmny());
            ygOrderDetailVO.setCeleIdInPurVou("");
//            ygOrderDetailVO.setCostCenterCode(pkOrgV);
            ygOrderDetailVO.setUnitPrice(fiConvertOrderDetail.getNqtorigtaxprice());
            ygOrderDetailVO.setAmt(fiConvertOrderDetail.getNastnum());
            ygOrderDetailVO.setPrvMkCode(prvMkCode);
//            ygOrderDetailVO.setIsReturnIteems(fiConvertOrderDetail.getBreturn());
//            ygOrderDetailVO.setPricUni(fiConvertOrderDetail.getCastunitid());
            ygOrderDetailVO.setTransmissionDate(transmissionDate);
            if(!YgConfig.environment.equals("dev")) {
                if (StrUtil.isBlank(fiConvertOrderDetail.getMaterialCode())) {
                    throw new CheckedException("物料关联为空");
                }
                if (!fiConvertOrderDetail.getMaterialCode().startsWith("CY")) {
                    throw new CheckedException("物料MDM编码未配置");
                }
            }
            ygOrderDetailVO.setMtCode(fiConvertOrderDetail.getMaterialCode().substring(2));
//            ygOrderDetailVO.setMtName(fiConvertOrderDetail.getName());
//            ygOrderDetailVO.setProCen("");
//            ygOrderDetailVO.setOrdTp("");
//            String Year = "";
//            if(StrUtil.isNotBlank(fiConvertOrderDetail.getDbilldate())) {
//                Year = fiConvertOrderDetail.getDbilldate().substring(0,4);
//            }
//            ygOrderDetailVO.setYear(Year);
            String recepId = ctrantypeidEnum.getRecepId();
            ygOrderDetailVO.setRecepId(recepId);
//            ygOrderDetailVO.setRecepInvVerSign("");
//            ygOrderDetailVO.setSerInvVerSign("");
            items.add(ygOrderDetailVO);
        }
        ygOrderVO.setItems(items);
        return ygOrderVO;
    }

    /**
     * 数据转换
     *
     * @param sourceMainData
     * @param details
     */
    private void turnData(FiSourceOrder sourceMainData, List<FiSourceOrderDetail> details) throws Exception {
        String pk = sourceMainData.getPkOrder();
        FiConvertOrder fiConvertOrder = new FiConvertOrder();
        apiCompareCache.convertData(sourceMainData,fiConvertOrder,BillTypeEnum.ORDER.getCode());
        FiConvertOrder convertMainData = fiConvertOrderService.getOne(Wrappers.<FiConvertOrder>lambdaQuery().eq(FiConvertOrder::getPkOrder, pk));
        fiConvertOrder.setId(null);
        if(convertMainData != null) {
            fiConvertOrder.setId(convertMainData.getId());
            fiConvertOrderDetailService.remove(Wrappers.<FiConvertOrderDetail>lambdaQuery().eq(FiConvertOrderDetail::getPkOrder,pk));
        }
        // 转换预算编制保存然后走MQ推送到bip
        List<FiConvertOrderDetail> convertDetailList = new ArrayList<>();
        for(FiSourceOrderDetail sourceDetailData : details) {
            FiConvertOrderDetail fiConvertOrderDetail = new FiConvertOrderDetail();
            apiCompareCache.convertData(sourceDetailData,fiConvertOrderDetail, BillTypeEnum.ORDER_DETAIL.getCode());
            fiConvertOrderDetail.setId(null);
            convertDetailList.add(fiConvertOrderDetail);
        }
        fiConvertOrderService.saveOrUpdate(fiConvertOrder);
        fiConvertOrderDetailService.saveOrUpdateBatch(convertDetailList);
    }

    /**
     * 判断订单是否准备就绪
     *
     * @return
     */
    @Override
    public boolean getOrderReadiness(String vbillcode) {
        FiSourceOrder sourceMainData = this.getOne(Wrappers.<FiSourceOrder>lambdaQuery().eq(FiSourceOrder::getVbillcode, vbillcode));
        if(sourceMainData != null && sourceMainData.getReadiness().equals(1l)) {
            return true;
        }
        return false;
    }

    /**
     * 订单转换前分页查询
     *
     * @return
     */
    @Override
    public Page<FiSourceOrderDTO> beforeConvertDataPage(Page page, FiSourceOrderQueryVO queryVO) {
        Page<FiSourceOrderDTO> result = this.baseMapper.beforeConvertDataPage(page,queryVO);
        if (CollectionUtil.isNotEmpty(result.getRecords())){
            for (FiSourceOrderDTO apiSource:result.getRecords()){
                ApiCompare apiCompare = apiCompareCache.getDictionaryValue(BillTypeEnum.ORDER.getCode(), "corigcurrencyid", YgUtil.toStringTrim(apiSource.getCorigcurrencyid()));
                apiSource.setCorigcurrencyidName(Objects.isNull(apiCompare)?null:apiCompare.getTargetSystemCode());
                if(StrUtil.isNotBlank(apiSource.getPkSupplier())) {
                    apiSource.setPkSupplierName(dmMapper.getSupplierName(apiSource.getPkSupplier()));
                }
                apiSource.setVtrantypecodeName(CtrantypeidEnum.getByCode(apiSource.getVtrantypecode()).getName());
            }
        }
        return result;
    }

    /**
     * 转换前数据详情查询
     * @param page
     * @param queryVO
     * @return
     */
    @Override
    public Page<FiSourceOrderDetailDTO> getBeforeConvertDataPage(Page page, FiSourceOrderQueryVO queryVO) {
        Page<FiSourceOrderDetailDTO> result = this.baseMapper.getBeforeConvertDataPage(page,queryVO);
        return result;
    }

    /**
     * 主体表转换后单条字段
     * @param pk
     * @return
     */
    @Override
    public List<FiConvertOrderDTO> getConvertList(String pk) {
        List<FiConvertOrderDTO> convertDataDTOList = new ArrayList<>();
        FiConvertOrderDTO convertDataDTO = new FiConvertOrderDTO();
        FiConvertOrder convertDataOld = fiConvertOrderService.getOne(Wrappers.<FiConvertOrder>lambdaQuery().eq(FiConvertOrder::getPkOrder, pk));
        if(null != convertDataOld) {
            BeanUtil.copyProperties(convertDataOld,convertDataDTO);
            convertDataDTOList.add(convertDataDTO);
        }
        return convertDataDTOList;
    }

    /**
     * 获取转换后详情分页数据
     *
     * @param page
     * @param pk
     * @return
     */
    @Override
    public Page<FiConvertOrderDetailDTO> getConvertData(Page page, String pk) {
        Page<FiConvertOrderDetailDTO> result = this.baseMapper.getConvertData(page,pk);
        return result;
    }

    @Override
    public void checkSync(String pkOrder) {
        FiSourceOrder fiSourceOrder = this.getOne(Wrappers.<FiSourceOrder>lambdaQuery().eq(FiSourceOrder::getPkOrder, pkOrder));

        if (Objects.isNull(fiSourceOrder)) {
            throw new CheckedException(
                    MessageFormat.format("采购订单未同步成功, 采购订单不存在，采购订单ID: {0}", pkOrder));
        }

        if (Objects.isNull(fiSourceOrder.getPushStatus()) ||
                ObjectUtil.notEqual(PushStatusEnum.PUSH_SUCCESS.getCode(), fiSourceOrder.getPushStatus())) {
            throw new CheckedException(
                    MessageFormat.format("采购订单未同步成功, pushStatus: {0}，采购订单编号: {1}",
                            fiSourceOrder.getPushStatus(), fiSourceOrder.getVbillcode()));
        }
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourceOrder fiSourceOrder = this.getById(id);
            try {
                this.pushAgain(fiSourceOrder.getPkOrder());
            } catch (Exception e) {
                resultList.add(fiSourceOrder.getVbillcode()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}

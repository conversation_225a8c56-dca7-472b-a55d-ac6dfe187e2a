

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通用报销单详情源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:40
 */
@Data
@TableName("fi_source_reimbursement_detail")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "通用报销单详情源数据主表")
public class FiSourceReimbursementDetail extends BaseEntity<FiSourceReimbursementDetail> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * pk详情主键
     */
    @ApiModelProperty(value="pk详情主键")
    private String pkPayableitem;

    /**
     * pk主键
     */
    @ApiModelProperty(value="pk主键")
    private String pkPayablebill;

    /**
     * 业务员
     */
    @ApiModelProperty(value="业务员")
    private String pkPsndoc;

    /**
     * 供应商
     */
    @ApiModelProperty(value="供应商")
    private String supplier;

    /**
     * 往来对象
     */
    @ApiModelProperty(value="往来对象")
    private String objtype;

    /**
     * 部门
     */
    @ApiModelProperty(value="部门")
    private String pkDeptid;

    /**
     * 部门版本
     */
    @ApiModelProperty(value="部门版本")
    private String pkDeptidV;

    /**
     * 发票号
     */
    @ApiModelProperty(value="发票号")
    private String invoiceno;

    /**
     * 收款银行账户
     */
    @ApiModelProperty(value="收款银行账户")
    private String recaccount;

    /**
     * 结算方式
     */
    @ApiModelProperty(value="结算方式")
    private String pkBalatype;

    /**
     * 付款银行账户
     */
    @ApiModelProperty(value="付款银行账户")
    private String payaccount;

    /**
     * 合同号
     */
    @ApiModelProperty(value="合同号")
    private String contractno;

    /**
     * 收支项目
     */
    @ApiModelProperty(value="收支项目")
    private String pkSubjcode;

    /**
     * 应付类型
     */
    @ApiModelProperty(value="应付类型")
    private String pkTradetypeid;

    /**
     * 组织本币金额
     */
    @ApiModelProperty(value="组织本币金额")
    private String localMoney;

    /**
     * 借方原币金额
     */
    @ApiModelProperty(value="借方原币金额")
    private String moneyDe;

    /**
     * 组织本币无税金额
     */
    @ApiModelProperty(value="组织本币无税金额")
    private String localNotaxDe;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String localTaxDe;

    /**
     * 组织本币金额
     */
    @ApiModelProperty(value="组织本币金额")
    private String localMoneyCr;

    /**
     * 组织本币无税金额
     */
    @ApiModelProperty(value="组织本币无税金额")
    private String localNotaxCr;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String localTaxCr;

    /**
     * 车船税
     */
    @ApiModelProperty(value="车船税")
    private String def50;

    /**
     * 摘要
     */
    @ApiModelProperty(value="摘要")
    private String scomment;
}

package com.cloud.ficonsumer.vo;

import com.cloud.ficonsumer.entity.FiSourceOrderDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @date 2025/1/17.
 */
@Data
public class FiSourceOrderDetailVO extends FiSourceOrderDetail {

    /**
     * 合同号
     */
    @ApiModelProperty(value="合同号")
    private String vcontractcode;

    /**
     * 无税金额
     */
    @ApiModelProperty(value="无税金额")
    private String norigmny;

    /**
     * 折本汇率
     */
    @ApiModelProperty(value="折本汇率")
    private String nexchangerate;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String ntax;
}

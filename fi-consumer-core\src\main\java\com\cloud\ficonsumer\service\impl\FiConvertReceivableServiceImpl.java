
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertReceivable;
import com.cloud.ficonsumer.mapper.FiConvertReceivableMapper;
import com.cloud.ficonsumer.service.FiConvertReceivableService;
import org.springframework.stereotype.Service;

/**
 * 项目应收单转换数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:29
 */
@Service
public class FiConvertReceivableServiceImpl extends ServiceImpl<FiConvertReceivableMapper, FiConvertReceivable> implements FiConvertReceivableService {

}

package com.cloud.ficonsumer.consumers;

import com.cloud.apiexchange.client.consumer.IMqConsumer;
import com.cloud.apiexchange.client.consumer.MqResult;
import com.cloud.apiexchange.client.consumer.annotation.MqConsumer;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.service.FiSourceProjsubPayableService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.cloud.ficonsumer.enums.Constants.TOPIC_PROJSUB_PAYBILL_TRANS;

@Slf4j
@MqConsumer(topic = TOPIC_PROJSUB_PAYBILL_TRANS, group = Constants.Group.group, transaction = false)
@Component
@AllArgsConstructor
public class ProjSubPayablePushConsumer implements IMqConsumer {

    private FiSourceProjsubPayableService fiSourceProjsubPayableService;

    @Override
    public MqResult consume(String pkSettlement) {
        LogUtil.info(log, "项目分包应付处理开始:", pkSettlement);
        try {
            fiSourceProjsubPayableService.push(pkSettlement);
        } catch (Exception e) {
            LogUtil.error(log, "项目分包应付处理失败:", e);
            return new MqResult(MqResult.FAIL_CODE, AdminUtils.truncateString(e.getMessage(), 1000));
        }
        return new MqResult(MqResult.SUCCESS_CODE, "项目分包应付处理成功");
    }
}

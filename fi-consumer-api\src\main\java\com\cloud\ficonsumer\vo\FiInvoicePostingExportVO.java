package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 采购/项目应付导出VO
 */
@Data
@ColumnWidth(25)
public class FiInvoicePostingExportVO {

    /**
     * 业务单据ID
     */
    @ExcelProperty(value="业务单据ID")
    private String busibillno;

    /**
     * 应用ID
     */
    @ExcelProperty(value="应用ID")
    private String appid;

    /**
     * 业务类型编码
     */
    @ExcelProperty(value="业务类型编码")
    private String typeid;

    /**
     * 标识：记账发票
     */
    @ExcelProperty(value="标识：记账发票")
    private String xrech;

    /**
     * 标识：后续借/贷
     */
    @ExcelProperty(value="标识：后续借/贷")
    private String tbtkz;

    /**
     * 公司代码MDM
     */
    @ExcelProperty(value="公司代码MDM")
    private String comcod;

    /**
     * 利润中心MDM
     */
    @ExcelProperty(value="利润中心MDM")
    private String prctr;

    /**
     * 凭证日期
     */
    @ExcelProperty(value="凭证日期")
    private String apldate;

    /**
     * 过账日期
     */
    @ExcelProperty(value="过账日期")
    private String voudat;

    /**
     * 出票方
     */
    @ExcelProperty(value="出票方")
    private String supcracc;

    /**
     * 附件张数
     */
    @ExcelProperty(value="附件张数")
    private String refdocnum;

    /**
     * 金额
     */
    @ExcelProperty(value="金额")
    private String invveramt;

    /**
     * 税额
     */
    @ExcelProperty(value="税额")
    private String taxamt;

    /**
     * 货币
     */
    @ExcelProperty(value="货币")
    private String trancurren;

    /**
     * 付款基准日期
     */
    @ExcelProperty(value="付款基准日期")
    private String basedate;

    /**
     * 付款条件
     */
    @ExcelProperty(value="付款条件")
    private String terpay;

    /**
     * 付款方式
     */
    @ExcelProperty(value="付款方式")
    private String paymmet;

    /**
     * 凭证类型
     */
    @ExcelProperty(value="凭证类型")
    private String doctype;

    /**
     * 凭证抬头文本
     */
    @ExcelProperty(value="凭证抬头文本")
    private String instruction;

    /**
     * 税率代码
     */
    @ExcelProperty(value="税率代码")
    private String taxrtcd;

    /**
     * 税项
     */
    @ExcelProperty(value="税项")
    private String taxitem;

    /**
     * 经办人编号
     */
    @ExcelProperty(value="经办人编号")
    private String managerid;

    /**
     * 制证人
     */
    @ExcelProperty(value="制证人")
    private String app;

    /**
     * 审核人
     */
    @ExcelProperty(value="审核人")
    private String checker;

    /**
     * 财务主管
     */
    @ExcelProperty(value="财务主管")
    private String financer;

    /**
     * 农维费使用标志
     */
    @ExcelProperty(value="农维费使用标志")
    private String agmaintfeeusemark;

    /**
     * 部门分类
     */
    @ExcelProperty(value="部门分类")
    private String maindepttype;

    /**
     * 业务事项
     */
    @ExcelProperty(value="业务事项")
    private String costype;

    /**
     * 明细业务事项
     */
    @ExcelProperty(value="明细业务事项")
    private String detcostype;

    @ExcelProperty(value = "同步状态")
    private String pushStatusName;

    /**
     * 推送成功失败信息
     */
    @ExcelProperty(value = "同步消息")
    private String pushMsg;

    /**
     * 创建时间
     */
    @ExcelProperty(value="拉取时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ExcelIgnore
    private Integer pushStatus;
}
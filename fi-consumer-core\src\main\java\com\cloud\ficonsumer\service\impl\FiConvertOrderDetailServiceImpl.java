
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertOrderDetail;
import com.cloud.ficonsumer.mapper.FiConvertOrderDetailMapper;
import com.cloud.ficonsumer.service.FiConvertOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * 订单转换数据详情表
 *
 * <AUTHOR>
 * @date 2025-01-16 16:53:19
 */
@Service
public class FiConvertOrderDetailServiceImpl extends ServiceImpl<FiConvertOrderDetailMapper, FiConvertOrderDetail> implements FiConvertOrderDetailService {

}

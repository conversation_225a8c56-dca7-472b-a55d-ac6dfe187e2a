package com.cloud.ficonsumer.service;

import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.vo.BillStatisticsVO;

import java.util.List;

/**
 * 单据统计服务接口
 */
public interface BillStatisticsService {
    
    /**
     * 获取统计数据
     * @param billType 单据类型
     * @return 统计数据列表
     */
    List<BillStatisticsVO> getStatistics(BillTypeEnum billType);

    /**
     * 获取支持的单据类型
     * @return 支持的单据类型
     */
    BillTypeEnum getSupportedBillType();
}

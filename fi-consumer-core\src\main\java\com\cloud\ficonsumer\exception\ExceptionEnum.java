package com.cloud.ficonsumer.exception;

/**
 * <AUTHOR>
 * @date 2023/3/30.
 */
public enum ExceptionEnum {

    NOT_ADD(10001,"该集团的子类已有里程碑配置,不可再次新增"),
    FILE_PROMPT(10002,"附件上传提示"),
    APPROVED(10003,"项目派工时已选分包但是还未发起分包业务,是否继续"),
    SETTLEMENT_CHECK(10004,"还未起草总包合同或分包合同，是否确认发起项目结算？"),
    DESIGN_APPROVED(10005,"设计派工时已选分包但是还未发起分包业务,是否继续"),
    CHECK_FRAMEWORK_AMOUNT(10006,"本次分包金额已超出框架剩余额度，是否继续提交"),
    ;
    private Integer code;
    private String msg;

    ExceptionEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据code获取枚举类
     *
     * @param code
     * @return
     */
    public static ExceptionEnum getByCode(Integer code) {
        for (ExceptionEnum en : values()) {
            if (code.equals(en.getCode())) {
                return en;
            }
        }
        return null;
    }
}

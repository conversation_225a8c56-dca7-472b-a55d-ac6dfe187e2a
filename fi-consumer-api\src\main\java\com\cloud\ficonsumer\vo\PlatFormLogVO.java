package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/1/21.
 */
@Data
public class PlatFormLogVO {

    /**
     * 日志主键
     */
    @ApiModelProperty(value="日志主键")
    private String pkLog;

    /**
     * 单据号
     */
    @ApiModelProperty(value="单据号")
    private String pkBill;

    /**
     * 单据类型
     */
    @ApiModelProperty(value="单据类型")
    private String pkBillType;

    /**
     * 错误编码
     */
    @ApiModelProperty(value="错误编码")
    private String code;

    /**
     * 错误消息
     */
    @ApiModelProperty(value="错误消息")
    private String msg;

    /**
     * msg为空的时候orcal会报错
     * @param msg
     */
    public void setMsg(String msg) {
        if(null==msg){
            msg = "";
        }
        this.msg = msg;
    }
}

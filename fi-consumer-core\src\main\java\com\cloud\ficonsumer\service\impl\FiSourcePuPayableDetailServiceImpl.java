package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourcePuPayableDetail;
import com.cloud.ficonsumer.mapper.FiSourcePayableDetailMapper;
import com.cloud.ficonsumer.service.FiSourcePuPayableDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourcePuPayableDetailServiceImpl extends ServiceImpl<FiSourcePayableDetailMapper, FiSourcePuPayableDetail> implements FiSourcePuPayableDetailService {

}

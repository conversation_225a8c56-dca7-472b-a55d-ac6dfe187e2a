package com.cloud.ficonsumer.dto;

import com.cloud.ficonsumer.entity.FiSourceReceivable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/2/13.
 */
@Data
public class FiSourceReceivableDTO extends FiSourceReceivable {


    /**
     * 单据类型
     * F0-Cxx-01 项目应收单
     * F0-Cxx-02 销售应收单
     */
    @ApiModelProperty(value="单据类型名称")
    private String pkTradetypeName;

    /**
     * 单据爬取时间
     */
    @ApiModelProperty(value="单据爬取时间")
    private String grabTime;

}

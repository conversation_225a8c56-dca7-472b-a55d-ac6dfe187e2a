package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;


/**
 * 项目分页查询实体  立项登记导出
 *
 * <AUTHOR>
 * @date 2023/7/5.
 */
@Data
@ColumnWidth(25)
public class ApiCompareExportVO {

    /**
     * 档案类型(取字典项值)
     */
    @ExcelProperty(value="档案类型" ,index = 0)
    private String fileType;

    /**
     * 来源系统主键
     */
    @ExcelProperty(value="业+系统主键" ,index = 1)
    private String sourceSystemId;

    /**
     * 来源系统id
     */
    @ExcelProperty(value="业+系统ID" ,index = 2)
    private String sourceSystemCode;

    /**
     * 来源系统名称
     */
    @ExcelProperty(value="业+系统名称" ,index = 3)
    private String sourceSystemName;

    /**
     * 目标系统主键
     */
    @ExcelProperty(value="财务管控系统主键" ,index = 4)
    private String targetSystemId;

    /**
     * 目标系统id
     */
    @ExcelProperty(value="财务管控系统ID" ,index = 5)
    private String targetSystemCode;

    /**
     * 目标系统名称
     */
    @ExcelProperty(value="财务管控系统名称" ,index = 6)
    private String targetSystemName;


}



package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.cloud.cloud.admin.api.dto.UserInfo;
import com.cloud.cloud.admin.api.feign.RemoteRoleService;
import com.cloud.cloud.admin.api.feign.RemoteUserService;
import com.cloud.cloud.admin.api.entity.SysRole;
import com.cloud.cloud.common.core.constant.SecurityConstants;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.R;
import com.cloud.cloud.common.log.annotation.SysLog;
import com.cloud.cloud.common.security.service.CloudUser;
import com.cloud.cloud.common.security.util.SecurityUtils;
import com.cloud.ficonsumer.service.FiOrgCompareService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.vo.FiOrgListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

import static java.util.stream.Collectors.toList;


/**
 * 公共接口管理
 *
 * <AUTHOR>
 * @date 2023-03-20 14:07:11
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin")
@Api(value = "公共接口管理(组织、用户)", tags = "公共接口管理(组织、用户)")
public class AdminController {

    private final RemoteUserService remoteUserService;
    private final RemoteRoleService remoteRoleService;
    private final FiOrgCompareService fiOrgCompareService;

    /**
     * 获得当前登录用户的信息
     *
     * @return
     */
    @ApiOperation(value = "获得当前登录用户的信息", notes = "获得当前登录用户的信息")
    @GetMapping("/getUserInfo")
    @SysLog("获得当前登录用户的信息")
    public R<UserInfoVO> getUserInfo() {

        UserInfoVO userInfoVO = new UserInfoVO();
        //获取登录信息
        CloudUser user = SecurityUtils.getUser();
        try {
            UserInfo userInfo = remoteUserService.info(user.getUsername(), SecurityConstants.FROM_IN).getData();
            userInfoVO.setUserInfo(userInfo);
            //获得角色
            List<SysRole> sysRoleList = remoteRoleService.listRoles(SecurityConstants.FROM_IN).getData();
            List<SysRole> userRoleList = sysRoleList.stream().filter(item -> {
                List<Integer> split = Arrays.asList(item.getRoleId());
                return CollectionUtil.containsAny(Arrays.asList(userInfo.getRoles()), split);
            }).collect(toList());
            userInfoVO.setUserRoleList(userRoleList);

            //获取用户当前选中的组织
            AdminUtils.handleOrg(userInfoVO);
        } catch (CheckedException e) {
            LogUtil.error(log, "获得当前登录用户的信息失败" + e.getMessage());
            throw new CheckedException(e.getMessage());
        } catch (Exception e) {
            LogUtil.error(log, "获得当前登录用户的信息失败" + e.getMessage());
            throw new CheckedException("获得当前登录用户的信息失败");
        }
        return R.ok(userInfoVO);
    }

    /**
     * 获取当前登录人集团和组织列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取当前登录人集团和组织列表信息", notes = "获取当前登录人集团和组织列表信息")
    @GetMapping("/getOrgList")
    @SysLog("获取当前登录人集团和组织列表信息")
    public R<FiOrgListVO> getOrgList() {
        String departCode = AdminUtils.getLoginUserGroupDepartCode();
        return R.ok(fiOrgCompareService.getGroupList(departCode));
    }

}

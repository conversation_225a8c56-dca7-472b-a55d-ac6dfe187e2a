package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiSourcePuPayableDetailQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPayableQueryDTO;
import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import com.cloud.ficonsumer.vo.FiConvertPuPayableDetailVO;
import com.cloud.ficonsumer.vo.FiSourcePuPayableDetailVO;
import com.cloud.ficonsumer.vo.FiSourcePuPayableVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.MapKey;

import java.util.List;
import java.util.Map;

/**
 * 采购应付源数据主表
 */
@Mapper
public interface FiSourcePuPayableMapper extends CloudBaseMapper<FiSourcePuPayable> {

    Page<FiSourcePuPayableVO> beforeConvertDataPage(Page<FiSourcePuPayableVO> page, @Param("param") FiSourcePuPayableQueryDTO queryDTO);

    Page<FiSourcePuPayableDetailVO> getBeforeConvertDataPage(Page<FiSourcePuPayableDetailVO> page, @Param("param") FiSourcePuPayableDetailQueryDTO queryDTO);

    Page<FiConvertPuPayableDetailVO> getConvertData(Page<FiConvertPuPayableDetailVO> page, @Param("pkPayablebill") String pkPayablebill);

    /**
     * 查询未挂账的应付单明细
     * <p>
     * 查询条件：
     * 1. 主表未挂账(return_status不为2或为空)
     * 2. 源单据ID在指定列表中
     *
     * @param srcBillIds 源单据ID列表
     * @return 返回Map结构，key为源单据ID(src_billid)，value为应付单明细信息
     */
    @MapKey("srcBillid")
    Map<String, FiSourcePuPayableDetailVO> getNoPostedBySrcBillIds(@Param("srcBillIds") List<String> srcBillIds);
}

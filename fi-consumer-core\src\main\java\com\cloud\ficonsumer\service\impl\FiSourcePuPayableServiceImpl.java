package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.builder.YgInvoiceCheckVOBuilder;
import com.cloud.ficonsumer.builder.context.YgInvoiceCheckContext;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.FiSourcePuPayableDetailQueryDTO;
import com.cloud.ficonsumer.dto.FiSourcePuPayableQueryDTO;
import com.cloud.ficonsumer.dto.YgResp;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.enums.*;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourcePuPayableMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 采购应付源数据主表服务实现
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiSourcePuPayableServiceImpl extends ServiceImpl<FiSourcePuPayableMapper, FiSourcePuPayable> implements FiSourcePuPayableService {

    private final FiSourcePuPayableDetailService detailService;
    private final FiConvertPuPayableService convertService;
    private final FiConvertPuPayableDetailService convertDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final YgConfig ygConfig;
    private final YgInvoiceCheckVOBuilder ygInvoiceCheckVOBuilder;
    private final PlatformLogService platformLogService;
    private final DmMapper dmMapper;

    @Override
    public boolean push(String pkPayablebill) {
        // 获取源数据
        FiSourcePuPayable source = this.getOne(Wrappers.<FiSourcePuPayable>lambdaQuery()
                .eq(FiSourcePuPayable::getPkPayablebill, pkPayablebill));
        List<FiSourcePuPayableDetail> sourceDetails = detailService.list(Wrappers.<FiSourcePuPayableDetail>lambdaQuery()
                .eq(FiSourcePuPayableDetail::getPkPayablebill, pkPayablebill));

        // 初始化Context
        YgInvoiceCheckContext context = YgInvoiceCheckContext.builder()
                .withSourceData(source, sourceDetails)
                .build();

        if (context.isProjectPayable()) {
            context.
        }

        // 转换数据
        try {
            convertData(source, sourceDetails);
        } catch (Exception e) {
            log.error("【采购-发票校验申请服务】集成信息转换失败, 单据号: {}", source.getBillno(), e);
            handleConvertFailure(source, e);
            throw new CheckedException("【采购-发票校验申请服务】集成信息转换失败:" + e.getMessage());
        }

        // 推送数据
        try {
            pushToYgPlatform(source);
        } catch (Exception e) {
            log.error("【采购-发票校验申请服务】集成信息推送失败, 单据号: {}", source.getBillno(), e);
            handlePushFailure(source, e);
            throw new CheckedException("【采购-发票校验申请服务】集成信息推送失败:" + e.getMessage());
        }

        return true;
    }

    @Override
    public Page<FiSourcePuPayableVO> beforeConvertDataPage(Page<FiSourcePuPayableVO> page, FiSourcePuPayableQueryDTO queryDTO) {
        Page<FiSourcePuPayableVO> resulte = baseMapper.beforeConvertDataPage(page, queryDTO);
        if (CollectionUtil.isNotEmpty(resulte.getRecords())){
            for (FiSourcePuPayableVO data : resulte.getRecords()){
                data.setReturnStatusName(ReturnStatusEnum.getByCode(data.getReturnStatus()).getName());
                if(StrUtil.isNotBlank(data.getPkTradetype())) {
                    if(data.getPkTradetype().equals("F1-Cxx-02")) {
                        data.setPkTradetypeName("采购应付单");
                    }else if(data.getPkTradetype().equals("F1-Cxx-01")) {
                        data.setPkTradetypeName("项目应付单");
                    }
                }
                FiSourcePuPayableDetail detail = detailService.getOne(Wrappers.<FiSourcePuPayableDetail>lambdaQuery()
                        .eq(FiSourcePuPayableDetail::getPkPayablebill, data.getPkPayablebill()).last("limit 1"));
                if(null != detail) {
                    data.setPkSupplierName(dmMapper.getSupplierName(detail.getSupplier()));
                    data.setBillcode(detail.getPurchaseorder());
                }
                ApiCompare apiCompare = apiCompareCache.getDictionaryValue(BillTypeEnum.PU_PAYABLE.getCode(), "billmaker", YgUtil.toStringTrim(data.getBillmaker()));
                data.setBillmakerName(Objects.isNull(apiCompare)?null:apiCompare.getTargetSystemName());
                ApiCompare apiCompare1 = apiCompareCache.getDictionaryValue(BillTypeEnum.PU_PAYABLE.getCode(), "pk_currtype", YgUtil.toStringTrim(data.getPkCurrtype()));
                data.setPkCurrtypeName(Objects.isNull(apiCompare1)?null:apiCompare1.getTargetSystemCode());
                if(StrUtil.isNotBlank(data.getDef47())) {
                    data.setDef47Name(dmMapper.getCosTypeNameByPk(data.getDef47()));
                }
            }
        }
        return resulte;
    }

    @Override
    public Page<FiSourcePuPayableDetailVO> getBeforeConvertDataPage(Page<FiSourcePuPayableDetailVO> page, FiSourcePuPayableDetailQueryDTO queryDTO) {
        return baseMapper.getBeforeConvertDataPage(page, queryDTO);
    }

    @Override
    public List<FiConvertPuPayableVO> getConvertList(String pkPayablebill) {
        List<FiConvertPuPayableVO> voList = new ArrayList<>();
        FiConvertPuPayable convert = convertService.getOne(
                Wrappers.<FiConvertPuPayable>lambdaQuery()
                        .eq(FiConvertPuPayable::getPkPayablebill, pkPayablebill));
        if (convert != null) {
            FiConvertPuPayableVO vo = new FiConvertPuPayableVO();
            BeanUtils.copyProperties(convert, vo);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public Page<FiConvertPuPayableDetailVO> getConvertData(Page<FiConvertPuPayableDetailVO> page, String pkPayablebill) {
        return baseMapper.getConvertData(page, pkPayablebill);
    }

    @Override
    public Map<String, FiSourcePuPayableDetailVO> getNoPostedBySrcBillIds(List<String> srcBillIds) {
        if (CollectionUtils.isEmpty(srcBillIds)) {
            return Collections.emptyMap();
        }
        return baseMapper.getNoPostedBySrcBillIds(srcBillIds);
    }

    private void convertData(FiSourcePuPayable source, List<FiSourcePuPayableDetail> sourceDetails) {
        String pkPayablebill = source.getPkPayablebill();

        // 转换主表数据
        FiConvertPuPayable convert = new FiConvertPuPayable();
        apiCompareCache.convertData(source, convert, BillTypeEnum.PU_PAYABLE.getCode());

        // 处理已存在的转换数据
        FiConvertPuPayable existing = convertService.getOne(Wrappers.<FiConvertPuPayable>lambdaQuery()
                .eq(FiConvertPuPayable::getPkPayablebill, pkPayablebill));
        if (existing != null) {
            convert.setId(existing.getId());
            convertDetailService.remove(Wrappers.<FiConvertPuPayableDetail>lambdaQuery()
                    .eq(FiConvertPuPayableDetail::getPkPayablebill, pkPayablebill));
        }

        // 转换明细数据
        List<FiConvertPuPayableDetail> convertDetails = new ArrayList<>();
        for (FiSourcePuPayableDetail detail : sourceDetails) {
            FiConvertPuPayableDetail convertDetail = new FiConvertPuPayableDetail();
            apiCompareCache.convertData(detail, convertDetail, BillTypeEnum.PU_PAYABLE_DETAIL.getCode());
            convertDetail.setId(null);
            convertDetails.add(convertDetail);
        }

        // 保存转换后的数据
        convertService.saveOrUpdate(convert);
        convertDetailService.saveOrUpdateBatch(convertDetails);
    }

    private void pushToYgPlatform(FiSourcePuPayable source) {
        if (!ygConfig.getEnable()) {
            return;
        }

        String pkPayablebill = source.getPkPayablebill();
        FiConvertPuPayable convert = convertService.getOne(Wrappers.<FiConvertPuPayable>lambdaQuery()
                .eq(FiConvertPuPayable::getPkPayablebill, pkPayablebill));
        List<FiConvertPuPayableDetail> convertDetails = convertDetailService.list(Wrappers.<FiConvertPuPayableDetail>lambdaQuery()
                .eq(FiConvertPuPayableDetail::getPkPayablebill, pkPayablebill));

        YgInvoiceCheckVO ygInvoiceCheckVO = ygInvoiceCheckVOBuilder.build(convert, convertDetails);
        ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000010.getServCode());

        Map<String, Object> data = new HashMap<>();
        data.put("list", Collections.singletonList(ygInvoiceCheckVO));
        log.info("【采购-发票校验申请服务】同步，同步参数为: {}", JSONObject.toJSONString(data));
        String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(data));
        // 对 result 的返回值进行判空处理
        if (StringUtils.isEmpty(result)) {
            throw new CheckedException("响应失败，响应为空。");
        }

        YgResult<List<YgResp>> ygResult = JSONObject.parseObject(result, 
            new TypeReference<YgResult<List<YgResp>>>() {});

        // 首先判断外层响应是否成功
        if ("6100000".equals(ygResult.getCode()) || "6710300".equals(ygResult.getCode())) {
            // 检查data是否为空
            if (ygResult.getData() == null || CollectionUtils.isEmpty(ygResult.getData())) {
                throw new CheckedException("响应数据为空");
            }
            
            // 检查内层响应状态
            boolean hasError = false;
            StringBuilder errorMsg = new StringBuilder();
            
            for (YgResp resp : ygResult.getData()) {
                if (!"0".equals(resp.getCode())) {
                    hasError = true;
                    errorMsg.append("单据[").append(resp.getDisCode()).append("]处理失败：")
                            .append(resp.getMessage()).append("; ");
                }
            }
            
            if (hasError) {
                throw new CheckedException("部分单据处理失败: " + errorMsg);
            }
            
            // 所有检查通过，更新状态为成功
            updatePushSuccess(source);
        } else if ("6990399".equals(ygResult.getCode())) {
            throw new CheckedException("响应失败，响应结果: " + JSON.toJSONString(ygResult));
        } else {
            throw new CheckedException("未知响应，响应结果: " + JSON.toJSONString(ygResult));
        }
    }

    private void handleConvertFailure(FiSourcePuPayable source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setPushTime(DateUtil.formatDateTime(new Date()));
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        platformLogService.recordLog(
            source.getPkPayablebill(),
            source.getPkTradetype(),
            Constants.FAIL,
            pushMsg
        );
        LogUtil.error(log, "【采购-发票校验申请服务】集成信息转换失败:", e);
    }

    private void handlePushFailure(FiSourcePuPayable source, Exception e) {
        String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
        source.setPushTime(DateUtil.formatDateTime(new Date()));
        source.setIntegrationStatus("1");
        source.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
        source.setPushMsg(pushMsg);
        this.updateById(source);

        platformLogService.recordLog(
            source.getPkPayablebill(),
            source.getPkTradetype(),
            Constants.FAIL,
            pushMsg
        );
        LogUtil.error(log, "【采购-发票校验申请服务】集成信息推送失败:", e);
    }

    private void updatePushSuccess(FiSourcePuPayable source) {
        source.setPushTime(DateUtil.formatDateTime(new Date()));
        source.setIntegrationStatus("0");
        source.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
        source.setPushMsg("");
        source.setReadiness(1l);
        source.setReturnStatus("");
        source.setReturnMsg("");
        source.setReturnTime("");
        source.setReturnBill("");
        this.updateById(source);

        platformLogService.recordLog(
            source.getPkPayablebill(),
            source.getPkTradetype(),
            Constants.SUCCESS,
            ""
        );
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourcePuPayable entity = this.getById(id);
            try {
                this.push(entity.getPkPayablebill());
            } catch (Exception e) {
                resultList.add(entity.getBillno()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}

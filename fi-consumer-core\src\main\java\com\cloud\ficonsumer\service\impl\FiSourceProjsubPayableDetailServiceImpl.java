package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceProjsubPayableDetail;
import com.cloud.ficonsumer.mapper.FiSourceProjsubPayableDetailMapper;
import com.cloud.ficonsumer.service.FiSourceProjsubPayableDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourceProjsubPayableDetailServiceImpl extends ServiceImpl<FiSourceProjsubPayableDetailMapper, FiSourceProjsubPayableDetail> implements FiSourceProjsubPayableDetailService {

}

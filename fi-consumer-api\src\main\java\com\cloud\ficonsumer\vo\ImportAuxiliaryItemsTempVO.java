package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/8/3.
 */
@Data
@ColumnWidth(25)
public class ImportAuxiliaryItemsTempVO {

    /**
     * 科目编码
     */
    @ExcelProperty(value="科目编码" ,index = 0)
    @ColumnWidth(20)
    private String itemNumber;

    /**
     * 科目名称
     */
    @ExcelProperty(value="科目名称" ,index = 1)
    @ColumnWidth(20)
    private String itemName;

    /**
     * 辅助核算
     */
    @ExcelProperty(value="辅助核算" ,index = 2)
    @ColumnWidth(20)
    private String auxiliary;

    /**
     * 方向
     */
    @ExcelProperty(value="方向" ,index = 3)
    @ColumnWidth(10)
    private String direction;

    /**
     * 期初余额
     */
    @ExcelProperty(value="期初余额" ,index = 4)
    @ColumnWidth(15)
    private String startBalance;

    /**
     * 本期借方
     */
    @ExcelProperty(value="本期借方" ,index = 5)
    @ColumnWidth(15)
    private String borrow;

    /**
     * 本期贷方
     */
    @ExcelProperty(value="本期贷方" ,index = 6)
    @ColumnWidth(15)
    private String loan;

    /**
     * 借方累计
     */
    @ExcelProperty(value="借方累计" ,index = 7)
    @ColumnWidth(15)
    private String borrowTotal;

    /**
     * 贷方累计
     */
    @ExcelProperty(value="贷方累计" ,index = 8)
    @ColumnWidth(15)
    private String loanTotal;

    /**
     * 期末方向
     */
    @ExcelProperty(value="期末方向" ,index = 9)
    @ColumnWidth(10)
    private String endDirection;

    /**
     * 期末余额
     */
    @ExcelProperty(value="期末余额" ,index = 10)
    @ColumnWidth(15)
    private String endBalance;

    /**
     * 部门编码
     */
    @ExcelProperty(value="部门编码" ,index = 11)
    private String def1Code;

    /**
     * 部门名称
     */
    @ExcelProperty(value="部门名称" ,index = 12)
    private String def1Name;

    /**
     * 银行账户编码
     */
    @ExcelProperty(value="银行账户编码" ,index = 13)
    private String def2Code;

    /**
     * 银行账户名称
     */
    @ExcelProperty(value="银行账户名称" ,index = 14)
    private String def2Name;

    /**
     * 供应商档案编码
     */
    @ExcelProperty(value="供应商档案编码" ,index = 15)
    private String def3Code;

    /**
     * 供应商档案名称
     */
    @ExcelProperty(value="供应商档案名称" ,index = 16)
    private String def3Name;

    /**
     * 应收票据编码
     */
    @ExcelProperty(value="应收票据编码" ,index = 17)
    private String def4Code;

    /**
     * 应收票据名称
     */
    @ExcelProperty(value="应收票据名称" ,index = 18)
    private String def4Name;

    /**
     * 客户档案编码
     */
    @ExcelProperty(value="客户档案编码" ,index = 19)
    private String def5Code;

    /**
     * 客户档案名称
     */
    @ExcelProperty(value="客户档案名称" ,index = 20)
    private String def5Name;

    /**
     * 融资类型编码
     */
    @ExcelProperty(value="融资类型编码" ,index = 21)
    private String def6Code;

    /**
     * 融资类型名称
     */
    @ExcelProperty(value="融资类型名称" ,index = 22)
    private String def6Name;
    
    /**
     * 增值税税码税率编码
     */
    @ExcelProperty(value="增值税税码税率编码" ,index = 23)
    private String def7Code;

    /**
     * 增值税税码税率名称
     */
    @ExcelProperty(value="增值税税码税率名称" ,index = 24)
    private String def7Name;
    
    /**
     * 税项编码
     */
    @ExcelProperty(value="税项编码" ,index = 25)
    private String def8Code;

    /**
     * 税项名称
     */
    @ExcelProperty(value="税项名称" ,index = 26)
    private String def8Name;

    /**
     * 往来款性质编码
     */
    @ExcelProperty(value="往来款性质编码" ,index = 27)
    private String def9Code;

    /**
     * 往来款性质名称
     */
    @ExcelProperty(value="往来款性质名称" ,index = 28)
    private String def9Name;

    /**
     * 筹融资合同编码
     */
    @ExcelProperty(value="筹融资合同编码" ,index = 29)
    private String def10Code;

    /**
     * 筹融资合同名称
     */
    @ExcelProperty(value="筹融资合同名称" ,index = 30)
    private String def10Name;

    /**
     * 应付债券编码
     */
//    @ExcelProperty(value="应付债券编码" ,index = 31)
    private String def11Code;

    /**
     * 应付债券名称
     */
//    @ExcelProperty(value="应付债券名称" ,index = 32)
    private String def11Name;

    /**
     * 成本大类编码
     */
    @ExcelProperty(value="成本大类编码" ,index = 31)
    private String def12Code;

    /**
     * 成本大类名称
     */
    @ExcelProperty(value="成本大类名称" ,index = 32)
    private String def12Name;

    /**
     * 项目编码
     */
    @ExcelProperty(value="项目编码" ,index = 33)
    private String def13Code;

    /**
     * 项目名称
     */
    @ExcelProperty(value="项目名称" ,index = 34)
    private String def13Name;

    /**
     * 费用明细编码
     */
    @ExcelProperty(value="费用明细编码" ,index = 35)
    private String def14Code;

    /**
     * 费用明细名称
     */
    @ExcelProperty(value="费用明细名称" ,index = 36)
    private String def14Name;

    /**
     * 房地产业务编码
     */
    @ExcelProperty(value="房地产业务编码" ,index = 37)
    private String def15Code;

    /**
     * 房地产业务名称
     */
    @ExcelProperty(value="房地产业务名称" ,index = 38)
    private String def15Name;

    /**
     * 内部订单编码
     */
    @ExcelProperty(value="内部订单编码" ,index = 39)
    private String def16Code;

    /**
     * 内部订单名称
     */
    @ExcelProperty(value="内部订单名称" ,index = 40)
    private String def16Name;

    /**
     * 现金流量项目编码
     */
    @ExcelProperty(value="现金流量项目编码" ,index = 41)
    private String def17Code;

    /**
     * 现金流量项目名称
     */
    @ExcelProperty(value="现金流量项目名称" ,index = 42)
    private String def17Name;

    /**
     * 产品服务编码
     */
    @ExcelProperty(value="产品服务编码" ,index = 43)
    private String def18Code;

    /**
     * 产品服务名称
     */
    @ExcelProperty(value="产品服务名称" ,index = 44)
    private String def18Name;

    /**
     * 电能类型编码
     */
    @ExcelProperty(value="电能类型编码" ,index = 45)
    private String def19Code;

    /**
     * 电能类型名称
     */
    @ExcelProperty(value="电能类型名称" ,index = 46)
    private String def19Name;

    /**
     * 人工福利薪酬编码
     */
    @ExcelProperty(value="人工福利薪酬编码" ,index = 47)
    private String def20Code;

    /**
     * 人工福利薪酬名称
     */
    @ExcelProperty(value="人工福利薪酬名称" ,index = 48)
    private String def20Name;

    /**
     * 物业分公司专用编码
     */
    @ExcelProperty(value="物业分公司专用编码" ,index = 49)
    private String def21Code;

    /**
     * 物业分公司专用名称
     */
    @ExcelProperty(value="物业分公司专用名称" ,index = 50)
    private String def21Name;

    /**
     * 项目合同编码
     */
    @ExcelProperty(value="项目合同编码" ,index = 51)
    private String def22Code;

    /**
     * 项目合同名称
     */
    @ExcelProperty(value="项目合同名称" ,index = 52)
    private String def22Name;

    /**
     * 业务线条编码
     */
    @ExcelProperty(value="业务线条编码" ,index = 53)
    private String def23Code;

    /**
     * 业务线条名称
     */
    @ExcelProperty(value="业务线条名称" ,index = 54)
    private String def23Name;

    /**
     * 工单编码
     */
    @ExcelProperty(value="工单编码" ,index = 55)
    private String def24Code;

    /**
     * 工单名称
     */
    @ExcelProperty(value="工单名称" ,index = 56)
    private String def24Name;

    /**
     * 人员档案编码
     */
    @ExcelProperty(value="人员档案编码" ,index = 57)
    private String def25Code;

    /**
     * 人员档案名称
     */
    @ExcelProperty(value="人员档案名称" ,index = 58)
    private String def25Name;

    /**
     * 应付票据编码
     */
    @ExcelProperty(value="应付票据编码" ,index = 59)
    private String def26Code;

    /**
     * 应付票据名称
     */
    @ExcelProperty(value="应付票据名称" ,index = 60)
    private String def26Name;

    /**
     * 资金拨付项目编码
     */
    @ExcelProperty(value="资金拨付项目编码" ,index = 61)
    private String def27Code;

    /**
     * 资金拨付项目名称
     */
    @ExcelProperty(value="资金拨付项目名称" ,index = 62)
    private String def27Name;

    /**
     * 长期借款合同编码
     */
//    @ExcelProperty(value="长期借款合同编码" ,index = 65)
    private String def28Code;

    /**
     * 长期借款合同名称
     */
//    @ExcelProperty(value="长期借款合同名称" ,index = 66)
    private String def28Name;

    /**
     * 集团账户业务编码
     */
    @ExcelProperty(value="集团账户业务编码" ,index = 63)
    private String def29Code;

    /**
     * 集团账户业务名称
     */
    @ExcelProperty(value="集团账户业务名称" ,index = 64)
    private String def29Name;

    /**
     * 采购订单编码
     */
    @ExcelProperty(value="采购订单编码" ,index = 65)
    private String def30Code;

    /**
     * 采购订单名称
     */
    @ExcelProperty(value="采购订单名称" ,index = 66)
    private String def30Name;

    /**
     * 短期借款合同编码
     */
//    @ExcelProperty(value="短期借款合同编码" ,index = 71)
    private String def31Code;

    /**
     * 短期借款合同名称
     */
//    @ExcelProperty(value="短期借款合同名称" ,index = 72)
    private String def31Name;

    /**
     * 市场成员编码
     */
    @ExcelProperty(value="市场成员编码" ,index = 67)
    private String def32Code;

    /**
     * 市场成员名称
     */
    @ExcelProperty(value="市场成员名称" ,index = 68)
    private String def32Name;

    /**
     * 循环借款合同编码
     */
//    @ExcelProperty(value="循环借款合同编码" ,index = 75)
    private String def33Code;

    /**
     * 循环借款合同名称
     */
//    @ExcelProperty(value="循环借款合同名称" ,index = 76)
    private String def33Name;

    /**
     * 外币借款合同编码
     */
//    @ExcelProperty(value="外币借款合同编码" ,index = 77)
    private String def34Code;

    /**
     * 外币借款合同名称
     */
//    @ExcelProperty(value="外币借款合同名称" ,index = 78)
    private String def34Name;

}

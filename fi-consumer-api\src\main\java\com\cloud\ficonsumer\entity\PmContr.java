package com.cloud.ficonsumer.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 付款合同实体
 */
@Data
@ApiModel(value = "付款合同")
public class PmContr implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private String pkContr;

    @ApiModelProperty(value = "单据号")
    private String billCode;

    @ApiModelProperty("无税合同额")
    private BigDecimal currMny;

    @ApiModelProperty("含税合同额")
    private BigDecimal currTaxmny;

    @ApiModelProperty(value = "同步集成中台状态")
    private String hdef44;
}
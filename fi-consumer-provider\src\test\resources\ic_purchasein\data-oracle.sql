INSERT INTO IC_PURCHASEIN_H
(APPROVER, B<PERSON>LMAKER, B<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CBIZID, CBIZTYP<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CCUSTOMERID, CDPTID, <PERSON><PERSON><PERSON>, CFAN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>AN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CGENERALHID, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CPAYFINORGO<PERSON>, CP<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>RORG<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CREA<PERSON><PERSON><PERSON><PERSON>, CREATOR, CRECECOUNTRYID, CSENDCOUNTRYID, <PERSON>ENDTYPEID, CTAXCOUNTRYID, CTRADEWORDID, CTRANT<PERSON>EID, CVENDORID, CWAREHOUSEID, CWHSMANAGERI<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DMAKE<PERSON>TE, DR, FBI<PERSON><PERSON>AG, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, F<PERSON><PERSON><PERSON><PERSON><PERSON>LAG, <PERSON>RINTCOUNT, MOD<PERSON>IEDTIME, MODIFIER, NTOTALNUM, NTOTALPIECE, NTOTALVOLUME, NTOTALWEIG<PERSON>, P<PERSON><PERSON><PERSON><PERSON>, P<PERSON>_<PERSON>AS<PERSON>RE, PK_<PERSON><PERSON>, PK_<PERSON>G_V, SAGA_BTXID, SAGA_FROZEN, SAGA_GTXID, SAGA_STATUS, TAUDITTIME, TS, VBILLCODE, VDEF1, VDEF10, VDEF11, VDEF12, VDEF13, VDEF14, VDEF15, VDEF16, VDEF17, VDEF18, VDEF19, VDEF2, VDEF20, VDEF3, VDEF4, VDEF5, VDEF6, VDEF7, VDEF8, VDEF9, VNOTE, VRETURNREASON, VTRANTYPECODE)
VALUES('1001A710000000JAFQ2S', '1001A710000000JAFQ2S', 'N', 'N', '0001A11000000000DG25', '1001A710000000JTPY0P', '1001A710000000K16WAA', '~', '1001A1100000000E7T8M', '0001A11000000000CZO5', '0001A110000000004OK3', '0001A110000000004OK2', '1001A710000000LD4Z4D', '0001A110000000004OK3', '0001A110000000004OK2', '0001A110000000004OK3', '0001A110000000004OK2', '0001A110000000004OK3', '0001A110000000004OK2', '2025-04-09 09:20:00', '1001A710000000JAFQ2S', '0001Z010000000079UJJ', '0001Z010000000079UJJ', '~', '0001Z010000000079UJJ', '~', '0001A110000000001KQ6', '1001A1100000000I8JIF', '1001A710000000K16R42', '~', '2025-04-09 09:19:27', '2025-04-09 09:19:56', 0, 3, 2, 'N', 0, NULL, '~', 3, 0, 0, 0, '0001A110000000000CE4', '~', '0001A110000000004OK3', '0001A110000000004OK2', '754605d8-7559-4b70-bdcd-49106a8aa707', 0, '202504092261d532-f8e5-4f12-9082-f2c91d2d9dbf', 0, '2025-04-09 09:20:19', '2025-04-09 09:20:24', 'CR2025040900087830', '~', 'N', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', 'N', 'Y', 'N', '~', '~', '~', NULL, '~', '45-01');

INSERT INTO IC_PURCHASEIN_B
(BASSETCARD, BBARCODECLOSE, BCOMPARE, BFIXEDASSET, BONROADFLAG, BOPPTAXFLAG, BORROWINFLAG, BSOURCELARGESS, CARRIVEORDER_BBID, CASSCUSTID, CASTUNITID, CBODYTRANSTYPECODE, CBODYWAREHOUSEID, CCHECKSTATEID, CCORRESPONDBID, CCORRESPONDCODE, CCORRESPONDHID, CCORRESPONDROWNO, CCORRESPONDTRANSTYPE, CCORRESPONDTYPE, CCURRENCYID, CDESTIAREAID, CDESTICOUNTRYID, CDETAILBID, CDETAILID, CDETAILROWNO, CETDETLPICKBID, CFANACEORGOID, CFFILEID, CFIRSTBILLBID, CFIRSTBILLHID, CFIRSTTRANSTYPE, CFIRSTTYPE, CGENERALBID, CGENERALHID, CGLOBALCURRENCYID, CGROUPCURRENCYID, CIOLIABILITYOID, CIOLIABILITYVID, CLIABILITYOID, CLIABILITYVID, CLOCATIONID, CMATERIALOID, CMATERIALVID, CORDER_BB1ID, CORIGAREAID, CORIGCOUNTRYID, CORIGCURRENCYID, CORPOID, CORPVID, CPRODUCTORID, CPROJECTID, CPROJECTTASKID, CQTUNITID, CREQSTOORGOID, CREQSTOORGVID, CROWNO, CSELASTUNITID, CSOURCEBILLBID, CSOURCEBILLHID, CSOURCETRANSTYPE, CSOURCETYPE, CSRC2BILLBID, CSRC2BILLHID, CSRC2BILLTYPE, CSRC2TRANSTYPE, CSRCMATERIALOID, CSRCMATERIALVID, CSTATEID, CTAXCODEID, CTPLCUSTOMERID, CUNITID, CVENDORID, CVMIVENDERID, DBIZDATE, DPRODUCEDATE, DR, DVALIDATE, FBILLROWFLAG, FCHECKED, FLARGESS, FTAXTYPEFLAG, IDESATYPE, NASSISTNUM, NBARCODENUM, NCALCOSTMNY, NCALTAXMNY, NCHANGESTDRATE, NCOUNTNUM, NGLOBALEXCHGRATE, NGLOBALMNY, NGLOBALTAXMNY, NGROSSNUM, NGROUPEXCHGRATE, NGROUPMNY, NGROUPTAXMNY, NITEMDISCOUNTRATE, NKDNUM, NMNY, NNETPRICE, NNOSUBTAX, NNOSUBTAXRATE, NNUM, NORIGMNY, NORIGNETPRICE, NORIGPRICE, NORIGTAXMNY, NORIGTAXNETPRICE, NORIGTAXPRICE, NPIECE, NPLANNEDMNY, NPLANNEDPRICE, NPRICE, NQTNETPRICE, NQTORIGNETPRICE, NQTORIGPRICE, NQTORIGTAXNETPRICE, NQTORIGTAXPRICE, NQTPRICE, NQTTAXNETPRICE, NQTTAXPRICE, NQTUNITNUM, NSHOULDASSISTNUM, NSHOULDNUM, NTARENUM, NTAX, NTAXMNY, NTAXNETPRICE, NTAXPRICE, NTAXRATE, NVOLUME, NWEIGHT, PK_BATCHCODE, PK_CREQWAREID, PK_GROUP, PK_MEASWARE, PK_ORG, PK_ORG_V, PK_PACKSORT, PK_SERIALCODE, TS, VBATCHCODE, VBDEF1, VBDEF10, VBDEF11, VBDEF12, VBDEF13, VBDEF14, VBDEF15, VBDEF16, VBDEF17, VBDEF18, VBDEF19, VBDEF2, VBDEF20, VBDEF3, VBDEF4, VBDEF5, VBDEF6, VBDEF7, VBDEF8, VBDEF9, VBILLBARCODE, VCHANGERATE, VDETAILBILLCODE, VEXIGENCYBID, VEXIGENCYCODE, VEXIGENCYHID, VEXIGENCYROWNO, VFIRSTBILLCODE, VFIRSTROWNO, VFREE1, VFREE10, VFREE2, VFREE3, VFREE4, VFREE5, VFREE6, VFREE7, VFREE8, VFREE9, VITCONTRACTBILLCODE, VNOTEBODY, VQTUNITRATE, VRETURNREASON, VSERIALCODE, VSOURCEBILLCODE, VSOURCEROWNO, VSRC2BILLCODE, VSRC2BILLROWNO, VBDEF21, VBDEF22, VBDEF23, VBDEF24, VBDEF25, VBDEF26, VBDEF27, VBDEF28, VBDEF29, VBDEF30)
VALUES('N', 'N', 'N', 'N', 'N', 'N', 'N', 'N', '~', '~', '0001Z0100000000000XT', '45-01', '1001A710000000K16R42', NULL, '~', NULL, '~', '~', '~', '~', '1002Z0100000000001K1', '~', '~', '~', '~', '~', '~', '0001A110000000004OK3', '~', '1001A710000000LD4YOF', '1001A710000000LD4YOE', '1001A710000000K7SDS3', '21', '1001A710000000LD4Z4E', '1001A710000000LD4Z4D', '~', '~', '~', '~', '~', '~', '~', '1001A710000000K5LDVT', '1001A710000000K5LDVT', NULL, '~', '~', '1002Z0100000000001K1', '0001A110000000004OK3', '0001A110000000004OK2', '~', '~', '~', '0001Z0100000000000XT', '0001A110000000004OK3', '0001A110000000004OK2', '10', '~', '1001A710000000LD4YZ1', '1001A710000000LD4YZ0', '0001A110000000001KPF', '23', '~', '~', '~', '~', '1001A710000000K5LDVT', '1001A710000000K5LDVT', '~', '1002Z01000000001CNE2', '~', '0001Z0100000000000XT', '1001A1100000000I8JIF', '~', '2025-04-09 09:19:27', NULL, 0, NULL, NULL, NULL, 'N', 1, NULL, 3, NULL, 30000, 30000, 1, NULL, NULL, NULL, NULL, NULL, 1, 30000, 32700, 100, NULL, 30000, 10000, 0, 0, 3, 30000, 10000, 10000, 32700, 10900, 10900, NULL, NULL, NULL, 10000, 10000, 10000, 10000, 10900, 10900, 10000, 10900, 10900, 3, 3, 3, NULL, 2700, 32700, 10900, 10900, 9, 0, 0, '~', '~', '0001A110000000000CE4', '~', '0001A110000000004OK3', '0001A110000000004OK2', '~', '~', '2025-04-09 09:23:24', NULL, NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, '~', NULL, '1/1', NULL, '~', NULL, '~', NULL, 'CD2025040900104915', '10', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', NULL, NULL, '1/1', '~', NULL, 'DH2025040900028355', '10', NULL, '~', '~', '~', '~', '~', '~', '~', '~', '~', '~', '~');


package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertReceivableDetail;
import com.cloud.ficonsumer.mapper.FiConvertReceivableDetailMapper;
import com.cloud.ficonsumer.service.FiConvertReceivableDetailService;
import org.springframework.stereotype.Service;

/**
 * 项目应收详情单转换数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:32
 */
@Service
public class FiConvertReceivableDetailServiceImpl extends ServiceImpl<FiConvertReceivableDetailMapper, FiConvertReceivableDetail> implements FiConvertReceivableDetailService {

}

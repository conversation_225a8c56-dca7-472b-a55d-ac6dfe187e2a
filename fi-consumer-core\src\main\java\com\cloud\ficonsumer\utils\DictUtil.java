package com.cloud.ficonsumer.utils;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.cloud.cloud.admin.api.entity.SysDictItem;
import com.cloud.cloud.admin.api.feign.RemoteDictService;
import com.cloud.cloud.common.core.constant.SecurityConstants;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.cloud.common.data.dict.annotation.Dict;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 字典工具类
 * 用于将标注了@Dict注解的字段进行字典值转换
 */
@Slf4j
@UtilityClass
public class DictUtil {

    /**
     * 转换集合中的字典值
     *
     * @param coll 需要转换的集合
     * @param <T>  集合元素类型
     */
    public <T> void convertDict(Collection<T> coll) {
        if (CollectionUtil.isEmpty(coll)) {
            return;
        }

        try {
            // 获取集合中元素的类型
            Class<?> collClazz = coll.iterator().next().getClass();
            Field[] fields = collClazz.getDeclaredFields();

            // 收集需要转换的字典代码
            Map<String, Map<String, SysDictItem>> dictMap = new HashMap<>();
            for (Field field : fields) {
                if (AnnotationUtil.hasAnnotation(field, Dict.class)) {
                    String dicCode = String.valueOf(
                            AnnotationUtil.getAnnotationValueMap(field, Dict.class).get("dicCode")
                    );
                    // 批量获取字典数据
                    dictMap.put(dicCode, getDictValue(dicCode));
                }
            }

            // 批量处理字段转换
            for (T item : coll) {
                for (Field field : fields) {
                    if (AnnotationUtil.hasAnnotation(field, Dict.class)) {
                        String dicCode = String.valueOf(
                                AnnotationUtil.getAnnotationValueMap(field, Dict.class).get("dicCode")
                        );
                        replaceValueWithDict(item, field, dictMap.get(dicCode));
                    }
                }
            }
        } catch (Exception e) {
            log.error("字典转换失败", e);
            throw new CheckedException("字典转换过程中发生错误：" + e.getMessage());
        }
    }

    /**
     * 获取字典值映射
     */
    private static Map<String, SysDictItem> getDictValue(String dicCode) {
        try {
            return getRemoteDictService()
                    .getDictByTypeInner(dicCode, SecurityConstants.FROM_IN)
                    .getData()
                    .stream()
                    .collect(Collectors.toMap(SysDictItem::getValue, Function.identity()));
        } catch (Exception e) {
            log.error("获取字典数据失败", e);
            throw new CheckedException("获取字典数据失败：" + e.getMessage());
        }
    }

    /**
     * 替换单个字段值为字典标签
     */
    private static <T> void replaceValueWithDict(T item, Field field, Map<String, SysDictItem> dictValue) {
        try {
            field.setAccessible(true);
            Object fieldValue = field.get(item);
            if (Objects.nonNull(fieldValue)) {
                SysDictItem dictItem = dictValue.get(String.valueOf(fieldValue));
                if (Objects.nonNull(dictItem)) {
                    field.set(item, dictItem.getLabel());
                }
            }
        } catch (IllegalAccessException e) {
            log.error("字段值替换失败", e);
            throw new CheckedException("字段值替换失败：" + e.getMessage());
        }
    }

    /**
     * 获取字典服务实例
     */
    private static RemoteDictService getRemoteDictService() {
        try {
            return SpringContextHolder.getBean(RemoteDictService.class);
        } catch (Exception e) {
            log.error("获取字典服务失败", e);
            throw new CheckedException("获取字典服务失败：" + e.getMessage());
        }
    }
}
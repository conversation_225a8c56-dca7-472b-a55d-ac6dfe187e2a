package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiSourceFileInfoQueryDTO;
import com.cloud.ficonsumer.dto.FiSourceFileOcrInfoQueryDTO;
import com.cloud.ficonsumer.entity.FiSourceFileOcrInfo;
import com.cloud.ficonsumer.vo.FiConvertFileInfoVO;
import com.cloud.ficonsumer.vo.FiSourceFileInfoVO;
import com.cloud.ficonsumer.vo.FiSourceFileOcrInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 进项发票源数据Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface FiSourceFileOcrInfoMapper extends CloudBaseMapper<FiSourceFileOcrInfo> {

    /**
     * 查询转换前的分页数据
     */
    Page<FiSourceFileOcrInfoVO> beforeConvertDataPage(Page<FiSourceFileOcrInfoVO> page, @Param("param") FiSourceFileOcrInfoQueryDTO queryDTO);

    /**
     * 查询转换前的明细分页数据
     */
    Page<FiSourceFileInfoVO> getBeforeConvertDataPage(Page<FiSourceFileInfoVO> page, @Param("param") FiSourceFileInfoQueryDTO queryDTO);

    /**
     * 查询转换后的分页数据
     */
    Page<FiConvertFileInfoVO> getConvertData(Page<FiConvertFileInfoVO> page, @Param("uniquecodeofinvoice") String uniquecodeofinvoice);
}
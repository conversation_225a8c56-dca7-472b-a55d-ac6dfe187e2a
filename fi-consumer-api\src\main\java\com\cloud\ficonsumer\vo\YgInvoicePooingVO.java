package com.cloud.ficonsumer.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 进项发票入池请求VO
 */
@Data
@ApiModel(value = "YgInvoicePooingVO", description = "进项发票入池请求VO")
public class YgInvoicePooingVO {

    @ApiModelProperty(value = "发票使用单位", required = true)
    private String unitCode;

    @ApiModelProperty(value = "提交gid", required = true)
    private String subGid;

    @ApiModelProperty(value = "上传人名称", required = true)
    private String managerNm;

    @ApiModelProperty(value = "经办人ID")
    private String managerId;

    @ApiModelProperty(value = "上传人部门名称", required = true)
    private String hdlgDept;

    @ApiModelProperty(value = "经办部门编码")
    private String hdlgDeptCod;

    @ApiModelProperty(value = "使用人ID")
    private String userid;

    @ApiModelProperty(value = "来源文件存储系统类型", required = true)
    private String fileSource;

    @ApiModelProperty(value = "来源文件存储系统ID", required = true)
    private String fromFileSystemId;

    @ApiModelProperty(value = "电子凭证文件资源ID")
    private String elecBillInvFormatResId;

    @ApiModelProperty(value = "电子凭证文件名称")
    private String elecBillInvDoc;

    @ApiModelProperty(value = "电子凭证文件格式")
    private String elecBillInvType;

    @ApiModelProperty(value = "电子凭证下载地址")
    private String elecBillInvFormatAddr;

    @ApiModelProperty(value = "电子凭证UDS-ID")
    private String elecBillUdsResId;

    @ApiModelProperty(value = "发票文件资源id")
    private String resId;

    @ApiModelProperty(value = "发票文件资源名称")
    private String invoiceOrginDoc;

    @ApiModelProperty(value = "发票文件资源格式")
    private String invoiceOrginType;

    @ApiModelProperty(value = "发票文件下载地址")
    private String intranetDownloadAddr;

    @ApiModelProperty(value = "发票文件UDS-ID")
    private String udsResId;

    @ApiModelProperty(value = "OFD版式文件资源id")
    private String formatFileImgResId;

    @ApiModelProperty(value = "OFD版式文件资源名称")
    private String formatFileImgDoc;

    @ApiModelProperty(value = "OFD版式文件资源格式")
    private String formatFileImgType;

    @ApiModelProperty(value = "OFD版式文件下载地址")
    private String formatFileDownloadAddr;

    @ApiModelProperty(value = "OFD版式文件UDS-ID")
    private String formatFileUdsResId;

    @ApiModelProperty(value = "税局是否有销货清单")
    private String salList;

    @ApiModelProperty(value = "影像是否上传销货清单")
    private String fileHasSalList;

    @ApiModelProperty(value = "销货清单资源列表")
    private List<AccountSalesList> accountSalesList;

    @ApiModelProperty(value = "发票详情列表")
    private List<YgInvoicePooingDetail> invoiceDetailList;

    @ApiModelProperty(value = "价税合计", required = true)
    private String totPriTax;

    @ApiModelProperty(value = "发票类型编码", required = true)
    private String invoType;

    @ApiModelProperty(value = "发票代码")
    private String invoCode;

    @ApiModelProperty(value = "发票号码", required = true)
    private String invoiceNo;

    @ApiModelProperty(value = "纸质全电发票号码")
    private String elecInvoiceNo;

    @ApiModelProperty(value = "开票日期", required = true)
    private String invDate;

    @ApiModelProperty(value = "校验码")
    private String checkCode;

    @ApiModelProperty(value = "合计金额", required = true)
    private String amouInTot;

    @ApiModelProperty(value = "开票人", required = true)
    private String dParty;

    @ApiModelProperty(value = "发票来源")
    private String invoiceSources;

    @ApiModelProperty(value = "机器编号")
    private String machyCod;

    @ApiModelProperty(value = "是否代开")
    private String issueInvc;

    @ApiModelProperty(value = "密码区")
    private String taxCtrlCd;

    @ApiModelProperty(value = "收款人")
    private String payee;

    @ApiModelProperty(value = "成品油标志")
    private String refinedOil;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "复核人")
    private String reviewer;

    @ApiModelProperty(value = "合计税额", required = true)
    private String totalTax;

    @ApiModelProperty(value = "购买方银行账号")
    private String pAccBkNo;

    @ApiModelProperty(value = "购买方纳税人识别号", required = true)
    private String buyTxPyrNo;

    @ApiModelProperty(value = "购买方名称", required = true)
    private String buyerName;

    @ApiModelProperty(value = "销货单位名称", required = true)
    private String sellerName;

    @ApiModelProperty(value = "销货单位纳税人识别号", required = true)
    private String sellerTin;

    @ApiModelProperty(value = "销货单位地址")
    private String sellerAt;

    @ApiModelProperty(value = "销货单位开户银行")
    private String sellerBaNo;

    @ApiModelProperty(value = "购买方电话")
    private String byrAddrTel;

    @ApiModelProperty(value = "发票状态", required = true)
    private Integer invoStatus;

    @ApiModelProperty(value = "所属区域")
    private String belongArea;

    @ApiModelProperty(value = "收款员")
    private String recvTeller;

    @ApiModelProperty(value = "通行费标志")
    private String passFeeInd;
}

@Data
@ApiModel(value = "AccountSalesList", description = "销货清单资源列表")
class AccountSalesList {

    @ApiModelProperty(value = "销货清单文件资源id")
    private String resId;

    @ApiModelProperty(value = "销货清单资源文件名称")
    private String invoiceOrginDoc;

    @ApiModelProperty(value = "销货清单资源文件名称")
    private String invoiceOrginType;
}
package com.cloud.ficonsumer.controller;

import com.cloud.cloud.common.security.annotation.InnerService;
import com.cloud.ficonsumer.service.YgInvoiceService;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 调用远光校验增值税发票真伪服务
 */
@AllArgsConstructor
@RestController
@RequestMapping("/yg/invoice")
public class YgInvoiceController {

    private final YgInvoiceService ygInvoiceService;

    @InnerService
    @ApiOperation(value = "远光校验增值税发票真伪服务", notes = "远光校验增值税发票真伪服务")
    @PostMapping("/authenticity")
    public String ygInvoiceAuthenticity(@RequestParam("orgId") Long orgId, @RequestBody Map<String, Object> dto) {
        return ygInvoiceService.ygInvoiceAuthenticity(orgId, dto);
    }
}

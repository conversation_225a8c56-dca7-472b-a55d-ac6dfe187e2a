package com.cloud.ficonsumer.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.security.annotation.InnerService;
import com.cloud.common.idempotent.annotations.Idempotent;
import com.cloud.ficonsumer.dto.FiSourceCalBack;
import com.cloud.ficonsumer.dto.FiSourceCalBackDTO;
import com.cloud.ficonsumer.dto.FiVoucherCalBack;
import com.cloud.ficonsumer.dto.FiVoucherCalBackDTO;
import com.cloud.ficonsumer.dto.MaterialReceiptStatus;
import com.cloud.ficonsumer.dto.YgInvoiceStatus;
import com.cloud.ficonsumer.dto.YgRequestInvoicePosting;
import com.cloud.ficonsumer.dto.YgRequestInvoicePostingDTO;
import com.cloud.ficonsumer.dto.YgRequestInvoiceWriteOff;
import com.cloud.ficonsumer.dto.YgRequestInvoiceWriteOffDTO;
import com.cloud.ficonsumer.dto.YgResponseInvoiceVO;
import com.cloud.ficonsumer.dto.YgResponseStatusVO;
import com.cloud.ficonsumer.dto.YgRequestMaterialReceiptDTO;
import com.cloud.ficonsumer.entity.FiSourceAccounting;
import com.cloud.ficonsumer.entity.FiSourcePuPayable;
import com.cloud.ficonsumer.entity.FiSourcePuPaybill;
import com.cloud.ficonsumer.entity.FiSourceReimbursement;
import com.cloud.ficonsumer.enums.ReturnStatusEnum;
import com.cloud.ficonsumer.integration.dto.Param;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.FiInvoicePostingService;
import com.cloud.ficonsumer.service.FiSourceAccountingService;
import com.cloud.ficonsumer.service.FiSourcePuPayableService;
import com.cloud.ficonsumer.service.FiSourcePuPaybillService;
import com.cloud.ficonsumer.service.FiSourceReimbursementService;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.AssertUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/4/2.
 */
@RestController
@AllArgsConstructor
@Slf4j
@Api(value = "集成功能", tags = "集成功能")
@RequestMapping("/integration")
public class CalBackController {

    private final FiSourceReimbursementService fiSourceReimbursementService;
    private final FiSourcePuPayableService fiSourcePuPayableService;
    private final FiSourcePuPaybillService fiSourcePuPaybillService;
    private  final FiSourceAccountingService fiSourceAccountingService;
    private  final FiInvoicePostingService fiInvoicePostingService;
    private final DmMapper dmMapper;

    @ApiOperation(value = "单据状态回写", notes = "单据状态回写")
    @PostMapping("/reimbursementCalBack")
    @InnerService
    @Idempotent(key = "#param.head.seqNo", info = "#param.head.seqNo", expireTime = 10, timeUnit = TimeUnit.SECONDS)
    public Param<Boolean> reimbursementCalBack(@RequestBody Param<FiSourceCalBack> param) {
        try {
            FiSourceCalBack paramData = param.getBody().getData();
            List<FiSourceCalBackDTO> list = paramData.getList();
            if(CollectionUtil.isEmpty(list)) {
                throw new CheckedException("请求参数不能为空");
            }
            boolean result = false;
            for(FiSourceCalBackDTO data : list) {
                AssertUtils.notBlank(data.getBillno(), "编码不能为空");
                AssertUtils.notBlank(data.getBiType(), "业务类型不能为空");
                //只有“单据集成成功”才更新“返回接收时间”也就是智慧平台单据创建时间
                if(data.getBiType().equals("6")) {
                    result = fiSourceReimbursementService.update(Wrappers.<FiSourceReimbursement>lambdaUpdate()
                            .set(FiSourceReimbursement::getReturnStatus, data.getReturnStatus())
                            .set(StrUtil.isNotBlank(data.getReturnMsg()),FiSourceReimbursement::getReturnMsg, data.getReturnMsg())
                            .set(data.getReturnStatus().equals(ReturnStatusEnum.Enum12.getCode()),FiSourceReimbursement::getReturnBill, data.getReturnBill())
                            .set(FiSourceReimbursement::getReturnTime, data.getReturnTime())
                            .eq(FiSourceReimbursement::getBillno, data.getBillno()));
                }else if(data.getBiType().equals("3")) {
                    result = fiSourcePuPayableService.update(Wrappers.<FiSourcePuPayable>lambdaUpdate()
                            .set(FiSourcePuPayable::getReturnStatus, data.getReturnStatus())
                            .set(StrUtil.isNotBlank(data.getReturnMsg()),FiSourcePuPayable::getReturnMsg, data.getReturnMsg())
                            .set(data.getReturnStatus().equals(ReturnStatusEnum.Enum12.getCode()),FiSourcePuPayable::getReturnBill, data.getReturnBill())
                            .set(FiSourcePuPayable::getReturnTime, data.getReturnTime())
                            .set(FiSourcePuPayable::getYwid, data.getYwid())
                            .eq(FiSourcePuPayable::getBillno, data.getBillno()));
                }else if(data.getBiType().equals("1")||data.getBiType().equals("2")) {
                    result = fiSourcePuPaybillService.update(Wrappers.<FiSourcePuPaybill>lambdaUpdate()
                            .set(FiSourcePuPaybill::getReturnStatus, data.getReturnStatus())
                            .set(StrUtil.isNotBlank(data.getReturnMsg()),FiSourcePuPaybill::getReturnMsg, data.getReturnMsg())
                            .set(data.getReturnStatus().equals(ReturnStatusEnum.Enum12.getCode()),FiSourcePuPaybill::getReturnBill, data.getReturnBill())
                            .set(FiSourcePuPaybill::getReturnTime, data.getReturnTime())
                            .eq(FiSourcePuPaybill::getBillcode, data.getBillno()));
                }
            }
            return result ? Param.ok(param, true, "成功") : Param.failed(param, false,"失败");
        } catch (Exception e) {
            log.error("状态回写失败", e);
            return Param.failed(param, "状态回写失败,"+e.getMessage());
        }
    }

    @ApiOperation(value = "凭证状态回写", notes = "凭证状态回写")
    @PostMapping("/voucherCalBack")
    @InnerService
    @Idempotent(key = "#param.head.seqNo", info = "#param.head.seqNo", expireTime = 10, timeUnit = TimeUnit.SECONDS)
    public Param<Boolean> voucherCalBack(@RequestBody Param<FiVoucherCalBack> param) {
        try {
            FiVoucherCalBack paramData = param.getBody().getData();
            List<FiVoucherCalBackDTO> list = paramData.getList();
            if(CollectionUtil.isEmpty(list)) {
                throw new CheckedException("请求参数不能为空");
            }
            boolean result = false;
            for(FiVoucherCalBackDTO data : list) {
                AssertUtils.notBlank(data.getDocuniqueid(), "唯一键不能为空");
                result = fiSourceAccountingService.update(Wrappers.<FiSourceAccounting>lambdaUpdate()
                                .set(FiSourceAccounting::getReturnStatus,data.getCode())
                                .set(FiSourceAccounting::getReturnMsg,data.getMessage())
                                .set(FiSourceAccounting::getFindocid,data.getFindocid())
                        .eq(FiSourceAccounting::getPkVoucher, data.getDocuniqueid()));
                        dmMapper.updateAccounting(data);
            }
            return result ? Param.ok(param, true, "成功") : Param.failed(param, false,"失败");
        } catch (Exception e) {
            log.error("凭证状态回写失败", e);
            return Param.failed(param, "凭证状态回写失败,"+e.getMessage());
        }
    }

    @ApiOperation(value = "单据状态回写", notes = "单据状态回写")
    @PostMapping("/commonCalBack")
    @InnerService
    @Idempotent(key = "#param.head.seqNo", info = "#param.head.seqNo", expireTime = 10, timeUnit = TimeUnit.SECONDS)
    public JSONObject commonCalBack(@RequestBody Param<JSONObject> param) {
        Map<String,Object> data = new HashMap<>();
        try {
            JSONObject dataobject = param.getBody().getData();
            JSONArray dataArray = dataobject.getJSONArray("list");
            List<Map<String,Object>> list = new ArrayList<>();
            for(int i = 0;i < dataArray.size();i++) {
                Map<String,Object> tiems = new HashMap<>();
                JSONObject dataObject = (JSONObject) dataArray.get(i);
                String outerCode = dataObject.getStr("outerCode");
                String biType = dataObject.getStr("biType");
                String disCode = dataObject.getStr("disCode");
                String ywid = dataObject.getStr("ywid");
                String handData = dataObject.getStr("handData");
                String status = dataObject.getStr("status");
                String msg = dataObject.getStr("msg")==null?"":dataObject.getStr("msg");
                FiSourceCalBackDTO fiSourceCalBackDTO = new FiSourceCalBackDTO();
                if (status.equals(ReturnStatusEnum.Enum12.getCode())) {
                    fiSourceCalBackDTO.setSuccessMsg(msg);
                }
                fiSourceCalBackDTO.setBillno(disCode);
                fiSourceCalBackDTO.setReturnBill(ywid);
                fiSourceCalBackDTO.setReturnMsg(msg);
                fiSourceCalBackDTO.setReturnTime(handData);
                fiSourceCalBackDTO.setReturnStatus(status);
                if(msg.length()>150) {
                    msg = msg.substring(0,150);
                }
                AssertUtils.notBlank(disCode, "编码不能为空");
                AssertUtils.notBlank(biType, "业务类型不能为空");
                boolean result = false;
                //只有“单据集成成功”才更新“返回接收时间”也就是智慧平台单据创建时间
                if(biType.equals("6")) {
                    result = fiSourceReimbursementService.update(Wrappers.<FiSourceReimbursement>lambdaUpdate()
                            .set(FiSourceReimbursement::getReturnStatus, status)
                            .set(StrUtil.isNotBlank(msg),FiSourceReimbursement::getReturnMsg, msg)
                            .set(status.equals(ReturnStatusEnum.Enum12.getCode()),FiSourceReimbursement::getReturnBill, ywid)
                            .set(FiSourceReimbursement::getReturnTime, handData)
                            .eq(FiSourceReimbursement::getBillno, disCode));
                    dmMapper.updatePayablebillStatus(fiSourceCalBackDTO);
                }else if(biType.equals("3")) {
                    result = fiSourcePuPayableService.update(Wrappers.<FiSourcePuPayable>lambdaUpdate()
                            .set(FiSourcePuPayable::getReturnStatus, status)
                            .set(StrUtil.isNotBlank(msg),FiSourcePuPayable::getReturnMsg, msg)
                            .set(status.equals(ReturnStatusEnum.Enum12.getCode()),FiSourcePuPayable::getReturnBill, ywid)
                            .set(FiSourcePuPayable::getReturnTime, handData)
                            .set(FiSourcePuPayable::getYwid, ywid)
                            .eq(FiSourcePuPayable::getBillno, disCode));
                    dmMapper.updatePayablebillStatus(fiSourceCalBackDTO);
                }else if(biType.equals("1")||biType.equals("2")) {
                    result = fiSourcePuPaybillService.update(Wrappers.<FiSourcePuPaybill>lambdaUpdate()
                            .set(FiSourcePuPaybill::getReturnStatus, status)
                            .set(StrUtil.isNotBlank(msg),FiSourcePuPaybill::getReturnMsg, msg)
                            .set(status.equals(ReturnStatusEnum.Enum12.getCode()),FiSourcePuPaybill::getReturnBill, ywid)
                            .set(FiSourcePuPaybill::getReturnTime, handData)
                            .eq(FiSourcePuPaybill::getBillcode, disCode));
                    dmMapper.updatePaybillStatus(fiSourceCalBackDTO);
                }
                tiems.put("code",0);
                tiems.put("message","成功");
                tiems.put("ywid",ywid);
                tiems.put("disCode",disCode);
                list.add(tiems);
            }
            data.put("code","6100000");
            data.put("message","成功");
            data.put("data",list);
        } catch (Exception e) {
            log.error("状态回写失败", e);
            String message = AdminUtils.truncateString(e.getMessage(), 1000);
            data.put("code","6990399");
            data.put("message", message);
        }
        JSONObject jsonObject = JSONUtil.parseObj(data);
        return jsonObject;
    }

    @ApiOperation(value = "凭证状态回写", notes = "凭证状态回写")
    @PostMapping("/voucherStatusCalBack")
    @InnerService
    @Idempotent(key = "#param.head.seqNo", info = "#param.head.seqNo", expireTime = 10, timeUnit = TimeUnit.SECONDS)
    public JSONObject voucherStatusCalBack(@RequestBody Param<JSONObject> param) {
        Map<String,Object> resultData = new HashMap<>();
        try {
            JSONObject dataobject = param.getBody().getData();
            JSONArray dataArray = dataobject.getJSONArray("data");
            boolean result = false;
            for(int i = 0;i < dataArray.size();i++) {
                JSONObject dataObject = (JSONObject) dataArray.get(i);
                FiVoucherCalBackDTO data = dataObject.toBean(FiVoucherCalBackDTO.class);
                AssertUtils.notBlank(data.getDocuniqueid(), "唯一键不能为空");
                result = fiSourceAccountingService.update(Wrappers.<FiSourceAccounting>lambdaUpdate()
                        .set(FiSourceAccounting::getReturnStatus,data.getCode())
                        .set(FiSourceAccounting::getReturnMsg,data.getMessage())
                        .set(FiSourceAccounting::getFindocid,data.getFindocid())
                        .eq(FiSourceAccounting::getPkVoucher, data.getDocuniqueid()));
                dmMapper.updateAccounting(data);
            }
            resultData.put("code",0);
            resultData.put("message","成功");
        } catch (Exception e) {
            log.error("凭证状态回写失败", e);
            resultData.put("code",null);
            resultData.put("message","失败");
        }
        JSONObject jsonObject = JSONUtil.parseObj(resultData);
        return jsonObject;
    }

    @ApiOperation(value = "物资收货状态更新服务", notes = "物资收货状态更新服务")
    @PostMapping("/updateOfMaterialReceiptStatus")
    @InnerService
    @Idempotent(key = "#param.head.seqNo", info = "#param.head.seqNo", expireTime = 10, timeUnit = TimeUnit.SECONDS)
    public YgResponseStatusVO updateOfMaterialReceiptStatus(@RequestBody Param<YgRequestMaterialReceiptDTO> param) {
        YgResponseStatusVO ygResponseStatusVO = new YgResponseStatusVO();
        try {
            YgRequestMaterialReceiptDTO paramData = param.getBody().getData();
            List<MaterialReceiptStatus> list = paramData.getList();
            if(CollectionUtil.isEmpty(list)) {
                throw new CheckedException("请求参数不能为空");
            }
            for(MaterialReceiptStatus data : list) {
                AssertUtils.notBlank(data.getPurOrdNo(), "采购订单号不能为空");
            }
            ygResponseStatusVO.setCode("200");
            ygResponseStatusVO.setMessage("成功");
        } catch (Exception e) {
            ygResponseStatusVO.setCode("201");
            ygResponseStatusVO.setMessage(e.getMessage());
        }
        return ygResponseStatusVO;
    }

    @ApiOperation(value = "服务确认状态更新服务", notes = "物资收货状态更新服务")
    @PostMapping("/updateOfServiceConfirmationStatus")
    @InnerService
    @Idempotent(key = "#param.head.seqNo", info = "#param.head.seqNo", expireTime = 10, timeUnit = TimeUnit.SECONDS)
    public YgResponseStatusVO updateOfServiceConfirmationStatus(@RequestBody Param<YgRequestMaterialReceiptDTO> param) {
        YgResponseStatusVO ygResponseStatusVO = new YgResponseStatusVO();
        try {
            YgRequestMaterialReceiptDTO paramData = param.getBody().getData();
            List<MaterialReceiptStatus> list = paramData.getList();
            if(CollectionUtil.isEmpty(list)) {
                throw new CheckedException("请求参数不能为空");
            }
            for(MaterialReceiptStatus data : list) {
                AssertUtils.notBlank(data.getPurOrdNo(), "采购订单号不能为空");
            }
            ygResponseStatusVO.setCode("200");
            ygResponseStatusVO.setMessage("成功");
        } catch (Exception e) {
            ygResponseStatusVO.setCode("201");
            ygResponseStatusVO.setMessage(e.getMessage());
        }
        return ygResponseStatusVO;
    }

    @ApiOperation(value = "服务物资发票过账服务", notes = "服务物资发票过账服务")
    @PostMapping("/invoicePosting")
    @InnerService
    @Idempotent(key = "#param.head.seqNo", info = "#param.head.seqNo", expireTime = 10, timeUnit = TimeUnit.SECONDS)
    public YgResponseInvoiceVO invoicePosting(@RequestBody Param<YgRequestInvoicePostingDTO> param) {
        YgResponseInvoiceVO result = new YgResponseInvoiceVO();
        List<YgInvoiceStatus> dataList = new ArrayList<>();
        try {
            YgRequestInvoicePostingDTO paramData = param.getBody().getData();
            List<YgRequestInvoicePosting> list = paramData.getList();
            if(CollectionUtil.isEmpty(list)) {
                throw new CheckedException("请求参数不能为空");
            }
            dataList = fiInvoicePostingService.saveInvoicePosting(list);
            result.setStatuscode("200");
            result.setMessage("成功");
            result.setResult(dataList);
        } catch (Exception e) {
            result.setStatuscode("201");
            result.setMessage(e.getMessage());
            result.setResult(dataList);
        }
        return result;
    }

    @ApiOperation(value = "服务物资发票冲销服务", notes = "服务物资发票冲销服务")
    @PostMapping("/invoiceWriteOff")
    @InnerService
    @Idempotent(key = "#param.head.seqNo", info = "#param.head.seqNo", expireTime = 10, timeUnit = TimeUnit.SECONDS)
    public YgResponseInvoiceVO invoiceWriteOff(@RequestBody Param<YgRequestInvoiceWriteOffDTO> param) {
        YgResponseInvoiceVO result = new YgResponseInvoiceVO();
        List<YgInvoiceStatus> dataList = new ArrayList<>();
        try {
            YgRequestInvoiceWriteOffDTO paramData = param.getBody().getData();
            List<YgRequestInvoiceWriteOff> list = paramData.getList();
            if(CollectionUtil.isEmpty(list)) {
                throw new CheckedException("请求参数不能为空");
            }
            for(YgRequestInvoiceWriteOff data : list) {
                YgInvoiceStatus source = new YgInvoiceStatus();
                source.setBusibillno(data.getBusibillno());
                source.setSapdocid(data.getBusibillno());
                source.setAppid(data.getAppid());
                source.setTypeid(data.getTypeid());
                source.setType("S");
                source.setMessage("成功");
                source.setGjahr(data.getFisyea());
                source.setBukrs(data.getBukrs());
                source.setPrctr(data.getPrctr());
                source.setAccvouno(data.getAccvouno());
                dataList.add(source);
            }
            result.setStatuscode("200");
            result.setMessage("成功");
            result.setResult(dataList);
        } catch (Exception e) {
            result.setStatuscode("201");
            result.setMessage(e.getMessage());
            result.setResult(dataList);
        }
        return result;
    }

}

package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertFileOcrInfo;
import com.cloud.ficonsumer.mapper.FiConvertFileOcrInfoMapper;
import com.cloud.ficonsumer.service.FiConvertFileOcrInfoService;
import org.springframework.stereotype.Service;

@Service
public class FiConvertFileOcrInfoServiceImpl extends ServiceImpl<FiConvertFileOcrInfoMapper, FiConvertFileOcrInfo> implements FiConvertFileOcrInfoService {

}
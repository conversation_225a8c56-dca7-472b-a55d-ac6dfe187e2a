package com.cloud.ficonsumer.enums;

/**
 * 集成枚举
 */
public enum IntegrationEnum {
    ERP_YG_000012("ERP_YG_000012", "9ADAAB4DDAAB250B","接收成本类预付/付款申请服务"),
    ERP_YG_000011("ERP_YG_000011", "9ADAAB4DDAAB250B","采购订单接口接收"),
    ERP_YG_000010("ERP_YG_000010", "9ADAAB4DDAAB250B", "采购-发票校验申请服务"),
    ERP_YG_000008("ERP_YG_000008", "9ADAAB4DDAAB250B", "通用报账-接收通用报账单服务"),
    ERP_YG_000007("ERP_YG_000007", "9ADAAB4DDAAB250B", "承包业务-收入确认单服务"),
    ERP_YG_000009("ERP_YG_000009", "9ADAAB4DDAAB250B", "存货核算-采购入库"),

    ERP_YG_000023("ERP_YG_000023", "9ADAAB4DDAAB250B", "预付/付款申请服务"),
    ERP_YG_000024("ERP_YG_000024", "9ADAAB4DDAAB250B", "接收通用报账单服务"),
    ERP_YG_000025("ERP_YG_000025", "9ADAAB4DDAAB250B", "接收销售订单信息"),
    ERP_YG_000028("ERP_YG_000028", "9ADAAB4DDAAB250B", "根据比对一致和人票关联数据创建进项发票入池信息服务"),
    ERP_YG_000029("ERP_YG_000029", "9ADAAB4DDAAB250B", "智慧共享财务平台接收物料凭证入库明细信息（采购订单历史信息表）"),
    ERP_YG_000040("ERP_YG_000040", "9ADAAB4DDAAB250B", "校验增值税发票真伪服务，根据发票关健要素查询发票的全票面信息，鉴别发票真伪。"),
    ERP_YG_000041("ERP_YG_000041","9ADAAB4DDAAB250B","会计凭证回传财务平台接收服务");
    /**
     * 服务端编码
     */
    private String servCode;
    /**
     * 密钥
     */
    private String accessKey;

    /**
     * 描述
     */
    private String desc;


    IntegrationEnum(String servCode, String accessKey, String desc) {
        this.servCode = servCode;
        this.accessKey = accessKey;
        this.desc = desc;
    }

    public String getServCode() {
        return servCode;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public String getDesc() {
        return desc;
    }
    /**
     * 根据类型获取枚举
     *
     * @param code
     * @return
     */
    public static IntegrationEnum getEnumByCode(String code) {
        IntegrationEnum[] values = values();
        for (IntegrationEnum en : values) {
            if (en.getServCode().equals(code)) {
                return en;
            }
        }
        return null;
    }

}

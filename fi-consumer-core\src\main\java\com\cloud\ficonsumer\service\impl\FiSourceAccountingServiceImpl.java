
package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.*;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceAccountingMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.*;
import com.mysql.cj.util.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


/**
 *
 */
@Slf4j
@Service
@AllArgsConstructor
public class FiSourceAccountingServiceImpl extends ServiceImpl<FiSourceAccountingMapper, FiSourceAccounting> implements FiSourceAccountingService {

    private final FiConvertAccountingService fiConvertAccountingService;
    private final FiConvertAccountingDetailService fiConvertAccountingDetailService;
    private final FiSourceAccountingDetailService fiSourceAccountingDetailService;
    private final FiSourceAccountingDocfreeService fiSourceAccountingDocfreeService;
    private final FiConvertAccountingDocfreeService fiConvertAccountingDocfreeService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private final DmMapper dmMapper;


    /**
     * 凭证记账数据重新推送
     *
     * @param pk
     * @return
     */
    @Override
    public boolean pushAgain(String pk) {
        FiSourceAccounting sourceMainData = this.getOne(Wrappers.<FiSourceAccounting>lambdaQuery().eq(FiSourceAccounting::getPkVoucher, pk));
        if (sourceMainData.getPushStatus().equals(PushStatusEnum.PUSH_SUCCESS.getCode())) {
            //mq的transaction = false可能出现重复消费，判断状态不进行重复推送
            return true;
        }
        List<FiSourceAccountingDetail> details = fiSourceAccountingDetailService.list(Wrappers.<FiSourceAccountingDetail>lambdaQuery().eq(FiSourceAccountingDetail::getPkVoucher, pk));
        for (FiSourceAccountingDetail detail : details) {
            List<FiSourceAccountingDocfree> docfreeList = fiSourceAccountingDocfreeService.list(Wrappers.<FiSourceAccountingDocfree>lambdaQuery().eq(FiSourceAccountingDocfree::getPkDetail, detail.getPkDetail()));
            //重新转换
            try {
                this.turnData(sourceMainData, detail, docfreeList);
            } catch (Exception e) {
                String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
                sourceMainData.setIntegrationStatus("1");
                sourceMainData.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
                sourceMainData.setPushMsg(pushMsg);
                this.updateById(sourceMainData);
                LogUtil.info(log, "凭证记账数据转换失败:", e);
                throw new CheckedException("凭证记账数据转换失败:" + pushMsg);
            }
        }

        //重新推送智慧平台
        try {
            YgResult ygResult = this.pushOrderData(pk);
            sourceMainData.setIntegrationStatus("0");
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
            sourceMainData.setPushMsg("");
            sourceMainData.setReadiness(1L);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk, Constants.TradeType.material);
            if (CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000000");
                updatePlatFormLog.setMsg("");
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            } else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TradeType.material);
                platFormLogVO.setCode("000000");
                platFormLogVO.setMsg("");
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
            sourceMainData.setIntegrationStatus("1");
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            //保存日志
            List<PlatFormLogVO> platFormLogVOList = dmMapper.selectPlatFormLog(pk, Constants.TradeType.material);
            if (CollectionUtil.isNotEmpty(platFormLogVOList)) {
                PlatFormLogVO updatePlatFormLog = platFormLogVOList.get(0);
                updatePlatFormLog.setCode("000001");
                updatePlatFormLog.setMsg(pushMsg);
                dmMapper.updatePlatFormLog(updatePlatFormLog);
            } else {
                PlatFormLogVO platFormLogVO = new PlatFormLogVO();
                platFormLogVO.setPkLog(UUID.randomUUID().toString());
                platFormLogVO.setPkBill(pk);
                platFormLogVO.setPkBillType(Constants.TradeType.material);
                platFormLogVO.setCode("000001");
                platFormLogVO.setMsg(pushMsg);
                dmMapper.insertPlatFormLog(platFormLogVO);
            }
            this.updateById(sourceMainData);
            LogUtil.info(log, "凭证记账数据推送失败:", e);
            throw new CheckedException("凭证记账数据推送失败:" + pushMsg);
        }
        return true;
    }


    /**
     * 数据推送
     *
     * @param pk
     * @return
     * @throws Exception
     */
    private YgResult pushOrderData(String pk) throws Exception {
        YgResult<YgCommonResult> ygResult = new YgResult();
//        if (ygConfig.getEnable()) {
        FiConvertAccounting convertNewStore = fiConvertAccountingService.getOne(Wrappers.<FiConvertAccounting>lambdaQuery().eq(FiConvertAccounting::getPkVoucher, pk));
        List<FiConvertAccountingDetail> details = fiConvertAccountingDetailService.list(Wrappers.<FiConvertAccountingDetail>lambdaQuery().eq(FiConvertAccountingDetail::getPkVoucher, pk));
        AccountingToYgVO request = this.turnOrderYgData(convertNewStore, details);
        ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000041.getServCode());
        String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(request));
        ygResult = JSONObject.parseObject(result, YgResult.class);
        //000000：成功 其它：失败
        if (!ygResult.getCode().equals("6100000")) {
            throw new CheckedException(ygResult.getmessage());
        } else {
            if (null != ygResult.getData() && ygResult.getData().getResultState().equals("1")) {
                throw new CheckedException(ygResult.getData().getResultMsg());
            }
        }
//        }
        return ygResult;
    }

    /**
     * 将转换后的数据用远光提供的格式推送
     *
     * @param convertNewStore
     * @param details
     * @return
     */
    private AccountingToYgVO turnOrderYgData(FiConvertAccounting convertNewStore, List<FiConvertAccountingDetail> details) {
        AccountingToYgVO request = new AccountingToYgVO();
        String pkOrg = apiCompareService.getTurnByType(convertNewStore.getPkOrg(), Constants.PkOrg);
        if (StrUtil.isBlank(pkOrg)) {
            throw new CheckedException("单位代号不可为空");
        }

        AccountingVO voucherdata = new AccountingVO();
        FidocheaderVO fidocheaderVO = new FidocheaderVO();

        //构造voucher 凭证列表
        fidocheaderVO.setSapdocid(ObjectUtils.isNull(convertNewStore.getNum()) ? null : convertNewStore.getNum().toString());
        fidocheaderVO.setDocumenttype(convertNewStore.getPkVouchertype());
        fidocheaderVO.setCompany(pkOrg);
        if (convertNewStore.getFree5() == null) {
            fidocheaderVO.setProfcenter(pkOrg);
        } else {
            fidocheaderVO.setProfcenter(convertNewStore.getFree5());
        }
        fidocheaderVO.setSummary(convertNewStore.getExplanation());
        fidocheaderVO.setDocumentdate(convertNewStore.getPrepareddate().substring(0, 10));
        fidocheaderVO.setPostingdate(convertNewStore.getTallydate().substring(0, 10));
        fidocheaderVO.setPerioddate(convertNewStore.getPeriod());
        fidocheaderVO.setCreator(convertNewStore.getPkPrepared());
        fidocheaderVO.setChecker(convertNewStore.getPkChecked());
        fidocheaderVO.setCurrency(convertNewStore.getPkCurrtype());
        fidocheaderVO.setReference(convertNewStore.getAttachment());
        fidocheaderVO.setZyear(Integer.valueOf(convertNewStore.getYear()));
        fidocheaderVO.setRanged(convertNewStore.getAdjustperiod());
        fidocheaderVO.setNegdocument(ObjectUtils.isNull(convertNewStore.getNum()) ? null : convertNewStore.getNum().toString());
        fidocheaderVO.setNegyear(convertNewStore.getYear());
        fidocheaderVO.setXreversal(convertNewStore.getOffervoucher());
        List<FidocitemsVO> fidocitems = new ArrayList<>();
        for (FiConvertAccountingDetail detail : details) {
            FidocitemsVO fidocitem = new FidocitemsVO();
            fidocitem.setSapdocid(ObjectUtils.isNull(convertNewStore.getNum()) ? null : convertNewStore.getNum().toString());
            fidocitem.setAccount(detail.getPkAccasoa());
            fidocitem.setWbamount(detail.getAmount());
            fidocitem.setBushaptime(detail.getBusidate());
            fidocitem.setCrde(detail.getCrde());
            fidocitem.setFzhs(addFzhsList(detail.getPkDetail()));
            fidocitems.add(fidocitem);
        }
        List<VoucherVO> voucher = new ArrayList<>();
        VoucherVO voucherVO = new VoucherVO();
        voucherVO.setFidocheader(fidocheaderVO);
        voucherVO.setFidocitems(fidocitems);
        voucher.add(voucherVO);
        voucherdata.setSendSys("50");
        voucherdata.setVoucher(voucher);
        request.setVoucherdata(voucherdata);
        return request;
    }

    /**
     * 数据转换
     *
     * @param sourceMainData
     * @param
     */
    private void turnData(FiSourceAccounting sourceMainData, FiSourceAccountingDetail detail, List<FiSourceAccountingDocfree> docfreeList) throws Exception {
        String pk = sourceMainData.getPkVoucher();
        FiConvertAccounting fiConvertAccounting = new FiConvertAccounting();
        apiCompareCache.convertData(sourceMainData, fiConvertAccounting, BillTypeEnum.ACCOUNTING.getCode());
        FiConvertAccounting convertMainData = fiConvertAccountingService.getOne(Wrappers.<FiConvertAccounting>lambdaQuery().eq(FiConvertAccounting::getPkVoucher, pk));
        fiConvertAccounting.setId(null);
        if (convertMainData != null) {
            fiConvertAccounting.setId(convertMainData.getId());
            fiConvertAccountingDetailService.remove(Wrappers.<FiConvertAccountingDetail>lambdaQuery().eq(FiConvertAccountingDetail::getPkDetail, detail.getPkDetail()));
        }

        FiConvertAccountingDetail fiConvertNewStoreDetail = new FiConvertAccountingDetail();
        apiCompareCache.convertData(detail, fiConvertNewStoreDetail, BillTypeEnum.ACCOUNTING_DETAIL.getCode());
        fiConvertNewStoreDetail.setId(null);

        List<FiConvertAccountingDocfree> convertDetailList = new ArrayList<>();
        for (FiSourceAccountingDocfree sourceDocfreeData : docfreeList) {
            FiConvertAccountingDocfree fiConvertAccountingDocfree = new FiConvertAccountingDocfree();
            apiCompareCache.convertData(sourceDocfreeData, fiConvertAccountingDocfree, BillTypeEnum.ACCOUNTING_DOCFREE.getCode());
            fiConvertAccountingDocfreeService.remove(Wrappers.<FiConvertAccountingDocfree>lambdaQuery().eq(FiConvertAccountingDocfree::getPkDetail, detail.getPkDetail()));
            fiConvertAccountingDocfree.setId(null);
            convertDetailList.add(fiConvertAccountingDocfree);
        }

        fiConvertAccountingService.saveOrUpdate(fiConvertAccounting);
        fiConvertAccountingDetailService.saveOrUpdate(fiConvertNewStoreDetail);
        fiConvertAccountingDocfreeService.saveOrUpdateBatch(convertDetailList);
    }

    @Override
    public Page<FiSourceAccountingDTO> beforeConvertDataPage(Page page, FiSourceAccountingQueryVO queryVO) {
        Page<FiSourceAccountingDTO> result = this.baseMapper.beforeConvertDataPage(page, queryVO);
        return result;
    }

    @Override
    public Page<FiSourceAccountingDetailDTO> getBeforeConvertDataPage(Page page, FiSourceAccountingQueryVO queryVO) {
        Page<FiSourceAccountingDetailDTO> detailList = this.baseMapper.getBeforeConvertDataPage(page, queryVO);
        Page<FiSourceAccountingDetailDTO> result = new Page<>();
        List<FiSourceAccountingDetailDTO> records = new ArrayList<>();
        for (FiSourceAccountingDetailDTO record : detailList.getRecords()) {
            StringBuffer docfree = new StringBuffer();
            List<FiSourceAccountingDocfree> docfreeData = this.baseMapper.getBeforeConvertDocfreeData(record.getPkDetail());
            for (FiSourceAccountingDocfree docfreeDatum : docfreeData) {
                docfree.append(docfreeDatum.getType()).append(":").append(docfreeDatum.getValue()).append(",");
            }
            record.setDocfree(docfree.toString());
            records.add(record);
        }
        result.setRecords(records);
        return result;
    }

    @Override
    public List<FiConvertAccountingDTO> getConvertList(String pk) {
        List<FiConvertAccountingDTO> convertDataDTOList = new ArrayList<>();
        FiConvertAccountingDTO convertDataDTO = new FiConvertAccountingDTO();
        FiConvertAccounting convertDataOld = fiConvertAccountingService.getOne(Wrappers.<FiConvertAccounting>lambdaQuery().eq(FiConvertAccounting::getPkVoucher, pk));
        if (null != convertDataOld) {
            BeanUtil.copyProperties(convertDataOld, convertDataDTO);
            convertDataDTOList.add(convertDataDTO);
        }
        return convertDataDTOList;
    }

    @Override
    public Page<FiConvertAccountingDetailDTO> getConvertData(Page page, String pk) {
        Page<FiConvertAccountingDetailDTO> detailList = this.baseMapper.getConvertData(page, pk);
        Page<FiConvertAccountingDetailDTO> result = new Page<>();
        List<FiConvertAccountingDetailDTO> records = new ArrayList<>();
        for (FiConvertAccountingDetailDTO record : detailList.getRecords()) {
            StringBuffer docfree = new StringBuffer();
            List<FiConvertAccountingDocfree> docfreeData = this.baseMapper.getConvertDocfreeData(record.getPkDetail());
            for (FiConvertAccountingDocfree docfreeDatum : docfreeData) {
                docfree.append("[").append(docfreeDatum.getType()).append(":").append(docfreeDatum.getValue()).append("]");
            }
            record.setDocfree(docfree.toString());
            records.add(record);
        }
        result.setRecords(records);
        return result;
    }

    public List<FzhsVO> addFzhsList(String pkDetail) {
        List<FzhsVO> fzhsVO = new ArrayList<>();
        List<FiConvertAccountingDocfree> list = fiConvertAccountingDocfreeService.list(Wrappers.<FiConvertAccountingDocfree>lambdaQuery().eq(FiConvertAccountingDocfree::getPkDetail, pkDetail));
        for (FiConvertAccountingDocfree docfree : list) {
            FzhsVO fzhs = new FzhsVO();
            fzhs.setType(docfree.getType());
            if (docfree.getType().equals("profitcenter") && !StringUtils.isNullOrEmpty(docfree.getValue())) {
                String pkOrg = apiCompareService.getTurnByType(docfree.getValue(), Constants.PkOrg);
                fzhs.setValue(pkOrg);
            } else {
                fzhs.setValue(docfree.getValue());
            }
            fzhsVO.add(fzhs);
        }

        return fzhsVO;
    }

    /**
     * 批量重推
     *
     * @param queryVO
     * @return
     */
    @Override
    public FiPushStatusChangeVO pushAgainBatch(FiPushStatusChangeVO queryVO) {
        FiPushStatusChangeVO fiPushStatusChangeVO = new FiPushStatusChangeVO();
        List<String> resultList = new ArrayList<>();
        for(Long id : queryVO.getIdList()) {
            FiSourceAccounting entity = this.getById(id);
            try {
                this.pushAgain(entity.getPkVoucher());
            } catch (Exception e) {
                resultList.add(entity.getPkVoucher()+e.getMessage());
            }
        }
        fiPushStatusChangeVO.setResultList(resultList);
        return fiPushStatusChangeVO;
    }
}


package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertReimbursementDetail;
import com.cloud.ficonsumer.mapper.FiConvertReimbursementDetailMapper;
import com.cloud.ficonsumer.service.FiConvertReimbursementDetailService;
import org.springframework.stereotype.Service;

/**
 * 通用报销单详情转换数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:53:03
 */
@Service
public class FiConvertReimbursementDetailServiceImpl extends ServiceImpl<FiConvertReimbursementDetailMapper, FiConvertReimbursementDetail> implements FiConvertReimbursementDetailService {

}

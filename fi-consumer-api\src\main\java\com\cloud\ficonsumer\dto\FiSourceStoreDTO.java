package com.cloud.ficonsumer.dto;

import com.cloud.ficonsumer.entity.FiSourceStore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/13.
 */
@Data
public class FiSourceStoreDTO extends FiSourceStore {

    /**
     * 单据爬取时间
     */
    @ApiModelProperty(value="单据爬取时间")
    private String grabTime;


    @ApiModelProperty(value = "供应商名称")
    private String cvendorvidName;

    /**
     * 用途
     */
    @ApiModelProperty(value="用途")
    private String vbdef7Name;

    @ApiModelProperty(value = "暂估标志")
    private String bestimateflagName;

    @ApiModelProperty(value = "订单编号")
    private String orderNum;

}

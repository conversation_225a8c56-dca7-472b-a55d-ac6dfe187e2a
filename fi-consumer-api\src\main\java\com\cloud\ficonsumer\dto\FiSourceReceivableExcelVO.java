package com.cloud.ficonsumer.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/2/13.
 */
@Data
@ColumnWidth(25)
public class FiSourceReceivableExcelVO {

    /**
     * 单据类型
     * F0-Cxx-01 项目应收单
     * F0-Cxx-02 销售应收单
     */
    @ExcelProperty(value="单据类型名称")
    private String pkTradetypeName;

    @ExcelProperty(value = "订单编号")
    private String billno;

    /**
     * 财务组织名称
     */
    @ExcelProperty(value="财务组织名称")
    private String pkOrgName;



    /**
     * 推送时间
     */
    @ExcelProperty(value = "推送时间")
    private String pushTime;

    @ExcelProperty(value = "同步状态")
    private String pushStatusName;

    @ExcelProperty(value = "同步消息")
    private String pushMsg;

    /**
     * 创建时间
     */
    @ExcelProperty(value="拉取时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ExcelIgnore
    private Integer pushStatus;
}

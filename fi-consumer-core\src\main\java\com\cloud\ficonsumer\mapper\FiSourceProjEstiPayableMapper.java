package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiSourceProjEstiPayableDetailQueryDTO;
import com.cloud.ficonsumer.dto.FiSourceProjEstiPayableQueryDTO;
import com.cloud.ficonsumer.entity.FiSourceProjEstiPayable;
import com.cloud.ficonsumer.vo.FiConvertProjEstiPayableDetailVO;
import com.cloud.ficonsumer.vo.FiConvertProjEstiPayableVO;
import com.cloud.ficonsumer.vo.FiSourceProjEstiPayableDetailVO;
import com.cloud.ficonsumer.vo.FiSourceProjEstiPayableVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FiSourceProjEstiPayableMapper extends CloudBaseMapper<FiSourceProjEstiPayable> {

    Page<FiSourceProjEstiPayableVO> beforeConvertDataPage(Page<FiSourceProjEstiPayableVO> page, @Param("query") FiSourceProjEstiPayableQueryDTO queryDTO);

    Page<FiSourceProjEstiPayableDetailVO> getBeforeConvertDataPage(Page<FiSourceProjEstiPayableDetailVO> page, @Param("query") FiSourceProjEstiPayableDetailQueryDTO queryDTO);

    List<FiConvertProjEstiPayableVO> getConvertList(@Param("pkEstipayablebill") String pkEstipayablebill);

    Page<FiConvertProjEstiPayableDetailVO> getConvertData(Page<FiConvertProjEstiPayableDetailVO> page, @Param("pkEstipayablebill") String pkEstipayablebill);

}

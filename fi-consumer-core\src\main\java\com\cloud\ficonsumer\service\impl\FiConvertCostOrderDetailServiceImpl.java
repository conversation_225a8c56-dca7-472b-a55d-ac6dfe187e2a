
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertCostOrderDetail;
import com.cloud.ficonsumer.mapper.FiConvertCostOrderDetailMapper;
import com.cloud.ficonsumer.service.FiConvertCostOrderDetailService;
import org.springframework.stereotype.Service;

/**
 * 虚拟采购订单转换数据详情表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:03
 */
@Service
public class FiConvertCostOrderDetailServiceImpl extends ServiceImpl<FiConvertCostOrderDetailMapper, FiConvertCostOrderDetail> implements FiConvertCostOrderDetailService {

}

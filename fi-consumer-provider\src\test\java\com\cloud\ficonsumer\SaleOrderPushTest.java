package com.cloud.ficonsumer;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.entity.FiSourceSaleOrder;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.service.FiSourceSaleOrderService;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.FiSourceSaleOrderVO;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.sql.Connection;
import java.sql.Statement;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class SaleOrderPushTest {

    @SpyBean
    private DmMapper dmMapper;

    @SpyBean
    private ApiProjectService apiProjectService;

    @SpyBean
    private FiSourceSaleOrderService fiSourceSaleOrderService;

    @SpyBean
    private ApiCompareCache apiCompareCache;

    private String ygSuccessResp;
    private String ygFailResp;

    @BeforeAll
    public void mockResp() throws Exception {
        ygSuccessResp = new String(Files.readAllBytes(new ClassPathResource("YgSuccessResp.json").getFile().toPath()), StandardCharsets.UTF_8);
        ygFailResp = new String(Files.readAllBytes(new ClassPathResource("YgFailResp.json").getFile().toPath()), StandardCharsets.UTF_8);
    }

    @BeforeEach
    public void initData() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);

        // 初始化 erp 库数据：销售订单 oracle 数据库
        DynamicDataSourceContextHolder.push("erp");
        try {
            ResourceDatabasePopulator erpPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("saleorder/schema-oracle.sql"),
                    new ClassPathResource("saleorder/data-oracle.sql")
            );
            DatabasePopulatorUtils.execute(erpPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 初始化 mysql 库数据：销售订单 mysql 数据库
        DynamicDataSourceContextHolder.push("mysql");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("saleorder/schema-mysql.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 同步 ERP 销售订单到 MySQL
        List<FiSourceSaleOrderVO> fiSourceSaleOrderVOS = dmMapper.listSaleOrder();
        for (FiSourceSaleOrderVO fiSourceSaleOrder : fiSourceSaleOrderVOS) {
            apiProjectService.processSaleOrder(fiSourceSaleOrder);
        }
    }

    @AfterEach
    public void cleanData() {
        DynamicRoutingDataSource dynamicDataSource = (DynamicRoutingDataSource) SpringContextHolder.getBean(DataSource.class);

        // 清理 erp 库
        DataSource erpDataSource = dynamicDataSource.getDataSource("erp");
        try (Connection conn = erpDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 ERP 数据库失败", e);
        }

        // 清理 mysql 库
        DataSource mysqlDataSource = dynamicDataSource.getDataSource("mysql");
        try (Connection conn = mysqlDataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            stmt.execute("DROP ALL OBJECTS");
        } catch (Exception e) {
            throw new RuntimeException("清理 MySQL 数据库失败", e);
        }
    }

    @Test
    public void testPushSuccess() {
        String csaleorderid = "1001A710000000LBO69G";

        try (MockedStatic<YgUtil> ygUtil = mockStatic(YgUtil.class)) {
            ygUtil.when(() -> YgUtil.sendToYg(any(), any())).thenReturn(ygSuccessResp);

            // 执行推送
            boolean result = fiSourceSaleOrderService.push(csaleorderid);
            assertTrue(result, "推送成功");

            // 验证数据库状态
            FiSourceSaleOrder updatedSource = fiSourceSaleOrderService.getOne(
                    Wrappers.<FiSourceSaleOrder>lambdaQuery()
                            .eq(FiSourceSaleOrder::getCsaleorderid, csaleorderid)
            );
            assertEquals("0", updatedSource.getIntegrationStatus(), "集成状态应为成功");
            assertEquals(PushStatusEnum.PUSH_SUCCESS.getCode(), updatedSource.getPushStatus(), "推送状态应为成功");

            // 验证平台日志
            List<PlatFormLogVO> logs = dmMapper.selectPlatFormLog(csaleorderid, null);
            assertFalse(logs.isEmpty(), "平台日志为空");
            assertEquals(Constants.SUCCESS, logs.get(0).getCode(), "日志状态应为成功");
        }
    }

    @Test
    public void testConvertFail() {
        String csaleorderid = "1001A710000000LBO69G";

        // 模拟数据转换异常
        doThrow(new CheckedException("销售订单集成信息转换失败"))
                .when(apiCompareCache).convertData(any(), any(), any());

        // 执行推送并验证异常
        CheckedException exception = assertThrows(CheckedException.class,
                () -> fiSourceSaleOrderService.push(csaleorderid));
        assertTrue(exception.getMessage().contains("销售订单集成信息转换失败"), "异常消息不匹配");

        // 验证数据库状态
        FiSourceSaleOrder order = fiSourceSaleOrderService.getOne(
                Wrappers.<FiSourceSaleOrder>lambdaQuery()
                        .eq(FiSourceSaleOrder::getCsaleorderid, csaleorderid));
        assertEquals("1", order.getIntegrationStatus(), "集成状态不正确");
        assertEquals(PushStatusEnum.CONVERT_FAIL.getCode(), order.getPushStatus(), "推送状态应为转换失败");
        assertNotNull(order.getPushMsg(), "推送错误消息应不为空");
    }

    @Test
    public void testPushFail() {
        String csaleorderid = "1001A710000000LBO69G";

        try (MockedStatic<YgUtil> ygUtil = mockStatic(YgUtil.class)) {
            ygUtil.when(() -> YgUtil.sendToYg(any(), any())).thenReturn(ygFailResp);

            // 执行推送并验证异常
            CheckedException exception = assertThrows(CheckedException.class,
                    () -> fiSourceSaleOrderService.push(csaleorderid));
            assertTrue(exception.getMessage().contains("响应失败，响应结果: "), "异常消息不匹配");

            // 验证数据库状态
            FiSourceSaleOrder updatedOrder = fiSourceSaleOrderService.getOne(
                    Wrappers.<FiSourceSaleOrder>lambdaQuery()
                            .eq(FiSourceSaleOrder::getCsaleorderid, csaleorderid)
            );
            assertEquals("1", updatedOrder.getIntegrationStatus(), "集成状态不正确");
            assertEquals(PushStatusEnum.PUSH_FAIL.getCode(), updatedOrder.getPushStatus(), "推送状态应为推送失败");

            // 验证平台日志
            List<PlatFormLogVO> logs = dmMapper.selectPlatFormLog(csaleorderid, null);
            assertFalse(logs.isEmpty(), "平台日志为空");
            assertEquals(Constants.FAIL, logs.get(0).getCode(), "日志CODE不正确");
            assertTrue(logs.get(0).getMsg().contains("响应失败，响应结果: "), "日志消息不包含失败信息");
        }
    }
}
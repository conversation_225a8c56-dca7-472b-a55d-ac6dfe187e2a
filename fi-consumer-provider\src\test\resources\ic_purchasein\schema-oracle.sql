create table IC_PURCHASEIN_H
(
    APPROVER       VARCHAR2(20) default '~',
    BILLMAKER      VARCHAR2(20) default '~',
    BITIN<PERSON><PERSON>      CHAR,
    BTRIATRADEFLAG CHAR,
    CBIZID         VARCHAR2(20) default '~',
    CBIZTYPE       VARCHAR2(20) default '~',
    CCOSTDOMAINID  VARCHAR2(20) default '~',
    CCUSTOMERID    VARCHAR2(20) default '~',
    CDPTID         VARCHAR2(20) default '~',
    CDPTVID        VARCHAR2(20) default '~',
    CFANACEORGOID  VARCHAR2(20) default '~',
    CFANACEORGVID  VARCHAR2(20) default '~',
    CGENERALHID    CHAR(20) not null
        constraint PK_IC_PURCHASEIN_H
            primary key,
    CORPOID        VARCHAR2(20) default '~',
    CORPVID        VARCHAR2(20) default '~',
    CPAYFINORGOID  VARCHAR2(20) default '~',
    CPAYFINORGVID  VARCHAR2(20) default '~',
    CPURORGOID     VARCHAR2(20) default '~',
    CPURORGVID     VARCHAR2(20) default '~',
    CREATION<PERSON><PERSON>   CHAR(19),
    CREATOR        VARCHAR2(20) default '~',
    CRECECOUNTRYID VARCHAR2(20) default '~',
    CSENDCOUNTRYID VARCHAR2(20) default '~',
    CSENDTYPEID    VARCHAR2(20) default '~',
    CTAXCOUNTRYID  VARCHAR2(20) default '~',
    CTRADEWORDID   VARCHAR2(20) default '~',
    CTRANTYPEID    VARCHAR2(20) default '~',
    CVENDORID      VARCHAR2(20) default '~',
    CWAREHOUSEID   VARCHAR2(20) default '~',
    CWHSMANAGERID  VARCHAR2(20) default '~',
    DBILLDATE      CHAR(19),
    DMAKEDATE      CHAR(19),
    DR             NUMBER(10)   default 0,
    FBILLFLAG      NUMBER(38)   default 2,
    FBUYSELLFLAG   NUMBER(38),
    FREPLENISHFLAG CHAR,
    IPRINTCOUNT    NUMBER(38),
    MODIFIEDTIME   CHAR(19),
    MODIFIER       VARCHAR2(20) default '~',
    NTOTALNUM      NUMBER(28, 8),
    NTOTALPIECE    NUMBER(28, 8),
    NTOTALVOLUME   NUMBER(28, 8),
    NTOTALWEIGHT   NUMBER(28, 8),
    PK_GROUP       VARCHAR2(20) default '~',
    PK_MEASWARE    VARCHAR2(20) default '~',
    PK_ORG         VARCHAR2(20) default '~',
    PK_ORG_V       VARCHAR2(20) default '~',
    TAUDITTIME     CHAR(19),
    TS             CHAR(19)     default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VBILLCODE      VARCHAR2(40),
    VDEF1          VARCHAR2(101),
    VDEF10         VARCHAR2(101),
    VDEF11         VARCHAR2(101),
    VDEF12         VARCHAR2(101),
    VDEF13         VARCHAR2(101),
    VDEF14         VARCHAR2(101),
    VDEF15         VARCHAR2(101),
    VDEF16         VARCHAR2(101),
    VDEF17         VARCHAR2(101),
    VDEF18         VARCHAR2(101),
    VDEF19         VARCHAR2(101),
    VDEF2          VARCHAR2(101),
    VDEF20         VARCHAR2(101),
    VDEF3          VARCHAR2(101),
    VDEF4          VARCHAR2(101),
    VDEF5          VARCHAR2(101),
    VDEF6          VARCHAR2(101),
    VDEF7          VARCHAR2(101),
    VDEF8          VARCHAR2(101),
    VDEF9          VARCHAR2(101),
    VNOTE          VARCHAR2(500),
    VRETURNREASON  VARCHAR2(20) default '~',
    VTRANTYPECODE  VARCHAR2(20) default '~',
    SAGA_BTXID     VARCHAR2(64) default '~',
    SAGA_FROZEN    NUMBER(38)   default 0,
    SAGA_GTXID     VARCHAR2(64) default '~',
    SAGA_STATUS    NUMBER(38)   default 0
);

create table IC_PURCHASEIN_B
(
    BASSETCARD           CHAR,
    BBARCODECLOSE        CHAR,
    BCOMPARE             CHAR          default 'N',
    BFIXEDASSET          CHAR,
    BONROADFLAG          CHAR,
    BOPPTAXFLAG          CHAR,
    BORROWINFLAG         CHAR,
    BSOURCELARGESS       CHAR,
    CARRIVEORDER_BBID    VARCHAR2(20),
    CASSCUSTID           VARCHAR2(20)  default '~',
    CASTUNITID           VARCHAR2(20)  default '~',
    CBODYTRANSTYPECODE   VARCHAR2(20)  default '~',
    CBODYWAREHOUSEID     VARCHAR2(20)  default '~',
    CCHECKSTATEID        VARCHAR2(20),
    CCORRESPONDBID       VARCHAR2(20),
    CCORRESPONDCODE      VARCHAR2(40),
    CCORRESPONDHID       VARCHAR2(20),
    CCORRESPONDROWNO     VARCHAR2(20),
    CCORRESPONDTRANSTYPE VARCHAR2(20)  default '~',
    CCORRESPONDTYPE      VARCHAR2(20)  default '~',
    CCURRENCYID          VARCHAR2(20)  default '~',
    CDESTIAREAID         VARCHAR2(20)  default '~',
    CDESTICOUNTRYID      VARCHAR2(20)  default '~',
    CDETAILBID           VARCHAR2(20),
    CDETAILID            VARCHAR2(20),
    CDETAILROWNO         VARCHAR2(20),
    CETDETLPICKBID       VARCHAR2(20),
    CFANACEORGOID        VARCHAR2(20)  default '~',
    CFFILEID             VARCHAR2(20)  default '~',
    CFIRSTBILLBID        VARCHAR2(20),
    CFIRSTBILLHID        VARCHAR2(20),
    CFIRSTTRANSTYPE      VARCHAR2(20)  default '~',
    CFIRSTTYPE           VARCHAR2(20)  default '~',
    CGENERALBID          CHAR(20) not null
        constraint PK_IC_PURCHASEIN_B
            primary key,
    CGENERALHID          CHAR(20) not null,
    CGLOBALCURRENCYID    VARCHAR2(20)  default '~',
    CGROUPCURRENCYID     VARCHAR2(20)  default '~',
    CIOLIABILITYOID      VARCHAR2(20)  default '~',
    CIOLIABILITYVID      VARCHAR2(20)  default '~',
    CLIABILITYOID        VARCHAR2(20)  default '~',
    CLIABILITYVID        VARCHAR2(20)  default '~',
    CLOCATIONID          VARCHAR2(20)  default '~',
    CMATERIALOID         VARCHAR2(20)  default '~',
    CMATERIALVID         VARCHAR2(20)  default '~',
    CORDER_BB1ID         VARCHAR2(20),
    CORIGAREAID          VARCHAR2(20)  default '~',
    CORIGCOUNTRYID       VARCHAR2(20)  default '~',
    CORIGCURRENCYID      VARCHAR2(20)  default '~',
    CORPOID              VARCHAR2(20)  default '~',
    CORPVID              VARCHAR2(20)  default '~',
    CPRODUCTORID         VARCHAR2(20)  default '~',
    CPROJECTID           VARCHAR2(20)  default '~',
    CPROJECTTASKID       VARCHAR2(20),
    CQTUNITID            VARCHAR2(20)  default '~',
    CREQSTOORGOID        VARCHAR2(20)  default '~',
    CREQSTOORGVID        VARCHAR2(20)  default '~',
    CROWNO               VARCHAR2(20),
    CSELASTUNITID        VARCHAR2(20),
    CSOURCEBILLBID       VARCHAR2(20),
    CSOURCEBILLHID       VARCHAR2(20),
    CSOURCETRANSTYPE     VARCHAR2(20)  default '~',
    CSOURCETYPE          VARCHAR2(20)  default '~',
    CSRC2BILLBID         VARCHAR2(20),
    CSRC2BILLHID         VARCHAR2(20),
    CSRC2BILLTYPE        VARCHAR2(20)  default '~',
    CSRC2TRANSTYPE       VARCHAR2(20)  default '~',
    CSRCMATERIALOID      VARCHAR2(20)  default '~',
    CSRCMATERIALVID      VARCHAR2(20)  default '~',
    CSTATEID             VARCHAR2(20)  default '~',
    CTAXCODEID           VARCHAR2(20)  default '~',
    CTPLCUSTOMERID       VARCHAR2(20)  default '~',
    CUNITID              VARCHAR2(20)  default '~',
    CVENDORID            VARCHAR2(20)  default '~',
    CVMIVENDERID         VARCHAR2(20)  default '~',
    DBIZDATE             VARCHAR2(19),
    DPRODUCEDATE         VARCHAR2(19),
    DR                   NUMBER(10)    default 0,
    DVALIDATE            VARCHAR2(19),
    FBILLROWFLAG         NUMBER(38),
    FCHECKED             NUMBER(38),
    FLARGESS             CHAR,
    FTAXTYPEFLAG         NUMBER(38),
    IDESATYPE            NUMBER(38),
    NASSISTNUM           NUMBER(28, 8),
    NBARCODENUM          NUMBER(28, 8),
    NCALCOSTMNY          NUMBER(28, 8),
    NCALTAXMNY           NUMBER(28, 8),
    NCHANGESTDRATE       NUMBER(28, 8),
    NCOUNTNUM            NUMBER(28, 8),
    NGLOBALEXCHGRATE     NUMBER(28, 8),
    NGLOBALMNY           NUMBER(28, 8),
    NGLOBALTAXMNY        NUMBER(28, 8),
    NGROSSNUM            NUMBER(28, 8),
    NGROUPEXCHGRATE      NUMBER(28, 8),
    NGROUPMNY            NUMBER(28, 8),
    NGROUPTAXMNY         NUMBER(28, 8),
    NITEMDISCOUNTRATE    NUMBER(28, 8),
    NKDNUM               NUMBER(28, 8),
    NMNY                 NUMBER(28, 8),
    NNETPRICE            NUMBER(28, 8),
    NNOSUBTAX            NUMBER(28, 8),
    NNOSUBTAXRATE        NUMBER(28, 8),
    NNUM                 NUMBER(28, 8),
    NORIGMNY             NUMBER(28, 8),
    NORIGNETPRICE        NUMBER(28, 8),
    NORIGPRICE           NUMBER(28, 8),
    NORIGTAXMNY          NUMBER(28, 8),
    NORIGTAXNETPRICE     NUMBER(28, 8),
    NORIGTAXPRICE        NUMBER(28, 8),
    NPIECE               NUMBER(28, 8),
    NPLANNEDMNY          NUMBER(28, 8),
    NPLANNEDPRICE        NUMBER(28, 8),
    NPRICE               NUMBER(28, 8),
    NQTNETPRICE          NUMBER(28, 8),
    NQTORIGNETPRICE      NUMBER(28, 8),
    NQTORIGPRICE         NUMBER(28, 8),
    NQTORIGTAXNETPRICE   NUMBER(28, 8),
    NQTORIGTAXPRICE      NUMBER(28, 8),
    NQTPRICE             NUMBER(28, 8),
    NQTTAXNETPRICE       NUMBER(28, 8),
    NQTTAXPRICE          NUMBER(28, 8),
    NQTUNITNUM           NUMBER(28, 8),
    NSHOULDASSISTNUM     NUMBER(28, 8),
    NSHOULDNUM           NUMBER(28, 8),
    NTARENUM             NUMBER(28, 8),
    NTAX                 NUMBER(28, 8),
    NTAXMNY              NUMBER(28, 8),
    NTAXNETPRICE         NUMBER(28, 8),
    NTAXPRICE            NUMBER(28, 8),
    NTAXRATE             NUMBER(28, 8),
    NVOLUME              NUMBER(28, 8),
    NWEIGHT              NUMBER(28, 8),
    PK_BATCHCODE         VARCHAR2(20)  default '~',
    PK_CREQWAREID        VARCHAR2(20)  default '~',
    PK_GROUP             VARCHAR2(20)  default '~',
    PK_MEASWARE          VARCHAR2(20)  default '~',
    PK_ORG               VARCHAR2(20)  default '~',
    PK_ORG_V             VARCHAR2(20)  default '~',
    PK_PACKSORT          VARCHAR2(20)  default '~',
    PK_SERIALCODE        VARCHAR2(20),
    TS                   CHAR(19)      default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VBATCHCODE           VARCHAR2(40),
    VBDEF1               VARCHAR2(300),
    VBDEF10              VARCHAR2(101),
    VBDEF11              VARCHAR2(101),
    VBDEF12              VARCHAR2(101),
    VBDEF13              VARCHAR2(101),
    VBDEF14              VARCHAR2(101),
    VBDEF15              VARCHAR2(101),
    VBDEF16              VARCHAR2(101),
    VBDEF17              VARCHAR2(101),
    VBDEF18              VARCHAR2(101),
    VBDEF19              VARCHAR2(101),
    VBDEF2               VARCHAR2(101),
    VBDEF20              VARCHAR2(101),
    VBDEF3               VARCHAR2(101),
    VBDEF4               VARCHAR2(101),
    VBDEF5               VARCHAR2(101),
    VBDEF6               VARCHAR2(101),
    VBDEF7               VARCHAR2(101),
    VBDEF8               VARCHAR2(1000),
    VBDEF9               VARCHAR2(101),
    VBILLBARCODE         VARCHAR2(128),
    VCHANGERATE          VARCHAR2(60),
    VDETAILBILLCODE      VARCHAR2(40),
    VEXIGENCYBID         VARCHAR2(20),
    VEXIGENCYCODE        VARCHAR2(50),
    VEXIGENCYHID         VARCHAR2(20),
    VEXIGENCYROWNO       VARCHAR2(50),
    VFIRSTBILLCODE       VARCHAR2(40),
    VFIRSTROWNO          VARCHAR2(20),
    VFREE1               VARCHAR2(101),
    VFREE10              VARCHAR2(101),
    VFREE2               VARCHAR2(101),
    VFREE3               VARCHAR2(101),
    VFREE4               VARCHAR2(101),
    VFREE5               VARCHAR2(101),
    VFREE6               VARCHAR2(101),
    VFREE7               VARCHAR2(101),
    VFREE8               VARCHAR2(101),
    VFREE9               VARCHAR2(101),
    VITCONTRACTBILLCODE  VARCHAR2(40),
    VNOTEBODY            VARCHAR2(181),
    VQTUNITRATE          VARCHAR2(60),
    VRETURNREASON        VARCHAR2(20)  default '~',
    VSERIALCODE          VARCHAR2(128),
    VSOURCEBILLCODE      VARCHAR2(40),
    VSOURCEROWNO         VARCHAR2(20),
    VSRC2BILLCODE        VARCHAR2(40),
    VSRC2BILLROWNO       VARCHAR2(20),
    VBDEF21              VARCHAR2(101) default '~',
    VBDEF22              VARCHAR2(101) default '~',
    VBDEF23              VARCHAR2(101) default '~',
    VBDEF24              VARCHAR2(101) default '~',
    VBDEF25              VARCHAR2(101) default '~',
    VBDEF26              VARCHAR2(101) default '~',
    VBDEF27              VARCHAR2(101) default '~',
    VBDEF28              VARCHAR2(101) default '~',
    VBDEF29              VARCHAR2(101) default '~',
    VBDEF30              VARCHAR2(101) default '~'
)


package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiConvertReimbursementDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDTO;
import com.cloud.ficonsumer.dto.FiSourceReimbursementDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceReimbursement;
import com.cloud.ficonsumer.vo.FiSourceReimbursementQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 通用报销单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:38
 */
@Mapper
public interface FiSourceReimbursementMapper extends CloudBaseMapper<FiSourceReimbursement> {

    @Select("<script>SELECT t.*,(select source_system_name from api_compare where file_type =46 and source_system_id=t.billmaker and del_flag=0 limit 1) as billmakerName  " +
            " FROM fi_source_reimbursement t " +
            " WHERE t.del_flag = 0" +
            "<if test=\"param.pkPayablebill !=null and param.pkPayablebill !=''\"> " +
            " and pk_payablebill = #{param.pkPayablebill} " +
            "</if> " +
            "<if test=\"param.billno !=null and param.billno !=''\"> " +
            " and billno like CONCAT('%',#{param.billno},'%') " +
            "</if> " +
            " <if test=\"param.pkOrgList!=null and param.pkOrgList.size()>0\"> " +
            "   AND pk_org in " +
            "   <foreach collection=\"param.pkOrgList\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "      #{item} " +
            "   </foreach>" +
            " </if> " +
            "<if test=\"param.groupCodeFlag !=null and param.groupCodeFlag !=''\"> " +
            " and pk_org in (select pk_org " +
            "       from fi_org_compare " +
            "       where group_code = #{param.groupCodeFlag} and del_flag =0) " +
            "</if> " +
            "<if test=\"param.pushStatus !=null and param.pushStatus !=''\"> " +
            " and push_status = #{param.pushStatus} " +
            "</if> " +
            "<if test=\"param.returnStatus !=null and param.returnStatus !=''\"> " +
            " and return_status = #{param.returnStatus} " +
            "</if> " +
            "<if test=\"param.pushStatusList != null and param.pushStatusList.size() > 0\">"+
            " and push_status in " +
            "<foreach collection=\"param.pushStatusList\" item=\"item\" open=\"(\" separator=\",\" close=\")\">"+
            "#{item}" +
            "</foreach>" +
            "</if> " +
            "<if test=\"param.createTimeStart !=null and param.createTimeStart !=''\"> " +
            " and create_time >= #{param.createTimeStart} " +
            "</if> " +
            "<if test=\"param.createTimeEnd !=null and param.createTimeEnd !=''\"> " +
            " and #{param.createTimeEnd} >= create_time " +
            "</if> " +
            " order by create_time desc  " +
            "</script>")
    Page<FiSourceReimbursementDTO> beforeConvertDataPage(Page page, @Param("param") FiSourceReimbursementQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_source_reimbursement t " +
            "    LEFT JOIN fi_source_reimbursement_detail td ON t.pk_payablebill = td.pk_payablebill " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.pk_payablebill = #{param.pkPayablebill}" +
            "</script>")
    Page<FiSourceReimbursementDetailDTO> getBeforeConvertDataPage(Page page, @Param("param") FiSourceReimbursementQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_convert_reimbursement t " +
            "    LEFT JOIN fi_convert_reimbursement_detail td ON t.pk_payablebill = td.pk_payablebill " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.pk_payablebill = #{pk}" +
            "</script>")
    Page<FiConvertReimbursementDetailDTO> getConvertData(Page page, @Param("pk") String pk);
}

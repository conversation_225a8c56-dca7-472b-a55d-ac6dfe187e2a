

package com.cloud.ficonsumer.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.cloud.common.data.datascope.CloudBaseMapper;
import com.cloud.ficonsumer.dto.FiConvertReceivableDetailDTO;
import com.cloud.ficonsumer.dto.FiSourceReceivableDTO;
import com.cloud.ficonsumer.dto.FiSourceReceivableDetailDTO;
import com.cloud.ficonsumer.entity.FiSourceReceivable;
import com.cloud.ficonsumer.vo.FiSourceReceivableQueryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 项目应收单源数据主表
 *
 * <AUTHOR>
 * @date 2025-01-23 15:52:25
 */
@Mapper
public interface FiSourceReceivableMapper extends CloudBaseMapper<FiSourceReceivable> {

    @Select("<script>SELECT * " +
            " FROM fi_source_receivable t " +
            " WHERE t.del_flag = 0" +
            "<if test=\"param.pkRecbill !=null and param.pkRecbill !=''\"> " +
            " and pk_recbill = #{param.pkRecbill} " +
            "</if> " +
            "<if test=\"param.billno !=null and param.billno !=''\"> " +
            " and billno like CONCAT('%',#{param.billno},'%') " +
            "</if> " +
            "<if test=\"param.pkOrgList != null and param.pkOrgList.size() > 0\"> " +
            "   AND pk_org in " +
            "   <foreach collection=\"param.pkOrgList\" item=\"item\" open=\"(\" close=\")\" separator=\",\"> " +
            "      #{item} " +
            "   </foreach>" +
            " </if> " +
            "<if test=\"param.groupCodeFlag !=null and param.groupCodeFlag !=''\"> " +
            " and pk_org in (select pk_org " +
            "       from fi_org_compare " +
            "       where group_code = #{param.groupCodeFlag} and del_flag =0) " +
            "</if> " +
            "<if test=\"param.pkTradetype !=null and param.pkTradetype !=''\"> " +
            " and pk_tradetype = #{param.pkTradetype} " +
            "</if> " +
            "<if test=\"param.pushStatus !=null and param.pushStatus !=''\"> " +
            " and push_status = #{param.pushStatus} " +
            "</if> " +
            "<if test=\"param.pushStatusList != null and param.pushStatusList.size() > 0\">"+
            " and push_status in " +
            "<foreach collection=\"param.pushStatusList\" item=\"item\" open=\"(\" separator=\",\" close=\")\">"+
            "#{item}" +
            "</foreach>" +
            "</if> " +
            "<if test=\"param.createTimeStart !=null and param.createTimeStart !=''\"> " +
            " and create_time >= #{param.createTimeStart} " +
            "</if> " +
            "<if test=\"param.createTimeEnd !=null and param.createTimeEnd !=''\"> " +
            " and #{param.createTimeEnd} >= create_time " +
            "</if> " +
            " order by create_time desc  " +
            "</script>")
    Page<FiSourceReceivableDTO> beforeConvertDataPage(Page page, @Param("param") FiSourceReceivableQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_source_receivable t " +
            "    LEFT JOIN fi_source_receivable_detail td ON t.pk_recbill = td.pk_recbill " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.pk_recbill = #{param.pkRecbill}" +
            "</script>")
    Page<FiSourceReceivableDetailDTO> getBeforeConvertDataPage(Page page, @Param("param") FiSourceReceivableQueryVO queryVO);

    @Select("<script>SELECT td.* " +
            "    FROM fi_convert_receivable t " +
            "    LEFT JOIN fi_convert_receivable_detail td ON t.pk_recbill = td.pk_recbill " +
            "    WHERE t.del_flag = 0 AND td.del_flag = 0 and t.pk_recbill = #{pk}" +
            "</script>")
    Page<FiConvertReceivableDetailDTO> getConvertData(Page page, @Param("pk") String pk);
}

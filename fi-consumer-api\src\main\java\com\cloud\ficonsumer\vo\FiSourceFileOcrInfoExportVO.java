package com.cloud.ficonsumer.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "进项发票导出数据")
public class FiSourceFileOcrInfoExportVO {

    @ApiModelProperty(value = "发票ID")
    @ExcelProperty(value = "发票ID")
    private String einvoiceid;

    @ApiModelProperty(value = "发票唯一标识")
    @ExcelProperty(value = "发票唯一标识")
    private String uniquecodeofinvoice;

    @ApiModelProperty(value = "推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中、5:推送财务失败")
    @ExcelIgnore
    private Integer pushStatus;

    @ApiModelProperty(value = "推送状态")
    @ExcelProperty("推送状态名称")
    private String pushStatusName;

    @ApiModelProperty(value = "推送成功失败信息")
    @ExcelProperty(value = "推送成功失败信息")
    private String pushMsg;

    @ApiModelProperty(value = "公司代码")
    @ExcelProperty(value = "公司代码")
    private String bukrs;

    @ApiModelProperty(value = "发票号码")
    @ExcelProperty(value = "发票号码")
    private String einvoicenumber;

    @ApiModelProperty(value = "发票代码")
    @ExcelProperty(value = "发票代码")
    private String einvoicecode;

    @ApiModelProperty(value = "开票日期")
    @ExcelProperty(value = "开票日期")
    private String invoicedate;

    @ApiModelProperty(value = "发票类型")
    @ExcelProperty(value = "发票类型")
    private String invoiceTypeCode;

    @ApiModelProperty(value = "校验码")
    @ExcelProperty(value = "校验码")
    private String checkcode;

    @ApiModelProperty(value = "销方名称")
    @ExcelProperty(value = "销方名称")
    private String salername;

    @ApiModelProperty(value = "销方税号")
    @ExcelProperty(value = "销方税号")
    private String salertaxno;

    @ApiModelProperty(value = "销方地址、电话")
    @ExcelProperty(value = "销方地址、电话")
    private String saleraddressphone;

    @ApiModelProperty(value = "销方开户行及账号")
    @ExcelProperty(value = "销方开户行及账号")
    private String saleraccount;

    @ApiModelProperty(value = "购方名称")
    @ExcelProperty(value = "购方名称")
    private String buyername;

    @ApiModelProperty(value = "购方税号")
    @ExcelProperty(value = "购方税号")
    private String buyertaxno;

    @ApiModelProperty(value = "购方地址、电话")
    @ExcelProperty(value = "购方地址、电话")
    private String buyeraddressphone;

    @ApiModelProperty(value = "购方开户行及账号")
    @ExcelProperty(value = "购方开户行及账号")
    private String buyeraccount;

    @ApiModelProperty(value = "发票金额(不含税)")
    @ExcelProperty(value = "发票金额(不含税)")
    private String invoiceamount;

    @ApiModelProperty(value = "发票税额")
    @ExcelProperty(value = "发票税额")
    private String taxamount;

    @ApiModelProperty(value = "价税合计")
    @ExcelProperty(value = "价税合计")
    private String totalamount;

    @ApiModelProperty(value = "全电发票号码")
    @ExcelProperty(value = "全电发票号码")
    private String elecInvoiceNo;

    @ApiModelProperty(value = "发票状态")
    @ExcelProperty(value = "发票状态")
    private String invoiceStatus;

    @ApiModelProperty(value = "开票人")
    @ExcelProperty(value = "开票人")
    private String dparty;

    @ApiModelProperty(value = "税收分类编码")
    @ExcelProperty(value = "税收分类编码")
    private String taxCalCode;

    @ApiModelProperty(value = "特殊政策标识")
    @ExcelProperty(value = "特殊政策标识")
    private Integer specialPolicySign;

    @ApiModelProperty(value = "零税率标识")
    @ExcelProperty(value = "零税率标识")
    private Integer taxRateFlag;

    @ApiModelProperty(value = "发票明细数据(JSON)")
    @ExcelProperty(value = "发票明细数据(JSON)")
    private String body;

    @ApiModelProperty(value = "发票使用单位")
    @ExcelProperty(value = "发票使用单位")
    private String unitCode;

    @ApiModelProperty("部门主键")
    @ExcelProperty(value = "部门主键")
    private String pkDept;
}
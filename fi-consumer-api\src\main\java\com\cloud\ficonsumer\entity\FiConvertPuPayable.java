package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@TableName("fi_convert_pu_payable")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "采购应付转换数据主表")
public class FiConvertPuPayable extends BaseEntity<FiConvertPuPayable> {
    private static final long serialVersionUID = -1L;

    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    @ApiModelProperty(value = "应付财务组织", required = true)
    private String pkOrg;

    @ApiModelProperty(value = "应付财务组织编码", required = true)
    private String pkOrgCode;

    @ApiModelProperty(value = "应付财务组织名称", required = true)
    private String pkOrgName;

    @ApiModelProperty(value = "制单人", required = true)
    private String billmaker;

    @ApiModelProperty(value = "应付单标识")
    private String pkPayablebill;

    @ApiModelProperty(value = "费用类型")
    private String def6;

    @ApiModelProperty(value = "应付类型")
    private String pkTradetypeid;

    @ApiModelProperty(value = "应付类型编码")
    private String pkTradetype;

    @ApiModelProperty(value = "币种")
    private String pkCurrtype;

    @ApiModelProperty(value = "原币金额", required = true)
    private BigDecimal money;

    @ApiModelProperty(value = "单据状态")
    private Integer billstatus;

    @ApiModelProperty(value = "单据号", required = true)
    private String billno;

    @ApiModelProperty(value = "付款性质")
    private String def47;

    @ApiModelProperty(value = "部门分类")
    private String def52;

    @ApiModelProperty(value = "是否退回重传标识，Y-是，N-否；标识默认为N，回退成功后，更新为Y；单据集成成功后，重置为N")
    private String backTag;

    @ApiModelProperty(value = "订单编号")
    private String purchaseorder;
}

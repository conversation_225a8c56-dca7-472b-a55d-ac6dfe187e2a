package com.cloud.ficonsumer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.admin.api.entity.SysDictItem;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.mapper.ApiUpmsMapper;
import com.cloud.ficonsumer.mapper.FileManageMapper;
import com.cloud.ficonsumer.service.ApiCompareService;
import com.cloud.ficonsumer.service.FiOrgConvertService;
import com.cloud.ficonsumer.service.FiSourceFileOcrInfoService;
import com.cloud.ficonsumer.service.YgInvoiceService;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.InvoiceFileVO;
import com.cloud.ficonsumer.vo.YgInvoiceInfoVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class YgInvoiceServiceImpl implements YgInvoiceService {

    private final ApiCompareService apiCompareService;
    private final FiOrgConvertService fiOrgConvertService;
    private final FileManageMapper fileManageMapper;
    private final ApiUpmsMapper apiUpmsMapper;
    private final FiSourceFileOcrInfoService fiSourceFileOcrInfoService;

    @Override
    public String ygInvoiceAuthenticity(Long orgId, Map<String, Object> dto) {
        ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000040.getServCode());
        if (Objects.isNull(apiExchange)) {
            throw new CheckedException(MessageFormat.format("远光服务编码不存在, code: {0}", IntegrationEnum.ERP_YG_000040.getServCode()));
        }

        String mdmOrgCode = fiOrgConvertService.getMdmOrgCodeByOrgId(orgId);
        if (StringUtils.isEmpty(mdmOrgCode)) {
            throw new CheckedException(MessageFormat.format("业+组织ID对应mdm组织编码不存在, orgId: {0}", orgId));
        }

        log.info("远光接口原始URL为: {}", apiExchange.getUrl());
        String url = apiExchange.getUrl() + "&unitCode=" + mdmOrgCode;
        log.info("远光接口拼接组织编码后的URL为: {}", url);
        apiExchange.setUrl(url);

        return YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(dto));
    }

    @Override
    public Map<String, List<YgInvoiceInfoVO>> batchProcessInvoiceInfo(Set<String> billIds) {
        if (CollectionUtils.isEmpty(billIds)) {
            return new HashMap<>();
        }

        Map<String, List<YgInvoiceInfoVO>> resultMap = new HashMap<>();
        boolean hasPooledInvoice = false;  // 是否存在已入池的发票

        // 1. 遍历所有单据（包含应付单本身和上游单据）
        for (String billId : billIds) {
            try {
                // 获取单据关联的所有发票
                List<InvoiceFileVO> allInvoices = fileManageMapper.getInvoiceByPk(billId);

                // 获取已入池成功的发票
                List<YgInvoiceInfoVO> pooledInvoices = CollectionUtils.isEmpty(allInvoices) ?
                        new ArrayList<>() :
                        getPooledInvoices(allInvoices);

                // 只要有一张入池成功的发票，就记录下来
                if (!CollectionUtils.isEmpty(pooledInvoices)) {
                    hasPooledInvoice = true;
                }

                resultMap.put(billId, pooledInvoices);

            } catch (Exception e) {
                log.error("处理单据[{}]的发票信息失败: {}", billId, e.getMessage());
                resultMap.put(billId, new ArrayList<>());
            }
        }

        // 2. 如果没有任何已入池的发票，判断是否存在必须入池的发票，传空
        if (!hasPooledInvoice) {
            return handleNoPooledInvoice(billIds, resultMap);
        }

        return resultMap;
    }

    /**
     * 获取已入池成功的发票列表
     *
     * @param allInvoices 所有发票列表
     * @return 已入池成功的发票列表
     */
    private List<YgInvoiceInfoVO> getPooledInvoices(List<InvoiceFileVO> allInvoices) {
        // 获取发票入池状态
        Map<String, Integer> pushStatusMap = getPushStatusMap(allInvoices);

        // 只返回入池成功的发票
        return allInvoices.stream()
                .filter(invoice -> {
                    Integer pushStatus = pushStatusMap.get(invoice.getSourceId());
                    return pushStatus != null && pushStatus == 3;  // 3表示入池成功
                })
                .map(this::convertToInvoiceVO)
                .collect(Collectors.toList());
    }

    /**
     * 处理没有已入池发票的情况
     *
     * @param billIds 所有单据ID（包含应付单本身和上游单据）
     * @param resultMap 当前结果Map
     * @return 处理后的结果Map
     */
    private Map<String, List<YgInvoiceInfoVO>> handleNoPooledInvoice(Set<String> billIds,
                                                                     Map<String, List<YgInvoiceInfoVO>> resultMap) {
        // 检查所有单据是否都不需要入池发票
        for (String billId : billIds) {
            if (checkInvoiceRequired(billId)) {
                throw new CheckedException("存在必须入池的发票，请先完成发票入池");
            }
        }

        return resultMap;
    }

    /**
     * 检查单据是否必须要有入池发票
     *
     * @param billId 单据ID
     * @return true-必须要有入池发票；false-可以不需要入池发票
     */
    private boolean checkInvoiceRequired(String billId) {
        List<String> dictTypeList = new ArrayList<>();
        dictTypeList.add("999");
        // 获取需要入池的发票类型列表
        List<SysDictItem> ocrTypeList = apiUpmsMapper.getItemListByType("filemanage_ocr_type");
        for (SysDictItem sysDictItem : ocrTypeList) {
            if ("是".equals(sysDictItem.getRemarks())) {
                dictTypeList.add(sysDictItem.getValue());
            }
        }
        // 检查是否存在需要入池的发票
        List<Long> mainIdList = fileManageMapper.getSourceInvoiceList(billId, dictTypeList);
        return !CollectionUtils.isEmpty(mainIdList);
    }

    /**
     * 创建默认收据对象
     *
     * @return 默认收据对象
     */
    private YgInvoiceInfoVO createDefaultReceipt() {
        YgInvoiceInfoVO receipt = new YgInvoiceInfoVO();
        receipt.setInvoType(YgConfig.invoType);
        return receipt;
    }

    private Map<String, Integer> getPushStatusMap(List<InvoiceFileVO> invoices) {
        List<String> sourceIds = invoices.stream()
                .map(InvoiceFileVO::getSourceId)
                .collect(Collectors.toList());
        return fiSourceFileOcrInfoService.listOcrPushStatus(sourceIds);
    }

    /**
     * 将发票文件信息转换为发票VO对象
     *
     * @param invoice 发票文件信息
     * @return 发票VO对象
     */
    private YgInvoiceInfoVO convertToInvoiceVO(InvoiceFileVO invoice) {
        YgInvoiceInfoVO invoiceVO = new YgInvoiceInfoVO();
        invoiceVO.setInvoiceNo(invoice.getInvoiceNo());
        invoiceVO.setInvDate(invoice.getInvDate());
        invoiceVO.setInvoCode(invoice.getInvoCode() == null ? "" : invoice.getInvoCode());
        invoiceVO.setInvoType(invoice.getInvoType());
        return invoiceVO;
    }
}


package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiInvoicePostingItem;
import com.cloud.ficonsumer.mapper.FiInvoicePostingItemMapper;
import com.cloud.ficonsumer.service.FiInvoicePostingItemService;
import org.springframework.stereotype.Service;

/**
 * 采购应付明细源数据详情
 *
 * <AUTHOR>
 * @date 2025-04-22 10:04:42
 */
@Service
public class FiInvoicePostingItemServiceImpl extends ServiceImpl<FiInvoicePostingItemMapper, FiInvoicePostingItem> implements FiInvoicePostingItemService {

}

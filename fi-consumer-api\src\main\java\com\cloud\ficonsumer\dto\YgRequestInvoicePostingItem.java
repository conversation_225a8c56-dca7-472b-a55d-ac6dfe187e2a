package com.cloud.ficonsumer.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/4/15.
 */
@Data
public class YgRequestInvoicePostingItem {

    //发票行项目
    private String invoicedocitem;
    //凭证编号
    private String accvouno;
    //财年
    private String fisyea;
    //采购订单号
    private String purordno;
    //采购凭证的项目编号
    private String ordprojid;
    //物料
    private String mtcode;
    //公司代码
    private String comcod;
    //工厂
    private String facnum;
    //税率代码
    private String taxrtcd;
    //金额
    private String purorditemamt;
    //数量
    private String ordqua;
    //参考凭证
    private String refdocnum;
    //参考凭证财年
    private String mtdocyear;
    //参考凭证行项目
    private String referdocno;
    //行项目文本
    private String instruction;
    //对账编号
    private String chkid;
    //内部订单
    private String inteOrdCode;

}

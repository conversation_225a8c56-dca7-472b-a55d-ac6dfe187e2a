package com.cloud.ficonsumer.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/15.
 */
@Data
public class YgRequestInvoicePosting {

    /**
     * 业务单据ID
     */
    private String busibillno;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 业务类型编码
     */
    private String typeid;

    /**
     * 标识：记账发票
     */
    private String xrech;

    /**
     * 标识：后续借/贷
     */
    private String tbtkz;

    /**
     * 公司代码MDM
     */
    private String comcod;

    /**
     * 利润中心MDM
     */
    private String prctr;

    /**
     * 凭证日期
     */
    private String apldate;

    /**
     * 过账日期
     */
    private String voudat;

    /**
     * 出票方
     */
    private String supcracc;

    /**
     * 附件张数
     */
    private String refdocnum;

    /**
     * 金额
     */
    private String invveramt;

    /**
     * 税额
     */
    private String taxamt;

    /**
     * 货币
     */
    private String trancurren;

    /**
     * 付款基准日期
     */
    private String basedate;

    /**
     * 付款条件
     */
    private String terpay;

    /**
     * 付款方式
     */
    private String paymmet;

    /**
     * 凭证类型
     */
    private String doctype;

    /**
     * 凭证抬头文本
     */
    private String instruction;

    /**
     * 税率代码
     */
    private String taxrtcd;

    /**
     * 税项
     */
    private String taxitem;

    /**
     * 经办人编号
     */
    private String managerid;

    /**
     * 制证人
     */
    private String app;

    /**
     * 审核人
     */
    private String checker;

    /**
     * 财务主管
     */
    private String financer;

    /**
     * 分配
     */
    private String zuonr;

    /**
     * 农维费使用标志
     */
    private String agMaintFeeUseMark;

    /**
     * 部门分类
     */
    private String mainDeptType;

    /**
     * 业务事项
     */
    private String cosType;

    /**
     * 明细业务事项
     */
    private String detCosType;

    private List<YgRequestInvoicePostingItem> item;
    private List<YgRequestInvoicePostingItems> items;
    private List<YgRequestInvoicePostingDataItems> accountdataitems;


}

package com.cloud.ficonsumer.listener;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloud.ficonsumer.dto.FiSourceVirtualOrderDTO;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.service.FiSourceVirtualOrderService;
import com.cloud.ficonsumer.vo.FiSourceVirtualOrderExportVO;
import com.cloud.ficonsumer.vo.FiSourceVirtualOrderQueryVO;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/9.
 */
@Slf4j
public class FiSourceVirtualOrderExportListener extends AbstractExportListener<FiSourceVirtualOrderExportVO> {

    private final FiSourceVirtualOrderService fiSourceVirtualOrderService;
    private final FiSourceVirtualOrderQueryVO queryDTO;
    private final Page<FiSourceVirtualOrderDTO> page;
    private static final int PAGE_SIZE = 50000;

    public FiSourceVirtualOrderExportListener(UUID id, FiSourceVirtualOrderService fiSourceVirtualOrderService,
                                              Page<FiSourceVirtualOrderDTO> page,
                                              FiSourceVirtualOrderQueryVO queryDTO) {
        super(id, "付款合同采购订单导出");
        this.page = page;
        this.fiSourceVirtualOrderService = fiSourceVirtualOrderService;
        this.queryDTO = queryDTO;
    }

    @Override
    protected List<FiSourceVirtualOrderExportVO> doHandleData() {
        List<FiSourceVirtualOrderDTO> vos = new ArrayList<>();
        // 如果传入的page参数不为空，直接使用该参数查询
        if (Objects.nonNull(page) && page.getSize() > 0) {
            Page<FiSourceVirtualOrderDTO> pageResult = fiSourceVirtualOrderService.beforeConvertDataPage(page, queryDTO);
            vos.addAll(pageResult.getRecords());
        } else {
            // 如果page为空，使用分页循环查询所有数据
            Page<FiSourceVirtualOrderDTO> localPage = new Page<>(1, PAGE_SIZE);
            Page<FiSourceVirtualOrderDTO> pageResult;
            do {
                pageResult = fiSourceVirtualOrderService.beforeConvertDataPage(localPage, queryDTO);
                vos.addAll(pageResult.getRecords());
                localPage.setCurrent(localPage.getCurrent() + 1);
            } while (localPage.getCurrent() <= pageResult.getPages());
        }
        List<FiSourceVirtualOrderExportVO> result = BeanUtil.copyToList(vos, FiSourceVirtualOrderExportVO.class);
        result.forEach(item -> {
            String pushStatusName = Objects.requireNonNull(PushStatusEnum.getByCode(item.getPushStatus())).getName();
            item.setPushStatusName(pushStatusName);
        });

        return result;
    }
}

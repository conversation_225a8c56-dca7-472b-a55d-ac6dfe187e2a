package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiUdsFileRecord;
import com.cloud.ficonsumer.mapper.FiUdsFileRecordMapper;
import com.cloud.ficonsumer.service.FiUdsFileRecordService;
import com.sgcc.uds.cloud.sdk.action.result.CommonActionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Slf4j
@Service
public class FiUdsFileRecordServiceImpl extends ServiceImpl<FiUdsFileRecordMapper, FiUdsFileRecord> implements FiUdsFileRecordService {

    @Override
    public FiUdsFileRecord checkFileUploaded(String bucketName, String fileName) {
        return this.getOne(new LambdaQueryWrapper<FiUdsFileRecord>()
                .eq(FiUdsFileRecord::getBucketName, bucketName)
                .eq(FiUdsFileRecord::getFileName, fileName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrSaveUploadRecord(String bucketName, String objectName, String fileName,
                                         CommonActionResult udsResult, boolean isSuccess,
                                         FiUdsFileRecord existingRecord, String pushMsg) {
        FiUdsFileRecord record = existingRecord != null ? existingRecord : new FiUdsFileRecord()
                .setBucketName(bucketName)
                .setFileName(fileName)
                .setOriginal(objectName)
                .setFileType(FileUtil.extName(fileName))
                .setMetaTypeName("YJCWXT_FPWJ");
        
        record.setPushStatus(isSuccess ? "1" : "0")
              .setPushTime(LocalDateTime.now())
              .setPushMsg(isSuccess ? null : pushMsg); // 成功时清空错误信息

        if (isSuccess && udsResult != null) {
            record.setDocumentId(udsResult.getDocumentId())
                  .setVersionId(udsResult.getVersionId())
                  .setUdsFileName(udsResult.getFileName());
        }

        this.saveOrUpdate(record);
    }
}

package com.cloud.ficonsumer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.entity.ApiExchange;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.cache.ApiCompareCache;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.entity.FiConvertProjsubPayable;
import com.cloud.ficonsumer.entity.FiConvertProjsubPayableDetail;
import com.cloud.ficonsumer.entity.FiSourceProjsubPayable;
import com.cloud.ficonsumer.entity.FiSourceProjsubPayableDetail;
import com.cloud.ficonsumer.enums.BillTypeEnum;
import com.cloud.ficonsumer.enums.IntegrationEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FiSourceProjsubPayableMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.AdminUtils;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.utils.YgUtil;
import com.cloud.ficonsumer.vo.PlatFormLogVO;
import com.cloud.ficonsumer.vo.YgPayableVO;
import com.cloud.ficonsumer.vo.YgResult;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@AllArgsConstructor
public class FiSourceProjsubPayableServiceImpl extends ServiceImpl<FiSourceProjsubPayableMapper, FiSourceProjsubPayable> implements FiSourceProjsubPayableService {

    private FiSourceProjsubPayableDetailService fiSourceProjsubPayableDetailService;
    private FiConvertProjsubPayableService fiConvertProjsubPayableService;
    private FiConvertProjsubPayableDetailService fiConvertProjsubPayableDetailService;
    private final ApiCompareCache apiCompareCache;
    private final ApiCompareService apiCompareService;
    private DmMapper dmMapper;
    private final YgConfig ygConfig;

    @Override
    public boolean push(String pkPayablebill) {
        FiSourceProjsubPayable sourceMainData = this.getOne(Wrappers.<FiSourceProjsubPayable>lambdaQuery().eq(FiSourceProjsubPayable::getPkPayablebill, pkPayablebill));
        List<FiSourceProjsubPayableDetail> details = fiSourceProjsubPayableDetailService.list(Wrappers.<FiSourceProjsubPayableDetail>lambdaQuery().eq(FiSourceProjsubPayableDetail::getPkPayablebill, pkPayablebill));

        // 重新转换
        try {
            this.turnData(sourceMainData, details, BillTypeEnum.PROJ_SUB_PAYABLE.getCode());
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
            sourceMainData.setIntegrationStatus("1");
            sourceMainData.setPushStatus(PushStatusEnum.CONVERT_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);
            this.updateById(sourceMainData);
            LogUtil.info(log, "项目分包应付集成信息转换失败:", e);
            throw new CheckedException("项目分包应付集成信息转换失败:" + pushMsg);
        }

        // 重新推送智慧平台
        try {
            YgResult ygResult = this.pushData(sourceMainData.getPkPayablebill());
            sourceMainData.setIntegrationStatus("0");
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_SUCCESS.getCode());
            sourceMainData.setPushMsg("");
            sourceMainData.setReadiness(1L);

            // 保存日志
            PlatFormLogVO platFormLogVO = new PlatFormLogVO();
            platFormLogVO.setPkLog(UUID.randomUUID().toString());
            platFormLogVO.setPkBill(pkPayablebill);
            platFormLogVO.setPkBillType("PROJ_SUBPAYABLE");
            platFormLogVO.setCode("000000");
            platFormLogVO.setMsg("");
            dmMapper.insertPlatFormLog(platFormLogVO);
            this.updateById(sourceMainData);
        } catch (Exception e) {
            String pushMsg = AdminUtils.truncateString(e.getMessage(), 1000);
            sourceMainData.setIntegrationStatus("1");
            sourceMainData.setPushStatus(PushStatusEnum.PUSH_FAIL.getCode());
            sourceMainData.setPushMsg(pushMsg);

            // 保存日志
            PlatFormLogVO platFormLogVO = new PlatFormLogVO();
            platFormLogVO.setPkLog(UUID.randomUUID().toString());
            platFormLogVO.setPkBill(pkPayablebill);
            platFormLogVO.setPkBillType("PROJ_SUBPAYABLE");
            platFormLogVO.setCode("000001");
            platFormLogVO.setMsg(pushMsg);
            dmMapper.insertPlatFormLog(platFormLogVO);
            this.updateById(sourceMainData);
            LogUtil.info(log, "项目分包应付集成信息推送失败:", e);
            throw new CheckedException("项目分包应付集成信息推送失败:" + pushMsg);
        }

        return true;
    }

    private YgResult pushData(String pkPayablebill) throws Exception {
        YgResult ygResult = new YgResult();
        if (ygConfig.getEnable()) {
            FiConvertProjsubPayable convertMainData = fiConvertProjsubPayableService.getOne(Wrappers.<FiConvertProjsubPayable>lambdaQuery().eq(FiConvertProjsubPayable::getPkPayablebill, pkPayablebill));
            List<FiConvertProjsubPayableDetail> details = fiConvertProjsubPayableDetailService.list(Wrappers.<FiConvertProjsubPayableDetail>lambdaQuery().eq(FiConvertProjsubPayableDetail::getPkPayablebill, pkPayablebill));
            YgPayableVO ygPayableVO = this.turnYgData(convertMainData, details);
            ApiExchange apiExchange = apiCompareService.getApiExchangeByCode(IntegrationEnum.ERP_YG_000012.getServCode());
            String result = YgUtil.sendToYg(apiExchange, JSONObject.toJSONString(ygPayableVO));
            ygResult = JSONObject.parseObject(result, YgResult.class);
            if (!ygResult.getCode().equals("000000")) {
                throw new CheckedException(ygResult.getmessage());
            }
        }
        return ygResult;
    }

    private YgPayableVO turnYgData(FiConvertProjsubPayable convertMainData, List<FiConvertProjsubPayableDetail> details) {
        return new YgPayableVO(); // 这里可以进行具体的转换逻辑
    }

    private void turnData(FiSourceProjsubPayable sourceMainData, List<FiSourceProjsubPayableDetail> details, String code) throws Exception {
        String pkPayablebill = sourceMainData.getPkPayablebill();

        FiConvertProjsubPayable fiConvertProjsubPayable = new FiConvertProjsubPayable();
        apiCompareCache.convertData(sourceMainData, fiConvertProjsubPayable, code);

        FiConvertProjsubPayable convertMainData = fiConvertProjsubPayableService.getOne(Wrappers.<FiConvertProjsubPayable>lambdaQuery().eq(FiConvertProjsubPayable::getPkPayablebill, pkPayablebill));
        convertMainData.setId(null);
        if (convertMainData != null) {
            fiConvertProjsubPayable.setId(convertMainData.getId());
            fiConvertProjsubPayableDetailService.remove(Wrappers.<FiConvertProjsubPayableDetail>lambdaQuery().eq(FiConvertProjsubPayableDetail::getPkPayablebill, pkPayablebill));
        }

        // 转换预算编制保存然后走MQ推送到bip
        List<FiConvertProjsubPayableDetail> convertDetailList = new ArrayList<>();
        for (FiSourceProjsubPayableDetail sourceDetailData : details) {
            FiConvertProjsubPayableDetail fiConvertProjsubPayableDetail = new FiConvertProjsubPayableDetail();
            apiCompareCache.convertData(sourceDetailData, fiConvertProjsubPayableDetail, code);
            fiConvertProjsubPayableDetail.setId(null);
            convertDetailList.add(fiConvertProjsubPayableDetail);
        }
        fiConvertProjsubPayableService.saveOrUpdate(fiConvertProjsubPayable);
        fiConvertProjsubPayableDetailService.saveOrUpdateBatch(convertDetailList);
    }

}

package com.cloud.ficonsumer.service;

import com.cloud.ficonsumer.vo.YgInvoiceInfoVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface YgInvoiceService {

    /**
     * 调用远光校验增值税发票真伪服务
     *
     * @param orgId 组织ID
     * @param dto   发票四要素DTO
     * @return 远光服务响应对象
     */
    String ygInvoiceAuthenticity(Long orgId, Map<String, Object> dto);

    /**
     * 批量处理业务单据关联的发票信息
     *
     * @param billIds 单据主键集合
     * @return 处理后的发票信息列表Map，key为单据主键，value为发票信息列表
     */
    Map<String, List<YgInvoiceInfoVO>> batchProcessInvoiceInfo(Set<String> billIds);
}


package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertCostOrder;
import com.cloud.ficonsumer.mapper.FiConvertCostOrderMapper;
import com.cloud.ficonsumer.service.FiConvertCostOrderService;
import org.springframework.stereotype.Service;

/**
 * 虚拟采购订单转换数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:05
 */
@Service
public class FiConvertCostOrderServiceImpl extends ServiceImpl<FiConvertCostOrderMapper, FiConvertCostOrder> implements FiConvertCostOrderService {

}

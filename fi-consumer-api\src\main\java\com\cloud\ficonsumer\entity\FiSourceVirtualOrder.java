

package com.cloud.ficonsumer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloud.ficonsumer.common.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 虚拟采购订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-04-01 14:46:26
 */
@Data
@TableName("fi_source_virtual_order")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "虚拟采购订单源数据主表")
public class FiSourceVirtualOrder extends BaseEntity<FiSourceVirtualOrder> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 单据主键
     */
    @ApiModelProperty(value="单据主键")
    private String pkContr;

    /**
     * 组织
     */
    @ApiModelProperty(value="组织")
    private String pkOrg;

    /**
     * 财务组织名称
     */
    @ApiModelProperty(value="财务组织名称")
    private String pkOrgName;

    /**
     * 采购组织
     */
    @ApiModelProperty(value="采购组织")
    private String pkOrgV;

    /**
     * 项目
     */
    @ApiModelProperty(value="项目")
    private String pkProject;

    /**
     * 订单编号
     */
    @ApiModelProperty(value="订单编号")
    private String billCode;

    /**
     * 制单日期
     */
    @ApiModelProperty(value="制单日期")
    private String billmaketime;

    /**
     * 制单人
     */
    @ApiModelProperty(value="制单人")
    private String billmaker;

    /**
     * 供应商
     */
    @ApiModelProperty(value="供应商")
    private String pkSupplier;

    /**
     * 不含税合同金额
     */
    @ApiModelProperty(value="不含税合同金额")
    private String currMny;

    /**
     * 税额
     */
    @ApiModelProperty(value="税额")
    private String tax;

    /**
     * 合同类型
     */
    @ApiModelProperty(value="合同类型")
    private String pkContracttype;

    /**
     * 含税总金额
     */
    @ApiModelProperty(value="含税总金额")
    private String totTaxmny;

    /**
     * 推送状态：1:转换失败、2:推送失败、3:推送成功、4:推送中
     */
    @ApiModelProperty(value="推送状态")
    private Integer pushStatus;

    /**
     * 推送成功失败信息
     */
    @ApiModelProperty(value="推送成功失败信息")
    private String pushMsg;

    /**
     * 推送时间
     */
    @ApiModelProperty(value="推送时间")
    private String pushTime;

    /**
     * 重试次数
     */
    @ApiModelProperty(value="重试次数")
    private Long tryNumber;

    /**
     * 0 未就绪 1 就绪
     */
    @ApiModelProperty(value="0 未就绪 1 就绪")
    private Long readiness;

}

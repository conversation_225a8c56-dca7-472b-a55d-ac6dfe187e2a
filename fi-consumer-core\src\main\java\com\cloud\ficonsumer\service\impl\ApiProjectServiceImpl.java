
package com.cloud.ficonsumer.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.apiexchange.client.message.CloudMqMessage;
import com.cloud.apiexchange.client.producer.CloudMqProducer;
import com.cloud.cloud.common.core.exception.CheckedException;
import com.cloud.ficonsumer.common.FiOrgConvert;
import com.cloud.ficonsumer.common.UCN;
import com.cloud.ficonsumer.config.YgConfig;
import com.cloud.ficonsumer.dto.*;
import com.cloud.ficonsumer.entity.*;
import com.cloud.ficonsumer.enums.Constants;
import com.cloud.ficonsumer.enums.InvoiceBillCheckEnum;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.ApiProjectMapper;
import com.cloud.ficonsumer.mapper.DmMapper;
import com.cloud.ficonsumer.mapper.FileManageMapper;
import com.cloud.ficonsumer.service.*;
import com.cloud.ficonsumer.utils.LogUtil;
import com.cloud.ficonsumer.vo.*;
import io.seata.common.util.CollectionUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toSet;


/**
 * bip供应商
 *
 * <AUTHOR>
 * @date 2024-07-10 15:22:53
 */
@Slf4j
@Service
@AllArgsConstructor
public class ApiProjectServiceImpl extends ServiceImpl<ApiProjectMapper, ApiProject> implements ApiProjectService {

    private final DmMapper dmMapper;
    private final FileManageMapper fileManageMapper;
    private final FiSourceOrderService fiSourceOrderService;
    private final FiSourceOrderDetailService fiSourceOrderDetailService;
    private final FiSourcePuPayableService fiSourcePuPayableService;
    private final FiSourcePuPayableDetailService fiSourcePuPayableDetailService;
    private final FiSourcePuPaybillService fiSourcePuPaybillService;
    private final FiSourcePuPaybillDetailService fiSourcePuPaybillDetailService;
    private final FiSourceReimbursementService fiSourceReimbursementService;
    private final FiSourceReimbursementDetailService fiSourceReimbursementDetailService;
    private final FiSourceReceivableService fiSourceReceivableService;
    private final FiSourceReceivableDetailService fiSourceReceivableDetailService;
    private final FiSourceStoreService fiSourceStoreService;
    private final FiSourceAccountingService fiSourceAccountingService;
    private final FiSourceAccountingDocfreeService fiSourceAccountingDocfreeService;
    private final FiSourceAccountingDetailService fiSourceAccountingDetailService;
    private final FiSourceStoreDetailService fiSourceStoreDetailService;
    private final FiSourceProjEstiPayableService fiSourceProjEstiPayableService;
    private final FiSourceProjEstiPayableDetailService fiSourceProjEstiPayableDetailService;
    private final FiSourceProjPaybillService fiSourceProjPaybillService;
    private final FiSourceProjPaybillDetailService fiSourceProjPaybillDetailService;
    private final FiSourceProjsubPayableService fiSourceProjsubPayableService;
    private final FiSourceProjsubPayableDetailService fiSourceProjsubPayableDetailService;
    private final FiSourceSaleOrderService fiSourceSaleOrderService;
    private final FiSourceSaleOrderDetailService fiSourceSaleOrderDetailService;
    private final FiSourceFileOcrInfoService fiSourceFileOcrInfoService;
    private final FiSourceFileInfoService fiSourceFileInfoService;
    private final FiOrgConvertService fiOrgConvertService;
    private final FiSourceCostOrderService fiSourceCostOrderService;
    private final FiSourceCostOrderDetailService fiSourceCostOrderDetailService;
    private final FiSourceVirtualOrderService fiSourceVirtualOrderService;
    private final FiSourceVirtualOrderDetailService fiSourceVirtualOrderDetailService;

    /**
     * 定时拉取增量的订单
     */
    @Override
    public void getOrderList(FiSourceOrderDTO fiSourceOrderDTO) {
        //初次定时任务才会有超过30万数据，迭代太深会内存溢出（限制迭代次数，后续看看数据中心同步下数据）
//        for (int i = 0; i <= 5; i++) {
        if(StrUtil.isBlank(YgConfig.orderGrabTime)) {
            throw new CheckedException("配置异常");
        }
        fiSourceOrderDTO.setGrabTime(YgConfig.orderGrabTime);
        List<FiSourceOrderVO> fiSourceOrderList = dmMapper.getOrderMainList(fiSourceOrderDTO);
        if (CollectionUtil.isNotEmpty(fiSourceOrderList)) {
            batchSetOrgUcn(
                    fiSourceOrderList,
                    FiSourceOrderVO::getPkOrg,
                    (vo, ucn) -> {
                        vo.setPkOrgCode(ucn.getCode());
                        vo.setPkOrgName(ucn.getName());
                    });
            for (FiSourceOrderVO fiSourceOrderVO : fiSourceOrderList) {
                List<FiSourceOrderDetail> detailSourceList = new ArrayList<>();
                List<FiSourceOrderDetailVO> detailList = dmMapper.getOrderDetailList(fiSourceOrderVO.getPkOrder());
                BigDecimal norigmnySum = BigDecimal.ZERO;
                BigDecimal ntaxSum = BigDecimal.ZERO;
                for (FiSourceOrderDetailVO fiSourceOrderDetailVO : detailList) {
                    if (fiSourceOrderDetailVO.getCrowno().equals("10")) {
                        fiSourceOrderVO.setVcontractcode(fiSourceOrderDetailVO.getVcontractcode());
                        fiSourceOrderVO.setNexchangerate(fiSourceOrderDetailVO.getNexchangerate());
                    }
                    if (StrUtil.isNotBlank(fiSourceOrderDetailVO.getNorigmny())) {
                        norigmnySum = norigmnySum.add(new BigDecimal(fiSourceOrderDetailVO.getNorigmny()));
                    }
                    if (StrUtil.isNotBlank(fiSourceOrderDetailVO.getNtax())) {
                        ntaxSum = ntaxSum.add(new BigDecimal(fiSourceOrderDetailVO.getNtax()));
                    }
                    fiSourceOrderDetailVO.setVbillcode(fiSourceOrderVO.getVbillcode());
                    fiSourceOrderDetailVO.setPkDeptV(fiSourceOrderVO.getPkDeptV());
                    fiSourceOrderDetailVO.setPkOrgV(fiSourceOrderVO.getPkOrgV());
                    fiSourceOrderDetailVO.setBreturn(fiSourceOrderVO.getBreturn());
                    fiSourceOrderDetailVO.setCtrantypeid(fiSourceOrderVO.getCtrantypeid());
                    fiSourceOrderDetailVO.setDbilldate(fiSourceOrderVO.getDbilldate());
                    fiSourceOrderDetailVO.setNtotalorigmny(fiSourceOrderVO.getNtotalorigmny());
                    fiSourceOrderDetailVO.setForderstatus(fiSourceOrderVO.getForderstatus());
                    fiSourceOrderDetailVO.setPkOrder(fiSourceOrderVO.getPkOrder());
                    detailSourceList.add(fiSourceOrderDetailVO);
                }
                fiSourceOrderVO.setPushStatus(PushStatusEnum.PUSH_DING.getCode());
                fiSourceOrderVO.setNorigmny(norigmnySum.toPlainString());
                fiSourceOrderVO.setNtax(ntaxSum.toPlainString());
                //判断更新与否
                FiSourceOrder source = fiSourceOrderService.getOne(Wrappers.<FiSourceOrder>lambdaQuery().eq(FiSourceOrder::getPkOrder, fiSourceOrderVO.getPkOrder()));
                if (source != null) {
                    fiSourceOrderVO.setId(source.getId());
                    fiSourceOrderDetailService.remove(Wrappers.<FiSourceOrderDetail>lambdaQuery().eq(FiSourceOrderDetail::getPkOrder, fiSourceOrderVO.getPkOrder()));
                }
                fiSourceOrderService.saveOrUpdate(fiSourceOrderVO);
                fiSourceOrderDetailService.saveOrUpdateBatch(detailSourceList);
                dmMapper.updateOrderReceive(fiSourceOrderVO.getPkOrder());
                CloudMqMessage cloudMqMessage = new CloudMqMessage();
                cloudMqMessage.setTopic(Constants.TOPIC_ORDER_TRANS);
                cloudMqMessage.setMsgGroup(Constants.Group.group);
                cloudMqMessage.setMsgData(fiSourceOrderVO.getPkOrder());
                CloudMqProducer.produce(cloudMqMessage);
            }
        }
//        }
    }

    /**
     * 定时费用结算单接口（框架合同虚拟采购订单）增量数据
     *
     * @param fiSourceCostOrderDTO
     */
    @Override
    public void getCostOrderList(FiSourceCostOrderDTO fiSourceCostOrderDTO) {
        List<FiSourceCostOrderVO> fiSourceList = dmMapper.getCostOrderMainList(fiSourceCostOrderDTO);
        if (CollectionUtil.isNotEmpty(fiSourceList)) {
            batchSetOrgUcn(
                    fiSourceList,
                    FiSourceCostOrderVO::getPkOrg,
                    (vo, ucn) -> {
                        vo.setPkOrgCode(ucn.getCode());
                        vo.setPkOrgName(ucn.getName());
                    });
            for (FiSourceCostOrderVO fiSourceVO : fiSourceList) {
                List<FiSourceCostOrderDetail> detailSourceList = new ArrayList<>();
                List<FiSourceCostOrderDetailVO> detailList = dmMapper.getCostOrderDetailList(fiSourceVO.getPkFeebalance());

                // 设置明细中的protocolList，从10开始，每行递增10
                int protocolCounter = 10;
                for (FiSourceCostOrderDetailVO fiSourceDetailVO : detailList) {
                    fiSourceDetailVO.setBillCode(fiSourceVO.getBillCode());
                    // 设置行号值为当前计数器值
                    fiSourceDetailVO.setProtocolList(String.valueOf(protocolCounter));
                    // 计数器增加10，为下一行准备
                    protocolCounter += 10;
                    detailSourceList.add(fiSourceDetailVO);
                }

                fiSourceVO.setPushStatus(PushStatusEnum.PUSH_DING.getCode());
                //判断更新与否
                FiSourceCostOrder source = fiSourceCostOrderService.getOne(Wrappers.<FiSourceCostOrder>lambdaQuery().eq(FiSourceCostOrder::getPkFeebalance, fiSourceVO.getPkFeebalance()));
                if (source != null) {
                    fiSourceVO.setId(source.getId());
                    fiSourceCostOrderDetailService.remove(Wrappers.<FiSourceCostOrderDetail>lambdaQuery().eq(FiSourceCostOrderDetail::getPkFeebalance, fiSourceVO.getPkFeebalance()));
                }
                fiSourceCostOrderService.saveOrUpdate(fiSourceVO);
                fiSourceCostOrderDetailService.saveOrUpdateBatch(detailSourceList);
                dmMapper.updateCostOrderReceive(fiSourceVO.getPkFeebalance());
                CloudMqMessage cloudMqMessage = new CloudMqMessage();
                cloudMqMessage.setTopic(Constants.TOPIC_COST_ORDER_TRANS);
                cloudMqMessage.setMsgGroup(Constants.Group.group);
                cloudMqMessage.setMsgData(fiSourceVO.getPkFeebalance());
                CloudMqProducer.produce(cloudMqMessage);
            }
        }
    }

    /**
     * 付款合同接口（虚拟采购订单）
     *
     * @param fiSourceVirtualOrderDTO
     */
    @Override
    public void getVirtualOrderList(FiSourceVirtualOrderDTO fiSourceVirtualOrderDTO) {
        List<FiSourceVirtualOrderVO> fiSourceList = dmMapper.getVirtualOrderMainList(fiSourceVirtualOrderDTO);
        if (CollectionUtil.isNotEmpty(fiSourceList)) {
            batchSetOrgUcn(
                    fiSourceList,
                    FiSourceVirtualOrderVO::getPkOrg,
                    (vo, ucn) -> {
                        vo.setPkOrgCode(ucn.getCode());
                        vo.setPkOrgName(ucn.getName());
                    });
            for (FiSourceVirtualOrderVO fiSourceVO : fiSourceList) {
                List<FiSourceVirtualOrderDetail> detailSourceList = new ArrayList<>();
                List<FiSourceVirtualOrderDetailVO> detailList = dmMapper.getVirtualOrderDetailList(fiSourceVO.getPkContr());
                for (FiSourceVirtualOrderDetailVO fiSourceDetailVO : detailList) {
                    fiSourceDetailVO.setBillCode(fiSourceVO.getBillCode());
                    fiSourceDetailVO.setPkProject(fiSourceVO.getPkProject());
                    detailSourceList.add(fiSourceDetailVO);
                }
                fiSourceVO.setPushStatus(PushStatusEnum.PUSH_DING.getCode());
                //判断更新与否
                FiSourceVirtualOrder source = fiSourceVirtualOrderService.getOne(Wrappers.<FiSourceVirtualOrder>lambdaQuery().eq(FiSourceVirtualOrder::getPkContr, fiSourceVO.getPkContr()));
                if (source != null) {
                    fiSourceVO.setId(source.getId());
                    fiSourceVirtualOrderDetailService.remove(Wrappers.<FiSourceVirtualOrderDetail>lambdaQuery().eq(FiSourceVirtualOrderDetail::getPkContr, fiSourceVO.getPkContr()));
                }
                fiSourceVirtualOrderService.saveOrUpdate(fiSourceVO);
                fiSourceVirtualOrderDetailService.saveOrUpdateBatch(detailSourceList);
                dmMapper.updateVirtualOrderReceive(fiSourceVO.getPkContr());
                CloudMqMessage cloudMqMessage = new CloudMqMessage();
                cloudMqMessage.setTopic(Constants.TOPIC_VIRTUAL_ORDER_TRANS);
                cloudMqMessage.setMsgGroup(Constants.Group.group);
                cloudMqMessage.setMsgData(fiSourceVO.getPkContr());
                CloudMqProducer.produce(cloudMqMessage);
            }
        }
    }

    /**
     * 定时拉取通用报销单接口数据（通用报账）
     */
    @Override
    public void getReimbursementList(FiSourceReimbursementDTO fiSourceReimbursementDTO) {
        //初次定时任务才会有超过30万数据，迭代太深会内存溢出（限制迭代次数，后续看看数据中心同步下数据）
        fiSourceReimbursementDTO.setPkTradetype(Constants.TradeType.reimbursement);
        fiSourceReimbursementDTO.setInnercode(YgConfig.innercode);
//        for (int i = 0; i <= 5; i++) {
        List<FiSourceReimbursementVO> fiSourceList = dmMapper.getReimbursementMainList(fiSourceReimbursementDTO);
        if (CollectionUtil.isNotEmpty(fiSourceList)) {
            batchSetOrgUcn(
                    fiSourceList,
                    FiSourceReimbursementVO::getPkOrg,
                    (vo, ucn) -> {
                        vo.setPkOrgCode(ucn.getCode());
                        vo.setPkOrgName(ucn.getName());
                    });
            for (FiSourceReimbursementVO fiSourceVO : fiSourceList) {
                List<FiSourceReimbursementDetail> detailSourceList = new ArrayList<>();
                List<FiSourceReimbursementDetailVO> detailList = dmMapper.getReimbursementDetailList(fiSourceVO.getPkPayablebill());
                int count = 0;
                for (FiSourceReimbursementDetailVO fiSourceDetailVO : detailList) {
                    if (count == 0) {
                        fiSourceVO.setPkPsndoc(fiSourceDetailVO.getPkPsndoc());
                        fiSourceVO.setPkSubjcode(fiSourceDetailVO.getPkSubjcode());
                        fiSourceVO.setSupplier(fiSourceDetailVO.getSupplier());
                        fiSourceVO.setObjtype(fiSourceDetailVO.getObjtype());
                        fiSourceVO.setPkDeptid(fiSourceDetailVO.getPkDeptid());
                        fiSourceVO.setPkDeptidV(fiSourceDetailVO.getPkDeptidV());
                        fiSourceVO.setScomment(fiSourceDetailVO.getScomment());
                    }
                    count++;
                    detailSourceList.add(fiSourceDetailVO);
                }
//                fiSourceVO.setPkOrgName(dmMapper.getOrgNameByPk(fiSourceVO.getPkOrg()));
                fiSourceVO.setBillmakerT(fiSourceVO.getBillmaker());
                fiSourceVO.setBackTag("N");
                fiSourceVO.setPushStatus(PushStatusEnum.PUSH_DING.getCode());
                //判断更新与否
                FiSourceReimbursement source = fiSourceReimbursementService.getOne(Wrappers.<FiSourceReimbursement>lambdaQuery().eq(FiSourceReimbursement::getPkPayablebill, fiSourceVO.getPkPayablebill()));
                if (source != null) {
                    //暂不考虑退回重传，退回重传是指单据此处调用智慧平台退回，不是返回报错和erp平台的退回
//                        fiSourceVO.setBackTag("Y");
                    fiSourceVO.setId(source.getId());
                    fiSourceReimbursementDetailService.remove(Wrappers.<FiSourceReimbursementDetail>lambdaQuery().eq(FiSourceReimbursementDetail::getPkPayablebill, fiSourceVO.getPkPayablebill()));
                }
                fiSourceReimbursementService.saveOrUpdate(fiSourceVO);
                fiSourceReimbursementDetailService.saveOrUpdateBatch(detailSourceList);
                dmMapper.updateReimbursementReceive(fiSourceVO.getPkPayablebill());
                CloudMqMessage cloudMqMessage = new CloudMqMessage();
                cloudMqMessage.setTopic(Constants.TOPIC_REIMBURSEMENT_TRANS);
                cloudMqMessage.setMsgGroup(Constants.Group.group);
                cloudMqMessage.setMsgData(fiSourceVO.getPkPayablebill());
                CloudMqProducer.produce(cloudMqMessage);
            }
        }
//        }
    }

    /**
     * 定时拉取增量的项目应收数据（承包业务）
     */
    @Override
    public void getReceivableList(FiSourceReceivableDTO fiSourceReceivableDTO) {
        if(StrUtil.isBlank(YgConfig.receivableGrabTime)) {
            throw new CheckedException("配置异常");
        }
        fiSourceReceivableDTO.setGrabTime(YgConfig.receivableGrabTime);
        //初次定时任务才会有超过30万数据，迭代太深会内存溢出（限制迭代次数，后续看看数据中心同步下数据）
        fiSourceReceivableDTO.setPkTradetype(Constants.TradeType.receivable);
//        for (int i = 0; i <= 5; i++) {
        List<FiSourceReceivableVO> fiSourceList = dmMapper.getReceivableMainList(fiSourceReceivableDTO);
        if (CollectionUtil.isNotEmpty(fiSourceList)) {
            batchSetOrgUcn(
                    fiSourceList,
                    FiSourceReceivableVO::getPkOrg,
                    (vo, ucn) -> {
                        vo.setPkOrgCode(ucn.getCode());
                        vo.setPkOrgName(ucn.getName());
                    });
            for (FiSourceReceivableVO fiSourceVO : fiSourceList) {
                List<FiSourceReceivableDetail> detailSourceList = new ArrayList<>();
                List<FiSourceReceivableDetailVO> detailList = dmMapper.getReceivableDetailList(fiSourceVO.getPkRecbill());
                int count = 0;
                for (FiSourceReceivableDetailVO fiSourceDetailVO : detailList) {
                    if (count == 0) {
                        fiSourceVO.setContractno(fiSourceDetailVO.getContractno());
                        fiSourceVO.setProject(fiSourceDetailVO.getProject());
                        fiSourceVO.setPkSubjcode(fiSourceDetailVO.getPkSubjcode());
                        fiSourceVO.setCustomer(fiSourceDetailVO.getCustomer());
                        fiSourceVO.setScomment(fiSourceDetailVO.getScomment());
                    }
                    count++;
                    detailSourceList.add(fiSourceDetailVO);
                }
                fiSourceVO.setBackTag("N");
                fiSourceVO.setPushStatus(PushStatusEnum.PUSH_DING.getCode());
                //判断更新与否
                FiSourceReceivable source = fiSourceReceivableService.getOne(Wrappers.<FiSourceReceivable>lambdaQuery().eq(FiSourceReceivable::getPkRecbill, fiSourceVO.getPkRecbill()));
                if (source != null) {
                    fiSourceVO.setId(source.getId());
                    fiSourceReceivableDetailService.remove(Wrappers.<FiSourceReceivableDetail>lambdaQuery().eq(FiSourceReceivableDetail::getPkRecbill, fiSourceVO.getPkRecbill()));
                }
                fiSourceReceivableService.saveOrUpdate(fiSourceVO);
                fiSourceReceivableDetailService.saveOrUpdateBatch(detailSourceList);
                dmMapper.updateReceivableReceive(fiSourceVO.getPkRecbill());
                CloudMqMessage cloudMqMessage = new CloudMqMessage();
                cloudMqMessage.setTopic(Constants.TOPIC_RECEIVABLE_TRANS);
                cloudMqMessage.setMsgGroup(Constants.Group.group);
                cloudMqMessage.setMsgData(fiSourceVO.getPkRecbill());
                CloudMqProducer.produce(cloudMqMessage);
            }
        }
//        }
    }

    @Override
    public void getStoreList(FiSourceStoreDTO fiSourceStoreDTO) {
        if(StrUtil.isBlank(YgConfig.storeGrabTime)) {
            throw new CheckedException("配置异常");
        }
        fiSourceStoreDTO.setGrabTime(YgConfig.storeGrabTime);
        List<FiSourceStoreVO> fiSourceList = dmMapper.getStoreMainList(fiSourceStoreDTO);
        if (CollectionUtil.isNotEmpty(fiSourceList)) {
            for (FiSourceStoreVO fiSourcePkVO : fiSourceList) {
                FiSourceStoreVO fiSourceVO = dmMapper.getStoreMain(fiSourcePkVO.getCbillid());
                //主体是单个查询的，组织名称单独查询
                fiSourceVO.setPkOrgName(dmMapper.getOrgNameByPk(fiSourceVO.getPkOrg()));
                List<FiSourceStoreDetail> detailSourceList = new ArrayList<>();
                List<FiSourceStoreDetailVO> detailList = dmMapper.getStoreDetailList(fiSourcePkVO.getCbillid());
                int count = 0;
                for (FiSourceStoreDetailVO fiSourceDetailVO : detailList) {
                    if (count == 0) {
                        fiSourceVO.setVcontractcode(fiSourceDetailVO.getVcontractcode());
                        fiSourceVO.setNnum(fiSourceDetailVO.getNnum());
                    }
                    count++;
                    detailSourceList.add(fiSourceDetailVO);
                }
                fiSourceVO.setPushStatus(PushStatusEnum.PUSH_DING.getCode());
                //判断更新与否
                FiSourceStore source = fiSourceStoreService.getOne(Wrappers.<FiSourceStore>lambdaQuery().eq(FiSourceStore::getCbillid, fiSourceVO.getCbillid()));
                if (source != null) {
                    fiSourceVO.setId(source.getId());
                    fiSourceStoreDetailService.remove(Wrappers.<FiSourceStoreDetail>lambdaQuery().eq(FiSourceStoreDetail::getCbillid, fiSourceVO.getCbillid()));
                }
                fiSourceStoreService.saveOrUpdate(fiSourceVO);
                fiSourceStoreDetailService.saveOrUpdateBatch(detailSourceList);
                dmMapper.updateStoreReceive(fiSourceVO.getCbillid());
                CloudMqMessage cloudMqMessage = new CloudMqMessage();
                cloudMqMessage.setTopic(Constants.TOPIC_STORE_TRANS);
                cloudMqMessage.setMsgGroup(Constants.Group.group);
                cloudMqMessage.setMsgData(fiSourceVO.getCbillid());
                CloudMqProducer.produce(cloudMqMessage);
            }
        }
    }

    @Override
    public void incrementalPushPuPayable(FiSourcePuPayableQueryDTO queryDTO) {
        if(StrUtil.isBlank(YgConfig.puPayableGrabTime)) {
            throw new CheckedException("配置异常");
        }
        queryDTO.setGrabTime(YgConfig.puPayableGrabTime);

        List<FiSourcePuPayableVO> fiSourcePuPayableList = new ArrayList<>();
        CollUtil.addAll(fiSourcePuPayableList, dmMapper.listPuPayable(queryDTO));
        CollUtil.addAll(fiSourcePuPayableList, dmMapper.listPrjPayable(queryDTO));

        // 批量设置组织相关
        batchSetOrgUcn(
                fiSourcePuPayableList,
                FiSourcePuPayableVO::getPkOrg,
                (vo, ucn) -> {
                    vo.setPkOrgCode(ucn.getCode());
                    vo.setPkOrgName(ucn.getName());
                });

        if (CollectionUtils.isNotEmpty(fiSourcePuPayableList)) {
            for (FiSourcePuPayableVO fiSourcePuPayable : fiSourcePuPayableList) {
                // 处理并持久化单条采购应付
                processPuPayable(fiSourcePuPayable);
                // 发送异步消息，同步财务中台
                sendPuPayableMessage(fiSourcePuPayable.getPkPayablebill());
            }
        }
    }

    @Override
    public void processPuPayable(FiSourcePuPayable fiSourcePuPayable) {
        // 1. 查询ERP 采购应付明细
        List<FiSourcePuPayableDetailVO> detailList = dmMapper.getPuPayableDetails(fiSourcePuPayable.getPkPayablebill());
        List<FiSourcePuPayableDetail> detailsSourceList = BeanUtil.copyToList(detailList, FiSourcePuPayableDetail.class);

        // 项目应付单，任一项目为自建项目，则不同步
        if ("F1-Cxx-01".equals(fiSourcePuPayable.getPkPayablebill())) {
            List<String> projectList = detailList.stream()
                    .map(FiSourcePuPayableDetailVO::getProject)
                    .distinct()
                    .collect(Collectors.toList());
            // 检查是否存在自建项目
            Integer selfBuildCount = dmMapper.checkSelfBuildProjects(projectList);
            if (selfBuildCount > 0) {
                // 存在自建项目，回写ERP表为自建项目状态(2)后跳过
                dmMapper.updatePuPayableSelfBuild(fiSourcePuPayable.getPkPayablebill());
                return;
            }
        }

        // ========== 同步应付单，需要判断源头订单是否已拉取到集成中台 ==========
        // ========== 未拉取，跳过应付单同步；已同步，回写应付单单头、明细 ==========

        // 1. 处理采购订单来源
        List<String> pkOrderBList = filterSourceBillIds(detailList, "21", FiSourcePuPayableDetailVO::getSrcItemid);
        if (!pkOrderBList.isEmpty()) {
            List<PoOrderB> poOrderBList = dmMapper.listPoOrderBByPkB(pkOrderBList);
            // 检查同步状态
            for (PoOrderB poOrderB : poOrderBList) {
                if (ObjectUtil.notEqual("1", poOrderB.getVdef39())) {
                    log.warn("采购订单{}未同步集成中台，跳过处理", poOrderB.getVbillcode());
                    return;
                }
                // 回写订单号到明细
                detailsSourceList.stream()
                        .filter(d -> poOrderB.getPkOrderB().equals(d.getSrcItemid()))
                        .forEach(d -> d.setPurchaseorder(poOrderB.getVbillcode()));
            }
            // 回写第一个订单号到单头
            if (!poOrderBList.isEmpty()) {
                fiSourcePuPayable.setPurchaseorder(poOrderBList.get(0).getVbillcode());
            }
        }

        // 2. 处理付款合同来源
        List<String> pkContrList = filterSourceBillIds(detailList, "4D42", FiSourcePuPayableDetailVO::getSrcBillid);
        if (!pkContrList.isEmpty()) {
            List<PmContr> pmContrList = dmMapper.listPmContrByPk(pkContrList);
            // 检查同步状态
            for (PmContr pmContr : pmContrList) {
                if (ObjectUtil.notEqual("1", pmContr.getHdef44())) {
                    log.warn("付款合同{}未同步集成中台，跳过处理", pmContr.getBillCode());
                    return;
                }
                // 回写订单号到明细 - 使用付款合同的单据号
                detailsSourceList.stream()
                        .filter(d -> pmContr.getPkContr().equals(d.getSrcBillid()))
                        .forEach(d -> d.setPurchaseorder(pmContr.getBillCode()));
            }
            // 回写第一个合同号到单头
            if (!pmContrList.isEmpty()) {
                fiSourcePuPayable.setPurchaseorder(pmContrList.get(0).getBillCode());
            }
        }

        // 3. 处理费用结算单来源
        List<String> pkFeebalanceList = filterSourceBillIds(detailList, "4D83", FiSourcePuPayableDetailVO::getSrcBillid);
        if (!pkFeebalanceList.isEmpty()) {
            List<PmFeebalanceB> pmFeebalanceBList = dmMapper.listPmFeebalanceByPks(pkFeebalanceList);
            // 检查同步状态
            for (PmFeebalanceB pmFeebalanceB : pmFeebalanceBList) {
                if (ObjectUtil.notEqual("1", pmFeebalanceB.getDef8())) {
                    log.warn("费用结算单{}未同步集成中台，跳过处理", pmFeebalanceB.getBillCode());
                    return;
                }
                detailsSourceList.stream()
                        .filter(d -> pmFeebalanceB.getPkFeebalance().equals(d.getSrcBillid()))
                        .forEach(d -> d.setPurchaseorder(pmFeebalanceB.getBillCode()));
            }
            // 回写第一个结算单号到单头
            if (!pmFeebalanceBList.isEmpty()) {
                fiSourcePuPayable.setPurchaseorder(pmFeebalanceBList.get(0).getBillCode());
            }
        }
        // ========== 同步应付单，源头单据校验结束 ==========

        // 2. 判断MySQL中是否已存在对应数据
        FiSourcePuPayable existing = fiSourcePuPayableService.getOne(
                Wrappers.<FiSourcePuPayable>lambdaQuery()
                        .eq(FiSourcePuPayable::getPkPayablebill, fiSourcePuPayable.getPkPayablebill())
        );
        // 设置默认重传标识为N
        fiSourcePuPayable.setBackTag("N");
        if (Objects.nonNull(existing)) {
            // 设置已有数据的主键，执行更新操作
            fiSourcePuPayable.setId(existing.getId());
            // 后退后，设置重传标识为Y
            fiSourcePuPayable.setBackTag("Y");
            // 删除旧明细数据
            fiSourcePuPayableDetailService.remove(
                    Wrappers.<FiSourcePuPayableDetail>lambdaQuery()
                            .eq(FiSourcePuPayableDetail::getPkPayablebill, fiSourcePuPayable.getPkPayablebill())
            );
        }

        // 3. 设置推送状态为推送中
        fiSourcePuPayable.setPushStatus(PushStatusEnum.PUSH_DING.getCode());

        // 为每条明细记录分配从1开始递增的行号
        int lineNumber = 1;
        for (FiSourcePuPayableDetail detail : detailsSourceList) {
            detail.setLineNumber(lineNumber++);
        }

        // 4. 保存或更新采购应付数据和明细数据到MySQL库
        fiSourcePuPayableService.saveOrUpdate(fiSourcePuPayable);
        fiSourcePuPayableDetailService.saveOrUpdateBatch(detailsSourceList);

        // 5. 回写ERP的Oracle库中的状态（例如更新订单接收状态）
        dmMapper.updatePuPayableReceive(fiSourcePuPayable.getPkPayablebill());
    }

    @Override
    public void sendPuPayableMessage(String pkPayablebill) {
        CloudMqMessage message = new CloudMqMessage();
        message.setTopic(Constants.TOPIC_PU_PAYABLE_TRANS);
        message.setMsgGroup(Constants.Group.group);
        message.setMsgData(pkPayablebill);
        CloudMqProducer.produce(message);
    }

    @Override
    public void incrementalPushPuPaybill(FiSourcePuPaybillQueryDTO queryDTO) {
        if(StrUtil.isBlank(YgConfig.puPaybillGrabTime)) {
            throw new CheckedException("配置异常");
        }
        queryDTO.setGrabTime(YgConfig.puPaybillGrabTime);
        // 获取所有待处理的 FiSourcePuPaybill 列表，采购预付
        List<FiSourcePuPaybillVO> fiSourcePuPaybillList_CG = dmMapper.listPuPaybill(queryDTO);
        // 批量设置组织相关
        batchSetOrgUcn(
                fiSourcePuPaybillList_CG,
                FiSourcePuPaybillVO::getPkOrg,
                (vo, ucn) -> {
                    vo.setPkOrgCode(ucn.getCode());
                    vo.setPkOrgName(ucn.getName());
                });
        if (CollectionUtils.isNotEmpty(fiSourcePuPaybillList_CG)) {
            for (FiSourcePuPaybillVO fiSourcePuPaybillVO : fiSourcePuPaybillList_CG) {
                String billno = fiSourcePuPaybillVO.getBillno();
                log.info("incrementalPushPuPaybill查询订单编号开始：");
                String vbillcode = dmMapper.selectVbillcode(billno);
                log.info("incrementalPushPuPaybill查询订单编号结束：");
                fiSourcePuPaybillVO.setVbillcode(vbillcode);
                FiSourcePuPaybill fiSourcePuPaybill = new FiSourcePuPaybill();
                BeanUtil.copyProperties(fiSourcePuPaybillVO, fiSourcePuPaybill);
                // 处理并持久化单条采购付账数据
                processPuPaybill(fiSourcePuPaybill);
                // 发送异步消息，同步财务中台
                sendPuPaybillMessage(fiSourcePuPaybill.getPkSettlement());
            }
        }
        // 项目预付
        List<FiSourcePuPaybillVO> fiSourcePuPaybillList_XM = dmMapper.listPuPaybillXM(queryDTO);
        // 批量设置组织相关
        batchSetOrgUcn(
                fiSourcePuPaybillList_XM,
                FiSourcePuPaybillVO::getPkOrg,
                (vo, ucn) -> {
                    vo.setPkOrgCode(ucn.getCode());
                    vo.setPkOrgName(ucn.getName());
                });
        if (CollectionUtils.isNotEmpty(fiSourcePuPaybillList_XM)) {
            for (FiSourcePuPaybillVO fiSourcePuPaybillVO : fiSourcePuPaybillList_XM) {
                //表体：合同订单/费用结算单;表体：源头单据类型+源头单据主键,
                //（1）当单据类型是4D42去找src_billid付款合同得pm_contr 中bill_code；
                //（2）当单据类型是4D83找src_billid费用结算单 pm_feebalance 中bill_code
                if ("4D42".equals(fiSourcePuPaybillVO.getPkBilltype())) {
                    String billno = fiSourcePuPaybillVO.getBillno();
                    String Cbillcode = dmMapper.selectBillcodeFromPmcontr(billno);
                    fiSourcePuPaybillVO.setCbillcode(Cbillcode);
                }
                if ("4D83".equals(fiSourcePuPaybillVO.getPkBilltype())) {
                    String pkPaybill = fiSourcePuPaybillVO.getPkPaybill();
                    String Cbillcode = dmMapper.selectBillcodeFromPmfeeblance(pkPaybill);
                    fiSourcePuPaybillVO.setCbillcode(Cbillcode);
                }
                FiSourcePuPaybill fiSourcePuPaybill = new FiSourcePuPaybill();
                BeanUtil.copyProperties(fiSourcePuPaybillVO, fiSourcePuPaybill);
                // 处理并持久化单条项目预付数据
                processPuPaybill(fiSourcePuPaybill);
                // 发送异步消息，同步财务中台
                sendPuPaybillMessage(fiSourcePuPaybill.getPkSettlement());
            }
        }
    }

    @Override
    public void processPuPaybill(FiSourcePuPaybill puPaybill) {
        // 1. 查询 ERP 采购付账明细
        List<FiSourcePuPaybillDetailVO> detailList = dmMapper.getPuPaybillDetails(puPaybill.getPkSettlement());
        List<FiSourcePuPaybillDetail> detailsSourceList = BeanUtil.copyToList(detailList, FiSourcePuPaybillDetail.class);

        // 项目应付单，任一项目为自建项目，则不同步
        if ("F3-Cxx-01".equals(puPaybill.getPkTradetype())) {
            List<String> projectList = detailList.stream()
                    .map(FiSourcePuPaybillDetailVO::getProject)
                    .distinct()
                    .collect(Collectors.toList());
            // 检查是否存在自建项目
            Integer selfBuildCount = dmMapper.checkSelfBuildProjects(projectList);
            if (selfBuildCount > 0) {
                // 存在自建项目，回写ERP表为自建项目状态(2)后跳过
                dmMapper.updatePuPaybillSelfBuild(puPaybill.getBillcode());
                return;
            }
        }

        // 2. 判断 MySQL 中是否已存在对应数据
        FiSourcePuPaybill existing = fiSourcePuPaybillService.getOne(
                Wrappers.<FiSourcePuPaybill>lambdaQuery()
                        .eq(FiSourcePuPaybill::getPkSettlement, puPaybill.getPkSettlement())
        );
        // 设置默认重传标识为N
        puPaybill.setBackTag("N");
        if (Objects.nonNull(existing)) {
            // 设置已有记录的主键，用于后续的更新操作
            puPaybill.setId(existing.getId());
            // 后退后，设置重传标识为Y
            puPaybill.setBackTag("Y");
            // 删除旧的明细数据
            fiSourcePuPaybillDetailService.remove(
                    Wrappers.<FiSourcePuPaybillDetail>lambdaQuery()
                            .eq(FiSourcePuPaybillDetail::getPkSettlement, puPaybill.getPkSettlement())
            );
        }

        // 3. 保存或更新采购付账数据以及对应明细
        fiSourcePuPaybillService.saveOrUpdate(puPaybill);
        fiSourcePuPaybillDetailService.saveOrUpdateBatch(detailsSourceList);

        // 4. 回写 ERP 系统，更新相关订单接收状态
        dmMapper.updatePuPaybillReceive(puPaybill.getBillcode());
    }

    @Override
    public void sendPuPaybillMessage(String pkSettlement) {
        CloudMqMessage cloudMqMessage = new CloudMqMessage();
        cloudMqMessage.setTopic(Constants.TOPIC_PU_PAYBILL_TRANS);
        cloudMqMessage.setMsgGroup(Constants.Group.group);
        cloudMqMessage.setMsgData(pkSettlement);
        CloudMqProducer.produce(cloudMqMessage);
    }

    @Override
    public void incrementalPushProjEstiPayable() {
        // 获取所有待处理的FiSourceProjEstiPayable列表
        List<FiSourceProjEstiPayableVO> fiSourceProjEstiPayableList = dmMapper.listProjEstiPayable();

        if (CollectionUtils.isNotEmpty(fiSourceProjEstiPayableList)) {
            for (FiSourceProjEstiPayable fiSourceProjEstiPayable : fiSourceProjEstiPayableList) {
                // 处理并持久化单条预估应付数据
                processProjEstiPayable(fiSourceProjEstiPayable);
                // 发送异步消息，同步财务中台
                sendProjEstiPayableMessage(fiSourceProjEstiPayable.getPkEstipayablebill());
            }
        }
    }

    @Override
    public void processProjEstiPayable(FiSourceProjEstiPayable projEstiPayable) {
        // 1. 查询预估应付明细
        List<FiSourceProjEstiPayableDetailVO> detailList = dmMapper.getProjEstiPayableDetails(projEstiPayable.getPkEstipayablebill());
        List<FiSourceProjEstiPayableDetail> detailsSourceList = BeanUtil.copyToList(detailList, FiSourceProjEstiPayableDetail.class);

        // 2. 判断MySQL中是否已存在对应数据
        FiSourceProjEstiPayable existing = fiSourceProjEstiPayableService.getOne(
                Wrappers.<FiSourceProjEstiPayable>lambdaQuery()
                        .eq(FiSourceProjEstiPayable::getPkEstipayablebill, projEstiPayable.getPkEstipayablebill())
        );
        if (Objects.nonNull(existing)) {
            projEstiPayable.setId(existing.getId());
            // 删除旧的明细数据
            fiSourceProjEstiPayableDetailService.remove(
                    Wrappers.<FiSourceProjEstiPayableDetail>lambdaQuery()
                            .eq(FiSourceProjEstiPayableDetail::getPkEstipayablebill, projEstiPayable.getPkEstipayablebill())
            );
        }

        // 3. 保存或更新预估应付数据以及对应明细
        fiSourceProjEstiPayableService.saveOrUpdate(projEstiPayable);
        fiSourceProjEstiPayableDetailService.saveOrUpdateBatch(detailsSourceList);

        // 4. 回写系统，更新相关订单接收状态
        dmMapper.updateOrderReceive(projEstiPayable.getPkEstipayablebill());
    }

    @Override
    public void sendProjEstiPayableMessage(String pkEstipayablebill) {
        CloudMqMessage cloudMqMessage = new CloudMqMessage();
        cloudMqMessage.setTopic(Constants.TOPIC_PROJ_ESTI_PAYABLE_TRANS);
        cloudMqMessage.setMsgGroup(Constants.Group.group);
        cloudMqMessage.setMsgData(pkEstipayablebill);
        CloudMqProducer.produce(cloudMqMessage);
    }

    @Override
    public void incrementalPushProjPaybill() {
        // 获取所有待处理的FiSourceProjPaybill列表
        List<FiSourceProjPaybillVO> fiSourceProjPaybillList = dmMapper.listProjPaybill();

        if (CollectionUtils.isNotEmpty(fiSourceProjPaybillList)) {
            for (FiSourceProjPaybill fiSourceProjPaybill : fiSourceProjPaybillList) {
                // 处理并持久化单条项目付账数据
                processProjPaybill(fiSourceProjPaybill);
                // 发送异步消息，同步财务中台
                sendProjPaybillMessage(fiSourceProjPaybill.getPkPaybill());
            }
        }
    }

    @Override
    public void processProjPaybill(FiSourceProjPaybill projPaybill) {
        // 1. 查询项目付账明细
        List<FiSourceProjPaybillDetailVO> detailList = dmMapper.getProjPaybillDetails(projPaybill.getPkPaybill());
        List<FiSourceProjPaybillDetail> detailsSourceList = BeanUtil.copyToList(detailList, FiSourceProjPaybillDetail.class);

        // 2. 判断MySQL中是否已存在对应数据
        FiSourceProjPaybill existing = fiSourceProjPaybillService.getOne(
                Wrappers.<FiSourceProjPaybill>lambdaQuery()
                        .eq(FiSourceProjPaybill::getPkPaybill, projPaybill.getPkPaybill())
        );
        if (Objects.nonNull(existing)) {
            projPaybill.setId(existing.getId());
            // 删除旧的明细数据
            fiSourceProjPaybillDetailService.remove(
                    Wrappers.<FiSourceProjPaybillDetail>lambdaQuery()
                            .eq(FiSourceProjPaybillDetail::getPkPaybill, projPaybill.getPkPaybill())
            );
        }

        // 3. 保存或更新项目付账数据以及对应明细
        fiSourceProjPaybillService.saveOrUpdate(projPaybill);
        fiSourceProjPaybillDetailService.saveOrUpdateBatch(detailsSourceList);

        // 4. 回写系统，更新相关订单接收状态
        dmMapper.updateOrderReceive(projPaybill.getPkPaybill());
    }

    @Override
    public void sendProjPaybillMessage(String pkSettlement) {
        CloudMqMessage cloudMqMessage = new CloudMqMessage();
        cloudMqMessage.setTopic(Constants.TOPIC_PROJ_PAYBILL_TRANS);
        cloudMqMessage.setMsgGroup(Constants.Group.group);
        cloudMqMessage.setMsgData(pkSettlement);
        CloudMqProducer.produce(cloudMqMessage);
    }

    @Override
    public void incrementalPushProjsubPayable() {
        // 获取所有待处理的FiSourceProjsubPayable列表
        List<FiSourceProjsubPayableVO> fiSourceProjsubPayableList = dmMapper.listProjsubPayable();

        if (CollectionUtils.isNotEmpty(fiSourceProjsubPayableList)) {
            for (FiSourceProjsubPayable fiSourceProjsubPayable : fiSourceProjsubPayableList) {
                // 处理并持久化单条项目分包应付数据
                processProjsubPayable(fiSourceProjsubPayable);
                // 发送异步消息，同步财务中台
                sendProjsubPayableMessage(fiSourceProjsubPayable.getPkPayablebill());
            }
        }
    }

    @Override
    public void processProjsubPayable(FiSourceProjsubPayable projsubPayable) {
        // 1. 查询项目分包应付明细
        List<FiSourceProjsubPayableDetailVO> detailList = dmMapper.getProjsubPayableDetails(projsubPayable.getPkPayablebill());
        List<FiSourceProjsubPayableDetail> detailsSourceList = BeanUtil.copyToList(detailList, FiSourceProjsubPayableDetail.class);

        // 2. 判断MySQL中是否已存在对应数据
        FiSourceProjsubPayable existing = fiSourceProjsubPayableService.getOne(
                Wrappers.<FiSourceProjsubPayable>lambdaQuery()
                        .eq(FiSourceProjsubPayable::getPkPayablebill, projsubPayable.getPkPayablebill())
        );
        if (Objects.nonNull(existing)) {
            projsubPayable.setId(existing.getId());
            // 删除旧的明细数据
            fiSourceProjsubPayableDetailService.remove(
                    Wrappers.<FiSourceProjsubPayableDetail>lambdaQuery()
                            .eq(FiSourceProjsubPayableDetail::getPkPayablebill, projsubPayable.getPkPayablebill())
            );
        }

        // 3. 保存或更新项目分包应付数据以及对应明细
        fiSourceProjsubPayableService.saveOrUpdate(projsubPayable);
        fiSourceProjsubPayableDetailService.saveOrUpdateBatch(detailsSourceList);

        // 4. 回写系统，更新相关订单接收状态
        dmMapper.updateOrderReceive(projsubPayable.getPkPayablebill());
    }

    @Override
    public void sendProjsubPayableMessage(String pkPayablebill) {
        CloudMqMessage cloudMqMessage = new CloudMqMessage();
        cloudMqMessage.setTopic(Constants.TOPIC_PROJSUB_PAYBILL_TRANS);
        cloudMqMessage.setMsgGroup(Constants.Group.group);
        cloudMqMessage.setMsgData(pkPayablebill);
        CloudMqProducer.produce(cloudMqMessage);
    }

    @Override
    public void incrementalPushSaleOrder() {
        // 获取所有待处理的FiSourceSalesOrder列表
        List<FiSourceSaleOrderVO> fiSourceSalesOrderList = dmMapper.listSaleOrder();

        if (CollectionUtils.isNotEmpty(fiSourceSalesOrderList)) {
            for (FiSourceSaleOrderVO fiSourceSalesOrderVO : fiSourceSalesOrderList) {
                // VO 转换成 Entity
                FiSourceSaleOrder fiSourceSaleOrder = BeanUtil.copyProperties(fiSourceSalesOrderVO, FiSourceSaleOrder.class);
                // 处理并持久化单条销售订单数据
                processSaleOrder(fiSourceSaleOrder);
                // 发送异步消息，同步财务中台
                sendSaleOrderMessage(fiSourceSaleOrder.getCsaleorderid());
            }
        }
    }

    @Override
    public void processSaleOrder(FiSourceSaleOrder salesOrder) {
        // 1. 查询销售订单明细
        List<FiSourceSaleOrderDetailVO> detailList = dmMapper.getSaleOrderDetails(salesOrder.getCsaleorderid());
        List<FiSourceSaleOrderDetail> detailsSourceList = BeanUtil.copyToList(detailList, FiSourceSaleOrderDetail.class);

        // 2. 判断MySQL中是否已存在对应数据
        FiSourceSaleOrder existing = fiSourceSaleOrderService.getOne(
                Wrappers.<FiSourceSaleOrder>lambdaQuery()
                        .eq(FiSourceSaleOrder::getCsaleorderid, salesOrder.getCsaleorderid())
        );
        if (Objects.nonNull(existing)) {
            salesOrder.setId(existing.getId());
            // 删除旧的明细数据
            fiSourceSaleOrderDetailService.remove(
                    Wrappers.<FiSourceSaleOrderDetail>lambdaQuery()
                            .eq(FiSourceSaleOrderDetail::getCsaleorderid, salesOrder.getCsaleorderid())
            );
        }

        // 3. 保存或更新销售订单数据以及对应明细
        fiSourceSaleOrderService.saveOrUpdate(salesOrder);
        // 设置外键关联
        if (CollectionUtils.isNotEmpty(detailsSourceList)) {
            for (FiSourceSaleOrderDetail detail : detailsSourceList) {
                detail.setCsaleorderid(salesOrder.getCsaleorderid());
            }
        }
        fiSourceSaleOrderDetailService.saveOrUpdateBatch(detailsSourceList);


        // 4. 回写系统，更新相关订单接收状态
        dmMapper.updateSaleOrderReceive(salesOrder.getCsaleorderid());
    }

    @Override
    public void sendSaleOrderMessage(String pkSalesOrder) {
        CloudMqMessage cloudMqMessage = new CloudMqMessage();
        cloudMqMessage.setTopic(Constants.TOPIC_SALE_ORDER_TRANS);
        cloudMqMessage.setMsgGroup(Constants.Group.group);
        cloudMqMessage.setMsgData(pkSalesOrder);
        CloudMqProducer.produce(cloudMqMessage);
    }

    @Override
    public void incrementalPushFileOcrInfo() {
        // 需要同步发票池的组织列表
        Set<Long> enableOrgIds = fiOrgConvertService.list().stream().map(FiOrgConvert::getOrgId)
                .collect(toSet());
        // 获取所有待处理的FiSourcePurchaseInvoice列表
        List<FiSourceFileOcrInfoVO> fileOcrInfos = fileManageMapper.listFileOrcInfo(enableOrgIds);
        // 按业务代码分组，使用枚举作为key
        Map<InvoiceBillCheckEnum, List<FiSourceFileOcrInfoVO>> businessCodeGroup = fileOcrInfos.stream()
                .collect(Collectors.groupingBy(
                        info -> InvoiceBillCheckEnum.getByBusinessCode(info.getBusinessCode())
                ));
        // 批量验证单据状态并过滤
        List<FiSourceFileOcrInfoVO> validFileOcrInfos = fiSourceFileOcrInfoService.filterValidBillStatus(businessCodeGroup);

        for (FiSourceFileOcrInfoVO fileOcrInfoVO : validFileOcrInfos) {
            try {
                // 获取FileInfo
                List<FiSourceFileInfoVO> fileInfoVOS = fileManageMapper.getFileInfos(fileOcrInfoVO.getFileId());
                // VO 转换成 Entity
                FiSourceFileOcrInfo fileOcrInfo = BeanUtil.copyProperties(fileOcrInfoVO, FiSourceFileOcrInfo.class,
                        "id");
                fileOcrInfo.setPushStatus(PushStatusEnum.PUSH_DING.getCode());
                // 处理并持久化单条进项发票数据
                processFileOcrInfo(fileOcrInfo, fileInfoVOS);
                // 发送异步消息，同步财务中台
                sendFileOcrInfoMessage(fileOcrInfo.getUniquecodeofinvoice());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    @Override
    public void processFileOcrInfo(FiSourceFileOcrInfo fileOcrInfo, List<FiSourceFileInfoVO> fileInfoVOS) {
        // 1. 查询进项发票明细
        List<FiSourceFileInfo> fileInfos = BeanUtil.copyToList(fileInfoVOS, FiSourceFileInfo.class,
                CopyOptions.create().setIgnoreProperties("id"));

        // 2. 判断MySQL中是否已存在对应数据
        FiSourceFileOcrInfo existing = fiSourceFileOcrInfoService.getOne(
                Wrappers.<FiSourceFileOcrInfo>lambdaQuery()
                        .eq(FiSourceFileOcrInfo::getUniquecodeofinvoice, fileOcrInfo.getUniquecodeofinvoice())
        );
        if (Objects.nonNull(existing)) {
            fileOcrInfo.setId(existing.getId());
            // 删除旧的明细数据
            fiSourceFileInfoService.remove(
                    Wrappers.<FiSourceFileInfo>lambdaQuery()
                            .eq(FiSourceFileInfo::getFileManageId, fileOcrInfo.getFileId())
            );
        }

        // 3. 保存或更新进项发票数据以及对应明细
        fiSourceFileOcrInfoService.saveOrUpdate(fileOcrInfo);
        fiSourceFileInfoService.saveOrUpdateBatch(fileInfos);

        // 4. 回写系统，更新相关状态 (根据实际情况修改)
        fileManageMapper.updateFileOcrInfoReceive(fileOcrInfo.getFileManageId());
    }

    @Override
    public void sendFileOcrInfoMessage(String uniquecodeofinvoice) {
        CloudMqMessage cloudMqMessage = new CloudMqMessage();
        cloudMqMessage.setTopic(Constants.TOPIC_FILE_OCR_INFO_TRANS);
        cloudMqMessage.setMsgGroup(Constants.Group.group);
        cloudMqMessage.setMsgData(uniquecodeofinvoice);
        CloudMqProducer.produce(cloudMqMessage);
    }

    @Override
    public void getAccountingList(FiSourceAccountingDTO fiSourceAccountingDTO) {
        List<FiSourceAccountingVO> fiSourceList = dmMapper.getAccountingMainList(fiSourceAccountingDTO.getPkVoucher());
        if (CollectionUtil.isNotEmpty(fiSourceList)) {
            for (FiSourceAccountingVO fiSourcePkVO : fiSourceList) {
                String explanation = dmMapper.getSAccountingExplanMain(fiSourcePkVO.getPkVoucher());
                fiSourcePkVO.setExplanation(explanation);
                String orgName = dmMapper.getOrgNameByPk(fiSourcePkVO.getPkOrg());
                fiSourcePkVO.setPkOrgName(orgName);
                String orgCode = dmMapper.getPkOrgByCode(fiSourcePkVO.getPkOrg());
                fiSourcePkVO.setPkOrgCode(orgCode);
                List<FiSourceAccountingDetail> detailSourceList = new ArrayList<>();
                List<FiSourceAccountingDetailVO> detailList = dmMapper.getAccountingDetailList(fiSourcePkVO.getPkVoucher());

                for (FiSourceAccountingDetailVO fiSourceDetailVO : detailList) {
                    detailSourceList.add(fiSourceDetailVO);
                    if (fiSourceDetailVO.getAssid() != null) {
                        List<FiSourceAccountingDocfree> docfreeList = getAccountingDocfreeList(fiSourceDetailVO);
                        if (CollectionUtil.isNotEmpty(docfreeList)){
                            fiSourceAccountingDocfreeService.remove(Wrappers.<FiSourceAccountingDocfree>lambdaQuery().eq(FiSourceAccountingDocfree::getPkDetail, fiSourceDetailVO.getPkDetail()));
                        }
                        fiSourceAccountingDocfreeService.saveOrUpdateBatch(docfreeList);
                    }
                }
                fiSourcePkVO.setIntegrationStatus("4");
                fiSourcePkVO.setPushStatus(PushStatusEnum.PUSH_DING.getCode());
                fiSourcePkVO.setPushOrderStatus(PushStatusEnum.PUSH_DING.getCode());
                //判断更新与否
                FiSourceAccounting source = fiSourceAccountingService.getOne(Wrappers.<FiSourceAccounting>lambdaQuery().eq(FiSourceAccounting::getPkVoucher, fiSourcePkVO.getPkVoucher()));

                if (source != null) {
                    fiSourcePkVO.setId(source.getId());
                    fiSourceAccountingDetailService.remove(Wrappers.<FiSourceAccountingDetail>lambdaQuery().eq(FiSourceAccountingDetail::getPkVoucher, fiSourcePkVO.getPkVoucher()));
                }
                //将拉取成功的数据回写到erp
                dmMapper.insertBdVoucherresult(fiSourcePkVO);
                fiSourceAccountingService.saveOrUpdate(fiSourcePkVO);
                fiSourceAccountingDetailService.saveOrUpdateBatch(detailSourceList);
                CloudMqMessage cloudMqMessageOrder = new CloudMqMessage();
                cloudMqMessageOrder.setTopic(Constants.TOPIC_ACCOUNTING_TRANS);
                cloudMqMessageOrder.setMsgGroup(Constants.Group.group);
                cloudMqMessageOrder.setMsgData(fiSourcePkVO.getPkVoucher());
                CloudMqProducer.produce(cloudMqMessageOrder);
            }
        }
    }

    /**
     * “推送失败”的单据进行重推，10次为上线，定时任务配置一天一次
     */
    @Override
    public void pushAgainTryJob() {
        //通用报销单，最多进行10次重推操作，转换失败的业务手动处理，推送失败才进行重试
        List<FiSourceReimbursement> fiSourceReimbursementList = fiSourceReimbursementService.list(Wrappers.<FiSourceReimbursement>lambdaQuery().eq(FiSourceReimbursement::getPushStatus, "2")
                .lt(FiSourceReimbursement::getTryNumber, 10)
                .last("limit 10000"));
        LogUtil.info(log, "pushAgainTryJob 通用报销单:" + fiSourceReimbursementList.size());
        for (FiSourceReimbursement fiSourceReimbursement : fiSourceReimbursementList) {
            fiSourceReimbursementService.pushAgain(fiSourceReimbursement.getPkPayablebill());
            fiSourceReimbursementService.update(Wrappers.<FiSourceReimbursement>lambdaUpdate()
                    .set(FiSourceReimbursement::getTryNumber, fiSourceReimbursement.getTryNumber() + 1)
                    .eq(FiSourceReimbursement::getPkPayablebill, fiSourceReimbursement.getPkPayablebill()));
        }
        //采购订单，最多进行10次重推操作，转换失败的业务手动处理，推送失败才进行重试
        List<FiSourceOrder> fiSourceOrderList = fiSourceOrderService.list(Wrappers.<FiSourceOrder>lambdaQuery().eq(FiSourceOrder::getPushStatus, "2")
                .lt(FiSourceOrder::getTryNumber, 10)
                .last("limit 10000"));
        LogUtil.info(log, "pushAgainTryJob 采购订单:" + fiSourceOrderList.size());
        for (FiSourceOrder fiSourceOrder : fiSourceOrderList) {
            fiSourceOrderService.pushAgain(fiSourceOrder.getPkOrder());
            fiSourceOrderService.update(Wrappers.<FiSourceOrder>lambdaUpdate()
                    .set(FiSourceOrder::getTryNumber, fiSourceOrder.getTryNumber() + 1)
                    .eq(FiSourceOrder::getPkOrder, fiSourceOrder.getPkOrder()));
        }
        //采购订单，最多进行10次重推操作，转换失败的业务手动处理，推送失败才进行重试
        List<FiSourceCostOrder> fiSourceCostOrderList = fiSourceCostOrderService.list(Wrappers.<FiSourceCostOrder>lambdaQuery().eq(FiSourceCostOrder::getPushStatus, "2")
                .lt(FiSourceCostOrder::getTryNumber, 10)
                .last("limit 10000"));
        LogUtil.info(log, "pushAgainTryJob 费用结算单:" + fiSourceOrderList.size());
        for (FiSourceCostOrder fiSourceCostOrder : fiSourceCostOrderList) {
            fiSourceCostOrderService.pushAgain(fiSourceCostOrder.getPkFeebalance());
            fiSourceCostOrderService.update(Wrappers.<FiSourceCostOrder>lambdaUpdate()
                    .set(FiSourceCostOrder::getTryNumber, fiSourceCostOrder.getTryNumber() + 1)
                    .eq(FiSourceCostOrder::getPkFeebalance, fiSourceCostOrder.getPkFeebalance()));
        }
        //采购订单，最多进行10次重推操作，转换失败的业务手动处理，推送失败才进行重试
        List<FiSourceVirtualOrder> fiSourceVirtualOrderList = fiSourceVirtualOrderService.list(Wrappers.<FiSourceVirtualOrder>lambdaQuery().eq(FiSourceVirtualOrder::getPushStatus, "2")
                .lt(FiSourceVirtualOrder::getTryNumber, 10)
                .last("limit 10000"));
        LogUtil.info(log, "pushAgainTryJob 虚拟采购订单:" + fiSourceVirtualOrderList.size());
        for (FiSourceVirtualOrder fiSourceVirtualOrder : fiSourceVirtualOrderList) {
            fiSourceVirtualOrderService.pushAgain(fiSourceVirtualOrder.getPkContr());
            fiSourceVirtualOrderService.update(Wrappers.<FiSourceVirtualOrder>lambdaUpdate()
                    .set(FiSourceVirtualOrder::getTryNumber, fiSourceVirtualOrder.getTryNumber() + 1)
                    .eq(FiSourceVirtualOrder::getPkContr, fiSourceVirtualOrder.getPkContr()));
        }
    }

    private List<FiSourceAccountingDocfree> getAccountingDocfreeList(FiSourceAccountingDetailVO fiSourceDetailVO) {
        String assid = fiSourceDetailVO.getAssid();
        String accountcode = dmMapper.getAccountingCode(fiSourceDetailVO.getPkDetail());
        List<FiSourceAccountingDocfree> list = new ArrayList<>();
        if (accountcode != null){
//       cbdl 成本大类---业+传默认值“成本大类”-会计科目：500201、500130、********、500102、**********、**********
            if (accountcode.equals("500201") || accountcode.equals("500130") || accountcode.equals("********")
                    || accountcode.equals("500102") || accountcode.equals("**********") || accountcode.equals("**********")){
                FiSourceAccountingDocfree cbdl = new FiSourceAccountingDocfree();
                cbdl.setType("cbdl");
                cbdl.setValue("其他生产成本");
                list.add(cbdl);
            }else if(accountcode.equals("********") || accountcode.equals("********") || accountcode.equals("********")
            || accountcode.equals("********") || accountcode.equals("********")){
//                dimProdAndSerIns 产品服务（内部）----业+会计科目：********、********、********、********、********
                FiSourceAccountingDocfree cpfw = new FiSourceAccountingDocfree();
                cpfw.setType("dimProdAndSerIns产品服务（内部）");
                cpfw.setValue("产品服务\\硬件\\其他设备");
                list.add(cpfw);
            }
        }
        //费用明细
        FiSourceAccountingDocfree fymx = new FiSourceAccountingDocfree();
        fymx.setType("fymx");
        String zj = dmMapper.getAccountingInoutbusiclass(assid);
        fymx.setValue(zj);
        list.add(fymx);
        //组织机构
        String zzjgValue = dmMapper.getAccountingZzjg(assid);
        FiSourceAccountingDocfree zzjg = new FiSourceAccountingDocfree();
        zzjg.setType("costcenter");
        zzjg.setValue(zzjgValue);
        list.add(zzjg);
        //项目定义
        String projectCode = dmMapper.getAccountingProjectCode(assid);
        FiSourceAccountingDocfree project = new FiSourceAccountingDocfree();
        project.setType("projdef");
        project.setValue(projectCode);
        list.add(project);
        for (FiSourceAccountingDocfree item : list) {
            item.setAssid(assid);
            item.setPkDetail(fiSourceDetailVO.getPkDetail());
        }
        //人工福利薪酬
        FiSourceAccountingDocfree rgflxc = new FiSourceAccountingDocfree();
        rgflxc.setType("rgflxc");
        String rgflxcValue = dmMapper.getRgflxc(fiSourceDetailVO.getPkAccasoa());
        rgflxc.setValue(rgflxcValue);
        list.add(rgflxc);
        //利润中心
        FiSourceAccountingDocfree lrzx = new FiSourceAccountingDocfree();
        lrzx.setType("profitcenter");
        String lrzxValue = dmMapper.getLrzx(fiSourceDetailVO.getPkVoucher());
        lrzx.setValue(lrzxValue);
        list.add(lrzx);
        return list;
    }

    /**
     * 批量获取组织UCN信息的通用方法
     *
     * @param dataList     数据列表
     * @param orgKeyGetter 获取组织主键的函数
     * @param orgSetter    设置UCN的函数
     * @param <T>          数据类型
     */
    private <T> void batchSetOrgUcn(Collection<T> dataList,
                                    Function<T, String> orgKeyGetter,
                                    BiConsumer<T, UCN> orgSetter) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        // 1. 提取所有组织主键并去重
        Set<String> pkOrgs = dataList.stream()
                .map(orgKeyGetter)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        // 2. 批量查询组织信息
        Map<String, UCN> orgUcnMap = dmMapper.batchGetOrgUcn(pkOrgs);

        // 3. 设置UCN信息
        dataList.forEach(data -> {
            String pkOrg = orgKeyGetter.apply(data);
            UCN orgUCN = orgUcnMap.get(pkOrg);
            if (Objects.nonNull(orgUCN)) {
                orgSetter.accept(data, orgUCN);
            }
        });
    }


    /**
     * 根据单据类型过滤源单ID
     *
     * @param detailList 明细列表
     * @param srcBillType 单据类型
     * @param idGetter 主键获取函数
     * @return 过滤后的源单ID列表
     */
    private List<String> filterSourceBillIds(List<FiSourcePuPayableDetailVO> detailList,
                                             String srcBillType,
                                             Function<FiSourcePuPayableDetailVO, String> idGetter) {
        return detailList.stream()
                .filter(d -> srcBillType.equals(d.getSrcBilltype()))
                .map(idGetter)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }
}

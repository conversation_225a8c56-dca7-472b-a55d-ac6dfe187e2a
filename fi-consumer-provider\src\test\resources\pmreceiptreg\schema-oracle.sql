-- auto-generated definition
create table PM_RECEIPTREG
(
    ACCDIF_MONEY         NUMBER(28, 8) default 0,
    ACCDIF_MONEY_GLOBAL  NUMBER(28, 8) default 0,
    ACCDIF_MONEY_GROUP   NUMBER(28, 8) default 0,
    ACCDIF_TAXMNY        NUMBER(28, 8) default 0,
    ACCDIF_TAXMNY_GLOBAL NUMBER(28, 8) default 0,
    ACCDIF_TAXMNY_GROUP  NUMBER(28, 8) default 0,
    AUDITOR              VARCHAR2(20)  default '~',
    AUDITTIME            CHAR(19),
    BILL_CODE            VARCHAR2(40),
    BILL_STATUS          NUMBER(38),
    BILL_TYPE            VARCHAR2(4),
    BILLMAKER            VARCHAR2(20)  default '~',
    B<PERSON>LMAKETIME         CHAR(19),
    BUSI_TYPE            VARCHAR2(20)  default '~',
    BUSIDATE             CHAR(19),
    CHECK_OPINION        VARCHAR2(50),
    CONTRACT_CODE        VARCHAR2(50),
    CONTRACT_NAME        VARCHAR2(50),
    CREATIONTIME         CHAR(19),
    CREATOR              VARCHAR2(20)  default '~',
    CURR_MNY             NUMBER(28, 8) default 0,
    CURR_MNY_GLOBAL      NUMBER(28, 8) default 0,
    CURR_MNY_GROUP       NUMBER(28, 8) default 0,
    DEF1                 VARCHAR2(101),
    DEF10                VARCHAR2(101),
    DEF11                VARCHAR2(101),
    DEF12                VARCHAR2(101),
    DEF13                VARCHAR2(101),
    DEF14                VARCHAR2(101),
    DEF15                VARCHAR2(101),
    DEF16                VARCHAR2(101),
    DEF17                VARCHAR2(101),
    DEF18                VARCHAR2(101),
    DEF19                VARCHAR2(101),
    DEF2                 VARCHAR2(101),
    DEF20                VARCHAR2(101),
    DEF3                 VARCHAR2(101),
    DEF4                 VARCHAR2(101),
    DEF5                 VARCHAR2(101),
    DEF6                 VARCHAR2(101),
    DEF7                 VARCHAR2(101),
    DEF8                 VARCHAR2(101),
    DEF9                 VARCHAR2(101),
    DR                   NUMBER(10)    default 0,
    END_INVOICE_DATE     CHAR(19),
    INVOICE_CODE         VARCHAR2(50),
    INVOICE_MNY          NUMBER(28, 8) default 0,
    INVOICE_MNY_GLOBAL   NUMBER(28, 8) default 0,
    INVOICE_MNY_GROUP    NUMBER(28, 8) default 0,
    INVOICE_NUMBER       VARCHAR2(8),
    INVOICE_TYPE         NUMBER(38)    default 1,
    IS_VIRTUALINVOICE    CHAR,
    ISQUALITYMY          CHAR,
    MEMO                 VARCHAR2(200),
    MODIFIEDTIME         CHAR(19),
    MODIFIER             VARCHAR2(20)  default '~',
    NEXCHANGERATE        VARCHAR2(50),
    PAYER                VARCHAR2(20)  default '~',
    PK_CURRTYPE          VARCHAR2(20)  default '~',
    PK_CURRTYPE_ORG      VARCHAR2(20)  default '~',
    PK_DEPT              VARCHAR2(20)  default '~',
    PK_DEPT_V            VARCHAR2(20)  default '~',
    PK_GROUP             VARCHAR2(20)  default '~',
    PK_ORG               VARCHAR2(20)  default '~',
    PK_ORG_V             VARCHAR2(20)  default '~',
    PK_PAYWAY            VARCHAR2(20)  default '~',
    PK_PROJECT           VARCHAR2(20)  default '~',
    PK_RECEIPTREG        CHAR(20) not null
        constraint PK_PM_RECEIPTREG
            primary key,
    PK_SALESMAN          VARCHAR2(20)  default '~',
    PK_SUPBANKACC        VARCHAR2(20)  default '~',
    PK_SUPPLIER          VARCHAR2(20)  default '~',
    PK_TRANSITYPE        VARCHAR2(20)  default '~',
    PREPAID_MNY          NUMBER(28, 8) default 0,
    QUAL_TAXMNY          NUMBER(28, 8) default 0,
    SRC_BILL_TYPE        VARCHAR2(50)  default '~',
    SRC_PK_BILL          VARCHAR2(20),
    START_INVOICE_DATE   CHAR(19),
    TAX                  NUMBER(28, 8) default 0,
    TOT_TAXMNY           NUMBER(28, 8) default 0,
    TOT_TAXMNY_GLOBAL    NUMBER(28, 8) default 0,
    TOT_TAXMNY_GROUP     NUMBER(28, 8) default 0,
    TRANSI_TYPE          VARCHAR2(30),
    TS                   CHAR(19)      default to_char(current_timestamp, 'yyyy-mm-dd hh24:mi:ss'),
    VERI_TAXMNY          NUMBER(28, 8) default 0,
    VERI_TAXMNY_GLOBAL   NUMBER(28, 8) default 0,
    VERI_TAXMNY_GROUP    NUMBER(28, 8) default 0,
    VERIFY_MNY           NUMBER(28, 8) default 0,
    VERIFY_MNY_GLOBAL    NUMBER(28, 8) default 0,
    VERIFY_MNY_GROUP     NUMBER(28, 8) default 0,
    VERIFY_TAX           NUMBER(28, 8) default 0,
    SAGA_BTXID           VARCHAR2(64),
    SAGA_FROZEN          NUMBER(38)    default 0,
    SAGA_GTXID           VARCHAR2(64),
    SAGA_STATUS          NUMBER(38)    default 0,
    QUAL_MNY_DEADLINE    CHAR(19),
    BILL_NSPARE_MNY      NUMBER(28, 8) default '0'
);
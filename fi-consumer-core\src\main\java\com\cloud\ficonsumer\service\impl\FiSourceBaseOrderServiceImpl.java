
package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiSourceBaseOrder;
import com.cloud.ficonsumer.mapper.FiSourceBaseOrderMapper;
import com.cloud.ficonsumer.service.FiSourceBaseOrderService;
import org.springframework.stereotype.Service;

/**
 * 订单源数据主表
 *
 * <AUTHOR>
 * @date 2025-06-03 11:10:09
 */
@Service
public class FiSourceBaseOrderServiceImpl extends ServiceImpl<FiSourceBaseOrderMapper, FiSourceBaseOrder> implements FiSourceBaseOrderService {

}

package com.cloud.ficonsumer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloud.ficonsumer.entity.FiConvertProjPaybillDetail;
import com.cloud.ficonsumer.mapper.FiConvertProjPaybillDetailMapper;
import com.cloud.ficonsumer.service.FiConvertProjPaybillDetailService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class FiConvertProjPaybillDetailServiceImpl extends ServiceImpl<FiConvertProjPaybillDetailMapper, FiConvertProjPaybillDetail> implements FiConvertProjPaybillDetailService {

}

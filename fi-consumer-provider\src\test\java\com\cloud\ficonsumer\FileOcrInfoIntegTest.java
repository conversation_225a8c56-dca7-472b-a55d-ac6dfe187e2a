package com.cloud.ficonsumer;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.cloud.cloud.common.core.util.SpringContextHolder;
import com.cloud.discovery.common.nacos.configuration.NacosAutoConfiguration;
import com.cloud.ficonsumer.entity.FiSourceFileInfo;
import com.cloud.ficonsumer.entity.FiSourceFileOcrInfo;
import com.cloud.ficonsumer.enums.PushStatusEnum;
import com.cloud.ficonsumer.mapper.FileManageMapper;
import com.cloud.ficonsumer.service.ApiProjectService;
import com.cloud.ficonsumer.service.FiSourceFileInfoService;
import com.cloud.ficonsumer.service.FiSourceFileOcrInfoService;
import com.cloud.ficonsumer.vo.FiSourceFileInfoVO;
import com.cloud.ficonsumer.vo.FiSourceFileOcrInfoVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.security.oauth2.OAuth2AutoConfiguration;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.MockBeans;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.datasource.init.DatabasePopulatorUtils;
import org.springframework.jdbc.datasource.init.ResourceDatabasePopulator;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.sql.DataSource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = FiConsumerApplication.class)
@ActiveProfiles("test")
@MockBeans({
        @MockBean(OAuth2AutoConfiguration.class),
        @MockBean(NacosAutoConfiguration.class),
        @MockBean(ResourceServerProperties.class)
})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class FileOcrInfoIntegTest {

    @SpyBean
    private FileManageMapper fileManageMapper;

    @SpyBean
    private ApiProjectService apiProjectService;

    @SpyBean
    private FiSourceFileInfoService fiSourceFileInfoService;

    @Autowired
    private FiSourceFileOcrInfoService fiSourceFileOcrInfoService;

    @BeforeEach
    public void initData() {
        DataSource dynamicDataSource = SpringContextHolder.getBean(DataSource.class);

        // 初始化 filemanage 数据库数据
        DynamicDataSourceContextHolder.push("filemanage");
        try {
            ResourceDatabasePopulator filemanagePopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("fileocrinfo/schema-filemanage.sql"),
                    new ClassPathResource("fileocrinfo/data-filemanage.sql")
            );
            DatabasePopulatorUtils.execute(filemanagePopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }

        // 初始化 mysql 库数据
        DynamicDataSourceContextHolder.push("mysql");
        try {
            ResourceDatabasePopulator mysqlPopulator = new ResourceDatabasePopulator(
                    new ClassPathResource("fileocrinfo/schema-ficonsumer.sql")
            );
            DatabasePopulatorUtils.execute(mysqlPopulator, dynamicDataSource);
        } finally {
            DynamicDataSourceContextHolder.clear();
        }
    }

    @Test
    public void testProcessFileOcrInfo() {
        // 1. 从 影像 数据库中获取源文件数据
        List<FiSourceFileOcrInfoVO> fileOcrInfoVOs = fileManageMapper.listFileOrcInfo(null);

        // 2. 验证获取到的数据量
        assertEquals(1, fileOcrInfoVOs.size(), "影像数据库 进项发票表 中应存在1条数据");

        // 3. 循环处理每个文件的OCR信息
        for (FiSourceFileOcrInfoVO fileOcrInfoVO : fileOcrInfoVOs) {
            List<FiSourceFileInfoVO> fileInfoVOS = fileManageMapper.getFileInfos(fileOcrInfoVO.getFileId());
            apiProjectService.processFileOcrInfo(fileOcrInfoVO, fileInfoVOS);
        }

        // 4. 从 MySQL 数据库中获取处理后的源文件和转换后文件数据
        List<FiSourceFileOcrInfo> fileOcrInfos = fiSourceFileOcrInfoService.list();
        List<FiSourceFileInfo> fileInfos = fiSourceFileInfoService.list();

        // 5. 验证MySQL数据库中的数据量
        assertEquals(1, fileOcrInfos.size(), "fi-consumer 进项发票表 中应存在1条数据");
        assertEquals(1, fileInfos.size(), "fi-consumer 文件表 中应存在1条数据");

        // 6. 再次从 影像 数据库中获取数据，验证处理状态
        fileOcrInfoVOs = fileManageMapper.listFileOrcInfo(null);
        for (FiSourceFileOcrInfoVO fileOcrInfoVO : fileOcrInfoVOs) {
            assertEquals("1", fileOcrInfoVO.getSyncStatus(), "ERP 源文件的处理状态应更新为已处理");
            assertEquals(String.valueOf(PushStatusEnum.PUSH_DING.getCode()), fileOcrInfoVO.getIntegrationStatus(), "ERP 源文件的集成状态应更新为集成中");
            assertEquals(PushStatusEnum.PUSH_DING.getCode(), fileOcrInfoVO.getPushStatus(), "ERP 源文件的推送状态应更新为集成中");
        }
    }
}

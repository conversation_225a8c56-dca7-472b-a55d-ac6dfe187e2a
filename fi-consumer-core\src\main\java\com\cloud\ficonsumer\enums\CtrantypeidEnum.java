package com.cloud.ficonsumer.enums;

/**
 * 审批状态枚举  取字典pm_process_approve_status
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
public enum CtrantypeidEnum {

    Enum1("21-Cxx-001","11111111", "0L02","是","工程项目物资采购"),
    Enum2("21-Cxx-007", "21-Cxx-007","0L10","否","服务类采购"),
    Enum3("21-Cxx-015", "21-Cxx-23","0L10","否","固定资产采购(入库不传存货)"),
    Enum4("21-Cxx-018", "21-Cxx-18","0L10","否","国网电商采购(服务类)"),
    Enum5("21-Cxx-N01", "21-Cxx-20","0L02","是","普通采购"),
    Enum6("21-Cxx-N02", "21-Cxx-017","0L02","是","直运采购(销售)"),
    Enum7("21-Cxx-N03", "21-Cxx-21","0L02","是","直运采购(入库推出库)"),
    Enum8("21-Cxx-N05", "21-Cxx-17","0L02","是","国网电商采购(货物类)"),
    Enum9("21-Cxx-N06", "21-Cxx-24","0L02","是","销售协同采购"),
    Enum14("", "","","",""),
    ;
    private String code;
    private String testCode;
    private String thirdCode;
    private String recepId;
    private String name;

    CtrantypeidEnum(String code,String testCode, String thirdCode, String recepId, String name) {
        this.code = code;
        this.testCode = testCode;
        this.thirdCode = thirdCode;
        this.recepId = recepId;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getTestCode() {
        return testCode;
    }

    public String getThirdCode() {
        return thirdCode;
    }

    public String getRecepId() {
        return recepId;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code获取枚举类
     *
     * @param code
     * @return
     */
    public static CtrantypeidEnum getByCode(String code) {
        if(null == code) {
            return CtrantypeidEnum.Enum14;
        }
        for (CtrantypeidEnum en : values()) {
            if (code.equals(en.getCode())) {
                return en;
            }
        }
        return CtrantypeidEnum.Enum14;
    }

    /**
     * 根据code获取枚举类
     *
     * @param code
     * @return
     */
    public static CtrantypeidEnum getByTestCode(String code) {
        if(null == code) {
            return CtrantypeidEnum.Enum14;
        }
        for (CtrantypeidEnum en : values()) {
            if (code.equals(en.getTestCode())) {
                return en;
            }
        }
        return CtrantypeidEnum.Enum14;
    }

}
